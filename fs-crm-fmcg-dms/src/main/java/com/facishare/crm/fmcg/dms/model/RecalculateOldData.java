package com.facishare.crm.fmcg.dms.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

public interface RecalculateOldData {

    @Data
    class Arg implements Serializable {

        @JSONField(name = "tenant_id_list")
        @JsonProperty(value = "tenant_id_list")
        @SerializedName("tenant_id_list")
        private List<String> tenantIdList;
    }

    @Data
    class Result implements Serializable {

        private String placeholder = "__placeholder";
    }
}
