package com.facishare.crm.fmcg.dms.web.manager;

import com.facishare.crm.fmcg.dms.i18n.I18NKeys;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Component
public class AccountsPayableCommonManager {
    public static final String CHECK_MATCH_DATA = "check_match_data";
    @Resource
    protected ServiceFacade serviceFacade;

    @Resource
    private AccountsPayableManager accountsPayableManager;
    @Resource
    private AccountsPayableDetailManager accountsPayableDetailManager;
    @Resource
    private CommonAccountsReceivableNoteManager commonAccountsReceivableNoteManager;

    private static Integer AUTO_MATCH_RECEIVABLE_LIMIT;
    private static Integer MATCH_SAVE_MAX;

    static {
        ConfigFactory.getInstance().getConfig("fs-crm-sales-config",
                config -> {
                    AUTO_MATCH_RECEIVABLE_LIMIT = config.getInt("auto_match_receivable_limit", 100);
                    MATCH_SAVE_MAX = config.getInt("match_save_max", 1000);
                });
    }

    public List<IObjectData> autoMatchPayable(User user, String supplierId, BigDecimal amount) {
        List<IObjectData> objectDataList;
        List<IObjectData> blueApObjectDataList
                = accountsPayableManager.getMatchApBySupplierId(user, supplierId, PayFields.PAY_TYPE__BLUE);
        List<IObjectData> redApObjectDataList
                = accountsPayableManager.getMatchApBySupplierId(user, supplierId, PayFields.PAY_TYPE__RED);

        objectDataList = autoMatchApBlueAndRed(redApObjectDataList, blueApObjectDataList, amount);
        //VirThisMatchAmount为核销掉剩余金额，而终端根据这个字段显示可核销金额，所以用NoSettledAmount-VirThisMatchAmount
        convertResultVirMatchAmount(objectDataList);
        return objectDataList;
    }

    private List<IObjectData> autoMatchApBlueAndRed(List<IObjectData> redList, List<IObjectData> blueList, BigDecimal amount) {
        if (Objects.isNull(amount)) {
            amount = BigDecimal.ZERO;
        }
        boolean isRed = amount.compareTo(BigDecimal.ZERO) < 0;
        redList.forEach(x -> x.set(AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT
                , x.get(AccountsPayableNoteFields.PRE_MATCH_AMOUNT)));
        blueList.forEach(x -> x.set(AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT
                , x.get(AccountsPayableNoteFields.PRE_MATCH_AMOUNT)));
        List<IObjectData> apOffApMatchList;

        //先红蓝对冲，看剩下的情况
        //红蓝判断没什么用，下次去掉
        if (!isRed) {
            apOffApMatchList = matchApOffsetApForAutoMatch(blueList, redList, AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT, AUTO_MATCH_RECEIVABLE_LIMIT);
        } else {
            apOffApMatchList = matchApOffsetApForAutoMatch(redList, blueList, AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT, AUTO_MATCH_RECEIVABLE_LIMIT);
        }

        if (apOffApMatchList.size() == AUTO_MATCH_RECEIVABLE_LIMIT) {
            return apOffApMatchList;
        }

        //红蓝对冲后，有剩余金额的数据为最后一个冲一部分的，获取剩余待核销部分余额
        IObjectData remianData = null;
        BigDecimal remainDataAmount = BigDecimal.ZERO;
        Optional<IObjectData> oRemianData = apOffApMatchList.stream()
                .filter(x -> x.get(AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT, BigDecimal.class, BigDecimal.ZERO).compareTo(BigDecimal.ZERO) != 0)
                .findFirst();
        if (oRemianData.isPresent()) {
            remianData = oRemianData.get();
        }
        if (!Objects.isNull(remianData)) {
            remainDataAmount = remianData.get(AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        }
        //余额跟付款金额符号相同，则可以继续核销
        //判断剩余金额是否能把付款全部核销掉
        //能核销掉，直接核销掉，剩余金额为剩余待核销金额付款金额
        if (remainDataAmount.multiply(amount).compareTo(BigDecimal.ZERO) >= 0) {
            if (remainDataAmount.abs().compareTo(amount.abs()) >= 0) {
                if (!Objects.isNull(remianData)) {
                    remianData.set(AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT, remainDataAmount.subtract(amount));
                }
                return apOffApMatchList;
            }
        } else {
            return apOffApMatchList;
        }

        //大多数情况，付款金额还能继续核销，把红蓝对冲剩余数据金额直接核销掉付款剩余金额为回款金额-对冲后剩余金额
        //剩余付款金额跟生效的应收继续核
        if (remainDataAmount.compareTo(BigDecimal.ZERO) != 0) {
            amount = amount.subtract(remainDataAmount);
            remianData.set(AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT, BigDecimal.ZERO);
        }

        int matchedSize = apOffApMatchList.size();

        BigDecimal redTotalAmount = redList.stream()
                .map(x -> x.get(AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT, BigDecimal.class, BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal blueTotalAmount = blueList.stream()
                .map(x -> x.get(AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT, BigDecimal.class, BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //蓝字付款，蓝应收没钱了，不核了
        if (!isRed && blueTotalAmount.compareTo(BigDecimal.ZERO) == 0) {
            return apOffApMatchList;
        }
        if (isRed && redTotalAmount.compareTo(BigDecimal.ZERO) == 0) {
            return apOffApMatchList;
        }

        //巡航递减继续核销
        List<IObjectData> remainDataList = new ArrayList<>();
        if (!isRed) {
            blueList.removeIf(x -> apOffApMatchList.contains(x));
            remainDataList = getMatchList(blueList, amount, AUTO_MATCH_RECEIVABLE_LIMIT - matchedSize);
        }
        if (isRed) {
            redList.removeIf(x -> apOffApMatchList.contains(x));
            remainDataList = getMatchList(redList, amount, AUTO_MATCH_RECEIVABLE_LIMIT - matchedSize);
        }

        List<IObjectData> result = new ArrayList<>();
        result.addAll(apOffApMatchList);
        result.addAll(remainDataList);
        return result;
    }

    private void convertResultVirMatchAmount(List<IObjectData> dataList) {
        for (IObjectData data : dataList) {
            BigDecimal a1 = data.get(AccountsPayableNoteFields.PRE_MATCH_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
            BigDecimal a2 = data.get(AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
            data.set(AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT, a1.subtract(a2));
        }
        dataList.removeIf(x -> x.get(AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT, BigDecimal.class, BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0);
    }

    private List<IObjectData> getMatchList(List<IObjectData> apList, BigDecimal amount, int limitSize) {
        List<IObjectData> listResult = new ArrayList<>();
        for (IObjectData ap : apList) {
            BigDecimal settledAmount = ap.get(AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
            if (settledAmount.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            amount = amount.subtract(settledAmount);
            if (amount.multiply(settledAmount).compareTo(BigDecimal.ZERO) >= 0) {
                ap.set(AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT, BigDecimal.ZERO);
                listResult.add(ap);
            } else {
                ap.set(AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT, settledAmount.subtract(amount.add(settledAmount)));
                listResult.add(ap);
                break;
            }
            if (listResult.size() == limitSize) {
                break;
            }
        }
        return listResult;
    }

    @Transactional
    public void bulkCreateMatchNote(User user, String payId, List<Map> datas) {
        IObjectData pay = null;
        if (!Strings.isNullOrEmpty(payId)) {
            pay = serviceFacade.findObjectData(user, payId, ApiNames.PAY_OBJ);
            if (Objects.isNull(pay)) {
                throw new ValidateException(I18N.text(I18NKeys.ACCOUNTS_PAYABLE_COMMON_MANAGER_0));
            }
            pay.set(CHECK_MATCH_DATA, datas);
            if (!commonAccountsReceivableNoteManager.checkPayMatchNote(user, pay)) {
                return;
            }
        }

        List<String> dataIds = datas.stream().map(x -> x.get("_id").toString()).collect(Collectors.toList());
        if (CollectionUtils.empty(dataIds)) {
            return;
        }
        MetaDataFindService.QueryContext queryContext = MetaDataFindService.QueryContext.builder()
                .user(user).notFillQuote(true).skipRelevantTeam(true).calculateCount(true).calculateFormula(true).build();
        List<IObjectData> accountsPaybleData = serviceFacade.findObjectDataByIdsWithQueryContext(queryContext, dataIds, ApiNames.ACCOUNTS_PAYABLE_NOTE_OBJ);
        if (CollectionUtils.empty(accountsPaybleData)) {
            return;
        }
        accountsPaybleData = accountsPaybleData.stream()
                .sorted(Comparator.comparing(x -> x.get(AccountsPayableNoteFields.NOTE_DATE, String.class, "")))
                .collect(Collectors.toList());
        accountsPaybleData.forEach(data -> {
            datas.stream()
                    .filter(argData -> Objects.equals(data.getId(), argData.get("_id").toString()))
                    .findFirst()
                    .ifPresent(argData -> data.set(AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT, argData.get(AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT)));
        });

        List<IObjectData> blueAccountsPayableData = new ArrayList<>();
        List<IObjectData> redAccountsPayableData = new ArrayList<>();
        for (IObjectData apData : accountsPaybleData) {
            BigDecimal virThisMatchAmount = apData.get(AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
            BigDecimal noSettledAmount = apData.get(AccountsPayableNoteFields.PRE_MATCH_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
            if (noSettledAmount.multiply(virThisMatchAmount).compareTo(BigDecimal.ZERO) < 0
                    || virThisMatchAmount.abs().compareTo(noSettledAmount.abs()) > 0
                    || virThisMatchAmount.compareTo(BigDecimal.ZERO) == 0) {
                throw new ValidateException(String.format(I18N.text(I18NKeys.ACCOUNTS_PAYABLE_COMMON_MANAGER_1), apData.getName()));
            }

            if (virThisMatchAmount.compareTo(BigDecimal.ZERO) > 0) {
                blueAccountsPayableData.add(apData);
            } else {
                redAccountsPayableData.add(apData);
            }
        }

        List<IObjectData> accountsPayableDetails = accountsPayableDetailManager.details(user, dataIds);

        if (CollectionUtils.empty(accountsPayableDetails)) {
            return;
        }

        Map<String, List<IObjectData>> accountsPayableDetailsMap = accountsPayableDetails.stream()
                .collect(Collectors.groupingBy(data -> data.get(AccountsPayableDetailFields.ACCOUNTS_PAYABLE_NOTE_ID, String.class)));
        //核销单不一定都能核完，所以明细不一定核的完，循环明细一条一条算能核多少
        if (CollectionUtils.notEmpty(blueAccountsPayableData)) {
            for (IObjectData data : blueAccountsPayableData) {
                List<IObjectData> dataDetails = accountsPayableDetailsMap.get(data.getId());
                fillDetailVirMatchAmount(dataDetails, data);
            }
        }
        if (CollectionUtils.notEmpty(redAccountsPayableData)) {
            for (IObjectData data : redAccountsPayableData) {
                List<IObjectData> dataDetails = accountsPayableDetailsMap.get(data.getId());
                fillDetailVirMatchAmount(dataDetails, data);
            }
        }
        //开始冲应付
        //冲应付同时生成核销单
        //生成核销单时同时冲应付明细，生成核销明细
        //最后统一保存
        Map<IObjectData, List<IObjectData>> matchNoteAndDetailMap;
        matchNoteAndDetailMap = doMatch(user, pay, blueAccountsPayableData, redAccountsPayableData
                , AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT, accountsPayableDetailsMap);

        if (CollectionUtils.notEmpty(matchNoteAndDetailMap)) {
            List<IObjectData> matchNoteList = new ArrayList<>();
            List<IObjectData> matchNoteDetailList = new ArrayList<>();

            for (Map.Entry<IObjectData, List<IObjectData>> matchNote : matchNoteAndDetailMap.entrySet()) {
                matchNoteList.add(matchNote.getKey());
                matchNoteDetailList.addAll(matchNote.getValue());
            }
            if (matchNoteList.size() + matchNoteDetailList.size() > MATCH_SAVE_MAX) {
                throw new ValidateException(I18N.text("accountsreceivable.validate.matchnote.too.manny.data"));
            }
            if (CollectionUtils.notEmpty(matchNoteList) && CollectionUtils.notEmpty(matchNoteDetailList)) {
                serviceFacade.bulkSaveObjectData(matchNoteList, user);
                serviceFacade.bulkSaveObjectData(matchNoteDetailList, user);
            }
        }
    }

    /*
     * 应付冲应付，应付明细冲应付明细
     * 包括蓝冲红，和红冲红
     * 付款可能蓝或者红
     * 根据冲销结果生成核销单和核销单明细
     * */
    private Map<IObjectData, List<IObjectData>> doMatch(User user, IObjectData pay
            , List<IObjectData> debitDataList, List<IObjectData> creditDataList
            , String fieldName, Map<String, List<IObjectData>> apDataDetailsMap) {
        Map<IObjectData, List<IObjectData>> matchNoteDetailsMap = new HashMap<>();
        BigDecimal remainAmount;
        for (IObjectData debitData : debitDataList) {
            List<IObjectData> debitDetails = apDataDetailsMap.get(debitData.getId());
            BigDecimal debitAmount = debitData.get(fieldName, BigDecimal.class, BigDecimal.ZERO);
            if (debitAmount.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            for (IObjectData creditData : creditDataList) {
                List<IObjectData> creditDetails = apDataDetailsMap.get(creditData.getId());
                debitAmount = debitData.get(fieldName, BigDecimal.class, BigDecimal.ZERO);
                BigDecimal creditAmount = creditData.get(fieldName, BigDecimal.class, BigDecimal.ZERO);
                if (creditAmount.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                remainAmount = debitAmount.add(creditAmount);

                if (debitAmount.abs().compareTo(creditAmount.abs()) > 0) {
                    debitData.set(fieldName, remainAmount);
                    creditData.set(fieldName, BigDecimal.ZERO);
                    //生成核销单
                    Map<IObjectData, List<IObjectData>> matchNoteAndDetails =
                            generateMatchNote(user, debitData, creditData, debitDetails, creditDetails, creditAmount.negate(), creditAmount);
                    matchNoteDetailsMap.putAll(matchNoteAndDetails);
                    continue;
                } else {
                    debitData.set(fieldName, BigDecimal.ZERO);
                    creditData.set(fieldName, remainAmount);
                    //生成核销单，debitAmount其实和creditAmount.negate()是一样的
                    Map<IObjectData, List<IObjectData>> matchNoteAndDetails =
                            generateMatchNote(user, debitData, creditData, debitDetails, creditDetails, debitAmount, debitAmount.negate());
                    matchNoteDetailsMap.putAll(matchNoteAndDetails);
                    break;
                }
            }
        }
        if (Objects.isNull(pay)) {
            return matchNoteDetailsMap;
        }
        //应付冲应付主数据，看下还有没有待核销金额，有继续跟付款核销
        BigDecimal apDebitTotalAmount = debitDataList.stream()
                .map(x -> x.get(fieldName, BigDecimal.class, BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal arCreditTotalAmount = creditDataList.stream()
                .map(x -> x.get(fieldName, BigDecimal.class, BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (apDebitTotalAmount.compareTo(BigDecimal.ZERO) != 0 || arCreditTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
            //还有剩余可核销金额，跟付款待核销金额核，这个时候付款和剩余金额一定是同号的，因为上面校验已经有校验了
            //此处可以通过相乘来判断符号是否相等，不需要依赖校验，更通用些
            BigDecimal payAmount = pay.get(PayFields.NO_MATCH_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
            List<IObjectData> dataList = new ArrayList<>();
            if (apDebitTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                dataList = debitDataList;
            } else {
                dataList = creditDataList;
            }
            for (IObjectData data : dataList) {
                List<IObjectData> debitDetails = apDataDetailsMap.get(data.getId());
                BigDecimal debitAmount = data.get(fieldName, BigDecimal.class, BigDecimal.ZERO);
                if (debitAmount.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                if (payAmount.abs().compareTo(debitAmount.abs()) < 0) {
                    break;
                }
                payAmount = payAmount.subtract(debitAmount);
                //应付冲付款，付款没有明细，冲掉的金额debitAmount符号相同，所以是一样的
                Map<IObjectData, List<IObjectData>> matchNoteAndDetails =
                        generateMatchNote(user, data, pay, debitDetails, null, debitAmount, debitAmount);
                matchNoteDetailsMap.putAll(matchNoteAndDetails);
            }
        }
        return matchNoteDetailsMap;
    }

    private void fillDetailVirMatchAmount(List<IObjectData> detailList, IObjectData apData) {
        BigDecimal virNoMatchAmount = apData.get(AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        for (IObjectData detail : detailList) {
            BigDecimal detailNoSettledAmount = detail.get(AccountsPayableNoteFields.PRE_MATCH_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
            virNoMatchAmount = virNoMatchAmount.subtract(detailNoSettledAmount);
            if (virNoMatchAmount.multiply(detailNoSettledAmount).compareTo(BigDecimal.ZERO) > 0) {
                detail.set(AccountsPayableDetailFields.VIR_THIS_MATCH_AMOUNT, detailNoSettledAmount);
            } else {
                detail.set(AccountsPayableDetailFields.VIR_THIS_MATCH_AMOUNT, virNoMatchAmount.add(detailNoSettledAmount));
                break;
            }
        }
    }

    /*
     * 应付冲应付，应付明细冲应付明细
     * 包括蓝冲红，和红冲红
     * 付款可能蓝或者红
     * 根据冲销结果生成核销单和核销单明细
     * */
    private List<IObjectData> matchApDetailOffsetApDetail(User user, List<IObjectData> debitDataList, List<IObjectData> creditDataList
            , String fieldName, String masterDataId) {
        List<IObjectData> matchNoteDetailList = new ArrayList<>();
        BigDecimal remainAmount = BigDecimal.ZERO;
        for (IObjectData debitData : debitDataList) {
            BigDecimal debitAmount = debitData.get(fieldName, BigDecimal.class, BigDecimal.ZERO);
            if (debitAmount.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            for (IObjectData creditData : creditDataList) {
                debitAmount = debitData.get(fieldName, BigDecimal.class, BigDecimal.ZERO);
                BigDecimal creditAmount = creditData.get(fieldName, BigDecimal.class, BigDecimal.ZERO);
                if (creditAmount.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                remainAmount = debitAmount.add(creditAmount);

                if (debitAmount.abs().compareTo(creditAmount.abs()) > 0) {
                    debitData.set(fieldName, remainAmount);
                    creditData.set(fieldName, BigDecimal.ZERO);
                    IObjectData matchNoteDetail = buildMatchNoteDetailData(user, masterDataId, null, debitData, creditData, creditAmount.negate(), creditAmount);
                    matchNoteDetailList.add(matchNoteDetail);
                    continue;
                } else {//蓝小于等于红，跳出循环，找下一个蓝
                    debitData.set(fieldName, BigDecimal.ZERO);
                    creditData.set(fieldName, remainAmount);

                    IObjectData matchNoteDetail = buildMatchNoteDetailData(user, masterDataId, null, debitData, creditData, debitAmount, debitAmount.negate());
                    matchNoteDetailList.add(matchNoteDetail);
                    break;
                }
            }
        }
        return matchNoteDetailList;
    }

    private List<IObjectData> matchApOffsetApForAutoMatch(List<IObjectData> debitDataList, List<IObjectData> creditDataList
            , String fieldName, int limitSize) {
        List<IObjectData> dataList = new ArrayList<>();
        BigDecimal remainAmount;
        for (IObjectData debitData : debitDataList) {
            BigDecimal debitAmount = debitData.get(fieldName, BigDecimal.class, BigDecimal.ZERO);
            if (debitAmount.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            for (IObjectData creditData : creditDataList) {
                debitAmount = debitData.get(fieldName, BigDecimal.class, BigDecimal.ZERO);
                BigDecimal creditAmount = creditData.get(fieldName, BigDecimal.class, BigDecimal.ZERO);
                if (creditAmount.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                remainAmount = debitAmount.add(creditAmount);

                if (debitAmount.abs().compareTo(creditAmount.abs()) > 0) {
                    debitData.set(fieldName, remainAmount);
                    creditData.set(fieldName, BigDecimal.ZERO);
                    if (!dataList.contains(creditData)) {
                        dataList.add(creditData);
                    }
                    if (!dataList.contains(debitData)) {
                        dataList.add(debitData);
                    }
                    if (dataList.size() == limitSize) {
                        return dataList;
                    }
                    continue;
                } else {//蓝小于等于红，跳出循环，找下一个蓝
                    debitData.set(fieldName, BigDecimal.ZERO);
                    creditData.set(fieldName, remainAmount);
                    if (!dataList.contains(debitData)) {
                        dataList.add(debitData);
                    }
                    if (!dataList.contains(creditData)) {
                        dataList.add(creditData);
                    }
                    if (dataList.size() == limitSize) {
                        return dataList;
                    }
                    break;
                }
            }
        }

        return dataList;
    }

    private Map<IObjectData, List<IObjectData>> generateMatchNote(User user, IObjectData debitData, IObjectData creditData, List<IObjectData> debitDetailDatas, List<IObjectData> creditDetailDatas
            , BigDecimal debitMatchAmount, BigDecimal creditMatchAmount) {
        Map<IObjectData, List<IObjectData>> matchNoteAndDetailMap = new HashMap<>();

        IObjectData matchNote = buildMatchNoteData(user, debitData, creditData, debitMatchAmount, creditMatchAmount);

        if (CollectionUtils.notEmpty(creditDetailDatas)) {//上面已经计算过每笔明细能冲销的金额，跟应收冲一样，冲掉明细，理论上红蓝明细金额全能冲，不会有剩余
            List<IObjectData> matchNoteDetails = matchApDetailOffsetApDetail(user, debitDetailDatas, creditDetailDatas
                    , AccountsPayableDetailFields.VIR_THIS_MATCH_AMOUNT, matchNote.getId());
            matchNoteAndDetailMap.put(matchNote, matchNoteDetails);
        } else {//没有从对象，应付冲付款，红冲红，蓝冲蓝，符号相同，金额相等，由于应收金额不能大于回款金额（前面有校验），所以应收能满额冲掉，每笔明细能核销多少在应收能核销金额确定时候就确定了
            List<IObjectData> matchNoteDetails = new ArrayList<>();
            for (IObjectData debitDetail : debitDetailDatas) {
                BigDecimal thisMatchAmount = debitDetail.get(AccountsPayableDetailFields.VIR_THIS_MATCH_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
                if (thisMatchAmount.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                IObjectData matchNoteDetail = buildMatchNoteDetailData(user, matchNote.getId(), creditData, debitDetail, null, thisMatchAmount, thisMatchAmount);
                matchNoteDetails.add(matchNoteDetail);
            }
            matchNoteAndDetailMap.put(matchNote, matchNoteDetails);
        }

        return matchNoteAndDetailMap;
    }


    private IObjectData buildMatchNoteData(User user, IObjectData debitData, IObjectData creditData, BigDecimal debitMatchAmount, BigDecimal cebitMatchAmount) {
        String supplierId = debitData.get(AccountsPayableNoteFields.SUPPLIER_ID, String.class);
        IObjectData matchNote = new ObjectData();
        matchNote.setId(serviceFacade.generateId());
        matchNote.setDescribeApiName(ApiNames.MATCH_NOTE_OBJ);
        matchNote.setOwner(Lists.newArrayList(user.getUpstreamOwnerIdOrUserId()));
        matchNote.setRecordType(ObjectData.RECORD_TYPE_DEFAULT);
        matchNote.setTenantId(user.getTenantId());
        matchNote.set(MatchNoteFields.THIS_MATCH_AMOUNT, debitMatchAmount);
        matchNote.set(MatchNoteFields.CREDIT_MATCH_AMOUNT, cebitMatchAmount);
        matchNote.set(MatchNoteFields.DEBIT_DATA_ID, debitData.getId());
        matchNote.set(MatchNoteFields.DEBIT_API_NAME, debitData.getDescribeApiName());
        matchNote.set(MatchNoteFields.CREDIT_DATA_ID, creditData.getId());
        matchNote.set(MatchNoteFields.CREDIT_API_NAME, creditData.getDescribeApiName());
        matchNote.set(MatchNoteFields.MATCH_DATE, System.currentTimeMillis());
        if (Objects.equals(ApiNames.ACCOUNTS_PAYABLE_NOTE_OBJ, debitData.getDescribeApiName())) {

            if (Objects.equals(ApiNames.ACCOUNTS_PAYABLE_NOTE_OBJ, creditData.getDescribeApiName())) {
                matchNote.set(MatchNoteFields.VERIFICATION_METHOD, MatchNoteFields.VERIFICATION_METHOD__AP_OFFSET_AP);
            }
        }
        if (Objects.equals(ApiNames.PAY_OBJ, creditData.getDescribeApiName())) {

            if (Objects.equals(ApiNames.ACCOUNTS_PAYABLE_NOTE_OBJ, debitData.getDescribeApiName())) {
                matchNote.set(MatchNoteFields.VERIFICATION_METHOD, MatchNoteFields.VERIFICATION_METHOD__PAY_OFFSET_AP);
            }
        }
        matchNote.set(MatchNoteFields.SUPPLIER_ID, supplierId);
        return matchNote;
    }

    private IObjectData buildMatchNoteDetailData(User user, String matchNoteId, IObjectData creditData
            , IObjectData debitDetailData, IObjectData creditDetailData, BigDecimal debitMatchAmount, BigDecimal cebitMatchAmount) {
        //目前debitData只支持应收单
        String productId = debitDetailData.get(AccountsPayableDetailFields.PRODUCT_ID, String.class);

        IObjectData matchNoteDetailData = new ObjectData();
        matchNoteDetailData.setDescribeApiName(ApiNames.MATCH_NOTE_DETAIL_OBJ);
        matchNoteDetailData.setOwner(Lists.newArrayList(user.getUpstreamOwnerIdOrUserId()));
        matchNoteDetailData.setRecordType(ObjectData.RECORD_TYPE_DEFAULT);
        matchNoteDetailData.setTenantId(user.getTenantId());
        if (!Strings.isNullOrEmpty(matchNoteId)) {
            matchNoteDetailData.set(MatchNoteDetailFields.MATCHNOTE_ID, matchNoteId);
        }

        matchNoteDetailData.set(MatchNoteDetailFields.DEBIT_DATA_ID, debitDetailData.get(AccountsPayableDetailFields.ACCOUNTS_PAYABLE_NOTE_ID, String.class));
        matchNoteDetailData.set(MatchNoteDetailFields.DEBIT_API_NAME, ApiNames.ACCOUNTS_PAYABLE_NOTE_OBJ);
        matchNoteDetailData.set(MatchNoteDetailFields.DEBIT_DETAIL_API_NAME, debitDetailData.getDescribeApiName());
        matchNoteDetailData.set(MatchNoteDetailFields.DEBIT_DETAIL_DATA_ID, debitDetailData.getId());

        matchNoteDetailData.set(MatchNoteDetailFields.PRODUCT_ID, productId);
        matchNoteDetailData.set(MatchNoteDetailFields.THIS_MATCH_AMOUNT, debitMatchAmount);

        if (!Objects.isNull(creditData)) {

            matchNoteDetailData.set(MatchNoteDetailFields.CREDIT_API_NAME, creditData.getDescribeApiName());
            matchNoteDetailData.set(MatchNoteDetailFields.CREDIT_DATA_ID, creditData.getId());
            matchNoteDetailData.set(MatchNoteDetailFields.CREDIT_MATCH_AMOUNT, cebitMatchAmount);

        }
        if (!Objects.isNull(creditDetailData)) {

            matchNoteDetailData.set(MatchNoteDetailFields.CREDIT_API_NAME, ApiNames.ACCOUNTS_PAYABLE_NOTE_OBJ);
            matchNoteDetailData.set(MatchNoteDetailFields.CREDIT_DATA_ID, creditDetailData.get(AccountsPayableDetailFields.ACCOUNTS_PAYABLE_NOTE_ID, String.class));

            matchNoteDetailData.set(MatchNoteDetailFields.CREDIT_DETAIL_API_NAME, creditDetailData.getDescribeApiName());
            matchNoteDetailData.set(MatchNoteDetailFields.CREDIT_DETAIL_DATA_ID, creditDetailData.getId());
            matchNoteDetailData.set(MatchNoteDetailFields.CREDIT_MATCH_AMOUNT, cebitMatchAmount);

        }

        return matchNoteDetailData;
    }
}
