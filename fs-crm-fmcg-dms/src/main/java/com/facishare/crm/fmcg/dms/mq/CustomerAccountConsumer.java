package com.facishare.crm.fmcg.dms.mq;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.dms.business.DMSScriptHandlerFactory;
import com.facishare.crm.fmcg.dms.business.PaasLicenseBusinessService;
import com.facishare.crm.fmcg.dms.business.abstraction.IDMSScriptHandler;
import com.facishare.crm.fmcg.dms.business.abstraction.IStockService;
import com.facishare.crm.fmcg.dms.business.enums.ScriptHandlerNameEnum;
import com.facishare.crm.fmcg.dms.mq.model.CustomerAccount;
import com.facishare.crm.fmcg.dms.service.abastraction.PluginInstanceService;
import com.facishare.crm.fmcg.dms.web.abstraction.IAccountsPayableService;
import com.facishare.crm.fmcg.dms.web.abstraction.IRebateService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import static com.facishare.crm.fmcg.dms.mq.RebateEnableMqConsumer.ACCOUNTS_RECEIVABLE_REL_REBATE_PLUGIN_NAME;


@Component
public class CustomerAccountConsumer {

    private static final Logger logger = LoggerFactory.getLogger(CustomerAccountConsumer.class);
    private static final String CONFIG_NAME = "rocketmq-consumer.ini";
    private static final String SECTION_NAME = "common,name_server_02,consumer_customer_account_fmcg_dms";
    private AutoConfMQPushConsumer processor;

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private DMSScriptHandlerFactory dmsScriptHandlerFactory;
    @Resource
    private IAccountsPayableService accountsPayableService;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private IRebateService rebateService;
    @Resource
    private PluginInstanceService pluginInstanceService;
    @Resource
    private IStockService stockService;
    @Resource
    private PaasLicenseBusinessService paasLicenseBusinessService;

    @PostConstruct
    public void init() {
        if (!Objects.equals("1", System.getProperty("mn.dms.flag"))) {
            return;
        }
        logger.info("FundAccountConsumer start init.");
        MessageListenerConcurrently listener = (messages, context) -> {
            for (MessageExt messageExt : messages) {
                try {
                    TraceContext.get().setTraceId(UUID.randomUUID().toString());
                    process(messageExt);
                } catch (Exception ex) {
                    logger.error("[FundAccountConsumer consumer error :" + messages + "]", ex);
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                } finally {
                    TraceContext.remove();
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        try {
            processor = new AutoConfMQPushConsumer(CONFIG_NAME, SECTION_NAME, listener);
            processor.start();
        } catch (Exception e) {
            logger.error("init FundAccountConsumer mq consumer failed.", e);
        }
    }

    private void process(MessageExt messageExt) {
        CustomerAccount message = JSON.parseObject(messageExt.getBody(), CustomerAccount.class);
        if (messageExt.getReconsumeTimes() > 5) {
            logger.error("reach max err time.message。msg:{}", message);
            return;
        }

        if (message != null) {

            if (Boolean.TRUE.equals(message.getCustomerAccountEnable())) {
                if (accountsPayableService.denyAccountsPayable(message.getTenantId())) {
                    return;
                }
                Map<String, IObjectDescribe> describes = serviceFacade.findObjects(message.getTenantId(), Lists.newArrayList(ApiNames.OPENING_BALANCE_SETTING_OBJ));
                IDMSScriptHandler fundAccountEnable = dmsScriptHandlerFactory.resolve(ScriptHandlerNameEnum.FUND_ACCOUNT_ENABLE.getHandlerName());
                fundAccountEnable.addFields(message.getTenantId(), describes);
            } else if (Boolean.TRUE.equals(message.getAccountsReceivableEnable())) {
                handlerAccountsReceivable(message);
            }
        }

    }

    private void handlerAccountsReceivable(CustomerAccount accountReceivableMqObj) {
        logger.info("AccountsReceivableMqConsumer consume start.TenantId:{},enable:{}", accountReceivableMqObj.getTenantId(), accountReceivableMqObj.getAccountsReceivableEnable());
        if (!Boolean.TRUE.equals(accountReceivableMqObj.getAccountsReceivableEnable())) {
            return;
        }
        //控制是否开启正式企业刷预置字段
        if (!TPMGrayUtils.dmsLicenseMQFormalTenantEnable(accountReceivableMqObj.getTenantId())) {
            String tenantAccount = eieaConverter.enterpriseIdToAccount(Integer.parseInt(accountReceivableMqObj.getTenantId()));
            if (!tenantAccount.endsWith("sandbox")) {
                return;
            }
        }
        Map<String, IObjectDescribe> describes = serviceFacade.findObjects(accountReceivableMqObj.getTenantId(), Lists.newArrayList(ApiNames.GOODS_RECEIVED_NOTE_OBJ
                , ApiNames.RETURNED_GOODS_INVOICE_OBJ, ApiNames.RETURNED_GOODS_INVOICE_PRODUCT_OBJ, ApiNames.SALES_ORDER_OBJ, ApiNames.MATCH_NOTE_OBJ, ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ));

        boolean enableDealerKX = paasLicenseBusinessService.checkKXEnableByModuleCode(accountReceivableMqObj.getTenantId(), PaasLicenseConsumer.DEALER_EDITION);
        boolean enableAdvanceDealerKX = paasLicenseBusinessService.checkKXEnableByModuleCode(accountReceivableMqObj.getTenantId(), PaasLicenseConsumer.ADVANCED_DEALER_EDITION);
        boolean enableStock = stockService.enableDHTStock(accountReceivableMqObj.getTenantId());
        if (enableStock && (enableDealerKX || enableAdvanceDealerKX)) {
            IDMSScriptHandler handler = dmsScriptHandlerFactory.resolve(ScriptHandlerNameEnum.KX_DEALER_EDITION_ENABLE.getHandlerName());
            handler.addFields(accountReceivableMqObj.getTenantId(), describes);
        }

        IDMSScriptHandler onlyAccountReceivableEnableHandler = dmsScriptHandlerFactory.resolve(ScriptHandlerNameEnum.ACCOUNTS_RECEIVABLE_ENABLE.getHandlerName());
        //开启应收之后 入库单增加”累计结算金额“字段，退货单产品增加”已结算金额“字段
        //开启应收之后退货单增加字段“已退款金额”，“待退款金额”，”累计结算金额“
        onlyAccountReceivableEnableHandler.addFields(accountReceivableMqObj.getTenantId(), describes);
        try {
            //开启应收之后，更新字段计算公式
            onlyAccountReceivableEnableHandler.updateFields(accountReceivableMqObj.getTenantId(), describes);
        } catch (Exception ex) {
            logger.error("updateFields error", ex);
        }
        try {
            //启用应收之后增加退款按钮，下个版本放开
            onlyAccountReceivableEnableHandler.initButton(accountReceivableMqObj.getTenantId());
        } catch (Exception ex) {
            logger.error("initButton error", ex);
        }

        if (!TPMGrayUtils.dmsOpen1_1ScriptHandlerAuth(accountReceivableMqObj.getTenantId())) {
            return;
        }
        if (rebateService.isOpenRebate(accountReceivableMqObj.getTenantId())) {
            //如果开了返利刷返利单上的“生成红字应收按钮”
            IDMSScriptHandler onlyRebateEnableHandler = dmsScriptHandlerFactory.resolve(ScriptHandlerNameEnum.REBATE_ENABLE.getHandlerName());
            onlyRebateEnableHandler.initButton(accountReceivableMqObj.getTenantId());
            pluginInstanceService.addPluginUnit(Integer.parseInt(accountReceivableMqObj.getTenantId()), -10000, ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ, ACCOUNTS_RECEIVABLE_REL_REBATE_PLUGIN_NAME);
        }
    }

    @PreDestroy
    public void shutDown() {
        processor.close();
    }
}
