package com.facishare.crm.fmcg.dms.web.service;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.dms.model.RecalculateOldData;
import com.facishare.crm.fmcg.dms.model.RecalculateSingleData;
import com.facishare.crm.fmcg.dms.web.abstraction.IUpgradeService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class UpgradeService implements IUpgradeService {

    @Resource
    private ServiceFacade serviceFacade;

    @Override
    public RecalculateOldData.Result recalculateOldData(RecalculateOldData.Arg arg) {
        log.info("batch recalculate start : {}", arg.getTenantIdList());
        for (String tenantId : arg.getTenantIdList()) {
            recalculateOldData(tenantId);
        }
        return new RecalculateOldData.Result();
    }

    private void recalculateOldData(String tenantId) {
        log.info("recalculate start : {}", tenantId);
        recalculateOldReceivableData(tenantId);
        recalculateOldMatchNoteData(tenantId);
        log.info("recalculate end : {}", tenantId);
    }

    private void recalculateOldMatchNoteData(String tenantId) {
        List<IObjectData> data;
        try {
            IFilter creditMatchAmount = new Filter();
            creditMatchAmount.setFieldName(MatchNoteFields.CREDIT_MATCH_AMOUNT);
            creditMatchAmount.setOperator(Operator.IS);
            creditMatchAmount.setFieldValues(Lists.newArrayList(""));

            SearchTemplateQuery stq = QueryDataUtil.minimumQuery(creditMatchAmount);

            data = QueryDataUtil.find(
                    serviceFacade,
                    tenantId,
                    ApiNames.MATCH_NOTE_OBJ,
                    stq,
                    Lists.newArrayList(CommonFields.ID)
            );

            log.info("match note data size : {}", data.size());

        } catch (Exception ex) {
            log.error("query match note error : ", ex);
            return;
        }

        for (IObjectData datum : data) {
            RecalculateSingleData.Arg arg = new RecalculateSingleData.Arg();
            arg.setTenantId(tenantId);
            arg.setDataId(datum.getId());
            recalculateOldMatchNoteData(arg);
        }
    }

    @Override
    public RecalculateSingleData.Result recalculateOldMatchNoteData(RecalculateSingleData.Arg arg) {
        try {
            String tenantId = arg.getTenantId();
            String id = arg.getDataId();

            User sys = User.systemUser(tenantId);

            IObjectData master = serviceFacade.findObjectDataIgnoreAll(sys, id, ApiNames.MATCH_NOTE_OBJ);
            BigDecimal creditAmount = master.get(MatchNoteFields.CREDIT_MATCH_AMOUNT, BigDecimal.class);
            if (!Objects.isNull(creditAmount)) {
                return new RecalculateSingleData.Result();
            }

            List<IObjectData> details = queryMatchNoteDetails(tenantId, id);

            String verificationMethod = master.get(MatchNoteFields.VERIFICATION_METHOD, String.class);

            if (MatchNoteFields.VERIFICATION_METHOD__ACCOUNT_FLOW_OFFSET_AR.equals(verificationMethod) ||
                    MatchNoteFields.VERIFICATION_METHOD__PAYMENT_OFFSET_AR.equals(verificationMethod)) {
                master.set(MatchNoteFields.CREDIT_MATCH_AMOUNT, master.get(MatchNoteFields.THIS_MATCH_AMOUNT, BigDecimal.class));
                for (IObjectData detail : details) {
                    detail.set(MatchNoteDetailFields.CREDIT_MATCH_AMOUNT, detail.get(MatchNoteDetailFields.THIS_MATCH_AMOUNT, BigDecimal.class));
                }

                serviceFacade.updateObjectData(sys, master);
                serviceFacade.batchUpdateByFields(sys, details, Lists.newArrayList(MatchNoteDetailFields.CREDIT_MATCH_AMOUNT));
            } else if (MatchNoteFields.VERIFICATION_METHOD__AR_OFFSET_AR.equals(verificationMethod)) {
                master.set(MatchNoteFields.CREDIT_MATCH_AMOUNT, master.get(MatchNoteFields.THIS_MATCH_AMOUNT, BigDecimal.class).negate());
                for (IObjectData detail : details) {
                    detail.set(MatchNoteDetailFields.CREDIT_MATCH_AMOUNT, detail.get(MatchNoteDetailFields.THIS_MATCH_AMOUNT, BigDecimal.class).negate());
                }

                serviceFacade.updateObjectData(sys, master);
                serviceFacade.batchUpdateByFields(sys, details, Lists.newArrayList(MatchNoteDetailFields.CREDIT_MATCH_AMOUNT));
            }

        } catch (Exception ex) {
            log.error("upgrade receivable error : ", ex);
        }
        return new RecalculateSingleData.Result();
    }

    private void recalculateOldReceivableData(String tenantId) {
        List<IObjectData> data;
        try {
            IFilter apiNameEmptyFilter = new Filter();
            apiNameEmptyFilter.setFieldName(AccountsReceivableNoteFields.OBJECT_RECEIVABLE_API_NAME);
            apiNameEmptyFilter.setOperator(Operator.IS);
            apiNameEmptyFilter.setFieldValues(Lists.newArrayList(""));

            IFilter dataIdEmptyFilter = new Filter();
            dataIdEmptyFilter.setFieldName(AccountsReceivableNoteFields.OBJECT_RECEIVABLE_DATA_ID);
            dataIdEmptyFilter.setOperator(Operator.IS);
            dataIdEmptyFilter.setFieldValues(Lists.newArrayList(""));

            SearchTemplateQuery stq = QueryDataUtil.minimumQuery(apiNameEmptyFilter, dataIdEmptyFilter);

            data = QueryDataUtil.find(
                    serviceFacade,
                    tenantId,
                    ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ,
                    stq,
                    Lists.newArrayList(CommonFields.ID)
            );

            log.info("receivable data size : {}", data.size());

        } catch (Exception ex) {
            log.error("query receivable error : ", ex);
            return;
        }

        for (IObjectData datum : data) {
            RecalculateSingleData.Arg arg = new RecalculateSingleData.Arg();
            arg.setTenantId(tenantId);
            arg.setDataId(datum.getId());
            recalculateOldReceivableData(arg);
        }
    }

    @Override
    public RecalculateSingleData.Result recalculateOldReceivableData(RecalculateSingleData.Arg arg) {
        try {
            String tenantId = arg.getTenantId();
            String id = arg.getDataId();

            User sys = User.systemUser(tenantId);

            IObjectData master = serviceFacade.findObjectDataIgnoreAll(sys, id, ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ);
            if (!Strings.isNullOrEmpty(master.get(AccountsReceivableNoteFields.OBJECT_RECEIVABLE_API_NAME, String.class)) ||
                    !Strings.isNullOrEmpty(master.get(AccountsReceivableNoteFields.OBJECT_RECEIVABLE_DATA_ID, String.class))
            ) {
                return new RecalculateSingleData.Result();
            }

            List<IObjectData> details = queryReceivableNoteDetails(tenantId, id);

            String objectReceivable = master.get(AccountsReceivableNoteFields.OBJECT_RECEIVABLE, String.class);
            if (ApiNames.DELIVERY_NOTE_OBJ.equals(objectReceivable)) {
                Map<String, List<IObjectData>> grouped = details.stream()
                        .collect(Collectors.groupingBy(detail -> detail.get(AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DATA_ID, String.class)));

                if (grouped.size() == 1) {
                    for (Map.Entry<String, List<IObjectData>> entry : grouped.entrySet()) {
                        String deliveryNoteId = entry.getKey();

                        if (!Strings.isNullOrEmpty(deliveryNoteId)) {
                            IObjectData deliveryNote = serviceFacade.findObjectDataIgnoreAll(sys, deliveryNoteId, ApiNames.DELIVERY_NOTE_OBJ);
                            Map<String, IObjectData> deliveryNoteDetails = queryDeliveryNoteDetails(tenantId, deliveryNoteId).stream().collect(Collectors.toMap(DBRecord::getId, v -> v));

                            master.set(AccountsReceivableNoteFields.OBJECT_RECEIVABLE_API_NAME, ApiNames.SALES_ORDER_OBJ);
                            master.set(AccountsReceivableNoteFields.OBJECT_RECEIVABLE_DATA_ID, deliveryNote.get(DeliveryNoteFields.SALES_ORDER_ID, String.class));

                            List<IObjectData> innerDetails = entry.getValue();
                            for (IObjectData innerDetail : innerDetails) {
                                String deliveryNoteDetailId = innerDetail.get(AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DETAIL_DATA_ID, String.class);
                                if (deliveryNoteDetails.containsKey(deliveryNoteDetailId)) {
                                    IObjectData deliveryNoteDetail = deliveryNoteDetails.get(deliveryNoteDetailId);

                                    innerDetail.set(AccountsReceivableDetailFields.SOURCE_DETAIL_DATA_ID, deliveryNoteDetail.get(DeliveryNoteProductFields.SALES_ORDER_PRODUCT_ID, String.class));
                                    innerDetail.set(AccountsReceivableDetailFields.SOURCE_DETAIL_API_NAME, ApiNames.SALES_ORDER_PRODUCT_OBJ);
                                    innerDetail.set(AccountsReceivableDetailFields.SOURCE_DATA_ID, deliveryNote.get(DeliveryNoteFields.SALES_ORDER_ID, String.class));
                                    innerDetail.set(AccountsReceivableDetailFields.SOURCE_API_NAME, ApiNames.SALES_ORDER_OBJ);
                                }
                            }

                            serviceFacade.updateObjectData(sys, master);
                            serviceFacade.batchUpdateByFields(sys, innerDetails, Lists.newArrayList(
                                    AccountsReceivableDetailFields.SOURCE_DETAIL_DATA_ID,
                                    AccountsReceivableDetailFields.SOURCE_DETAIL_API_NAME,
                                    AccountsReceivableDetailFields.SOURCE_DATA_ID,
                                    AccountsReceivableDetailFields.SOURCE_API_NAME
                            ));
                        }
                    }
                }
            } else if (ApiNames.GOODS_RECEIVED_NOTE_OBJ.equals(objectReceivable)) {
                Map<String, List<IObjectData>> grouped = details.stream()
                        .collect(Collectors.groupingBy(detail -> detail.get(AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DATA_ID, String.class)));

                if (grouped.size() == 1) {
                    for (Map.Entry<String, List<IObjectData>> entry : grouped.entrySet()) {
                        String receivedNoteId = entry.getKey();

                        if (!Strings.isNullOrEmpty(receivedNoteId)) {
                            IObjectData receivedNote = serviceFacade.findObjectDataIgnoreAll(sys, receivedNoteId, ApiNames.GOODS_RECEIVED_NOTE_OBJ);
                            Map<String, IObjectData> receivedNoteDetails = queryReceivedNoteDetails(tenantId, receivedNoteId).stream().collect(Collectors.toMap(DBRecord::getId, v -> v));

                            master.set(AccountsReceivableNoteFields.OBJECT_RECEIVABLE_API_NAME, ApiNames.RETURNED_GOODS_INVOICE_OBJ);
                            master.set(AccountsReceivableNoteFields.OBJECT_RECEIVABLE_DATA_ID, receivedNote.get(GoodsReceivedNoteFields.RETURN_NOTE_ID, String.class));

                            List<IObjectData> innerDetails = entry.getValue();
                            for (IObjectData innerDetail : innerDetails) {
                                String receivedNoteDetailId = innerDetail.get(AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DETAIL_DATA_ID, String.class);
                                if (receivedNoteDetails.containsKey(receivedNoteDetailId)) {
                                    IObjectData receivedNoteDetail = receivedNoteDetails.get(receivedNoteDetailId);

                                    innerDetail.set(AccountsReceivableDetailFields.SOURCE_DETAIL_DATA_ID, receivedNoteDetail.get(GoodsReceivedNoteProductFields.RETURN_NOTE_PRODUCT_ID, String.class));
                                    innerDetail.set(AccountsReceivableDetailFields.SOURCE_DETAIL_API_NAME, ApiNames.RETURNED_GOODS_INVOICE_PRODUCT_OBJ);
                                    innerDetail.set(AccountsReceivableDetailFields.SOURCE_DATA_ID, receivedNote.get(GoodsReceivedNoteFields.RETURN_NOTE_ID, String.class));
                                    innerDetail.set(AccountsReceivableDetailFields.SOURCE_API_NAME, ApiNames.RETURNED_GOODS_INVOICE_OBJ);
                                }
                            }

                            serviceFacade.updateObjectData(sys, master);
                            serviceFacade.batchUpdateByFields(sys, innerDetails, Lists.newArrayList(
                                    AccountsReceivableDetailFields.SOURCE_DETAIL_DATA_ID,
                                    AccountsReceivableDetailFields.SOURCE_DETAIL_API_NAME,
                                    AccountsReceivableDetailFields.SOURCE_DATA_ID,
                                    AccountsReceivableDetailFields.SOURCE_API_NAME
                            ));
                        }
                    }
                }
            }
        } catch (Exception ex) {
            log.error("upgrade receivable error : ", ex);
        }
        return new RecalculateSingleData.Result();
    }

    private List<IObjectData> queryReceivedNoteDetails(String tenantId, String id) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(GoodsReceivedNoteProductFields.GOODS_RECEIVED_NOTE_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(id));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.GOODS_RECEIVED_NOTE_PRODUCT_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        CommonFields.NAME,
                        CommonFields.OWNER,
                        CommonFields.CREATE_BY,
                        CommonFields.CREATE_TIME,
                        GoodsReceivedNoteProductFields.GOODS_RECEIVED_NOTE_ID,
                        GoodsReceivedNoteProductFields.GOODS_RECEIVED_AMOUNT,
                        GoodsReceivedNoteProductFields.AUXILIARY_RECEIVED_QUANTITY,
                        GoodsReceivedNoteProductFields.UNIT,
                        GoodsReceivedNoteProductFields.RETURN_NOTE_PRODUCT_ID,
                        GoodsReceivedNoteProductFields.PRODUCT_ID
                )
        );
    }

    private List<IObjectData> queryDeliveryNoteDetails(String tenantId, String id) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(DeliveryNoteProductFields.DELIVERY_NOTE_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(id));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.DELIVERY_NOTE_PRODUCT_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        CommonFields.NAME,
                        CommonFields.OWNER,
                        CommonFields.CREATE_BY,
                        CommonFields.CREATE_TIME,
                        DeliveryNoteProductFields.DELIVERY_NOTE_ID,
                        DeliveryNoteProductFields.DELIVERY_MONEY,
                        DeliveryNoteProductFields.PRODUCT_ID,
                        DeliveryNoteProductFields.ACTUAL_UNIT,
                        DeliveryNoteProductFields.SALES_ORDER_ID,
                        DeliveryNoteProductFields.ORDER_PRODUCT_AMOUNT,
                        DeliveryNoteProductFields.AUXILIARY_DELIVERY_QUANTITY,
                        DeliveryNoteProductFields.SALES_PRICE,
                        DeliveryNoteProductFields.SALES_ORDER_PRODUCT_ID
                )
        );
    }

    private List<IObjectData> queryMatchNoteDetails(String tenantId, String id) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(MatchNoteDetailFields.MATCHNOTE_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(id));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.MATCH_NOTE_DETAIL_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        CommonFields.NAME,
                        CommonFields.OWNER,
                        CommonFields.CREATE_BY,
                        CommonFields.CREATE_TIME,
                        CommonFields.TENANT_ID,
                        CommonFields.OBJECT_DESCRIBE_API_NAME,
                        MatchNoteDetailFields.THIS_MATCH_AMOUNT,
                        MatchNoteDetailFields.CREDIT_MATCH_AMOUNT
                )
        );
    }

    private List<IObjectData> queryReceivableNoteDetails(String tenantId, String id) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(AccountsReceivableDetailFields.AR_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(id));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        CommonFields.NAME,
                        CommonFields.OWNER,
                        CommonFields.CREATE_BY,
                        CommonFields.CREATE_TIME,
                        CommonFields.TENANT_ID,
                        CommonFields.OBJECT_DESCRIBE_API_NAME,
                        AccountsReceivableDetailFields.PRICE_TAX_AMOUNT,
                        AccountsReceivableDetailFields.AR_ID,
                        AccountsReceivableDetailFields.ORDER_ID,
                        AccountsReceivableDetailFields.ORDER_PRODUCT_ID,
                        AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DATA_ID,
                        AccountsReceivableDetailFields.RECEIVABLE_OBJECT_API_NAME,
                        AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DETAIL_DATA_ID,
                        AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DETAIL_API_NAME,
                        AccountsReceivableDetailFields.UNIT,
                        AccountsReceivableDetailFields.AR_QUANTITY,
                        AccountsReceivableDetailFields.SKU_ID,
                        AccountsReceivableDetailFields.TAX_PRICE,
                        AccountsReceivableDetailFields.SOURCE_API_NAME,
                        AccountsReceivableDetailFields.SOURCE_DATA_ID,
                        AccountsReceivableDetailFields.SOURCE_DETAIL_API_NAME,
                        AccountsReceivableDetailFields.SOURCE_DETAIL_DATA_ID
                )
        );
    }
}
