package com.facishare.crm.fmcg.dms.util;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.OpeningBalanceSettingFields;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Component
public class OpeningBalanceSettingConvertBeanNameUtil {

    @Resource
    private ServiceFacade serviceFacade;
    private static final List<String> SUPPORT_OBJECTS = Lists.newArrayList();

    static {
        SUPPORT_OBJECTS.add(ApiNames.OPENING_BALANCE_SETTING_OBJ);

    }

    public String beanName(FinancialBill bill) {
        if (!SUPPORT_OBJECTS.contains(bill.getApiName())) {
            return null;
        }
        IObjectData data = serviceFacade.findObjectData(User.systemUser(bill.getTenantId()), bill.getId(), bill.getApiName());
        return beanName(data.get(OpeningBalanceSettingFields.OPENING_TYPE, String.class));
    }

    private static String beanName(String openingType) {
        for (OpeningType value : OpeningType.values()) {
            if (Objects.equals(value.openingType, openingType)) {
                return value.beanName;
            }
        }
        return null;
    }

    public enum OpeningType {
        ACCOUNTS_RECEIVABLE("accounts_receivable", "openingBalanceSettingAutoConvertToReceivableService"),
        PAYMENT("payment", "openingBalanceSettingAutoConvertToPaymentService"),
        ACCOUNTS_PAYABLE("accounts_payable", "openingBalanceSettingAutoConvertToPayableService"),
        PAY("pay", "openingBalanceSettingAutoConvertToPayService");

        private final String openingType;
        private final String beanName;

        OpeningType(String openingType, String beanName) {
            this.openingType = openingType;
            this.beanName = beanName;
        }

    }
}
