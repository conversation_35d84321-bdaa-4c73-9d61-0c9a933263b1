package com.facishare.crm.fmcg.dms.web.manager;


import com.facishare.crm.fmcg.common.apiname.AccountsPayableDetailFields;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.dms.util.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class AccountsPayableDetailManager {
    @Resource
    private ServiceFacade serviceFacade;


    public List<IObjectData> details(User user, List<String> apIds) {
        if (CollectionUtils.empty(apIds)) {
            return new ArrayList<>();
        }
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, AccountsPayableDetailFields.ACCOUNTS_PAYABLE_NOTE_ID, apIds);
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setPermissionType(0); //0不走权限  1走权限
        searchTemplateQuery.setLimit(2000);
        searchTemplateQuery.setOffset(0);

        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(user, ApiNames.ACCOUNTS_PAYABLE_DETAIL_OBJ, searchTemplateQuery);
        return queryResult.getData();
    }
}