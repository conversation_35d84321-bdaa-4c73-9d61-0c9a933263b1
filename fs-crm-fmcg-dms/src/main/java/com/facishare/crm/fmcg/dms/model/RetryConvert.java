package com.facishare.crm.fmcg.dms.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

public interface RetryConvert {

    @Data
    class Arg implements Serializable {

        @JSONField(name = "tenant_id")
        @JsonProperty(value = "tenant_id")
        @SerializedName("tenant_id")
        private String tenantId;

        @JSONField(name = "api_name")
        @JsonProperty(value = "api_name")
        @SerializedName("api_name")
        private String apiName;

        @JSONField(name = "data_id")
        @JsonProperty(value = "data_id")
        @SerializedName("data_id")
        private String dataId;

    }

    @Data
    class Result implements Serializable {
        private String placeholder = "__placeholder";

    }
}
