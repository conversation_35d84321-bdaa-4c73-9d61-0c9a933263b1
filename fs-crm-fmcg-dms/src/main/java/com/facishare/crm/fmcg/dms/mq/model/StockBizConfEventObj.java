package com.facishare.crm.fmcg.dms.mq.model;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
public class StockBizConfEventObj {
    /**
     * bizconfig key
     */
    private String key;

    /**
     * 更新后值
     */
    private String newValue;

    /**
     * 更新前值
     */
    private String oldValue;

    /**
     * 操作类型-更新-UPDATE
     */
    private String opType;

    /**
     * 操作人信息
     */
    private User user;
    /**
     * 配置项是否有效
     */
    private int errorCode;
    /**
     * 错误信息
     */
    private String errorMsg;

    @Data
    public static class User implements Serializable {

        private static final long serialVersionUID = -9128152979947806915L;

        private Boolean grayTenant;

        private String isCrmAdmin;

        private Boolean outUser;

        private Boolean supperAdmin;

        private String tenantId;

        private Integer tenantIdInt;

        private String upstreamOwnerIdOrUserId;

        private String userId;

        private Integer userIdInt;

        private String userIdOrOutUserIdIfOutUser;

        private String userIdWithFlowGray;
    }
}
