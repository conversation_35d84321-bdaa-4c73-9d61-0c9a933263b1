package com.facishare.crm.fmcg.dms.mq.model;


import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Set;


@Data
@ToString
public class LicenseMqObj {

    private String tenantId; //企业ID
    private String licenseVersion; //产品编码，不同类型产品按照指定编码返回（主版本，行业套件，应用，资源包）
    private Set<String> moduleCodes; // 产品对应的所有模块编码
    private List<MqModulePara> mqModuleParas; //产品对应的配额信息
    private String orderNumber; //订单标号
    private long createTime; //产品创建时间
    private long startTime; //产品开始时间
    private long expiredTime; //产品结束时间

    @Data
    @ToString
    public static class MqModulePara {
        private String moduleCode; // 对应的模块编码
        private String paraKey; // 此模块下的配额编码
        private String paraValue; // 配额数
    }
}
