package com.facishare.crm.fmcg.dms.web.service;

import com.alibaba.fastjson.JSON;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.PayFields;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.dms.business.DMSScriptHandlerFactory;
import com.facishare.crm.fmcg.dms.business.abstraction.FieldBusiness;
import com.facishare.crm.fmcg.dms.business.abstraction.IDMSScriptHandler;
import com.facishare.crm.fmcg.dms.business.enums.AccountPayableSwitchEnum;
import com.facishare.crm.fmcg.dms.business.enums.ScriptHandlerNameEnum;
import com.facishare.crm.fmcg.dms.constants.AccountsPayableConstants;
import com.facishare.crm.fmcg.dms.errors.DMSBusinessException;
import com.facishare.crm.fmcg.dms.errors.RetryActionException;
import com.facishare.crm.fmcg.dms.i18n.I18NKeys;
import com.facishare.crm.fmcg.dms.model.AccountsPayableAutoMatch;
import com.facishare.crm.fmcg.dms.model.AccountsPayableEnable;
import com.facishare.crm.fmcg.dms.model.AccountsPayableStatus;
import com.facishare.crm.fmcg.dms.model.BulkCreateMatchNote;
import com.facishare.crm.fmcg.dms.service.abastraction.PluginInstanceService;
import com.facishare.crm.fmcg.dms.web.abstraction.BaseService;
import com.facishare.crm.fmcg.dms.web.abstraction.IAccountsPayableService;
import com.facishare.crm.fmcg.dms.web.abstraction.IAccountsReceivableNoteService;
import com.facishare.crm.fmcg.dms.web.manager.AccountsPayableCommonManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.restful.client.exception.FRestClientException;
import com.fmcg.framework.http.PaasLayoutProxy;
import com.fmcg.framework.http.RecordTypeProxy;
import com.fmcg.framework.http.contract.paas.layout.PaasCreateLayout;
import com.fmcg.framework.http.contract.paas.layout.PaasCreateLayoutRule;
import com.fmcg.framework.http.contract.paas.layout.PaasEnableEditLayout;
import com.fmcg.framework.http.contract.recordtype.RecordType;
import com.fxiaoke.bizconf.arg.ConfigArg;
import com.fxiaoke.bizconf.arg.QueryConfigByRankArg;
import com.fxiaoke.bizconf.bean.Rank;
import com.fxiaoke.bizconf.factory.BizConfClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

//IgnoreI18nFile
@Service
@Slf4j
@SuppressWarnings("Duplicates")
public class AccountsPayableService extends BaseService implements IAccountsPayableService {
    public static final String PURCHASE_ORDER_PLUGIN_API_NAME = "purchase_order_save_validate";
    public static final String PURCHASE_RETURN_PLUGIN_API_NAME = "purchase_return_note_action_plugin";
    public static final String GOODS_RECEIVED_PLUGIN_API_NAME = "goods_receivable_note_action_plugin";
    private static final long LOCK_WAIT = 20;
    private static final long LOCK_LEASE = 60;
    @Resource
    private BizConfClient bizConfClient;
    @Resource
    private DMSScriptHandlerFactory dmsScriptHandlerFactory;
    @Resource
    private PluginInstanceService pluginInstanceService;
    @Resource
    protected RedissonClient redissonCmd;
    @Resource
    private AccountsPayableCommonManager accountsPayableCommonManager;
    @Resource
    protected FieldBusiness fieldBusiness;
    @Resource
    private PaasLayoutProxy paasLayoutProxy;
    @Resource
    private RecordTypeProxy recordTypeProxy;
    @Resource
    private IAccountsReceivableNoteService receivableNoteService;
    @Resource
    private EIEAConverter eieaConverter;

    @Override
    public AccountsPayableStatus.Result getAccountsPayableStatus(AccountsPayableStatus.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        return AccountsPayableStatus.Result.builder().status(queryAccountsPayableStatus(context.getTenantId())).build();
    }

    @Override
    public boolean denyAccountsPayable(String tenantId) {
        QueryConfigByRankArg queryStatusArg =
                QueryConfigByRankArg.builder().rank(Rank.TENANT).key(AccountsPayableConstants.ACCOUNTS_PAYABLE_SWITCH_KEY).tenantId(tenantId).pkg(AccountsPayableConstants.SWITCH_PKG).build();
        String config;
        try {
            config = bizConfClient.queryConfigByRank(queryStatusArg);
            log.info("denyAccountsPayable config:{}", config);
        } catch (FRestClientException ex) {
            throw new RetryActionException("query config error");
        }
        if (StringUtils.isEmpty(config)) {
            log.info("denyAccountsPayable config is null");
            return true;
        }

        return Integer.parseInt(config) != 2;
    }

    @Override
    public String updateStatus(String tenantId, Integer status) {
        try {
            User user = User.systemUser(tenantId);

            String config = bizConfClient
                    .queryConfigByRank(QueryConfigByRankArg.builder().rank(Rank.TENANT).key(AccountsPayableConstants.ACCOUNTS_PAYABLE_SWITCH_KEY).tenantId(user.getTenantId()).pkg(AccountsPayableConstants.SWITCH_PKG).build());
            log.info("config:{}", config);
            if (org.apache.commons.lang.StringUtils.isBlank(config)) {
                ConfigArg configArg = buildConfigArg(user, AccountsPayableConstants.ACCOUNTS_PAYABLE_SWITCH_KEY, String.valueOf(status));
                int result = bizConfClient.createConfig(configArg);
                log.info("bizConfApi.createConfig ,configArg:{},result:{}", configArg, result);
            } else {
                int result = bizConfClient.updateConfig(buildConfigArg(user, AccountsPayableConstants.ACCOUNTS_PAYABLE_SWITCH_KEY, String.valueOf(status)));
                log.info("bizConfApi.createConfig2 ,,result:{}", result);
            }
        } catch (Exception e) {
            log.error("updateAccountReceivableStatus error,", e);
            throw new MetaDataBusinessException("updateAccountReceivableStatus error");
        }
        return "success";
    }

    @Override
    public AccountsPayableEnable.Result accountsPayableEnable(AccountsPayableEnable.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        if (receivableNoteService.denyAccountsReceivableEnable(context.getTenantId())) {
            throw new DMSBusinessException(I18N.text(I18NKeys.ACCOUNTS_RECEIVABLE_DISABLE_ERROR));
        }
        if (!denyAccountPayable(context.getTenantId())) {
            return AccountsPayableEnable.Result.builder().enable(true).build();
        }
        String status = queryAccountsPayableStatus(context.getTenantId());
        if (Objects.equals(status, String.valueOf(AccountPayableSwitchEnum.OPENING.getStatus()))) {
            throw new DMSBusinessException(I18N.text(I18NKeys.ACCOUNTS_PAYABLE_DUPLICATE_ENABLE_ERROR));
        }
        if (!tryLock(context.getTenantId())) {
            throw new DMSBusinessException(I18N.text(I18NKeys.ACCOUNTS_PAYABLE_DUPLICATE_ENABLE_ERROR));
        }

        try {
            updateAccountsPayableStatus(User.systemUser(context.getTenantId()), AccountPayableSwitchEnum.OPENING.getStatus());
            ParallelUtils.createParallelTask().submit(() -> {
                doEnableAccountsPayable(context);
            }).run();
        } finally {
            unlock(context.getTenantId());
        }

        return AccountsPayableEnable.Result.builder().enable(true).build();
    }

    @Override
    public AccountsPayableAutoMatch.Result autoMatchPayable(AccountsPayableAutoMatch.Arg arg) {

        ApiContext context = ApiContextManager.getContext();

        AccountsPayableAutoMatch.Result result
                = AccountsPayableAutoMatch.Result.builder().build();
        if (Strings.isNullOrEmpty(arg.getSupplierId())) {
            throw new ValidateException(I18N.text(I18NKeys.ACCOUNTS_PAYABLE_SERVICE_0));
        }

        String supplierId = arg.getSupplierId();
        if (Strings.isNullOrEmpty(supplierId)) {
            return result;
        }

        List<IObjectData> arDataList = accountsPayableCommonManager.autoMatchPayable(new User(context.getTenantId(), String.valueOf(context.getEmployeeId()))
                , arg.getSupplierId(), arg.getAmount());
        result.setApData(ObjectDataDocument.ofList(arDataList));
        return result;

    }

    @Override
    public BulkCreateMatchNote.Result bulkCreateMatchNote(BulkCreateMatchNote.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        BulkCreateMatchNote.Result result
                = BulkCreateMatchNote.Result.builder().build();
        if (!CollectionUtils.isNotEmpty(arg.getCheckMatchData())) {
            throw new ValidateException(I18N.text("customeraccountobj.common.validate.params_error"));
        }
        String payId = arg.getPayId();
        accountsPayableCommonManager.bulkCreateMatchNote(new User(context.getTenantId(), String.valueOf(context.getEmployeeId())), payId, arg.getCheckMatchData());

        return result;
    }

    @Override
    public void initField(List<Integer> tenantIds, String flag) {
        List<String> exTenantIds1 = Lists.newArrayList();
        List<String> exTenantIds2 = Lists.newArrayList();
        for (Integer tenantId : tenantIds) {

            Map<String, IObjectDescribe> describes = serviceFacade.findObjects(String.valueOf(tenantId), Lists.newArrayList(ApiNames.OPENING_BALANCE_SETTING_OBJ, ApiNames.PAY_OBJ));
            if (StringUtils.isEmpty(flag) || "1".equals(flag)) {
                try {
                    boolean b = !denyAccountsPayable(String.valueOf(tenantId));
                    boolean c = false;
                    try {
                        serviceFacade.findObject(String.valueOf(tenantId), ApiNames.NEW_CUSTOMER_ACCOUNT_OBJ);
                        c = true;
                    } catch (ObjectDefNotFoundError ignore) {
                    }
                    if (b && c) {
                        IDMSScriptHandler fundAccountEnable = dmsScriptHandlerFactory.resolve(ScriptHandlerNameEnum.FUND_ACCOUNT_ENABLE.getHandlerName());
                        fundAccountEnable.addFields(String.valueOf(tenantId), describes);
                    }
                } catch (Exception ex) {
                    log.error("initField error,,tenantId1:{}", tenantId, ex);
                    exTenantIds1.add(tenantId.toString());
                }

            }

            if (StringUtils.isEmpty(flag) || "2".equals(flag)) {
                try {
                    fieldBusiness.addField(String.valueOf(tenantId), describes.get(ApiNames.PAY_OBJ), ApiNames.PAY_OBJ,
                            PayFields.PRE_PAY, true, false);
                } catch (Exception ex) {
                    log.error("initField error,,tenantId2:{}", tenantId, ex);
                    exTenantIds2.add(tenantId.toString());
                }

            }

        }
        if (CollectionUtils.isNotEmpty(exTenantIds1)) {
            log.info("initField error,exTenantIds1:{}", JSON.toJSONString(exTenantIds1));
        }
        if (CollectionUtils.isNotEmpty(exTenantIds2)) {
            log.info("initField error,exTenantIds2:{}", JSON.toJSONString(exTenantIds2));
        }
    }

    @Override
    public void initManualMatchLayout(List<Integer> tenantIds, String flag) {
        //增加付款单无明细布局并且开启新建/编辑页布局
        List<Integer> exTenantIds = Lists.newArrayList();
        for (Integer tenantId : tenantIds) {
            try {
                PaasCreateLayout.Arg arg = new PaasCreateLayout.Arg();
                arg.setLayoutData(JSON.parseObject(layout("PayObjNoDetail")));
                PaasCreateLayout.Result layout = paasLayoutProxy.createLayout(tenantId, -10000, arg);
                if (layout.getCode() != 0) {
                    log.error("init layout error:{}", layout.getMessage());
                }
                PaasEnableEditLayout.Arg enableEditLayout = new PaasEnableEditLayout.Arg();
                enableEditLayout.setDescribeApiName(ApiNames.PAY_OBJ);
                paasLayoutProxy.enableEditLayout(tenantId, -10000, enableEditLayout);

                RecordType.Arg recordTypeArg = new RecordType.Arg();
                recordTypeArg.setDescribeApiName(ApiNames.PAY_OBJ);
                Map<String, Object> typeMap = Maps.newHashMap();
                typeMap.put("label", "无明细");//ignorei18n
                typeMap.put("api_name", "no_details__c");
                typeMap.put("is_active", true);
                typeMap.put("description", "无明细业务类型");//ignorei18n
                recordTypeArg.setRecordType(JSON.toJSONString(typeMap));
                recordTypeProxy.create(tenantId, -10000, recordTypeArg);

            } catch (Exception ex) {
                log.error("init layout error", ex);
                exTenantIds.add(tenantId);
            }
        }

        if (CollectionUtils.isNotEmpty(exTenantIds)) {
            log.info("exTenantIds:{}", JSON.toJSONString(exTenantIds));
        }
    }

    @Override
    public void batchAccountsPayableEnable(List<Integer> tenantIds, String flag, String enablePayable) {
        String tenantIdStr;
        try {
            File mainJsonFile = ResourceUtils.getFile("classpath:dms/mnTenantIds.txt");
            tenantIdStr = new String(Files.readAllBytes(mainJsonFile.toPath()));
        } catch (Exception ex) {
            throw new RuntimeException("read mnTenantIds.txt error", ex);
        }
        List<String> tenantIdsResult = Lists.newArrayList();
        if ("true".equals(flag)) {
            tenantIdsResult.addAll(tenantIds.stream().map(String::valueOf).collect(Collectors.toList()));
        } else {
            String[] split = tenantIdStr.split(",");
            for (String s : split) {
                if (StringUtils.isEmpty(s)) {
                    continue;
                }
                tenantIdsResult.add(s.trim());
            }
        }
        List<String> openedTenantIds = Lists.newArrayList();
        List<String> openingTenantIds = Lists.newArrayList();
        List<String> lockErrorTenantIds = Lists.newArrayList();
        for (String tenantId : tenantIdsResult) {
            ApiContext context = ApiContext.builder().tenantId(String.valueOf(tenantId)).build();
            if (!denyAccountPayable(context.getTenantId())) {
                openedTenantIds.add(tenantId);
                continue;
            }
            String status = queryAccountsPayableStatus(context.getTenantId());
            if (Objects.equals(status, String.valueOf(AccountPayableSwitchEnum.OPENING.getStatus()))) {
                openingTenantIds.add(tenantId);
                continue;
            }
            if (!tryLock(context.getTenantId())) {
                lockErrorTenantIds.add(tenantId);
                continue;
            }

            try {
                if ("true".equals(enablePayable)) {
                    updateAccountsPayableStatus(User.systemUser(context.getTenantId()), AccountPayableSwitchEnum.OPENING.getStatus());
                    doEnableAccountsPayable(context);
                } else {
                    RecordType.Arg recordTypeArg = new RecordType.Arg();
                    recordTypeArg.setDescribeApiName(ApiNames.PAY_OBJ);
                    Map<String, Object> typeMap = Maps.newHashMap();
                    typeMap.put("label", "无明细");//ignorei18n
                    typeMap.put("api_name", "no_details__c");
                    typeMap.put("is_active", true);
                    typeMap.put("description", "无明细业务类型");//ignorei18n
                    recordTypeArg.setRecordType(JSON.toJSONString(typeMap));
                    recordTypeProxy.create(Integer.parseInt(tenantId), -10000, recordTypeArg);
                }

            } finally {
                unlock(context.getTenantId());
            }
        }

        log.info("openedTenantIds:{},openingTenantIds:{},lockErrorTenantIds:{}", JSON.toJSONString(openedTenantIds), JSON.toJSONString(openingTenantIds), JSON.toJSONString(lockErrorTenantIds));
    }

    @Override
    public void createOpeningBalanceSettingLayoutRule(List<Integer> tenantIds) {
        log.info("fixMengniuPaymentMatch start");
        if (!tryLock("fmcg_createOpeningBalanceSettingLayoutRule")) {
            log.info("fixMengniuPaymentMatch lck fail");
            return;
        }
        StringBuilder sb = new StringBuilder();
        try {
            if (!CollectionUtils.isEmpty(tenantIds)) {
                for (Integer tenantId : tenantIds) {
                    try {
                        createLayoutRule(tenantId);
                    } catch (Exception ex) {
                        sb.append("tenantId=").append(tenantId).append("msg=").append(ex.getMessage());
                    }
                }
            } else {
                List<String> allTenantIds = findAllTenantIds("777421");
                if (CollectionUtils.isEmpty(allTenantIds)) {
                    log.info("tenant null");
                    return;
                }
                for (String tenantId : allTenantIds) {
                    try {
                        createLayoutRule(Integer.parseInt(tenantId));
                    } catch (Exception ex) {
                        sb.append("tenantId=").append(tenantId).append("msg=").append(ex.getMessage());
                    }
                }
            }
        } finally {
            unlock("fmcg_createOpeningBalanceSettingLayoutRule");
        }
        log.info("createOpeningBalanceSettingLayoutRule end:{}", sb);
    }

    private void createLayoutRule(Integer tenantId) throws IOException {
        PaasCreateLayoutRule.Arg createLayoutRuleArg = new PaasCreateLayoutRule.Arg();
        createLayoutRuleArg.setJsonData(JSON.parseObject(layoutRule(ApiNames.OPENING_BALANCE_SETTING_OBJ)));
        PaasCreateLayoutRule.Result layoutRuleResult = paasLayoutProxy.createLayoutRule(tenantId, -10000, createLayoutRuleArg);
        if (layoutRuleResult.getErrCode() != 0) {
            throw new ValidateException(layoutRuleResult.getErrMessage());
        }
    }

    protected String layoutRule(String apiName) throws IOException {
        File mainJsonFile = ResourceUtils.getFile(String.format("classpath:dms/object/%sLayoutRule.json", apiName));
        return new String(Files.readAllBytes(mainJsonFile.toPath()));
    }

    private List<String> findAllTenantIds(String mengniuTenantId) {

        List<String> tenantIds = Lists.newArrayList();
        List<IObjectData> dataList = queryEnterpriseRelationObj(mengniuTenantId);
        if (CollectionUtils.isNotEmpty(dataList)) {
            List<String> enterpriseAccount = dataList.stream().map(data -> data.get("enterprise_account", String.class)).collect(Collectors.toList());
            Map<String, Integer> enterpriseAccountToIdMap = eieaConverter.enterpriseAccountToId(enterpriseAccount);
            tenantIds = enterpriseAccountToIdMap.values().stream().map(String::valueOf).distinct().collect(Collectors.toList());
        }
        return tenantIds;
    }

    private List<IObjectData> queryEnterpriseRelationObj(String tenantId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");
        query.setLimit(-1);


        //1 复制中; 2 复制成功; 3 复制失败
        IFilter copyStatusFilter = new Filter();
        copyStatusFilter.setFieldName("copy_status");
        copyStatusFilter.setOperator(Operator.EQ);
        copyStatusFilter.setFieldValues(Lists.newArrayList("2"));


        //1 正常
        IFilter typeFilter = new Filter();
        typeFilter.setFieldName("relation_type");
        typeFilter.setOperator(Operator.EQ);
        typeFilter.setFieldValues(Lists.newArrayList("1"));


        query.setFilters(Lists.newArrayList(copyStatusFilter, typeFilter));

        List<String> fields = Lists.newArrayList("enterprise_account");
        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.ENTERPRISE_RELATION_OBJ, query, fields);
    }

    protected String layout(String apiName) throws IOException {
        File mainJsonFile = ResourceUtils.getFile(String.format("classpath:dms/object/%sDetailLayout.json", apiName));
        return new String(Files.readAllBytes(mainJsonFile.toPath()));
    }

    private void doEnableAccountsPayable(ApiContext context) {
        try {

            Map<String, IObjectDescribe> describes = serviceFacade.findObjects(context.getTenantId(), Lists.newArrayList(ApiNames.PURCHASE_ORDER_OBJ,
                    ApiNames.PAYMENT_OBJ, ApiNames.MATCH_NOTE_OBJ, ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ));

            IDMSScriptHandler accountsPayableEnableHandler = dmsScriptHandlerFactory.resolve(ScriptHandlerNameEnum.ACCOUNTS_PAYABLE_ENABLE.getHandlerName());
            accountsPayableEnableHandler.initObjectDescribe(context.getTenantId());

            IObjectDescribe enterpriseFundAccountObj = null;
            try {
                enterpriseFundAccountObj = serviceFacade.findObject(context.getTenantId(), ApiNames.ENTERPRISE_FUND_ACCOUNT_OBJ);
            } catch (Exception ex) {
                log.error("query tenant fund account fail", ex);
            }
            describes.put(ApiNames.ENTERPRISE_FUND_ACCOUNT_OBJ, enterpriseFundAccountObj);

            accountsPayableEnableHandler.addFields(context.getTenantId(), describes);
            accountsPayableEnableHandler.updateFields(context.getTenantId(), describes);
            accountsPayableEnableHandler.initButton(context.getTenantId());

            try {
                pluginInstanceService.addPluginUnit(Integer.valueOf(context.getTenantId()), -10000, ApiNames.PURCHASE_ORDER_OBJ, PURCHASE_ORDER_PLUGIN_API_NAME);
                pluginInstanceService.addPluginUnit(Integer.valueOf(context.getTenantId()), -10000, ApiNames.PURCHASE_RETURN_NOTE_OBJ, PURCHASE_RETURN_PLUGIN_API_NAME);
                pluginInstanceService.addPluginUnit(Integer.valueOf(context.getTenantId()), -10000, ApiNames.GOODS_RECEIVED_NOTE_OBJ, GOODS_RECEIVED_PLUGIN_API_NAME);
            } catch (Exception e) {
                log.error("add purchase plugin fail, e:", e);
            }
            try {
                Map<String, IObjectDescribe> openingBalanceSettingDescribe = serviceFacade.findObjects(context.getTenantId(), Lists.newArrayList(ApiNames.OPENING_BALANCE_SETTING_OBJ));
                serviceFacade.findObject(context.getTenantId(), ApiNames.NEW_CUSTOMER_ACCOUNT_OBJ);
                IDMSScriptHandler fundAccountEnable = dmsScriptHandlerFactory.resolve(ScriptHandlerNameEnum.FUND_ACCOUNT_ENABLE.getHandlerName());
                fundAccountEnable.addFields(context.getTenantId(), openingBalanceSettingDescribe);
            } catch (ObjectDefNotFoundError ignore) {
            }
            //如果报错了，开关不开，允许重复点击
            updateAccountsPayableStatus(User.systemUser(context.getTenantId()), AccountPayableSwitchEnum.OPENED.getStatus());
        } catch (Exception ex) {
            updateAccountsPayableStatus(User.systemUser(context.getTenantId()), AccountPayableSwitchEnum.OPEN_FAIL.getStatus());
        }

    }

    private String queryAccountsPayableStatus(String tenantId) {
        QueryConfigByRankArg queryStatusArg =
                QueryConfigByRankArg.builder().rank(Rank.TENANT).key(AccountsPayableConstants.ACCOUNTS_PAYABLE_SWITCH_KEY).tenantId(tenantId).pkg(AccountsPayableConstants.SWITCH_PKG).build();
        String config;
        try {
            config = bizConfClient.queryConfigByRank(queryStatusArg);
        } catch (FRestClientException ex) {
            throw new DMSBusinessException("query config error");
        }
        return config;
    }

    private void unlock(String tenantId) {
        String key = buildLockKey(tenantId);
        RLock lock = redissonCmd.getLock(key);

        log.info("unlock accounts payable : {}", key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }


    private boolean tryLock(String tenantId) {
        String key = buildLockKey(tenantId);
        RLock lock = redissonCmd.getLock(key);

        log.info("try lock accounts payable : {}", key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            return true;
        }
        try {
            return lock.tryLock(LOCK_WAIT, LOCK_LEASE, TimeUnit.SECONDS);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new MetaDataBusinessException(String.format("try lock accounts payable cause thread interrupted exception : %s", key));
        }
    }

    private String buildLockKey(String tenantId) {
        return String.format("DMS_ACCOUNTS_PAYABLE_ENABLE_LOCK.%s", tenantId);

    }
}