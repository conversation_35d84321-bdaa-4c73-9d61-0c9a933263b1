package com.facishare.crm.fmcg.dms.model;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@Builder
public class FinancialBill implements Serializable {

    public static final String ACTION_LIFE_STATUS_UPDATE_NORMAL = "life_status_update_normal";
    public static final String ACTION_ENTERPRISE_FUND_ACCOUNT_INFO_UPDATE = "enterprise_fund_account_info_update";
    private String tenantId;

    private String apiName;

    private String id;

    private IObjectData data;
    private String action;
    private List<EnterpriseFundAccountInfoDTO> enterpriseFundAccountInfoUpdateBefore;
    private List<EnterpriseFundAccountInfoDTO> enterpriseFundAccountInfoUpdateAfter;

    private List<IObjectData> details;

    private FinancialBill relatedBill;

    private MatchType matchType;
}