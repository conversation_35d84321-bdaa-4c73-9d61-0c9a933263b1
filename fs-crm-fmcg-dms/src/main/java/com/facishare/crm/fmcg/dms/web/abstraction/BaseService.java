package com.facishare.crm.fmcg.dms.web.abstraction;

import com.facishare.crm.fmcg.dms.business.enums.AccountPayableSwitchEnum;
import com.facishare.crm.fmcg.dms.constants.AccountsPayableConstants;
import com.facishare.crm.fmcg.dms.constants.AccountsReceivableConstants;
import com.facishare.crm.fmcg.dms.i18n.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.restful.client.exception.FRestClientException;
import com.fxiaoke.bizconf.arg.ConfigArg;
import com.fxiaoke.bizconf.arg.QueryConfigByRankArg;
import com.fxiaoke.bizconf.bean.Rank;
import com.fxiaoke.bizconf.bean.ValueType;
import com.fxiaoke.bizconf.factory.BizConfClient;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
@SuppressWarnings("all")
@Slf4j
public abstract class BaseService {

    @Resource
    protected ServiceFacade serviceFacade;

    @Resource
    private BizConfClient bizConfClient;

    public static final String ACCOUNTS_PERIOD_CONFIG_KEY = "fmcg_accounts_period_config_key";

    protected boolean denyAccountPayable(String tenantId) {
        QueryConfigByRankArg queryStatusArg =
                QueryConfigByRankArg.builder().rank(Rank.TENANT).key(AccountsPayableConstants.ACCOUNTS_PAYABLE_SWITCH_KEY).tenantId(tenantId).pkg(AccountsPayableConstants.SWITCH_PKG).build();
        String config;
        try {
            config = bizConfClient.queryConfigByRank(queryStatusArg);
        } catch (FRestClientException ex) {
            throw new ValidateException(I18N.text(I18NKeys.ACCOUNTS_PAYABLE_QUERY_ERROR));
        }

        if (Strings.isNullOrEmpty(config)) {
            return true;
        }
        int value = Integer.parseInt(config);
        return value != AccountPayableSwitchEnum.OPENED.getStatus();
    }

    protected boolean enableAccountsReceivableAutoMatchButton(String tenantId) {
        QueryConfigByRankArg queryStatusArg =
                QueryConfigByRankArg.builder().rank(Rank.TENANT).key(AccountsReceivableConstants.ACCOUNTS_RECEIVABLE_AUTO_MATCH_BUTTON_SWITCH_KEY).tenantId(tenantId).pkg(AccountsReceivableConstants.SWITCH_PKG).build();
        String config;
        try {
            config = bizConfClient.queryConfigByRank(queryStatusArg);
        } catch (FRestClientException ex) {
            throw new ValidateException(I18N.text(I18NKeys.ACCOUNTS_PAYABLE_QUERY_ERROR));
        }
        if (Strings.isNullOrEmpty(config)) {
            return false;
        }
        int value = Integer.parseInt(config);
        return value == AccountPayableSwitchEnum.OPENED.getStatus();
    }

    protected int updateAccountsPayableStatus(User user, int status) {
        try {
            int result;
            String config = bizConfClient
                    .queryConfigByRank(QueryConfigByRankArg.builder().rank(Rank.TENANT).key(AccountsPayableConstants.ACCOUNTS_PAYABLE_SWITCH_KEY).tenantId(user.getTenantId()).pkg(AccountsPayableConstants.SWITCH_PKG).build());
            if (StringUtils.isBlank(config)) {
                ConfigArg configArg = buildConfigArg(user, AccountsPayableConstants.ACCOUNTS_PAYABLE_SWITCH_KEY, String.valueOf(status));
                result = bizConfClient.createConfig(configArg);
                log.info("bizConfApi.createConfig ,configArg:{}", configArg);
            } else {
                if (Objects.equals(String.valueOf(status), config)) {
                    return AccountPayableSwitchEnum.OPENING.getStatus();
                }
                result = bizConfClient.updateConfig(buildConfigArg(user, AccountsPayableConstants.ACCOUNTS_PAYABLE_SWITCH_KEY, String.valueOf(status)));
            }
            return result;
        } catch (Exception e) {
            log.error("updateAccountReceivableStatus error,", e);
            throw new MetaDataBusinessException("updateAccountReceivableStatus error");
        }
    }

    protected int updateReceivabelAutoMatchButtonStatus(User user, int status) {
        try {
            int result;
            String config = bizConfClient
                    .queryConfigByRank(QueryConfigByRankArg.builder().rank(Rank.TENANT).key(AccountsReceivableConstants.ACCOUNTS_RECEIVABLE_AUTO_MATCH_BUTTON_SWITCH_KEY).tenantId(user.getTenantId()).pkg(AccountsReceivableConstants.SWITCH_PKG).build());
            if (StringUtils.isBlank(config)) {
                ConfigArg configArg = buildConfigArg(user, AccountsReceivableConstants.ACCOUNTS_RECEIVABLE_AUTO_MATCH_BUTTON_SWITCH_KEY, String.valueOf(status));
                result = bizConfClient.createConfig(configArg);
                log.info("updateReceivabelAutoMatchButtonStatus bizConfApi.createConfig ,configArg:{}", configArg);
            } else {
                if (Objects.equals(String.valueOf(status), config)) {
                    return AccountPayableSwitchEnum.OPENING.getStatus();
                }
                result = bizConfClient.updateConfig(buildConfigArg(user, AccountsReceivableConstants.ACCOUNTS_RECEIVABLE_AUTO_MATCH_BUTTON_SWITCH_KEY, String.valueOf(status)));
            }
            return result;
        } catch (Exception e) {
            log.error("updateReceivabelAutoMatchButtonStatus error,", e);
            throw new MetaDataBusinessException("updateReceivabelAutoMatchButtonStatus error");
        }
    }

    protected int updateAccountsPeriodConfig(User user, String value) {
        try {
            int result;
            String config = bizConfClient
                    .queryConfigByRank(QueryConfigByRankArg.builder().rank(Rank.TENANT).key(ACCOUNTS_PERIOD_CONFIG_KEY).tenantId(user.getTenantId()).pkg(AccountsPayableConstants.SWITCH_PKG).build());
            if (StringUtils.isBlank(config)) {
                ConfigArg configArg = buildConfigArg(user, ACCOUNTS_PERIOD_CONFIG_KEY, value);
                result = bizConfClient.createConfig(configArg);
                log.info("bizConfApi.createConfig ,configArg:{}", configArg);
            } else {
                if (Objects.equals(value, config)) {
                    return AccountPayableSwitchEnum.OPENING.getStatus();
                }
                result = bizConfClient.updateConfig(buildConfigArg(user, ACCOUNTS_PERIOD_CONFIG_KEY, value));
            }
            return result;
        } catch (Exception e) {
            log.error("updateAccountsPeriodConfig error,", e);
            throw new MetaDataBusinessException("updateAccountsPeriodConfig error");
        }
    }

    protected String getAccountsPeriodConfig(String tenantId) {
        QueryConfigByRankArg queryStatusArg =
                QueryConfigByRankArg.builder().rank(Rank.TENANT).key(ACCOUNTS_PERIOD_CONFIG_KEY).tenantId(tenantId).pkg(AccountsPayableConstants.SWITCH_PKG).build();
        String config;
        try {
            config = bizConfClient.queryConfigByRank(queryStatusArg);
        } catch (FRestClientException ex) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_BASE_SERVICE_0));
        }

        if (Strings.isNullOrEmpty(config)) {
            return "";
        }
        return config;
    }

    public ConfigArg buildConfigArg(User user, String key, String value) {
        return ConfigArg.builder().key(key).operator(user.getUserId()).pkg("CRM").userId(user.getUserId()).tenantId(user.getTenantId()).valueType(ValueType.STRING).value(value).rank(Rank.TENANT).build();
    }
}
