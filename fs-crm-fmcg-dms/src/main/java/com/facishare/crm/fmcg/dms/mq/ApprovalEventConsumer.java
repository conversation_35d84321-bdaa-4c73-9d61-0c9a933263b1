package com.facishare.crm.fmcg.dms.mq;

import com.alibaba.fastjson.JSON;
import com.facishare.common.rocketmq.AutoConfRocketMQProcessor;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.dms.errors.AbandonActionException;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.crm.fmcg.dms.mq.model.WorkFlowEventOBJ;
import com.facishare.crm.fmcg.dms.service.mapper.MatchableBillMapper;
import com.facishare.crm.fmcg.dms.service.matcher.AutoMatcher;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


@Slf4j
@SuppressWarnings("Duplicates")
@Component("dmsApprovalEventConsumer")
public class ApprovalEventConsumer {

    private AutoConfRocketMQProcessor processor;

    @Resource
    private AutoMatcher autoMatcher;
    @Resource
    private MatchableBillMapper matchableBillMapper;
    private static final String CONFIG_NAME = "fs-fmcg-framework-config";
    private static final String NAME_SERVER_KEY = "WORKFLOW_NAMESERVER";
    private static final String TOPIC_KEY = "WORKFLOW_TOPIC";
    private static final String GROUP_NAME = "DMS_WORKFLOW_GROUP";

    @PostConstruct
    public void init() {
        if (!Objects.equals("1", System.getProperty("mn.dms.flag"))) {
            return;
        }
        log.info("dms ApprovalEventConsumer start init.");

        MessageListenerConcurrently listener = (messages, context) -> {
            for (MessageExt messageExt : messages) {
                try {
                    processMessage(messageExt);
                } catch (AbandonActionException ex) {
                    log.info("dms-object-data consumer abandon : ", ex);
                } catch (Exception ex) {
                    log.error("ApprovalEventConsumer consumer error.", ex);
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                } finally {
                    TraceContext.remove();
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        try {
            processor = new AutoConfRocketMQProcessor(CONFIG_NAME, NAME_SERVER_KEY, GROUP_NAME, TOPIC_KEY, listener);
            processor.init();
            log.info("dms ApprovalEventConsumer started.");
        } catch (Exception e) {
            log.error("init notice consumer failed.", e);
        }
    }

    public void processMessage(MessageExt messageExt) {
        String dataJsonStr = new String(messageExt.getBody());
        WorkFlowEventOBJ message = JSON.parseObject(dataJsonStr, WorkFlowEventOBJ.class);
        String tenantId = message.getContext().getString("tenantId");
        String apiName = message.getEntityId();
        String dataId = message.getObjectId();
        String status = message.getTriggerType();

        FinancialBill bill = FinancialBill.builder().tenantId(tenantId).apiName(apiName).id(dataId).build();

        if (!"Create".equals(status)) {
            return;
        }
        if (!ApiNames.PAYMENT_OBJ.equals(apiName)) {
            return;
        }

        log.info("approval bill id:{},apiName:{}", bill.getId(), bill.getApiName());
        TraceContext context = TraceContext.get();
        context.setTraceId(String.format("%s.%s.%s.%s", "DMS_WORKFLOW_MQ." + messageExt.getMsgId(), tenantId, bill.getApiName(), bill.getId()));
        List<FinancialBill> matchableBills = matchableBillMapper.map(bill);
        for (FinancialBill matchableBill : matchableBills) {
            autoMatcher.match(matchableBill);
        }

    }

    @PreDestroy
    public void shutDown() {
        processor.shutDown();
    }
}
