package com.facishare.crm.fmcg.dms.model;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface ReceivableNoSettledAmountSum {
    @Data
    class Arg {
        private List<String> accountIds;
    }

    @Data
    class Result {
        private Map<String, BigDecimal> sumNoSettledAmount;
        private boolean isOpenAccountReceivable = true;
    }
}
