package com.facishare.crm.fmcg.dms.util;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@SuppressWarnings("all")
public class ReceivableConvertBeanNameUtil {


    private static final Map<String, String> RECEIVABLE_SUPPORT_OBJECTS = Maps.newHashMap();

    static {
        RECEIVABLE_SUPPORT_OBJECTS.put(ApiNames.GOODS_RECEIVED_NOTE_OBJ, "goodsReceivedNoteAutoReceivableConvertToReceivableService");
        RECEIVABLE_SUPPORT_OBJECTS.put(ApiNames.OUTBOUND_DELIVERY_NOTE_OBJ, "outboundDeliveryNoteAutoReceivableConvertToReceivableService");


    }

    public String beanName(FinancialBill bill) {
        return RECEIVABLE_SUPPORT_OBJECTS.get(bill.getApiName());
    }


}
