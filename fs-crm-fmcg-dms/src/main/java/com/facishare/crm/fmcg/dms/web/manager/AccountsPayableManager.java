package com.facishare.crm.fmcg.dms.web.manager;

import com.facishare.crm.fmcg.common.apiname.AccountsPayableNoteFields;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.PayFields;
import com.facishare.crm.fmcg.dms.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class AccountsPayableManager {


    @Resource
    protected ServiceFacade serviceFacade;

    public List<IObjectData> getMatchApBySupplierId(User user, String supplierId, String collectionType) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();


        SearchUtil.fillFilterEq(filters, AccountsPayableNoteFields.SUPPLIER_ID, supplierId);

        if (Objects.equals(PayFields.PAY_TYPE__RED, collectionType)) {
            SearchUtil.fillFilterLT(filters, AccountsPayableNoteFields.PRE_MATCH_AMOUNT, BigDecimal.ZERO);
        } else {
            SearchUtil.fillFilterGT(filters, AccountsPayableNoteFields.PRE_MATCH_AMOUNT, BigDecimal.ZERO);
        }
        SearchUtil.fillFilterIn(filters, "life_status", Lists.newArrayList(
                CommonFields.LIFE_STATUS__NORMAL,
                CommonFields.LIFE_STATUS__IN_CHANGE));
        SearchUtil.fillFilterNotEq(filters, AccountsPayableNoteFields.MATCH_STATUS, AccountsPayableNoteFields.MATCH_STATUS__ALL_MATCH);
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setPermissionType(0); //0不走权限  1走权限
        searchTemplateQuery.setLimit(2000);
        searchTemplateQuery.setOffset(0);

        List<OrderBy> orders = new ArrayList<>();
        orders.add(new OrderBy(AccountsPayableNoteFields.NOTE_DATE, Boolean.TRUE));
        orders.add(new OrderBy(CommonFields.LAST_MODIFY_TIME, Boolean.TRUE));

        searchTemplateQuery.setOrders(orders);

        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(user, ApiNames.ACCOUNTS_PAYABLE_NOTE_OBJ, searchTemplateQuery);
        return queryResult.getData();
    }


}