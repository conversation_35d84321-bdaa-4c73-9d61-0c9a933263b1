package com.facishare.crm.fmcg.dms.mq;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.dms.business.DMSScriptHandlerFactory;
import com.facishare.crm.fmcg.dms.business.abstraction.IDMSScriptHandler;
import com.facishare.crm.fmcg.dms.business.abstraction.IStockService;
import com.facishare.crm.fmcg.dms.business.enums.ScriptHandlerNameEnum;
import com.facishare.crm.fmcg.dms.mq.model.LicenseMqObj;
import com.facishare.crm.fmcg.dms.web.abstraction.IAccountsReceivableNoteService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.github.trace.TraceContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;


@Component
public class PaasLicenseConsumer {

    private static final Logger logger = LoggerFactory.getLogger(PaasLicenseConsumer.class);
    private static final String CONFIG_NAME = "rocketmq-consumer.ini";
    private static final String SECTION_NAME = "common,name_server_02,consumer_license_create_fmcg_dms";
    public static final String KX_INDUSTRY = "kx_industry";
    public static final String DEALER_EDITION = "dealer_edition";
    public static final String ADVANCED_DEALER_EDITION = "advanced_dealer_edition";
    private AutoConfMQPushConsumer processor;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private IAccountsReceivableNoteService accountsReceivableNoteService;
    @Resource
    private DMSScriptHandlerFactory dmsScriptHandlerFactory;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private IStockService stockService;

    @PostConstruct
    public void init() {
        if (!Objects.equals("1", System.getProperty("mn.dms.flag"))) {
            return;
        }
        logger.info("PaasLicenseConsumer start init.");
        MessageListenerConcurrently listener = (messages, context) -> {
            for (MessageExt messageExt : messages) {
                try {
                    TraceContext.get().setTraceId(UUID.randomUUID().toString());
                    process(messageExt);
                } catch (Exception ex) {
                    logger.error("[PaasLicenseConsumer consumer error :" + messages + "]", ex);
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                } finally {
                    TraceContext.remove();
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        try {
            processor = new AutoConfMQPushConsumer(CONFIG_NAME, SECTION_NAME, listener);
            processor.start();
        } catch (Exception e) {
            logger.error("init PaasLicenseConsumer mq consumer failed.", e);
        }
    }

    private void process(MessageExt messageExt) {
        LicenseMqObj licenseMqObj = JSON.parseObject(messageExt.getBody(), LicenseMqObj.class);
        //控制是否开启正式企业刷预置字段
        if (!TPMGrayUtils.dmsLicenseMQFormalTenantEnable(licenseMqObj.getTenantId())) {
            String tenantAccount = eieaConverter.enterpriseIdToAccount(Integer.parseInt(licenseMqObj.getTenantId()));
            if (!tenantAccount.endsWith("sandbox")) {
                return;
            }
        }
        if (CollectionUtils.isEmpty(licenseMqObj.getModuleCodes())) {
            return;
        }

        if (licenseMqObj.getModuleCodes().contains(KX_INDUSTRY)) {
            processKXIndustry(licenseMqObj);
        } else if (licenseMqObj.getModuleCodes().contains(DEALER_EDITION) || licenseMqObj.getModuleCodes().contains(ADVANCED_DEALER_EDITION)) {
            processDealer(licenseMqObj);
        }
    }

    private void processKXIndustry(LicenseMqObj licenseMqObj) {
        Map<String, IObjectDescribe> describes =
                serviceFacade.findObjects(licenseMqObj.getTenantId(), Lists.newArrayList(ApiNames.SALES_ORDER_OBJ));

        if (CollectionUtils.isEmpty(describes)) {
            return;
        }

        if (accountsReceivableNoteService.denyAccountsReceivableEnable(licenseMqObj.getTenantId())) {
            //开启快消，未开启应收，处理字段逻辑
            IDMSScriptHandler onlyKXEnable = dmsScriptHandlerFactory.resolve(ScriptHandlerNameEnum.KX_ENABLE.getHandlerName());
            onlyKXEnable.updateFields(licenseMqObj.getTenantId(), describes);
            return;
        }
        //开启了应收、开启了快消行业套件
        IDMSScriptHandler handler = dmsScriptHandlerFactory.resolve(ScriptHandlerNameEnum.ACCOUNTS_RECEIVABLE_AND_KX_ENABLE.getHandlerName());
        handler.updateFields(licenseMqObj.getTenantId(), describes);
    }

    private void processDealer(LicenseMqObj licenseMqObj) {
        Map<String, IObjectDescribe> describes =
                serviceFacade.findObjects(licenseMqObj.getTenantId(), Lists.newArrayList(ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ));
        boolean enableAccountsReceivable = !accountsReceivableNoteService.denyAccountsReceivableEnable(licenseMqObj.getTenantId());
        boolean enableStock = stockService.enableDHTStock(licenseMqObj.getTenantId());
        if (enableAccountsReceivable && enableStock) {
            IDMSScriptHandler handler = dmsScriptHandlerFactory.resolve(ScriptHandlerNameEnum.KX_DEALER_EDITION_ENABLE.getHandlerName());
            handler.addFields(licenseMqObj.getTenantId(), describes);
        }
    }

    @PreDestroy
    public void shutDown() {
        processor.close();
    }
}
