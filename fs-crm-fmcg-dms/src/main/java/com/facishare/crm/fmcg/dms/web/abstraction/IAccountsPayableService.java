package com.facishare.crm.fmcg.dms.web.abstraction;

import com.facishare.crm.fmcg.dms.model.AccountsPayableAutoMatch;
import com.facishare.crm.fmcg.dms.model.AccountsPayableEnable;
import com.facishare.crm.fmcg.dms.model.AccountsPayableStatus;
import com.facishare.crm.fmcg.dms.model.BulkCreateMatchNote;

import java.util.List;

public interface IAccountsPayableService {


    AccountsPayableStatus.Result getAccountsPayableStatus(AccountsPayableStatus.Arg arg);

    AccountsPayableEnable.Result accountsPayableEnable(AccountsPayableEnable.Arg arg);

    boolean denyAccountsPayable(String tenantId);

    String updateStatus(String tenantId,Integer status);

    AccountsPayableAutoMatch.Result autoMatchPayable(AccountsPayableAutoMatch.Arg arg);

    BulkCreateMatchNote.Result bulkCreateMatchNote(BulkCreateMatchNote.Arg arg);

    void initField(List<Integer> tenantIds, String flag);

    void initManualMatchLayout(List<Integer> tenantIds, String flag);

    void batchAccountsPayableEnable(List<Integer> tenantIds, String flag,String enablePayable);

    void createOpeningBalanceSettingLayoutRule(List<Integer> tenantIds);
}
