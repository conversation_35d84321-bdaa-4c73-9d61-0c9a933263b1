package com.facishare.crm.fmcg.dms.web.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.dms.i18n.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.dms.business.PaasLicenseBusinessService;
import com.facishare.crm.fmcg.dms.constants.PluginAPINames;
import com.facishare.crm.fmcg.dms.model.GetAccountsPeriodConfigObjectInformation;
import com.facishare.crm.fmcg.dms.model.SaveAccountsPeriodConfigArg;
import com.facishare.crm.fmcg.dms.model.SaveAccountsPeriodConfigResult;
import com.facishare.crm.fmcg.dms.mq.PaasLicenseConsumer;
import com.facishare.crm.fmcg.dms.service.abastraction.PluginInstanceService;
import com.facishare.crm.fmcg.dms.web.abstraction.BaseService;
import com.facishare.crm.fmcg.dms.web.abstraction.IAccountsPeriodConfigService;
import com.facishare.crm.fmcg.dms.web.abstraction.IAccountsReceivableNoteService;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.RecordTypeFieldDescribe;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AccountsPeriodConfigService extends BaseService implements IAccountsPeriodConfigService {
    private static final List<String> ACCOUNTS_PERIOD_SUPPORT_API_NAMES = Lists.newArrayList(ApiNames.SALES_ORDER_OBJ, ApiNames.DELIVERY_NOTE_OBJ);

    @Resource
    private IAccountsReceivableNoteService accountsReceivableNoteService;
    @Resource
    private PaasLicenseBusinessService paasLicenseBusinessService;
    @Resource
    private PluginInstanceService pluginInstanceService;

    @Override
    public GetAccountsPeriodConfigObjectInformation.Result getAccountsPeriodConfigInitInformation(GetAccountsPeriodConfigObjectInformation.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        GetAccountsPeriodConfigObjectInformation.Result result = new GetAccountsPeriodConfigObjectInformation.Result();
        if (accountsReceivableNoteService.denyAccountsReceivableEnable(context.getTenantId())) {
            return result;
        }

        result.setObjects(Lists.newArrayList());

        for (String accountsPeriodSupportApiName : ACCOUNTS_PERIOD_SUPPORT_API_NAMES) {
            IObjectDescribe describe;
            try {
                describe = serviceFacade.findDescribeAndLayout(User.systemUser(context.getTenantId()), accountsPeriodSupportApiName, false, null).getObjectDescribe();
            } catch (ObjectDataNotFoundException exception) {
                log.info("find describe : {} not found", accountsPeriodSupportApiName);
                continue;
            } catch (Exception exception) {
                log.error("find describe error,", exception);
                continue;
            }
            result.getObjects().add(buildAccountsPeriodObjectVO(describe));
        }

        return result;
    }

    @Override
    public SaveAccountsPeriodConfigResult saveAccountsPeriodConfig(SaveAccountsPeriodConfigArg arg) {
        if (arg.getObjects() == null) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_0));
        }
        if (arg.getOpen() == null) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_1));
        }
        ApiContext context = ApiContextManager.getContext();
        if (accountsReceivableNoteService.denyAccountsReceivableEnable(context.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_2));
        }
        if (!paasLicenseBusinessService.checkKXEnableByModuleCode(context.getTenantId(), PaasLicenseConsumer.KX_INDUSTRY)) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_3));
        }
        if (Boolean.FALSE.equals(arg.getOpen())) {
            String accountsPeriodConfig = getAccountsPeriodConfig(context.getTenantId());
            if (StringUtils.isEmpty(accountsPeriodConfig)) {
                SaveAccountsPeriodConfigResult result = new SaveAccountsPeriodConfigResult();
                result.setOpen(false);
                return result;
            }
            SaveAccountsPeriodConfigResult result = JSON.parseObject(accountsPeriodConfig, SaveAccountsPeriodConfigResult.class);
            result.setOpen(false);
            updateAccountsPeriodConfig(User.systemUser(context.getTenantId()), JSON.toJSONString(result));
            return result;
        }
        SaveAccountsPeriodConfigResult result = new SaveAccountsPeriodConfigResult();
        result.setOpen(arg.getOpen());
        result.setObjects(Lists.newArrayList());
        for (SaveAccountsPeriodConfigArg.ObjectVO objectVO : arg.getObjects()) {
            if (!ACCOUNTS_PERIOD_SUPPORT_API_NAMES.contains(objectVO.getApiName())) {
                throw new ValidateException(objectVO.getApiName() + I18N.text(I18NKeys.VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_4));
            }

            IObjectDescribe describe;
            try {
                describe = serviceFacade.findDescribeAndLayout(User.systemUser(context.getTenantId()), objectVO.getApiName(), false, null).getObjectDescribe();
            } catch (ObjectDataNotFoundException exception) {
                throw new ValidateException(objectVO.getApiName() + I18N.text(I18NKeys.VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_5));
            }

            validateRecordType(objectVO, describe);

            if (objectVO.getBlock() == null) {
                throw new ValidateException(objectVO.getApiName() + I18N.text(I18NKeys.VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_6));
            }

            SaveAccountsPeriodConfigResult.RecordTypeVO recordTypeResult = SaveAccountsPeriodConfigResult.RecordTypeVO.builder().apiName(objectVO.getRecordType().getApiName()).label(getRecordTypeLabelByApiName(objectVO.getRecordType().getApiName(), describe)).build();
            result.getObjects().add(SaveAccountsPeriodConfigResult.ObjectVO.builder().apiName(objectVO.getApiName()).label(describe.getDisplayName()).block(objectVO.getBlock())
                    .recordType(recordTypeResult).build());
        }
        pluginInstanceService.addPluginUnit(Integer.parseInt(context.getTenantId()), -10000, ApiNames.SALES_ORDER_OBJ, PluginAPINames.SALES_ORDER_ACCOUNTS_PERIOD_VALIDATE);
        pluginInstanceService.addPluginUnit(Integer.parseInt(context.getTenantId()), -10000, ApiNames.DELIVERY_NOTE_OBJ, PluginAPINames.DELIVERY_NOTE_ACCOUNTS_PERIOD_VALIDATE);

        updateAccountsPeriodConfig(User.systemUser(context.getTenantId()), JSON.toJSONString(arg));

        return result;
    }


    @Override
    public SaveAccountsPeriodConfigResult getAccountsPeriodConfig(GetAccountsPeriodConfigObjectInformation.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        String accountsPeriodConfig = getAccountsPeriodConfig(context.getTenantId());
        if (StringUtils.isEmpty(accountsPeriodConfig)) {
            return new SaveAccountsPeriodConfigResult();
        }
        SaveAccountsPeriodConfigResult result = JSON.parseObject(accountsPeriodConfig, SaveAccountsPeriodConfigResult.class);
        for (SaveAccountsPeriodConfigResult.ObjectVO objectVO : result.getObjects()) {
            IObjectDescribe describe;
            try {
                describe = serviceFacade.findDescribeAndLayout(User.systemUser(context.getTenantId()), objectVO.getApiName(), false, null).getObjectDescribe();
            } catch (ObjectDataNotFoundException exception) {
                log.info("find describe : {} not found", objectVO.getApiName());
                continue;
            }
            objectVO.setLabel(describe.getDisplayName());

            if (objectVO.getRecordType() != null) {
                objectVO.getRecordType().setLabel(getRecordTypeLabelByApiName(objectVO.getRecordType().getApiName(), describe));
            }
        }
        return result;
    }

    private String getRecordTypeLabelByApiName(String apiName, IObjectDescribe describe) {

        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        RecordTypeFieldDescribe recordTypeField = describeExt.getRecordTypeField().orElse(null);

        if (recordTypeField == null) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_7));
        }
        for (IRecordTypeOption recordTypeOption : recordTypeField.getRecordTypeOptions()) {
            if (Objects.equals(apiName, recordTypeOption.getApiName())) {
                return recordTypeOption.getLabel();
            }
        }
        return "";
    }

    private void validateRecordType(SaveAccountsPeriodConfigArg.ObjectVO objectVO, IObjectDescribe describe) {
        SaveAccountsPeriodConfigArg.RecordTypeVO recordType = objectVO.getRecordType();
        if (Objects.isNull(recordType)) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_8) + objectVO.getApiName() + I18N.text(I18NKeys.VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_9));
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        RecordTypeFieldDescribe recordTypeField = describeExt.getRecordTypeField().orElse(null);

        if (recordTypeField == null) {
            throw new ValidateException(objectVO.getApiName() + I18N.text(I18NKeys.VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_10));
        }
        List<String> objectRecordTypeApiNames = recordTypeField.getRecordTypeOptions().stream().map(IRecordTypeOption::getApiName).collect(Collectors.toList());

        if (!objectRecordTypeApiNames.contains(recordType.getApiName())) {
            throw new ValidateException(recordType.getApiName() + I18N.text(I18NKeys.VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_11));
        }

    }


    private GetAccountsPeriodConfigObjectInformation.ObjectVO buildAccountsPeriodObjectVO(IObjectDescribe describe) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        RecordTypeFieldDescribe recordTypeField = describeExt.getRecordTypeField().orElse(null);

        if (recordTypeField == null) {
            return GetAccountsPeriodConfigObjectInformation.ObjectVO.builder().apiName(describe.getApiName()).label(describe.getDisplayName()).build();
        }
        List<GetAccountsPeriodConfigObjectInformation.RecordTypeVO> recordTypes = Lists.newArrayList();
        for (IRecordTypeOption recordTypeOption : recordTypeField.getRecordTypeOptions()) {
            recordTypes.add(GetAccountsPeriodConfigObjectInformation.RecordTypeVO.builder().apiName(recordTypeOption.getApiName()).label(recordTypeOption.getLabel()).build());
        }
        return GetAccountsPeriodConfigObjectInformation.ObjectVO.builder()
                .apiName(describe.getApiName()).label(describe.getDisplayName())
                .recordTypes(recordTypes).block(Lists.newArrayList(Boolean.FALSE, Boolean.TRUE)).build();
    }

}
