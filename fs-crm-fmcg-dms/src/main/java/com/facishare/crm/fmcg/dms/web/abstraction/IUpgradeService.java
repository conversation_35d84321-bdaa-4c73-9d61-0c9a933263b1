package com.facishare.crm.fmcg.dms.web.abstraction;

import com.facishare.crm.fmcg.dms.model.RecalculateOldData;
import com.facishare.crm.fmcg.dms.model.RecalculateSingleData;

public interface IUpgradeService {

    RecalculateOldData.Result recalculateOldData(RecalculateOldData.Arg arg);

    RecalculateSingleData.Result recalculateOldMatchNoteData(RecalculateSingleData.Arg arg);

    RecalculateSingleData.Result recalculateOldReceivableData(RecalculateSingleData.Arg arg);
}
