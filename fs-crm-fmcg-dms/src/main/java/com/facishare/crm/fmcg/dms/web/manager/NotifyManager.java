package com.facishare.crm.fmcg.dms.web.manager;

import com.facishare.crm.notify.model.RemindRecord;
import com.facishare.crm.notify.model.Service.AddRemindRecordArg;
import com.facishare.crm.notify.model.Service.AddRemindRecordResult;
import com.facishare.crm.notify.service.RemindRecordService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.github.trace.TraceContext;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class NotifyManager {
    @Resource
    private RemindRecordService remindRecordService;

    public void notifyObjectData(User user, IObjectData objectData, String title, String content, List<String> receiverIds) {
        if (CollectionUtils.isEmpty(receiverIds)) {
            return;
        }
        AddRemindRecordArg remindRecordArg = new AddRemindRecordArg();
        RemindRecord remindRecord = new RemindRecord();
        remindRecord.setEi(user.getTenantIdInt());
        remindRecord.setAppId("CRM");
        remindRecord.setTitle(title);
        remindRecord.setReceiverIDs(receiverIds.stream().map(Integer::parseInt).collect(Collectors.toList()));
        remindRecord.setFullContent(content);
        String sourceId = TraceContext.get().getTraceId();
        remindRecord.setSourceId(sourceId);
        remindRecord.setType(30);
        remindRecord.setUrlType(0);
        remindRecord.setUrlParameter(Maps.newHashMap());
        remindRecordArg.setRemindRecord(remindRecord);

        Map<String, String> headers = getHeader(user);
        AddRemindRecordResult result = remindRecordService.addRemindRecord(headers, remindRecordArg);
        log.info("transactionStatementNotify,arg:{}, result:{}", remindRecordArg, result);
    }

    private static Map<String, String> getHeader(User user) {
        Map<String, String> headers = Maps.newHashMap();
        headers.put("x-fs-ei", user.getTenantId());
        headers.put("x-fs-userInfo", user.getUserId());
        headers.put("x-tenant-id", user.getTenantId());
        headers.put("x-user-id", user.getUserId());
        return headers;
    }
}
