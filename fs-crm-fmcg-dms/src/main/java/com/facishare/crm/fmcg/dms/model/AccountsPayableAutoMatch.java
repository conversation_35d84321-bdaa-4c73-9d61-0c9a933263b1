package com.facishare.crm.fmcg.dms.model;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

public interface AccountsPayableAutoMatch {
    @Data
    class Arg {
        private String supplierId;
        private BigDecimal amount;
    }

    @Builder
    @Data
    class Result {

        private List<ObjectDataDocument> apData;
    }
}
