package com.facishare.crm.fmcg.dms.i18n;


public class I18NKeys {
    public static final String VALIDATE_ENTERPRISE_FUND_ACCOUNT_DUPLICATE_ERROR = "fmcg.crm.dms.VALIDATE_ENTERPRISE_FUND_ACCOUNT_DUPLICATE_ERROR";
    public static final String EXISTS_MATCH_NOTE_ERROR = "fmcg.crm.dms.EXISTS_MATCH_NOTE_ERROR";
    public static final String FUND_ACCOUNT_NOT_FOUND_ERROR= "fmcg.crm.dms.FUND_ACCOUNT_NOT_FOUND_ERROR";
    public static final String FUND_ACCOUNT_AMOUNT_NOT_FOUND_ERROR= "fmcg.crm.dms.FUND_ACCOUNT_AMOUNT_NOT_FOUND_ERROR";
    public static final String FUND_ACCOUNT_AVAILABLE_AMOUNT_NOT_ENOUGH_ERROR= "fmcg.crm.dms.FUND_ACCOUNT_AVAILABLE_AMOUNT_NOT_ENOUGH_ERROR";
    public static final String FUND_ACCOUNT_OPENING_BALANCE_NULL_ERROR= "fmcg.crm.dms.FUND_ACCOUNT_OPENING_BALANCE_NULL_ERROR";
    public static final String ACCOUNTS_PAYABLE_QUERY_ERROR= "fmcg.crm.dms.ACCOUNTS_PAYABLE_QUERY_ERROR";
    public static final String PAY_TOTAL_MONEY_GT_PURCHASE_TOTAL_MONEY_ERROR= "fmcg.crm.dms.PAY_TOTAL_MONEY_GT_PURCHASE_TOTAL_MONEY_ERROR";
    public static final String FMCG_ACCOUNTS_PAYABLE_VALIDATE_NO_PAY= "fmcg_accounts_payable.validate.no_pay";
    public static final String FMCG_ACCOUNTS_PAYABLE_VALIDATE_PAY_NO_MATCH_AMOUNT_NOT_ZERO= "fmcg_accounts_payable.validate.pay.no_match_amount.not.zero";
    public static final String FMCG_ACCOUNTS_PAYABLE_VALIDATE_PAY_PAY_TYPE_NOT_EMPTY= "fmcg_accounts_payable.validate.pay.pay_type.not.empty";
    public static final String PAY_AMOUNT_LT_OR_EQ_ZERO_ERROR= "fmcg.crm.dms.PAY_AMOUNT_LT_OR_EQ_ZERO_ERROR";
    public static final String PURCHASE_AMOUNT_NULL_ERROR= "fmcg.crm.dms.PURCHASE_AMOUNT_NULL_ERROR";
    public static final String ACCOUNTS_PAYABLE_DUPLICATE_ENABLE_ERROR= "fmcg.crm.dms.ACCOUNTS_PAYABLE_DUPLICATE_ENABLE_ERROR";
    public static final String ACCOUNTS_RECEIVABLE_DISABLE_ERROR= "fmcg.crm.dms.ACCOUNTS_RECEIVABLE_DISABLE_ERROR";
    public static final String SUPPLIER_NULL_ERROR= "fmcg.crm.dms.SUPPLIER_NULL_ERROR";

    public static final String ACCOUNTS_PAYABLE_NOTE_OBJ_ASYNC_BULK_INVALID_ACTION_0 = "fmcg.crm.fmcg.tpm.ACCOUNTS_PAYABLE_NOTE_OBJ_ASYNC_BULK_INVALID_ACTION.0";
    public static final String ACCOUNTS_PAYABLE_NOTE_OBJ_BULK_INVALID_ACTION_0 = "fmcg.crm.fmcg.tpm.ACCOUNTS_PAYABLE_NOTE_OBJ_BULK_INVALID_ACTION.0";
    public static final String ACCOUNTS_PAYABLE_NOTE_OBJ_INVALID_ACTION_0 = "fmcg.crm.fmcg.tpm.ACCOUNTS_PAYABLE_NOTE_OBJ_INVALID_ACTION.0";
    public static final String OPENING_BALANCE_SETTING_OBJ_ADD_ACTION_0 = "fmcg.crm.fmcg.tpm.OPENING_BALANCE_SETTING_OBJ_ADD_ACTION.0";
    public static final String OPENING_BALANCE_SETTING_OBJ_ADD_ACTION_1 = "fmcg.crm.fmcg.tpm.OPENING_BALANCE_SETTING_OBJ_ADD_ACTION.1";
    public static final String PAY_DETAIL_OBJ_BULK_INVALID_ACTION_0 = "fmcg.crm.fmcg.tpm.PAY_DETAIL_OBJ_BULK_INVALID_ACTION.0";
    public static final String PAY_DETAIL_OBJ_INVALID_ACTION_0 = "fmcg.crm.fmcg.tpm.PAY_DETAIL_OBJ_INVALID_ACTION.0";
    public static final String PAY_OBJ_ADD_ACTION_0 = "fmcg.crm.fmcg.tpm.PAY_OBJ_ADD_ACTION.0";
    public static final String PAY_OBJ_ADD_ACTION_1 = "fmcg.crm.fmcg.tpm.PAY_OBJ_ADD_ACTION.1";
    public static final String PAY_OBJ_ADD_ACTION_2 = "fmcg.crm.fmcg.tpm.PAY_OBJ_ADD_ACTION.2";
    public static final String PAY_OBJ_ADD_ACTION_3 = "fmcg.crm.fmcg.tpm.PAY_OBJ_ADD_ACTION.3";
    public static final String PAY_OBJ_ADD_ACTION_4 = "fmcg.crm.fmcg.tpm.PAY_OBJ_ADD_ACTION.4";
    public static final String PAY_OBJ_ADD_ACTION_5 = "fmcg.crm.fmcg.tpm.PAY_OBJ_ADD_ACTION.5";
    public static final String PAY_OBJ_ADD_ACTION_6 = "fmcg.crm.fmcg.tpm.PAY_OBJ_ADD_ACTION.6";
    public static final String PAY_OBJ_ADD_ACTION_7 = "fmcg.crm.fmcg.tpm.PAY_OBJ_ADD_ACTION.7";
    public static final String PAY_OBJ_ADD_ACTION_8 = "fmcg.crm.fmcg.tpm.PAY_OBJ_ADD_ACTION.8";
    public static final String PAY_OBJ_ADD_ACTION_9 = "fmcg.crm.fmcg.tpm.PAY_OBJ_ADD_ACTION.9";
    public static final String PAY_OBJ_ADD_ACTION_10 = "fmcg.crm.fmcg.tpm.PAY_OBJ_ADD_ACTION.10";
    public static final String PAY_OBJ_ADD_ACTION_11 = "fmcg.crm.fmcg.tpm.PAY_OBJ_ADD_ACTION.11";
    public static final String PAY_OBJ_ADD_ACTION_12 = "fmcg.crm.fmcg.tpm.PAY_OBJ_ADD_ACTION.12";
    public static final String PAY_OBJ_ADD_ACTION_13 = "fmcg.crm.fmcg.tpm.PAY_OBJ_ADD_ACTION.13";
    public static final String PAY_OBJ_ADD_ACTION_14 = "fmcg.crm.fmcg.tpm.PAY_OBJ_ADD_ACTION.14";
    public static final String PAY_OBJ_ADD_ACTION_15 = "fmcg.crm.fmcg.tpm.PAY_OBJ_ADD_ACTION.15";
    public static final String PAY_OBJ_ADD_ACTION_16 = "fmcg.crm.fmcg.tpm.PAY_OBJ_ADD_ACTION.16";
    public static final String PAY_OBJ_ADD_ACTION_17 = "fmcg.crm.fmcg.tpm.PAY_OBJ_ADD_ACTION.17";
    public static final String PAY_OBJ_ADD_ACTION_18 = "fmcg.crm.fmcg.tpm.PAY_OBJ_ADD_ACTION.18";
    public static final String PAY_OBJ_ADD_ACTION_19 = "fmcg.crm.fmcg.tpm.PAY_OBJ_ADD_ACTION.19";
    public static final String PAY_OBJ_ASYNC_BULK_INVALID_ACTION_0 = "fmcg.crm.fmcg.tpm.PAY_OBJ_ASYNC_BULK_INVALID_ACTION.0";
    public static final String PAY_OBJ_BULK_INVALID_ACTION_0 = "fmcg.crm.fmcg.tpm.PAY_OBJ_BULK_INVALID_ACTION.0";
    public static final String PAY_OBJ_INVALID_ACTION_0 = "fmcg.crm.fmcg.tpm.PAY_OBJ_INVALID_ACTION.0";
    public static final String ACCOUNTS_PAYABLE_COMMON_MANAGER_0 = "fmcg.crm.fmcg.tpm.ACCOUNTS_PAYABLE_COMMON_MANAGER.0";
    public static final String ACCOUNTS_PAYABLE_COMMON_MANAGER_1 = "fmcg.crm.fmcg.tpm.ACCOUNTS_PAYABLE_COMMON_MANAGER.1";
    public static final String COMMON_ACCOUNTS_RECEIVABLE_NOTE_MANAGER_0 = "fmcg.crm.fmcg.tpm.COMMON_ACCOUNTS_RECEIVABLE_NOTE_MANAGER.0";
    public static final String COMMON_ACCOUNTS_RECEIVABLE_NOTE_MANAGER_1 = "fmcg.crm.fmcg.tpm.COMMON_ACCOUNTS_RECEIVABLE_NOTE_MANAGER.1";
    public static final String COMMON_ACCOUNTS_RECEIVABLE_NOTE_MANAGER_2 = "fmcg.crm.fmcg.tpm.COMMON_ACCOUNTS_RECEIVABLE_NOTE_MANAGER.2";
    public static final String ACCOUNTS_PAYABLE_SERVICE_0 = "fmcg.crm.fmcg.tpm.ACCOUNTS_PAYABLE_SERVICE.0";
    public static final String ACCOUNTS_RECEIVABLE_NOTE_SERVICE_0 = "fmcg.crm.fmcg.tpm.ACCOUNTS_RECEIVABLE_NOTE_SERVICE.0";
    public static final String ACCOUNTS_RECEIVABLE_NOTE_SERVICE_1 = "fmcg.crm.fmcg.tpm.ACCOUNTS_RECEIVABLE_NOTE_SERVICE.1";
    public static final String ACCOUNTS_RECEIVABLE_NOTE_SERVICE_2 = "fmcg.crm.fmcg.tpm.ACCOUNTS_RECEIVABLE_NOTE_SERVICE.2";
    public static final String ACCOUNTS_RECEIVABLE_NOTE_SERVICE_3 = "fmcg.crm.fmcg.tpm.ACCOUNTS_RECEIVABLE_NOTE_SERVICE.3";
    public static final String ACCOUNTS_RECEIVABLE_NOTE_SERVICE_4 = "fmcg.crm.fmcg.tpm.ACCOUNTS_RECEIVABLE_NOTE_SERVICE.4";
    public static final String ACCOUNTS_RECEIVABLE_NOTE_SERVICE_5 = "fmcg.crm.fmcg.tpm.ACCOUNTS_RECEIVABLE_NOTE_SERVICE.5";
    public static final String ACCOUNTS_RECEIVABLE_NOTE_SERVICE_6 = "fmcg.crm.fmcg.tpm.ACCOUNTS_RECEIVABLE_NOTE_SERVICE.6";
    public static final String ACCOUNTS_RECEIVABLE_NOTE_SERVICE_7 = "fmcg.crm.fmcg.tpm.ACCOUNTS_RECEIVABLE_NOTE_SERVICE.7";
    public static final String ACCOUNTS_RECEIVABLE_NOTE_SERVICE_8 = "fmcg.crm.fmcg.tpm.ACCOUNTS_RECEIVABLE_NOTE_SERVICE.8";
    public static final String ACCOUNTS_RECEIVABLE_NOTE_SERVICE_9 = "fmcg.crm.fmcg.tpm.ACCOUNTS_RECEIVABLE_NOTE_SERVICE.9";
    public static final String ACCOUNTS_RECEIVABLE_NOTE_SERVICE_10 = "fmcg.crm.fmcg.tpm.ACCOUNTS_RECEIVABLE_NOTE_SERVICE.10";
    public static final String METADATA_D_M_S_SCRIPT_HANDLER_FACTORY_0 = "fmcg.crm.fmcg.tpm.METADATA.D_M_S_SCRIPT_HANDLER_FACTORY.0";
    public static final String METADATA_REBATE_SERVICE_0 = "fmcg.crm.fmcg.tpm.METADATA.REBATE_SERVICE.0";
    public static final String METADATA_BASE_HANDLER_0 = "fmcg.crm.fmcg.tpm.METADATA.BASE_HANDLER.0";
    public static final String VALIDATE_PLUGIN_BASE_SERVICE_0 = "fmcg.crm.fmcg.tpm.VALIDATE.PLUGIN_BASE_SERVICE.0";
    public static final String VALIDATE_PLUGIN_BASE_SERVICE_1 = "fmcg.crm.fmcg.tpm.VALIDATE.PLUGIN_BASE_SERVICE.1";
    public static final String VALIDATE_PLUGIN_BASE_SERVICE_2 = "fmcg.crm.fmcg.tpm.VALIDATE.PLUGIN_BASE_SERVICE.2";
    public static final String VALIDATE_BASE_SERVICE_0 = "fmcg.crm.fmcg.tpm.VALIDATE.BASE_SERVICE.0";
    public static final String VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_0 = "fmcg.crm.fmcg.tpm.VALIDATE.ACCOUNTS_PERIOD_CONFIG_SERVICE.0";
    public static final String VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_1 = "fmcg.crm.fmcg.tpm.VALIDATE.ACCOUNTS_PERIOD_CONFIG_SERVICE.1";
    public static final String VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_2 = "fmcg.crm.fmcg.tpm.VALIDATE.ACCOUNTS_PERIOD_CONFIG_SERVICE.2";
    public static final String VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_3 = "fmcg.crm.fmcg.tpm.VALIDATE.ACCOUNTS_PERIOD_CONFIG_SERVICE.3";
    public static final String VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_4 = "fmcg.crm.fmcg.tpm.VALIDATE.ACCOUNTS_PERIOD_CONFIG_SERVICE.4";
    public static final String VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_5 = "fmcg.crm.fmcg.tpm.VALIDATE.ACCOUNTS_PERIOD_CONFIG_SERVICE.5";
    public static final String VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_6 = "fmcg.crm.fmcg.tpm.VALIDATE.ACCOUNTS_PERIOD_CONFIG_SERVICE.6";
    public static final String VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_7 = "fmcg.crm.fmcg.tpm.VALIDATE.ACCOUNTS_PERIOD_CONFIG_SERVICE.7";
    public static final String VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_8 = "fmcg.crm.fmcg.tpm.VALIDATE.ACCOUNTS_PERIOD_CONFIG_SERVICE.8";
    public static final String VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_9 = "fmcg.crm.fmcg.tpm.VALIDATE.ACCOUNTS_PERIOD_CONFIG_SERVICE.9";
    public static final String VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_10 = "fmcg.crm.fmcg.tpm.VALIDATE.ACCOUNTS_PERIOD_CONFIG_SERVICE.10";
    public static final String VALIDATE_ACCOUNTS_PERIOD_CONFIG_SERVICE_11 = "fmcg.crm.fmcg.tpm.VALIDATE.ACCOUNTS_PERIOD_CONFIG_SERVICE.11";
    public static final String ALREADY_ENABLE_SFA_AUTO_MATCH = "fmcg.crm.fmcg.tpm.ACCOUNTS_RECEIVABLE_NOTE_SERVICE.11";
    public static final String ALREADY_ENABLE_IS_OPEN_ORDER_PAYMENT_MULTI_SOURCE = "fmcg.crm.fmcg.tpm.is_open_order_payment_multi_source.error";
    public static final String ACCOUNTS_RECEIVABLE_AUTO_MATCH_BUTTON_DUPLICATE_ENABLE_ERROR= "fmcg.crm.dms.ACCOUNTS_RECEIVABLE_AUTO_MATCH_BUTTON_DUPLICATE_ENABLE_ERROR";

    private I18NKeys() {
    }
}
