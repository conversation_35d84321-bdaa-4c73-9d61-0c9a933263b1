package com.facishare.crm.fmcg.dms.web.service;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.dms.i18n.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.dms.business.DMSScriptHandlerFactory;
import com.facishare.crm.fmcg.dms.business.abstraction.IDMSScriptHandler;
import com.facishare.crm.fmcg.dms.business.enums.ScriptHandlerNameEnum;
import com.facishare.crm.fmcg.dms.service.abastraction.PluginInstanceService;
import com.facishare.crm.fmcg.dms.web.abstraction.BaseService;
import com.facishare.crm.fmcg.dms.web.abstraction.IRebateService;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.paas.data.GetConfigValueByKey;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static com.facishare.crm.fmcg.dms.mq.RebateEnableMqConsumer.ACCOUNTS_RECEIVABLE_REL_REBATE_PLUGIN_NAME;

@Service
@Slf4j
public class RebateService extends BaseService implements IRebateService {

    @Resource
    private DMSScriptHandlerFactory dmsScriptHandlerFactory;
    @Resource
    private PaasDataProxy paasDataProxy;
    @Resource
    private PluginInstanceService pluginInstanceService;

    @Override
    public void initButton(List<String> tenantIds) {
        List<String> exTenantIds = Lists.newArrayList();
        for (String tenantId : tenantIds) {
            try {
                IDMSScriptHandler onlyRebateEnableHandler = dmsScriptHandlerFactory.resolve(ScriptHandlerNameEnum.REBATE_ENABLE.getHandlerName());
                onlyRebateEnableHandler.initButton(tenantId);

                pluginInstanceService.addPluginUnit(Integer.parseInt(tenantId), -10000, ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ, ACCOUNTS_RECEIVABLE_REL_REBATE_PLUGIN_NAME);

            } catch (Exception ex) {
                log.error("initButton err,ex");
                exTenantIds.add(tenantId);
            }

        }
        if (CollectionUtils.isNotEmpty(exTenantIds)) {
            log.error("initButton err,exTenantIds:{}", exTenantIds);
        }
    }

    @Override
    public boolean isOpenRebate(String tenantId) {
        GetConfigValueByKey.Arg getConfigArg = new GetConfigValueByKey.Arg();
        getConfigArg.setKey("rebate");
        GetConfigValueByKey.Result configValueByKey = paasDataProxy.getConfigValueByKey(Integer.parseInt(tenantId), -10000, getConfigArg);
        if (configValueByKey.getCode() != 0) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_REBATE_SERVICE_0));
        }
        return Objects.equals(configValueByKey.getData().getValue(), "1");
    }
}
