package com.facishare.crm.fmcg.upgrade.api;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface Upgrade {

    @Data
    @ToString
    class Arg implements Serializable {

        private List<String> tenantIds;

        private String version;
    }

    @Data
    @ToString
    @Builder
    class ModuleUpgradeInformationDTO implements Serializable {

        private String module;

        private boolean success;

        private String message;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private Map<String, List<ModuleUpgradeInformationDTO>> data;
    }
}
