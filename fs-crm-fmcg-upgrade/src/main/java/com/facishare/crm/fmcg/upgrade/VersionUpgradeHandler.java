package com.facishare.crm.fmcg.upgrade;

import com.facishare.crm.fmcg.upgrade.api.Upgrade;
import com.facishare.crm.fmcg.upgrade.api.Version;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class VersionUpgradeHandler {

    @Resource
    private List<ModuleUpgradeHandler> moduleUpgradeHandlerList;

    public Upgrade.Result upgrade(Upgrade.Arg arg) {
        Version version = Version.valueOf(arg.getVersion());

        Map<String, List<Upgrade.ModuleUpgradeInformationDTO>> data = new HashMap<>();
        for (String tenantId : arg.getTenantIds()) {
            log.info("version upgrade start : {}.{}", tenantId, arg.getVersion());

            data.put(tenantId, batchModuleUpgrade(tenantId, version));
            log.info("version upgrade end : {}.{}", tenantId, arg.getVersion());
        }
        return Upgrade.Result.builder().data(data).build();
    }

    private List<Upgrade.ModuleUpgradeInformationDTO> batchModuleUpgrade(String tenantId, Version version) {
        List<Upgrade.ModuleUpgradeInformationDTO> data = Lists.newArrayList();

        for (ModuleUpgradeHandler handler : moduleUpgradeHandlerList) {
            if (version.equals(handler.version())) {
                try {

                    log.info("module upgrade start : {}.{}.{}", tenantId, version, handler.module());
                    handler.upgrade(tenantId);

                    log.info("module upgrade success : {}.{}.{}", tenantId, version, handler.module());
                    data.add(Upgrade.ModuleUpgradeInformationDTO.builder().success(true).module(handler.module()).build());
                } catch (Exception ex) {
                    log.error(String.format("module upgrade error : %s.%s.%s", tenantId, version, handler.module()), ex);
                    data.add(Upgrade.ModuleUpgradeInformationDTO.builder().success(false).message(ex.getMessage()).module(handler.module()).build());
                }
            }
        }
        return data;
    }
}