package com.facishare.crm.fmcg.upgrade.v860;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.tpm.business.CarryForwardActionService;
import com.facishare.crm.fmcg.common.utils.LanguageReplaceWrapper;
import com.facishare.crm.fmcg.upgrade.ModuleUpgradeHandler;
import com.facishare.crm.fmcg.upgrade.api.Version;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldLayoutPojo;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fxiaoke.notifier.support.NotifierClient;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

@Component
@SuppressWarnings("Duplicates")
public class AsyncCarryForwardModuleUpgradeHandler extends ModuleUpgradeHandler {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private CarryForwardActionService carryForwardActionService;

    public static final String DETAIL_LAYOUT_TYPE = "detail";

    @Override
    protected Version version() {
        return Version.V860;
    }

    @Override
    protected String module() {
        return "ASYNC_CARRY_FORWARD";
    }

    @Override
    protected void upgrade(String tenantId) {
        upgradeFields(tenantId, "TPMBudgetCarryForwardObj", "carry_forward_status", "carry_forward_time");
        upgradeFields(tenantId, "TPMBudgetCarryForwardDetailObj", "carry_forward_status", "carry_forward_failure_message");
        carryForwardActionService.initRetryButton(tenantId);
    }

    private void upgradeFields(String tenantId, String objectApiName, String... fieldApiNames) {
        IObjectDescribe describe = serviceFacade.findObject(tenantId, objectApiName);
        for (String fieldApiName : fieldApiNames) {
            upgradeField(tenantId, describe, fieldApiName);
        }
        clearDescribeCache(tenantId, describe.getApiName());
    }

    private void clearDescribeCache(String tenantId, String apiName) {
        NotifierClient.send("describe-extra-clear-room", String.format("%s_%s", tenantId, apiName));
    }

    private String loadFieldDescribeJsonFromResource(String objectApiName, String fieldApiName) {
        try {
            File file = ResourceUtils.getFile(String.format("classpath:tpm/module_fields/%s.%s.json", objectApiName, fieldApiName));
            return new String(Files.readAllBytes(file.toPath()));
        } catch (IOException e) {
            throw new MetaDataBusinessException("read field describe from file cause io exception.");
        }
    }

    private void upgradeField(String tenantId, IObjectDescribe describe, String fieldApiName) {
        String fieldDescribeJson = loadFieldDescribeJsonFromResource(describe.getApiName(), fieldApiName);
        if (describe.containsField(fieldApiName)) {
            doUpdateField(tenantId, describe, fieldDescribeJson);
        } else {
            doAddField(tenantId, describe, fieldDescribeJson);
        }
    }

    private void doUpdateField(String tenantId, IObjectDescribe describe, String fieldDescribe) {
        User superUser = User.systemUser(tenantId);
        FieldLayoutPojo fieldLayout = buildFieldLayout(tenantId, describe, fieldDescribe);

        serviceFacade.updateCustomFieldDescribe(superUser, describe.getApiName(), fieldDescribe, Lists.newArrayList(fieldLayout), Lists.newArrayList());
    }

    private void doAddField(String tenantId, IObjectDescribe describe, String fieldDescribe) {
        User superUser = User.systemUser(tenantId);
        FieldLayoutPojo fieldLayout = buildFieldLayout(tenantId, describe, fieldDescribe);

        LanguageReplaceWrapper.doInChinese(() -> {
            serviceFacade.addDescribeCustomField(superUser, describe.getApiName(), fieldDescribe, Lists.newArrayList(fieldLayout), Lists.newArrayList());
        });
    }

    private FieldLayoutPojo buildFieldLayout(String tenantId, IObjectDescribe describe, String fieldDescribe) {
        User superUser = User.systemUser(tenantId);

        JSONObject field = JSON.parseObject(fieldDescribe);

        FieldLayoutPojo fieldLayout = new FieldLayoutPojo();
        ILayout layout = serviceFacade.getLayoutLogicService().findDefaultLayout(superUser, DETAIL_LAYOUT_TYPE, describe.getApiName());

        fieldLayout.setApiName(layout.getName());
        fieldLayout.setLabel(field.getString("label"));
        fieldLayout.setRenderType(field.getString("type"));
        fieldLayout.setReadonly(Boolean.TRUE.equals(field.getBoolean("is_readonly")));
        fieldLayout.setRequired(false);
        fieldLayout.setShow(true);
        fieldLayout.setLayoutType(DETAIL_LAYOUT_TYPE);

        return fieldLayout;
    }
}
