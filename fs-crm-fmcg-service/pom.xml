<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.facishare</groupId>
        <artifactId>fs-crm-fmcg</artifactId>
        <version>9.6.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>fs-crm-fmcg-service</artifactId>
    <version>9.6.0-SNAPSHOT</version>
    <packaging>war</packaging>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fs-appcenter-rest-api</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-crm-rest-api</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-eservice-common</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-open-callback-api</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-open-common-storage</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-open-common-utils</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-open-msg-api</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-paas-function-biz-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-timezone-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpcore</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>checkins-v2-api</artifactId>
                    <groupId>com.facishare.appserver</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-open-material-api</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>swagger-annotations</artifactId>
                    <groupId>io.swagger</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>rocketmq-spring-support</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-sandbox-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare.appserver</groupId>
            <artifactId>fs-appserver-common-tools</artifactId>
            <version>${appserver-common-tools.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-fcp-biz-server</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-fcp-common</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-text</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>config-core</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-metadata</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>curator-client</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-framework</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-open-common-storage</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>kryo</artifactId>
                    <groupId>com.esotericsoftware</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>retrofit-spring2</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>retrofit-spring</artifactId>
                    <groupId>com.github.zhxing</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>swagger-annotations</artifactId>
                    <groupId>io.swagger</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>java-jwt</artifactId>
                    <groupId>com.auth0</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.facishare.open</groupId>
                    <artifactId>rocketmq-spring-support</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.rocketmq</groupId>
                    <artifactId>rocketmq-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.rocketmq</groupId>
                    <artifactId>rocketmq-client</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-crm-rest-api</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-timezone-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>rocketmq-remoting</artifactId>
                    <groupId>com.alibaba.rocketmq</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>rocketmq-common</artifactId>
                    <groupId>com.alibaba.rocketmq</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>rocketmq-client</artifactId>
                    <groupId>com.alibaba.rocketmq</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-flow</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-license</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-metadata-restdriver</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-privilege</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-fcp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-metadata-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-coordination</artifactId>
        </dependency>
        <dependency>
            <groupId>com.esotericsoftware</groupId>
            <artifactId>reflectasm</artifactId>
            <version>${reflectasm.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare.appserver</groupId>
            <artifactId>checkins-v2-api</artifactId>
            <version>${checkins-v2-api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-common-result</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-common-utils</artifactId>
            <version>${fs-open-common-utils.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare.paas</groupId>
            <artifactId>fs-paas-license-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.checkerframework</groupId>
                    <artifactId>checker-qual</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-sandbox-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-common-mq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-fmcg-integral</artifactId>
            <version>${crm.fmcg.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.fxiaoke</groupId>
                    <artifactId>fs-enterpriserelation-rest-api2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-common-mq</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>retrofit-spring2</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-open-msg-api</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-open-material-api</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-paas-function-biz-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>hamcrest</artifactId>
                    <groupId>org.hamcrest</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>javax.el-api</artifactId>
                    <groupId>javax.el</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ahocorasick</artifactId>
                    <groupId>org.ahocorasick</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpcore</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>protostuff-core</artifactId>
                    <groupId>com.dyuproject.protostuff</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>protostuff-api</artifactId>
                    <groupId>com.dyuproject.protostuff</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>bcprov-jdk16</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-fmcg-mengniu</artifactId>
            <version>${crm.fmcg.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-organization-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare.open</groupId>
                    <artifactId>fs-open-common-utils</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare.open</groupId>
                    <artifactId>fs-open-common-storage</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare.open</groupId>
                    <artifactId>fs-open-common-result</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare.open</groupId>
                    <artifactId>fs-oauth-base-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare.appserver</groupId>
                    <artifactId>checkins-v2-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpcore</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dyuproject.protostuff</groupId>
                    <artifactId>protostuff-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.zxing</groupId>
                    <artifactId>core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.jsoup</groupId>
                    <artifactId>jsoup</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dyuproject.protostuff</groupId>
                    <artifactId>protostuff-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dyuproject.protostuff</groupId>
                    <artifactId>protostuff-runtime</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.webby.proto</groupId>
                    <artifactId>protostuff-runtime-proto</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dyuproject.protostuff</groupId>
                    <artifactId>protostuff-collectionschema</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>swagger-annotations</artifactId>
                    <groupId>io.swagger</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-open-material-api</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-appcenter-rest-api</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-eservice-common</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-timezone-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpmime</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>i18n-client</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-fmcg-ocr</artifactId>
            <version>${crm.fmcg.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>hamcrest-core</artifactId>
                    <groupId>org.hamcrest</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpmime</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-fmcg-others</artifactId>
            <version>${crm.fmcg.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-common-mq</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-fmcg-tpm</artifactId>
            <version>${crm.fmcg.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.dyuproject.protostuff</groupId>
                    <artifactId>protostuff-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-qixin-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare.open</groupId>
                    <artifactId>fs-open-common-result</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare.open</groupId>
                    <artifactId>fs-open-common-storage</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare.open</groupId>
                    <artifactId>fs-open-common-utils</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare.open</groupId>
                    <artifactId>fs-open-msg-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare.open</groupId>
                    <artifactId>fs-wechat-proxy-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.zhxing</groupId>
                    <artifactId>retrofit-spring</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sun.istack</groupId>
                    <artifactId>istack-commons-runtime</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sun.xml.fastinfoset</groupId>
                    <artifactId>FastInfoset</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-configuration</groupId>
                    <artifactId>commons-configuration</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.activation</groupId>
                    <artifactId>activation</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.xml.bind</groupId>
                    <artifactId>jaxb-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.curator</groupId>
                    <artifactId>curator-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.curator</groupId>
                    <artifactId>curator-framework</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpcore</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.yaml</groupId>
                    <artifactId>snakeyaml</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fxiaoke</groupId>
                    <artifactId>fs-appcenter-rest-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fxiaoke</groupId>
                    <artifactId>fs-enterpriserelation-rest-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fxiaoke</groupId>
                    <artifactId>i18n-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-uc-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.bytebuddy</groupId>
                    <artifactId>byte-buddy</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare.appserver</groupId>
                    <artifactId>checkins-v2-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare.paas</groupId>
                    <artifactId>fs-paas-license-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml</groupId>
                    <artifactId>classmate</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.zxing</groupId>
                    <artifactId>core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare.open</groupId>
                    <artifactId>fs-open-material-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-organization-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-paas-foundation-boot</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-models</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.amqp</groupId>
                    <artifactId>spring-rabbit</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.pagehelper</groupId>
                    <artifactId>pagehelper</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mongodb.morphia</groupId>
                    <artifactId>morphia-logging-slf4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mongodb</groupId>
                    <artifactId>mongo-java-driver</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.el</groupId>
                    <artifactId>javax.el-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpmime</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.ow2.asm</groupId>
                    <artifactId>asm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare.appserver</groupId>
                    <artifactId>fs-appserver-common-tools</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.ahocorasick</groupId>
                    <artifactId>ahocorasick</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dyuproject.protostuff</groupId>
                    <artifactId>protostuff-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dyuproject.protostuff</groupId>
                    <artifactId>protostuff-collectionschema</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dyuproject.protostuff</groupId>
                    <artifactId>protostuff-json</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dyuproject.protostuff</groupId>
                    <artifactId>protostuff-runtime</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk16</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.esotericsoftware</groupId>
                    <artifactId>reflectasm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare.open</groupId>
                    <artifactId>fs-eservice-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.opencsv</groupId>
                    <artifactId>opencsv</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-fsi-proxy</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>spotbugs-annotations</artifactId>
                    <groupId>com.github.spotbugs</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.rocketmq</groupId>
                    <artifactId>rocketmq-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hamcrest</groupId>
                    <artifactId>hamcrest-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.esotericsoftware</groupId>
                    <artifactId>kryo</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-timezone-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fxiaoke</groupId>
                    <artifactId>retrofit-spring2</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-crm-rest-api</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jopt-simple</artifactId>
                    <groupId>net.sf.jopt-simple</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-spring</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-wechat-union-common</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-fmcg-upgrade</artifactId>
            <version>${crm.fmcg.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-metadata-option-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-fsi-proxy</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>kafka-clients</artifactId>
                    <groupId>org.apache.kafka</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spotbugs-annotations</artifactId>
                    <groupId>com.github.spotbugs</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>${kafka-clients.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-privilege-temp</artifactId>
            <version>${appframework.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.colin-lee</groupId>
                    <artifactId>config-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-pod-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-codec</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-buffer</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-codec-http</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-codec-http2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-codec-socks</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-handler</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-handler-proxy</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-resolver</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-transport</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fxiaoke</groupId>
                    <artifactId>fs-rocketmq-support</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.ow2.util.bundles</groupId>
                    <artifactId>javassist-3.14.0-GA</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-dubbo-rest-plugin</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-qixin-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.dataformat</groupId>
                    <artifactId>jackson-dataformat-yaml</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fxiaoke.api</groupId>
                    <artifactId>fs-id-generator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.errorprone</groupId>
                    <artifactId>error_prone_annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-configuration</groupId>
                    <artifactId>commons-configuration</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.activation</groupId>
                    <artifactId>activation</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.sf.jopt-simple</groupId>
                    <artifactId>jopt-simple</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-text</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.curator</groupId>
                    <artifactId>curator-framework</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpcore</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.ehcache</groupId>
                    <artifactId>ehcache</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mongodb</groupId>
                    <artifactId>mongo-java-driver</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.yaml</groupId>
                    <artifactId>snakeyaml</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-pool2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fxiaoke</groupId>
                    <artifactId>i18n-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>fs-qixin-util</groupId>
                    <artifactId>fs-qixin-common-beans</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-plat-org-adapter-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-pod-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.fxiaoke.api</groupId>
                    <artifactId>fs-id-generator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.errorprone</groupId>
                    <artifactId>error_prone_annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-configuration</groupId>
                    <artifactId>commons-configuration</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.checkerframework</groupId>
                    <artifactId>checker-qual</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mongodb</groupId>
                    <artifactId>mongo-java-driver</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-transport-native-unix-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fxiaoke</groupId>
                    <artifactId>jdbc-sql-stat</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-rest-es7-support</artifactId>
                    <groupId>com.fxiaoke.common</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-qixin-api</artifactId>
            <version>${fs-qixin-api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-uc-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.squareup.okio</groupId>
                    <artifactId>okio-jvm</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>i18n-util</artifactId>
        </dependency>

        <!-- 统一的i18n-client依赖 -->
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>i18n-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>fs-job-core</artifactId>
            <version>${fs-job-core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>fs-rest-es7-support</artifactId>
            <version>${fs-rest-es7-support.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>metrics-oss</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.google.errorprone</groupId>
                    <artifactId>error_prone_annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.msg</groupId>
            <artifactId>fs-message-api</artifactId>
            <version>${fs-message-api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-crm-rest-api</artifactId>
            <version>${fs-crm-rest-api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-enterpriserelation-rest-api2</artifactId>
            <version>${fs-enterpriserelation-rest-api2.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-rocketmq-support</artifactId>
            <version>${rocketmq-support.version}</version>
        </dependency>


        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>logconfig-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-configuration</groupId>
                    <artifactId>commons-configuration</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>config-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>core-filter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>java-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>mongo-spring-support</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.google.errorprone</groupId>
                    <artifactId>error_prone_annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>4.1.74.Final</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty</artifactId>
            <version>${netty3.version}</version>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.ws.rs</groupId>
            <artifactId>javax.ws.rs-api</artifactId>
            <version>${javax.ws.rs-api.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>${commons-compress.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okio</groupId>
                    <artifactId>okio-jvm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.luben</groupId>
                    <artifactId>zstd-jni</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>lz4-java</artifactId>
                    <groupId>org.lz4</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.ehcache</groupId>
            <artifactId>ehcache</artifactId>
            <version>${ehcache.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-client</artifactId>
            <version>${resteasy.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-jackson2-provider</artifactId>
            <version>${resteasy.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.fge</groupId>
                    <artifactId>msg-simple</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-coreutils</artifactId>
                    <groupId>com.github.fge</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-jaxrs</artifactId>
            <version>${resteasy.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-jdk-http</artifactId>
            <version>${resteasy.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-netty</artifactId>
            <version>${resteasy.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-servlet-initializer</artifactId>
            <version>${resteasy.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.jboss.resteasy</groupId>
                    <artifactId>resteasy-jaxrs</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-spring</artifactId>
            <version>${resteasy.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-validator-provider</artifactId>
            <version>${resteasy.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mongodb.morphia</groupId>
            <artifactId>morphia</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongo-java-driver</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>jdbc-sql-stat</artifactId>
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>${snakeyaml.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-fmcg-yqsl</artifactId>
            <version>${crm.fmcg.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-fmcg-dms</artifactId>
            <version>${crm.fmcg.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fs-druid</artifactId>
            <version>1.2.15-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>mybatis-spring-support</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>druid</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-fmcg-fesco</artifactId>
            <version>${crm.fmcg.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-fmcg-tongfu</artifactId>
            <version>${crm.fmcg.version}</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.tomcat.maven</groupId>
                <artifactId>tomcat7-maven-plugin</artifactId>
                <configuration>
                    <url>http://localhost:8080/manager/html</url>
                    <server>tomcat</server>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <pluginRepositories>
        <pluginRepository>
            <id>fxiaoke</id>
            <name>fxiaoke repo</name>
            <layout>default</layout>
            <url>https://maven.firstshare.cn/artifactory/plugins-release/</url>
        </pluginRepository>
        <pluginRepository>
            <id>alfresco-public</id>
            <url>https://artifacts.alfresco.com/nexus/content/groups/public</url>
        </pluginRepository>
        <pluginRepository>
            <id>alfresco-public-snapshots</id>
            <url>https://artifacts.alfresco.com/nexus/content/groups/public-snapshots</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
            </snapshots>
        </pluginRepository>
        <pluginRepository>
            <id>beardedgeeks-releases</id>
            <url>https://beardedgeeks.googlecode.com/svn/repository/releases</url>
        </pluginRepository>
    </pluginRepositories>
</project>
