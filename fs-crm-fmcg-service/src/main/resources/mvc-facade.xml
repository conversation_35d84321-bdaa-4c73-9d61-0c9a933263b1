<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns="http://www.springframework.org/schema/beans" xmlns:mvc="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd">

    <context:component-scan base-package="com.facishare.crm.fmcg.service.web.facade"/>
    <context:annotation-config/>

    <import resource="classpath:META-INF/fs-cep-plugin.xml"/>

    <mvc:interceptors>
        <bean class="com.facishare.crm.fmcg.service.web.facade.provider.CEPHandlerInterceptorAdapter"/>
    </mvc:interceptors>

</beans>
