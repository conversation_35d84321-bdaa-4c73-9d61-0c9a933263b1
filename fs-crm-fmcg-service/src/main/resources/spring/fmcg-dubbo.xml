<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <dubbo:reference id="checkinsDataV2Service"
                     interface="com.facishare.checkins.biz.api.service.CheckinsDataV2Service" timeout="60000"
                     retries="0"/>
    <!--企信消息推送-->
    <dubbo:reference id="pushSessionSendService" interface="com.facishare.qixin.api.service.PushSessionService"
                     timeout="5000" check="false" retries="0"/>

    <dubbo:reference version="1.0" id="queryAppAdminService" interface="com.facishare.open.app.center.api.service.QueryAppAdminService"
                     check="false" retries="0" timeout="5000"/>
    <dubbo:reference id="fileUnityService" interface="com.facishare.warehouse.api.service.FileUnityService"
                     protocol="dubbo" retries="0" timeout="15000" check="false"/>


</beans>
