<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       default-lazy-init="true" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd">

    <bean id="mongoContext" class="com.github.mongo.support.MongoDataStoreFactoryBean"
          p:configName="fs-fmcg-mongo"/>

    <bean id="activityTypeDAO" class="com.facishare.crm.fmcg.tpm.dao.mongo.ActivityTypeDAO">
        <constructor-arg name="clazz" value="com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypePO"/>
    </bean>
    <bean id="activityTypeDraftBoxDAO" class="com.facishare.crm.fmcg.tpm.dao.mongo.ActivityTypeDraftBoxDAO">
        <constructor-arg name="clazz" value="com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeDraftBoxPO"/>
    </bean>
    <bean id="activityNodeTemplateDAO" class="com.facishare.crm.fmcg.tpm.dao.mongo.ActivityNodeTemplateDAO">
        <constructor-arg name="clazz" value="com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityNodeTemplatePO"/>
    </bean>
    <bean id="budgetTypeDAO" class="com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO">
        <constructor-arg name="clazz" value="com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO"/>
    </bean>

    <bean id="budgetNewConsumeRuleDAO" class="com.facishare.crm.fmcg.tpm.dao.mongo.BudgetNewConsumeRuleDAO">
        <constructor-arg name="clazz" value="com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetNewConsumeRulePO"/>
    </bean>
    <bean id="budgetAccrualRuleDAO" class="com.facishare.crm.fmcg.tpm.dao.mongo.BudgetAccrualRuleDAO">
        <constructor-arg name="clazz" value="com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetAccrualRulePO"/>
    </bean>
    <bean id="configDAO" class="com.facishare.crm.fmcg.tpm.dao.mongo.ConfigDAO">
        <constructor-arg name="clazz" value="com.facishare.crm.fmcg.tpm.dao.mongo.po.ConfigPO"/>
    </bean>
    <bean id="pocRecordDAO" class="com.facishare.crm.fmcg.tpm.dao.mongo.POCRecordDAO">
        <constructor-arg name="clazz" value="com.facishare.crm.fmcg.tpm.dao.mongo.po.POCRecordPO"/>
    </bean>
    <bean id="promotionPolicyDAO" class="com.facishare.crm.fmcg.tpm.dao.mongo.PromotionPolicyDAO">
        <constructor-arg name="clazz" value="com.facishare.crm.fmcg.tpm.dao.mongo.po.PromotionPolicyPO"/>
    </bean>
    <bean id="activityRewardRuleDAO" class="com.facishare.crm.fmcg.tpm.dao.mongo.ActivityRewardRuleDAO">
        <constructor-arg name="clazz" value="com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityRewardRulePO"/>
    </bean>
    <bean id="retryTaskDAO" class="com.facishare.crm.fmcg.tpm.dao.mongo.RetryTaskDAO">
        <constructor-arg name="clazz" value="com.facishare.crm.fmcg.tpm.dao.mongo.po.RetryTaskPO"/>
    </bean>

    <bean id="bizCodeDAO" class="com.facishare.crm.fmcg.tpm.dao.mongo.BizCodeDAO">
        <constructor-arg name="clazz" value="com.facishare.crm.fmcg.tpm.dao.mongo.po.BizCodePO"/>
    </bean>

    <bean id="rewardTransferAccountDAO" class="com.facishare.crm.fmcg.tpm.dao.mongo.RewardTransferAccountDAO">
        <constructor-arg name="clazz" value="com.facishare.crm.fmcg.tpm.dao.mongo.po.RewardTransferAccountPO"/>
    </bean>
</beans>
