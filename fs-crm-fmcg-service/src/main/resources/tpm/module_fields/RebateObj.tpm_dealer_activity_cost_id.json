{"describe_api_name": "RebateObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "费用核销单", "target_api_name": "TPMDealerActivityCostObj", "target_related_list_name": "target_related_list_RebateObj_TPMDealerActivityCostObj__c", "target_related_list_label": "返利单", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "tpm_dealer_activity_cost_id", "help_text": "", "status": "new"}