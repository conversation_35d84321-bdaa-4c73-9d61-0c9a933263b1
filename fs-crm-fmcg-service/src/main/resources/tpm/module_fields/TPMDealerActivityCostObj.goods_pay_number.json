{"return_type": "number", "describe_api_name": "TPMDealerActivityCostObj", "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "type": "count", "decimal_places": 0, "sub_object_describe_apiname": "TPMDealerActivityCashingProductObj", "is_required": false, "wheres": [], "define_type": "package", "is_single": false, "field_api_name": "dealer_activity_cost_id", "is_index": true, "default_result": "d_zero", "is_active": true, "is_encrypted": false, "count_type": "sum", "count_field_api_name": "activity_cashing_quantity", "label": "申请货补产品数量", "count_to_zero": false, "api_name": "goods_pay_number", "count_field_type": "number", "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}