{"describe_api_name": "SalesOrderObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "description": "", "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "wheres": [{"connector": "OR", "filters": [{"value_type": 2, "operator": "EQ", "field_name": "activity_id", "field_values": ["$activity_id$"]}, {"value_type": 2, "operator": "EQ", "field_name": "store_id", "field_values": ["$account_id$"]}]}], "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "index_name": "s_23", "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "target_api_name": "TPMActivityStoreObj", "label": "参与活动客户", "target_related_list_name": "target_related_list_SalesOrderObj_TPMActivityStoreObj__c", "target_related_list_label": "销售订单", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "activity_store_id", "is_index_field": true, "status": "new", "help_text": ""}