delete from mt_unique where tenant_id = '%s' and describe_api_name = 'TPMActivityItemObj';
update mt_relation set is_deleted = '-1' where  tenant_id = '%s' and source_api_name= 'TPMActivityItemObj';
delete from mt_data_recycle where tenant_id = '%s' and object_describe_api_name = 'TPMActivityItemObj';
update dt_team set is_deleted = '-1' where  tenant_id = '%s' and object_describe_api_name = 'TPMActivityItemObj';
update fmcg_tpm_activity_item set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMActivityItemObj';
update mt_data  set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMActivityItemObj' and exists (select id from fmcg_tpm_activity_item where  fmcg_tpm_activity_item.extend_obj_data_id = mt_data.id);
delete from mt_unique where tenant_id = '%s' and describe_api_name = 'TPMActivityItemCostStandardObj';
update mt_relation set is_deleted = '-1' where  tenant_id = '%s' and source_api_name= 'TPMActivityItemCostStandardObj';
delete from mt_data_recycle where tenant_id = '%s' and object_describe_api_name = 'TPMActivityItemCostStandardObj';
update dt_team set is_deleted = '-1' where  tenant_id = '%s' and object_describe_api_name = 'TPMActivityItemCostStandardObj';
update fmcg_tpm_activity_item_cost_standard set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMActivityItemCostStandardObj';
update mt_data  set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMActivityItemCostStandardObj' and exists (select id from fmcg_tpm_activity_item_cost_standard where  fmcg_tpm_activity_item_cost_standard.extend_obj_data_id = mt_data.id);
delete from mt_unique where tenant_id = '%s' and describe_api_name = 'TPMActivityObj';
update mt_relation set is_deleted = '-1' where  tenant_id = '%s' and source_api_name= 'TPMActivityObj';
delete from mt_data_recycle where tenant_id = '%s' and object_describe_api_name = 'TPMActivityObj';
update dt_team set is_deleted = '-1' where  tenant_id = '%s' and object_describe_api_name = 'TPMActivityObj';
update fmcg_tpm_activity set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMActivityObj';
update mt_data  set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMActivityObj' and exists (select id from fmcg_tpm_activity where  fmcg_tpm_activity.extend_obj_data_id = mt_data.id);
delete from mt_unique where tenant_id = '%s' and describe_api_name = 'TPMActivityDetailObj';
update mt_relation set is_deleted = '-1' where  tenant_id = '%s' and source_api_name= 'TPMActivityDetailObj';
delete from mt_data_recycle where tenant_id = '%s' and object_describe_api_name = 'TPMActivityDetailObj';
update dt_team set is_deleted = '-1' where  tenant_id = '%s' and object_describe_api_name = 'TPMActivityDetailObj';
update fmcg_tpm_activity_detail set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMActivityDetailObj';
update mt_data  set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMActivityDetailObj' and exists (select id from fmcg_tpm_activity_detail where  fmcg_tpm_activity_detail.extend_obj_data_id = mt_data.id);
delete from mt_unique where tenant_id = '%s' and describe_api_name = 'TPMActivityStoreObj';
update mt_relation set is_deleted = '-1' where  tenant_id = '%s' and source_api_name= 'TPMActivityStoreObj';
delete from mt_data_recycle where tenant_id = '%s' and object_describe_api_name = 'TPMActivityStoreObj';
update dt_team set is_deleted = '-1' where  tenant_id = '%s' and object_describe_api_name = 'TPMActivityStoreObj';
update fmcg_tpm_activity_store set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMActivityStoreObj';
update mt_data  set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMActivityStoreObj' and exists (select id from fmcg_tpm_activity_store where  fmcg_tpm_activity_store.extend_obj_data_id = mt_data.id);
delete from mt_unique where tenant_id = '%s' and describe_api_name = 'TPMActivityAgreementObj';
update mt_relation set is_deleted = '-1' where  tenant_id = '%s' and source_api_name= 'TPMActivityAgreementObj';
delete from mt_data_recycle where tenant_id = '%s' and object_describe_api_name = 'TPMActivityAgreementObj';
update dt_team set is_deleted = '-1' where  tenant_id = '%s' and object_describe_api_name = 'TPMActivityAgreementObj';
update fmcg_tpm_activity_agreement set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMActivityAgreementObj';
update mt_data  set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMActivityAgreementObj' and exists (select id from fmcg_tpm_activity_agreement where  fmcg_tpm_activity_agreement.extend_obj_data_id = mt_data.id);
delete from mt_unique where tenant_id = '%s' and describe_api_name = 'TPMActivityAgreementDetailObj';
update mt_relation set is_deleted = '-1' where  tenant_id = '%s' and source_api_name= 'TPMActivityAgreementDetailObj';
delete from mt_data_recycle where tenant_id = '%s' and object_describe_api_name = 'TPMActivityAgreementDetailObj';
update dt_team set is_deleted = '-1' where  tenant_id = '%s' and object_describe_api_name = 'TPMActivityAgreementDetailObj';
update fmcg_tpm_activity_agreement_detail set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMActivityAgreementDetailObj';
update mt_data  set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMActivityAgreementDetailObj' and exists (select id from fmcg_tpm_activity_agreement_detail where  fmcg_tpm_activity_agreement_detail.extend_obj_data_id = mt_data.id);
delete from mt_unique where tenant_id = '%s' and describe_api_name = 'TPMDealerActivityObj';
update mt_relation set is_deleted = '-1' where  tenant_id = '%s' and source_api_name= 'TPMDealerActivityObj';
delete from mt_data_recycle where tenant_id = '%s' and object_describe_api_name = 'TPMDealerActivityObj';
update dt_team set is_deleted = '-1' where  tenant_id = '%s' and object_describe_api_name = 'TPMDealerActivityObj';
update fmcg_tpm_dealer_activity set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMDealerActivityObj';
update mt_data  set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMDealerActivityObj' and exists (select id from fmcg_tpm_dealer_activity where  fmcg_tpm_dealer_activity.extend_obj_data_id = mt_data.id);
delete from mt_unique where tenant_id = '%s' and describe_api_name = 'TPMDealerActivityCostObj';
update mt_relation set is_deleted = '-1' where  tenant_id = '%s' and source_api_name= 'TPMDealerActivityCostObj';
delete from mt_data_recycle where tenant_id = '%s' and object_describe_api_name = 'TPMDealerActivityCostObj';
update dt_team set is_deleted = '-1' where  tenant_id = '%s' and object_describe_api_name = 'TPMDealerActivityCostObj';
update fmcg_tpm_dealer_activity_cost set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMDealerActivityCostObj';
update mt_data  set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMDealerActivityCostObj' and exists (select id from fmcg_tpm_dealer_activity_cost where  fmcg_tpm_dealer_activity_cost.extend_obj_data_id = mt_data.id);
delete from mt_unique where tenant_id = '%s' and describe_api_name = 'TPMActivityProofObj';
update mt_relation set is_deleted = '-1' where  tenant_id = '%s' and source_api_name= 'TPMActivityProofObj';
delete from mt_data_recycle where tenant_id = '%s' and object_describe_api_name = 'TPMActivityProofObj';
update dt_team set is_deleted = '-1' where  tenant_id = '%s' and object_describe_api_name = 'TPMActivityProofObj';
update fmcg_tpm_activity_proof set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMActivityProofObj';
update mt_data  set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMActivityProofObj' and exists (select id from fmcg_tpm_activity_proof where  fmcg_tpm_activity_proof.extend_obj_data_id = mt_data.id);
delete from mt_unique where tenant_id = '%s' and describe_api_name = 'TPMActivityProofDetailObj';
update mt_relation set is_deleted = '-1' where  tenant_id = '%s' and source_api_name= 'TPMActivityProofDetailObj';
delete from mt_data_recycle where tenant_id = '%s' and object_describe_api_name = 'TPMActivityProofDetailObj';
update dt_team set is_deleted = '-1' where  tenant_id = '%s' and object_describe_api_name = 'TPMActivityProofDetailObj';
update fmcg_tpm_activity_proof_detail set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMActivityProofDetailObj';
update mt_data  set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMActivityProofDetailObj' and exists (select id from fmcg_tpm_activity_proof_detail where  fmcg_tpm_activity_proof_detail.extend_obj_data_id = mt_data.id);
delete from mt_unique where tenant_id = '%s' and describe_api_name = 'TPMActivityProofAuditObj';
update mt_relation set is_deleted = '-1' where  tenant_id = '%s' and source_api_name= 'TPMActivityProofAuditObj';
delete from mt_data_recycle where tenant_id = '%s' and object_describe_api_name = 'TPMActivityProofAuditObj';
update dt_team set is_deleted = '-1' where  tenant_id = '%s' and object_describe_api_name = 'TPMActivityProofAuditObj';
update fmcg_tpm_activity_proof_audit set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMActivityProofAuditObj';
update mt_data  set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMActivityProofAuditObj' and exists (select id from fmcg_tpm_activity_proof_audit where  fmcg_tpm_activity_proof_audit.extend_obj_data_id = mt_data.id);
delete from mt_unique where tenant_id = '%s' and describe_api_name = 'TPMActivityProofAuditDetailObj';
update mt_relation set is_deleted = '-1' where  tenant_id = '%s' and source_api_name= 'TPMActivityProofAuditDetailObj';
delete from mt_data_recycle where tenant_id = '%s' and object_describe_api_name = 'TPMActivityProofAuditDetailObj';
update dt_team set is_deleted = '-1' where  tenant_id = '%s' and object_describe_api_name = 'TPMActivityProofAuditDetailObj';
update fmcg_tpm_activity_proof_audit_detail set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMActivityProofAuditDetailObj';
update mt_data  set is_deleted = '-1' where tenant_id = '%s' and object_describe_api_name = 'TPMActivityProofAuditDetailObj' and exists (select id from fmcg_tpm_activity_proof_audit_detail where  fmcg_tpm_activity_proof_audit_detail.extend_obj_data_id = mt_data.id);