{"description": "", "enabled_change_order": false, "index_version": 1, "is_deleted": false, "define_type": "custom", "release_version": "6.4", "package": "CRM", "is_active": true, "display_name": "激励异常记录", "version": 6, "is_open_display_name": false, "icon_index": 0, "api_name": "RewardExceptionRecordObj__c", "icon_path": "", "is_udef": true, "short_name": "DDG", "fields": {"tenant_id": {"describe_api_name": "RewardExceptionRecordObj__c", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "index_name": "ei", "max_length": 200, "status": "released"}, "lock_rule": {"describe_api_name": "RewardExceptionRecordObj__c", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定规则", "is_unique": false, "rules": [], "default_value": "default_lock_rule", "label": "锁定规则", "type": "lock_rule", "field_num": 1, "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "label_r": "锁定规则", "is_index_field": false, "index_name": "s_0", "status": "new"}, "data_own_organization": {"describe_api_name": "RewardExceptionRecordObj__c", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "label": "归属组织", "type": "department", "is_need_convert": false, "is_required": false, "api_name": "data_own_organization", "define_type": "package", "is_single": true, "label_r": "归属组织", "is_index_field": false, "index_name": "a_0", "status": "released"}, "status__c": {"describe_api_name": "RewardExceptionRecordObj__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"font_color": "#ff522a", "label": "待处理", "value": "1"}, {"font_color": "#30c776", "label": "已处理", "value": "2"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "s_3", "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "异常状态", "field_num": 6, "api_name": "status__c", "is_index_field": false, "help_text": "", "status": "new"}, "origin_source": {"describe_api_name": "RewardExceptionRecordObj__c", "is_index": true, "is_active": true, "is_unique": false, "label": "数据来源", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "origin_source", "options": [{"label": "数据同步", "value": "0"}], "define_type": "system", "is_extend": false, "index_name": "s_os", "status": "released"}, "lock_user": {"describe_api_name": "RewardExceptionRecordObj__c", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "field_num": 5, "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "label_r": "加锁人", "is_index_field": false, "index_name": "a_1", "status": "new"}, "is_deleted": {"describe_api_name": "RewardExceptionRecordObj__c", "is_index": false, "is_unique": false, "description": "is_deleted", "default_value": false, "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "options": [], "define_type": "system", "index_name": "is_del", "status": "released"}, "life_status_before_invalid": {"describe_api_name": "RewardExceptionRecordObj__c", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "field_num": 4, "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_single": false, "label_r": "作废前生命状态", "is_index_field": false, "index_name": "t_0", "max_length": 256, "status": "new"}, "object_describe_api_name": {"describe_api_name": "RewardExceptionRecordObj__c", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "index_name": "api_name", "max_length": 200, "status": "released"}, "owner_department": {"describe_api_name": "RewardExceptionRecordObj__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "label_r": "负责人主属部门", "index_name": "owner_dept", "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "out_owner": {"describe_api_name": "RewardExceptionRecordObj__c", "is_index": true, "is_active": true, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "index_name": "o_owner", "status": "released"}, "error_detail_message__c": {"describe_api_name": "RewardExceptionRecordObj__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "t_2", "max_length": 2000, "is_index": true, "is_active": true, "is_encrypted": false, "min_length": 0, "default_value": "", "label": "异常详情", "field_num": 8, "api_name": "error_detail_message__c", "is_index_field": false, "help_text": "", "status": "new"}, "owner": {"describe_api_name": "RewardExceptionRecordObj__c", "default_is_expression": false, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "default_value": "", "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "wheres": [], "api_name": "owner", "define_type": "package", "is_single": true, "label_r": "负责人", "is_index_field": false, "index_name": "owner", "help_text": "", "status": "new"}, "biz_type__c": {"describe_api_name": "RewardExceptionRecordObj__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": false, "index_name": "t_3", "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "所属激励类型", "field_num": 9, "api_name": "biz_type__c", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "lock_status": {"describe_api_name": "RewardExceptionRecordObj__c", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "field_num": 2, "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "label_r": "锁定状态", "is_index_field": false, "index_name": "s_1", "status": "new"}, "package": {"describe_api_name": "RewardExceptionRecordObj__c", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "index_name": "pkg", "max_length": 200, "status": "released"}, "last_modified_time": {"describe_api_name": "RewardExceptionRecordObj__c", "is_index": true, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "md_time", "status": "released"}, "create_time": {"describe_api_name": "RewardExceptionRecordObj__c", "is_index": true, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "crt_time", "status": "released"}, "life_status": {"describe_api_name": "RewardExceptionRecordObj__c", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "label_r": "生命状态", "index_name": "s_2", "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "normal", "label": "生命状态", "field_num": 3, "is_need_convert": false, "api_name": "life_status", "is_index_field": false, "help_text": "", "status": "new"}, "last_modified_by": {"describe_api_name": "RewardExceptionRecordObj__c", "is_index": true, "is_active": true, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "index_name": "md_by", "status": "released"}, "out_tenant_id": {"describe_api_name": "RewardExceptionRecordObj__c", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "index_name": "o_ei", "max_length": 200, "status": "released"}, "version": {"describe_api_name": "RewardExceptionRecordObj__c", "is_index": false, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "index_name": "version", "round_mode": 4, "status": "released"}, "created_by": {"describe_api_name": "RewardExceptionRecordObj__c", "is_index": true, "is_active": true, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "index_name": "crt_by", "status": "released"}, "record_type": {"describe_api_name": "RewardExceptionRecordObj__c", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_single": false, "label_r": "业务类型", "is_index_field": false, "index_name": "r_type", "help_text": "", "status": "released"}, "relevant_team": {"describe_api_name": "RewardExceptionRecordObj__c", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "label_r": "相关团队", "is_index_field": false, "index_name": "a_team", "help_text": "相关团队", "status": "new"}, "data_own_department": {"describe_api_name": "RewardExceptionRecordObj__c", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "type": "department", "is_required": false, "wheres": [], "optional_type": "department", "define_type": "package", "is_single": true, "label_r": "归属部门", "index_name": "data_owner_dept_id", "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "归属部门", "is_need_convert": false, "api_name": "data_own_department", "is_index_field": false, "help_text": "", "status": "new"}, "name": {"describe_api_name": "RewardExceptionRecordObj__c", "default_is_expression": false, "prefix": "{yyyy}-{mm}-{dd}-", "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": true, "start_number": 1, "type": "auto_number", "default_to_zero": false, "is_required": true, "define_type": "system", "postfix": "", "input_mode": "", "is_single": false, "label_r": "主属性", "index_name": "name", "max_length": 100, "is_index": true, "is_active": true, "auto_number_type": "normal", "is_encrypted": false, "serial_number": 4, "default_value": "{yyyy}-{mm}-{dd}-0001", "label": "编号", "condition": "NONE", "api_name": "name", "func_api_name": "", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "order_by": {"describe_api_name": "RewardExceptionRecordObj__c", "is_index": false, "length": 8, "is_unique": false, "description": "order_by", "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "index_name": "l_by", "round_mode": 4, "status": "released"}, "_id": {"describe_api_name": "RewardExceptionRecordObj__c", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "_id", "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "index_name": "_id", "max_length": 200, "status": "released"}, "biz_code_id__c": {"describe_api_name": "RewardExceptionRecordObj__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "is_unique": true, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": false, "index_name": "t_1", "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "业务编号", "field_num": 7, "api_name": "biz_code_id__c", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}}}