{"components": [{"field_section": [], "buttons": [{"action_type": "default", "api_name": "Edit_button_default", "label": "编辑"}, {"action_type": "default", "api_name": "Dial_button_default", "label": "打电话"}, {"action_type": "default", "api_name": "ChangeOwner_button_default", "label": "更换负责人"}, {"action_type": "default", "api_name": "StartBPM_button_default", "label": "发起业务流程"}, {"action_type": "default", "api_name": "Abolish_button_default", "label": "作废"}, {"action_type": "default", "api_name": "StartStagePropellor_button_default", "label": "发起阶段推进器"}, {"action_type": "default", "api_name": "Lock_button_default", "label": "锁定"}, {"action_type": "default", "api_name": "Unlock_button_default", "label": "解锁"}, {"action_type": "default", "api_name": "Clone_button_default", "label": "复制"}, {"action_type": "default", "api_name": "ChangePartner_button_default", "label": "更换合作伙伴"}, {"action_type": "default", "api_name": "ChangePartnerOwner_button_default", "label": "更换外部负责人"}, {"action_type": "default", "api_name": "DeletePartner_button_default", "label": "移除合作伙伴"}, {"action_type": "default", "api_name": "SendMail_button_default"}, {"action_type": "default", "api_name": "Discuss_button_default"}, {"action_type": "default", "api_name": "Remind_button_default"}, {"action_type": "default", "api_name": "Schedule_button_default"}, {"action_type": "default", "api_name": "Print_button_default"}, {"action_type": "system", "api_name": "Unfollow_button_default", "action": "Unfollow", "label": "取消关注"}, {"action_type": "system", "api_name": "Follow_button_default", "action": "Follow", "label": "关注"}], "api_name": "head_info", "related_list_name": "", "header": "标题和按钮", "nameI18nKey": "paas.udobj.head_info", "exposedButton": 3, "type": "simple", "isSticky": false, "grayLimit": 1}, {"field_section": [{"field_name": "owner"}, {"field_name": "owner_department"}, {"field_name": "last_modified_time"}, {"field_name": "record_type"}], "buttons": [], "api_name": "top_info", "related_list_name": "", "header": "摘要信息", "nameI18nKey": "paas.udobj.summary_info", "type": "top_info", "isSticky": false, "grayLimit": 1}, {"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "is_required": true, "render_type": "auto_number", "field_name": "name"}, {"is_readonly": false, "is_required": false, "is_tiled": true, "render_type": "select_one", "field_name": "status__c"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "biz_code_id__c"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "biz_type__c"}, {"is_readonly": false, "full_line": true, "is_required": false, "render_type": "long_text", "field_name": "error_detail_message__c"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息", "collapse": false}, {"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "life_status"}, {"is_readonly": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}], "api_name": "group_9o2e0__c", "tab_index": "ltr", "column": 2, "header": "系统信息", "collapse": true}], "buttons": [], "api_name": "form_component", "related_list_name": "", "column": 2, "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "type": "form", "isSticky": false, "grayLimit": 1}, {"field_section": [], "buttons": [], "api_name": "bpm_component", "related_list_name": "", "header": "业务流组件", "nameI18nKey": "paas.udobj.bpm_component", "type": "bpm_component", "isSticky": false, "grayLimit": 1}, {"field_section": [], "buttons": [], "api_name": "stage_component", "related_list_name": "", "header": "阶段推进器组件", "nameI18nKey": "paas.udobj.stage_component", "type": "stage_component", "isSticky": false, "grayLimit": 1}, {"field_section": [], "buttons": [], "api_name": "approval_component", "related_list_name": "", "header": "审批流组件", "nameI18nKey": "paas.udobj.approval_component", "type": "approval_component", "isSticky": false, "grayLimit": 1}, {"components": [["form_component"], ["BPM_related_list"], ["Approval_related_list"]], "buttons": [], "api_name": "tabs_v41YY__c", "tabs": [{"api_name": "form_component_92jg2__c", "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info"}, {"api_name": "BPM_related_list_1NQv5__c", "header": "流程列表", "nameI18nKey": "paas.udobj.process_list"}, {"api_name": "Approval_related_list_ibiwv__c", "header": "审批流程", "nameI18nKey": "paas.udobj.approvalflow"}], "header": "页签容器", "type": "tabs", "isSticky": false}, {"field_section": [], "buttons": [], "api_name": "BPM_related_list", "related_list_name": "", "ref_object_api_name": "BPM", "header": "流程列表", "nameI18nKey": "paas.udobj.process_list", "type": "relatedlist", "grayLimit": 1}, {"field_section": [], "buttons": [], "api_name": "Approval_related_list", "related_list_name": "", "ref_object_api_name": "Approval", "header": "审批流程", "nameI18nKey": "paas.udobj.approvalflow", "type": "relatedlist", "grayLimit": 1}], "ref_object_api_name": "RewardExceptionRecordObj__c", "layout_type": "detail", "hidden_buttons": [], "ui_event_ids": [], "is_deleted": false, "default_component": "form_component", "layout_structure": {"layout": [{"components": [["head_info"]], "columns": [{"width": "100%"}]}, {"components": [["stage_component", "bpm_component", "top_info", "tabs_v41YY__c"], ["approval_component"]], "columns": [{"width": "auto"}, {"width": "500px", "retractable": true}]}]}, "buttons": [], "package": "CRM", "enable_mobile_layout": false, "display_name": "默认布局", "is_default": true, "hidden_components": ["payment_recordrelated_list_generate_by_UDObjectServer__c", "relevant_team_component", "sale_log", "operation_log", "biDashboardCom"], "version": 7, "api_name": "layout_e7q8M__c", "layout_description": ""}