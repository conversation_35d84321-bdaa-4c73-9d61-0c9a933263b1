{"api_name": "edit_layout_promotion_TPMActivityUnifiedCaseObj__c", "display_name": "促销类活动方案(新建/编辑页)", "layout_description": "", "ref_object_api_name": "TPMActivityUnifiedCaseObj", "is_default": false, "is_deleted": false, "layout_structure": {"layout": [{"components": [["head_info"]], "columns": [{"width": "100%"}]}, {"components": [["form_component", "tabs_component"]], "columns": [{"width": "100%"}]}]}, "package": "CRM", "version": 38, "layout_type": "edit", "buttons": [], "default_component": "form_component", "hidden_buttons": [], "hidden_components": ["TPMActivityCashingProductScopeObj_md_group_component", "TPMActivityCashingProductObj_md_group_component"], "events": [], "components": [{"field_section": [{"show_header": true, "form_fields": [{"is_readonly": true, "is_required": false, "render_type": "auto_number", "field_name": "activity_code"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "activity_type"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "name"}, {"is_readonly": false, "is_required": false, "render_type": "date", "field_name": "start_date"}, {"is_readonly": false, "is_required": false, "render_type": "date", "field_name": "end_date"}, {"is_readonly": true, "is_required": false, "render_type": "formula", "field_name": "activity_days"}, {"is_readonly": false, "is_required": false, "render_type": "department_many", "field_name": "activity_department_range"}, {"is_readonly": false, "is_required": false, "full_line": true, "render_type": "long_text", "field_name": "activity_desc"}, {"is_readonly": false, "is_required": true, "is_tiled": true, "render_type": "select_one", "field_name": "write_off_cash"}, {"is_readonly": false, "is_required": false, "render_type": "file_attachment", "field_name": "enclosure"}, {"is_readonly": true, "is_required": false, "render_type": "select_one", "field_name": "source_object_api_name"}, {"is_readonly": true, "is_required": false, "render_type": "select_one", "field_name": "mode_type"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息"}, {"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "currency", "field_name": "activity_amount"}, {"is_readonly": true, "is_required": false, "render_type": "currency", "field_name": "occupy_amount"}, {"is_readonly": true, "is_required": false, "render_type": "currency", "field_name": "available_amount"}, {"is_readonly": true, "is_required": false, "render_type": "currency", "field_name": "unified_total_policy_dynamic_amount"}], "api_name": "group_t2QmF__c", "tab_index": "ltr", "column": 2, "header": "方案费用信息"}, {"show_header": true, "form_fields": [{"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}], "api_name": "group_148j3__c", "tab_index": "ltr", "column": 2, "header": "系统信息"}], "buttons": [], "api_name": "form_component", "related_list_name": "", "header": "表单组件", "type": "form", "grayLimit": 1, "column": 2, "nameI18nKey": "paas.udobj.form_component", "order": 4, "isSticky": false}, {"components": [["TPMActivityDealerScopeObj_md_group_component"]], "buttons": [], "api_name": "tabs_component", "tabs": [{"api_name": "tab_TPMActivityDealerScopeObj_md_group_component", "header": "参与方案经销商范围", "nameI18nKey": "TPMActivityDealerScopeObj.field.activity_unified_case_id.reference_label"}], "header": "页签容器", "type": "tabs", "order": 5, "isSticky": false}, {"field_section": [], "buttons": [], "related_list_name": "", "button_info": [{"hidden": [], "page_type": "create", "render_type": "normal", "order": ["Add_Save_button_default", "Add_Save_Continue_button_default", "Add_Save_Draft_button_default"]}, {"hidden": [], "page_type": "edit", "render_type": "normal", "order": ["Edit_Save_button_default"]}], "type": "simple", "api_name": "head_info", "header": "标题和按钮", "grayLimit": 1, "nameI18nKey": "paas.udobj.head_info", "order": 3, "isSticky": false}, {"buttons": [], "related_list_name": "target_related_list_TPMActivityDealerScopeObj_TPMActivityUnifiedCaseObj__c", "ref_object_api_name": "TPMActivityDealerScopeObj", "button_info": [{"hidden": [], "render_type": "list_normal", "order": ["Single_Add_button_default", "Batch_Lookup_Add_button_dealer_id"]}, {"hidden": [], "render_type": "list_batch", "order": ["Batch_Edit_button_default", "Delete_button_default", "Clone_button_default"]}, {"hidden": [], "render_type": "list_single", "order": ["Delete_button_default", "Clone_button_default"]}], "type": "multi_table", "api_name": "TPMActivityDealerScopeObj_md_group_component", "child_components": [], "header": "参与方案经销商范围", "render_type": "card", "field_api_name": "activity_unified_case_id", "nameI18nKey": "TPMActivityDealerScopeObj.field.activity_unified_case_id.reference_label", "order": 6, "isSticky": false}], "enable_mobile_layout": false}