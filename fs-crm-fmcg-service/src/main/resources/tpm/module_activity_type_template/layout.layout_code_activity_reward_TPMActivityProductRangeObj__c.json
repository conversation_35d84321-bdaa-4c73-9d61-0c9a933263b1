{"components": [{"field_section": [], "buttons": [{"action_type": "default", "api_name": "SaleRecord_button_default", "label": "销售记录"}, {"action_type": "default", "api_name": "Dial_button_default", "label": "打电话"}, {"action_type": "default", "api_name": "StartBPM_button_default", "label": "发起业务流程"}, {"action_type": "default", "api_name": "SendMail_button_default"}, {"action_type": "default", "api_name": "Discuss_button_default"}, {"action_type": "default", "api_name": "Remind_button_default"}, {"action_type": "default", "api_name": "Schedule_button_default"}, {"action_type": "default", "api_name": "Print_button_default"}], "api_name": "head_info", "related_list_name": "", "header": "标题和按钮", "nameI18nKey": "paas.udobj.head_info", "exposedButton": 3, "type": "simple", "isSticky": false, "grayLimit": 1}, {"field_section": [{"field_name": "owner"}, {"field_name": "owner_department"}, {"field_name": "last_modified_time"}, {"field_name": "record_type"}], "buttons": [], "api_name": "top_info", "related_list_name": "", "header": "摘要信息", "nameI18nKey": "paas.udobj.summary_info", "type": "top_info", "isSticky": false, "grayLimit": 1}, {"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "auto_number", "field_name": "name"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "product_id"}, {"is_readonly": false, "is_required": false, "render_type": "master_detail", "field_name": "activity_id"}, {"is_readonly": false, "is_required": false, "is_tiled": true, "render_type": "select_one", "field_name": "match_method"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "to_be_expired_days"}, {"is_readonly": false, "is_required": false, "render_type": "date", "field_name": "manufacture_date_start"}, {"is_readonly": false, "is_required": false, "render_type": "date", "field_name": "manufacture_date_end"}, {"is_readonly": false, "is_required": false, "render_type": "record_type", "field_name": "record_type"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息", "collapse": false}, {"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "life_status"}], "api_name": "group_MS379__c", "tab_index": "ltr", "column": 2, "header": "系统信息", "collapse": true}], "buttons": [], "api_name": "form_component", "related_list_name": "", "column": 2, "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "type": "form", "isSticky": false, "grayLimit": 1}, {"field_section": [], "buttons": [], "api_name": "operation_log", "related_list_name": "", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log", "type": "related_record", "grayLimit": 1}, {"field_section": [], "buttons": [], "api_name": "bpm_component", "related_list_name": "", "header": "业务流组件", "nameI18nKey": "paas.udobj.bpm_component", "type": "bpm_component", "isSticky": false, "grayLimit": 1}, {"field_section": [], "buttons": [], "api_name": "stage_component", "related_list_name": "", "header": "阶段推进器组件", "nameI18nKey": "paas.udobj.stage_component", "type": "stage_component", "isSticky": false, "grayLimit": 1}, {"field_section": [], "buttons": [], "api_name": "approval_component", "related_list_name": "", "header": "审批流组件", "nameI18nKey": "paas.udobj.approval_component", "type": "approval_component", "isSticky": false, "grayLimit": 1}, {"components": [["form_component"], ["operation_log"]], "buttons": [], "api_name": "tabs_Y17bp__c", "tabs": [{"api_name": "form_component_gnyZl__c", "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info"}, {"api_name": "operation_log_rYLuw__c", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log"}], "header": "页签容器", "type": "tabs", "isSticky": false}], "ref_object_api_name": "TPMActivityProductRangeObj", "layout_type": "detail", "hidden_buttons": [], "ui_event_ids": [], "is_deleted": false, "default_component": "form_component", "layout_structure": {"layout": [{"components": [["head_info"]], "columns": [{"width": "100%"}]}, {"components": [["approval_component", "stage_component", "bpm_component", "top_info", "tabs_Y17bp__c"]], "columns": [{"width": "auto"}]}], "layout_structure_type": 1}, "buttons": [], "package": "CRM", "display_name": "码引擎激励", "is_default": false, "version": 6, "api_name": "layout_code_activity_reward_TPMActivityProductRangeObj__c", "layout_description": ""}