{"template_groups": [{"_id": "display", "name": "${陈列/铺货类}", "description": "<div>${默认支持<span>「品项范围」</span>及<span>「陈列标准」</span>的定义及控制}</div>", "templates": [{"_id": "display.simple", "name": "${极简-陈列/铺货类活动}", "template_name": "${极简}", "description": "", "activity_node_list": [{"type": "plan", "object_record_type": "display__c", "activity_plan_config": {"enable_activity_cycle_control": true, "enable_plan_template": false, "account_scope_config": {"account_scope_api_name": "TPMActivityStoreObj", "ref_account_field_api_name": "store_id", "ref_master_field_api_name": "activity_id"}}, "__object_record_type_name": "${陈列/铺货类申请单}", "__object_layout_api_name": "layout_display_TPMActivityObj__c", "__object_edit_layout_api_name": "edit_layout_display_TPMActivityObj__c", "__detail_objects": [{"__object_api_name": "TPMActivityProductRangeObj", "__object_record_type_api_name": "display__c", "__object_record_type_name": "${陈列/铺货类品项范围}", "__object_layout_api_name": "layout_display_TPMActivityProductRangeObj__c"}]}, {"type": "write_off", "object_record_type": "default__c", "activity_write_off_config": {"enable_over_write_off": false, "charge_up_config": {"charge_up_account_id": "", "charge_up_account_name": "", "charge_up_account_status": false}, "write_off_source_config": {"calculate_type": "activity", "api_name": "", "reference_write_off_field_api_name": "", "dealer_field_api_name": "", "account_field_api_name": "", "cost_field_api_name": ""}}}]}, {"_id": "display.standard", "name": "${标准-陈列/铺货类活动}", "template_name": "${标准}", "description": "", "activity_node_list": [{"type": "plan", "object_record_type": "display__c", "activity_plan_config": {"enable_activity_cycle_control": true, "enable_plan_template": false, "account_scope_config": {"account_scope_api_name": "TPMActivityStoreObj", "ref_account_field_api_name": "store_id", "ref_master_field_api_name": "activity_id"}}, "__object_record_type_name": "${陈列/铺货类申请单}", "__object_layout_api_name": "layout_display_TPMActivityObj__c", "__object_edit_layout_api_name": "edit_layout_display_TPMActivityObj__c", "__detail_objects": [{"__object_api_name": "TPMActivityProductRangeObj", "__object_record_type_api_name": "display__c", "__object_record_type_name": "${陈列/铺货类品项范围}", "__object_layout_api_name": "layout_display_TPMActivityProductRangeObj__c"}]}, {"type": "proof", "object_record_type": "default__c", "activity_proof_config": {"cost_calculate_config": {"calculate_type": "default", "ratio": "1"}, "frequency_config": {"frequency_limit": "0", "frequency_type": "activity", "limit_days": [], "limit_hours": []}}}, {"type": "audit", "object_record_type": "default__c", "activity_proof_audit_config": {"audit_source_config": {"use_complex_mode": true, "master_api_name": "TPMActivityProofObj", "master_record_type": "default__c", "reference_audit_source_field_api_name": "activity_proof_id", "reference_audit_source_detail_field_api_name": "activity_proof_detail_id", "reference_activity_field_api_name": "activity_id", "detail_api_name": "TPMActivityProofDetailObj", "master_detail_field_api_name": "activity_proof_id", "dealer_field_api_name": "dealer_id", "account_field_api_name": "store_id", "cost_field_api_name": "actual_total", "audit_status_api_name": "audit_status", "display_field_api_names_of_master": ["proof_images", "actual_total"], "display_field_api_names_of_detail": ["proof_item", "proof_detail_cost_standard", "amount", "subtotal"], "display_field_api_names_of_audit_master": ["audit_images", "audit_total", "opinion"], "display_field_api_names_of_audit_detail": ["audit_amount", "audit_subtotal"], "activity_item_field_api_name": ""}, "audit_mode_config": {"audit_mode": "all"}}}, {"type": "write_off", "object_record_type": "default__c", "activity_write_off_config": {"write_off_source_config": {"calculate_type": "store_cost_total", "api_name": "TPMActivityProofAuditObj", "record_type": "default__c", "reference_write_off_field_api_name": "dealer_activity_cost_id", "dealer_field_api_name": "dealer_id", "reference_activity_field_api_name": "activity_id", "account_field_api_name": "store_id", "cost_field_api_name": "audit_total", "display_field_api_names": ["activity_proof_id", "proof_images", "total", "audit_status", "audit_total"]}, "charge_up_config": {"charge_up_account_id": "", "charge_up_account_name": "", "charge_up_account_status": false}, "enable_over_write_off": false}}]}, {"_id": "display.complex", "name": "${精细-陈列/铺货类活动}", "template_name": "${精细}", "description": "", "activity_node_list": [{"type": "plan", "object_record_type": "display__c", "activity_plan_config": {"enable_activity_cycle_control": true, "enable_plan_template": false, "account_scope_config": {"account_scope_api_name": "TPMActivityStoreObj", "ref_account_field_api_name": "store_id", "ref_master_field_api_name": "activity_id"}}, "__object_record_type_name": "${陈列/铺货类申请单}", "__object_layout_api_name": "layout_display_TPMActivityObj__c", "__object_edit_layout_api_name": "edit_layout_display_TPMActivityObj__c", "__detail_objects": [{"__object_api_name": "TPMActivityProductRangeObj", "__object_record_type_api_name": "display__c", "__object_record_type_name": "${陈列/铺货类品项范围}", "__object_layout_api_name": "layout_display_TPMActivityProductRangeObj__c"}]}, {"type": "agreement", "object_record_type": "default__c", "activity_agreement_config": {"signing_mode": "normal", "begin_date_setting": {"type": "before_begin_date", "value": 0}, "end_date_setting": {"type": "after_end_date", "value": 0}}}, {"type": "proof", "object_record_type": "default__c", "activity_proof_config": {"cost_calculate_config": {"calculate_type": "default", "ratio": "1"}, "frequency_config": {"frequency_limit": "0", "frequency_type": "activity", "limit_days": [], "limit_hours": []}}}, {"type": "audit", "object_record_type": "default__c", "activity_proof_audit_config": {"audit_source_config": {"use_complex_mode": true, "master_api_name": "TPMActivityProofObj", "master_record_type": "default__c", "reference_audit_source_field_api_name": "activity_proof_id", "reference_audit_source_detail_field_api_name": "activity_proof_detail_id", "reference_activity_field_api_name": "activity_id", "detail_api_name": "TPMActivityProofDetailObj", "master_detail_field_api_name": "activity_proof_id", "dealer_field_api_name": "dealer_id", "account_field_api_name": "store_id", "cost_field_api_name": "actual_total", "audit_status_api_name": "audit_status", "display_field_api_names_of_master": ["proof_images", "actual_total"], "display_field_api_names_of_detail": ["proof_item", "proof_detail_cost_standard", "amount", "subtotal"], "display_field_api_names_of_audit_master": ["audit_images", "audit_total", "opinion"], "display_field_api_names_of_audit_detail": ["audit_amount", "audit_subtotal"], "activity_item_field_api_name": ""}, "audit_mode_config": {"audit_mode": "all"}}}, {"type": "write_off", "object_record_type": "default__c", "activity_write_off_config": {"write_off_source_config": {"calculate_type": "store_cost_total", "api_name": "TPMActivityProofAuditObj", "record_type": "default__c", "reference_write_off_field_api_name": "dealer_activity_cost_id", "dealer_field_api_name": "dealer_id", "reference_activity_field_api_name": "activity_id", "account_field_api_name": "store_id", "cost_field_api_name": "audit_total", "display_field_api_names": ["activity_proof_id", "proof_images", "total", "audit_status", "audit_total"]}, "charge_up_config": {"charge_up_account_id": "", "charge_up_account_name": "", "charge_up_account_status": false}, "enable_over_write_off": false}}]}]}, {"_id": "tasting", "name": "${品鉴类}", "description": "<div>${默认支持<span>「用酒范围」</span>及<span>「品鉴场地」</span>的定义及控制}</div>", "templates": [{"_id": "tasting.standard", "name": "${标准-品鉴类活动}", "template_name": "${标准}", "description": "", "activity_node_list": [{"type": "plan", "object_record_type": "tasting__c", "activity_plan_config": {"enable_activity_cycle_control": true, "enable_plan_template": false, "account_scope_config": {"account_scope_api_name": "TPMActivityStoreObj", "ref_account_field_api_name": "store_id", "ref_master_field_api_name": "activity_id"}}, "__object_record_type_name": "${品鉴类申请单}", "__object_layout_api_name": "layout_tasting_TPMActivityObj__c", "__object_edit_layout_api_name": "edit_layout_tasting_TPMActivityObj__c", "__detail_objects": [{"__object_api_name": "TPMActivityProductRangeObj", "__object_record_type_api_name": "tasting__c", "__object_record_type_name": "${品鉴类品项范围}", "__object_layout_api_name": "layout_tasting_TPMActivityProductRangeObj__c"}]}, {"type": "proof", "object_record_type": "default__c", "activity_proof_config": {"cost_calculate_config": {"calculate_type": "default", "ratio": "1"}, "frequency_config": {"frequency_limit": "0", "frequency_type": "activity", "limit_days": [], "limit_hours": []}}}, {"type": "write_off", "object_record_type": "default__c", "package": "system", "activity_write_off_config": {"charge_up_config": {"charge_up_account_id": "", "charge_up_account_name": "", "charge_up_account_status": false}, "enable_over_write_off": false, "write_off_source_config": {"account_field_api_name": "store_id", "api_name": "TPMActivityProofObj", "calculate_type": "store_cost_total", "cost_field_api_name": "actual_total", "dealer_field_api_name": "dealer_id", "display_field_api_names": ["proof_images", "total", "audit_status"], "record_type": "default__c", "reference_activity_field_api_name": "activity_id", "reference_write_off_field_api_name": "dealer_activity_cost_id"}}}]}, {"_id": "tasting.complex", "name": "${精细-品鉴类活动}", "template_name": "${精细}", "description": "", "activity_node_list": [{"type": "plan", "object_record_type": "tasting__c", "activity_plan_config": {"enable_activity_cycle_control": true, "enable_plan_template": false, "account_scope_config": {"account_scope_api_name": "TPMActivityStoreObj", "ref_account_field_api_name": "store_id", "ref_master_field_api_name": "activity_id"}}, "__object_record_type_name": "${品鉴类申请单}", "__object_layout_api_name": "layout_tasting_TPMActivityObj__c", "__object_edit_layout_api_name": "edit_layout_tasting_TPMActivityObj__c", "__detail_objects": [{"__object_api_name": "TPMActivityProductRangeObj", "__object_record_type_api_name": "tasting__c", "__object_record_type_name": "${品鉴类品项范围}", "__object_layout_api_name": "layout_tasting_TPMActivityProductRangeObj__c"}]}, {"type": "proof", "object_record_type": "default__c", "activity_proof_config": {"cost_calculate_config": {"calculate_type": "default", "ratio": "1"}, "frequency_config": {"frequency_limit": "0", "frequency_type": "activity", "limit_days": [], "limit_hours": []}}}, {"type": "audit", "object_record_type": "default__c", "activity_proof_audit_config": {"audit_source_config": {"use_complex_mode": true, "master_api_name": "TPMActivityProofObj", "master_record_type": "default__c", "reference_audit_source_field_api_name": "activity_proof_id", "reference_audit_source_detail_field_api_name": "activity_proof_detail_id", "reference_activity_field_api_name": "activity_id", "detail_api_name": "TPMActivityProofDetailObj", "master_detail_field_api_name": "activity_proof_id", "dealer_field_api_name": "dealer_id", "account_field_api_name": "store_id", "cost_field_api_name": "actual_total", "audit_status_api_name": "audit_status", "display_field_api_names_of_master": ["proof_images", "actual_total"], "display_field_api_names_of_detail": ["proof_item", "proof_detail_cost_standard", "amount", "subtotal"], "display_field_api_names_of_audit_master": ["audit_images", "audit_total", "opinion"], "display_field_api_names_of_audit_detail": ["audit_amount", "audit_subtotal"], "activity_item_field_api_name": ""}, "audit_mode_config": {"audit_mode": "all"}}}, {"type": "write_off", "object_record_type": "default__c", "activity_write_off_config": {"write_off_source_config": {"calculate_type": "store_cost_total", "api_name": "TPMActivityProofAuditObj", "record_type": "default__c", "reference_write_off_field_api_name": "dealer_activity_cost_id", "dealer_field_api_name": "dealer_id", "reference_activity_field_api_name": "activity_id", "account_field_api_name": "store_id", "cost_field_api_name": "audit_total", "display_field_api_names": ["activity_proof_id", "proof_images", "total", "audit_status", "audit_total"]}, "charge_up_config": {"charge_up_account_id": "", "charge_up_account_name": "", "charge_up_account_status": false}, "enable_over_write_off": false}}]}]}, {"_id": "promotion", "name": "${渠道促销类}", "description": "<div>${活动支持定义<span>「促销规则」</span>，与<span>「销售订单」</span>打通，完成促销活动闭环}</div>", "templates": [{"_id": "promotion.simple", "name": "${极简-渠道促销类活动}", "template_name": "${极简}", "description": "", "activity_node_list": [{"type": "plan", "object_record_type": "promotion__c", "activity_plan_config": {"enable_activity_cycle_control": true, "enable_plan_template": false, "account_scope_config": {"account_scope_api_name": "TPMActivityStoreObj", "ref_account_field_api_name": "store_id", "ref_master_field_api_name": "activity_id"}}, "__object_record_type_name": "${渠道促销类申请单}", "__object_layout_api_name": "layout_promotion_TPMActivityObj__c", "__object_edit_layout_api_name": "edit_layout_promotion_TPMActivityObj__c", "__detail_objects": []}]}, {"_id": "promotion.standard", "name": "${标准-渠道促销类活动}", "template_name": "${标准}", "description": "", "activity_node_list": [{"type": "plan_template", "object_record_type": "promotion__c", "__object_record_type_name": "${渠道促销类活动方案}", "__object_layout_api_name": "layout_promotion_TPMActivityUnifiedCaseObj__c", "__object_edit_layout_api_name": "edit_layout_promotion_TPMActivityUnifiedCaseObj__c", "__detail_objects": []}, {"type": "plan", "object_record_type": "promotion__c", "activity_plan_config": {"enable_activity_cycle_control": true, "enable_plan_template": false, "account_scope_config": {"account_scope_api_name": "TPMActivityStoreObj", "ref_account_field_api_name": "store_id", "ref_master_field_api_name": "activity_id"}}, "__object_record_type_name": "${渠道促销类申请单}", "__object_layout_api_name": "layout_promotion_TPMActivityObj__c", "__object_edit_layout_api_name": "edit_layout_promotion_TPMActivityObj__c", "__detail_objects": []}]}]}, {"_id": "online", "name": "${直播/电商类}", "description": "<div>${极简活动费用管理}</div>", "templates": [{"_id": "online.simple", "name": "${极简-直播/电商类活动}", "template_name": "${极简}", "description": "", "activity_node_list": [{"type": "plan", "object_record_type": "online__c", "activity_plan_config": {"enable_activity_cycle_control": true, "enable_plan_template": false, "account_scope_config": {"account_scope_api_name": "TPMActivityStoreObj", "ref_account_field_api_name": "store_id", "ref_master_field_api_name": "activity_id"}}, "__object_record_type_name": "${直播/电商类申请单}", "__object_layout_api_name": "layout_online_TPMActivityObj__c", "__object_edit_layout_api_name": "edit_layout_online_TPMActivityObj__c"}, {"type": "write_off", "object_record_type": "default__c", "activity_write_off_config": {"enable_over_write_off": false, "charge_up_config": {"charge_up_account_id": "", "charge_up_account_name": "", "charge_up_account_status": false}, "write_off_source_config": {"calculate_type": "activity", "api_name": "", "reference_write_off_field_api_name": "", "dealer_field_api_name": "", "account_field_api_name": "", "cost_field_api_name": ""}}}]}]}, {"_id": "reward", "name": "${营销激励类}", "description": "<div>${默认活动支持定义<span>「激励策略」</span>，与<span>「一物一码」</span>能力结合，完成码营销活动闭环}</div>", "templates": [{"_id": "reward.scan_code_get_reward", "name": "${码上有礼}", "template_name": "${码上有礼}", "description": "<div>${通过<span>「消费者扫码领红包」</span>，任选<span>「奖励方式」</span>激励<span>「消费者」</span>}</div>", "activity_node_list": [{"type": "plan", "object_record_type": "scan_code_get_reward__c", "__init_fields": ["product_range_fresh_standard"], "activity_plan_config": {"enable_activity_cycle_control": true, "enable_plan_template": false, "account_scope_config": {"account_scope_api_name": "TPMActivityStoreObj", "ref_account_field_api_name": "store_id", "ref_master_field_api_name": "activity_id"}}, "__object_record_type_name": "${码上有礼}", "__object_layout_api_name": "layout_scan_code_get_reward_TPMActivityObj__c", "__object_edit_layout_api_name": "edit_layout_scan_code_get_reward_TPMActivityObj__c", "__detail_objects": [{"__object_api_name": "TPMActivityProductRangeObj", "__object_record_type_api_name": "self_define_reward__c", "__object_record_type_name": "${码上有礼}", "__object_layout_api_name": "layout_code_activity_reward_TPMActivityProductRangeObj__c", "__init_fields": ["manufacture_date_end", "manufacture_date_start", "match_method", "to_be_expired_days"]}]}]}, {"_id": "reward.stock_up_reward", "name": "${门店进货激励}", "template_name": "${门店进货激励}", "description": "<div>${通过<span>「门店签收」</span>，任选<span>「奖励方式」</span>激励<span>「门店」</span>}</div>", "activity_node_list": [{"type": "plan", "object_record_type": "stock_up_reward__c", "__init_fields": ["product_range_fresh_standard"], "activity_plan_config": {"enable_activity_cycle_control": true, "enable_plan_template": false, "account_scope_config": {"account_scope_api_name": "TPMActivityStoreObj", "ref_account_field_api_name": "store_id", "ref_master_field_api_name": "activity_id"}}, "__object_record_type_name": "${门店进货激励}", "__object_layout_api_name": "layout_stock_up_reward_TPMActivityObj__c", "__object_edit_layout_api_name": "edit_layout_stock_up_reward_TPMActivityObj__c", "__detail_objects": [{"__object_api_name": "TPMActivityProductRangeObj", "__object_record_type_api_name": "self_define_reward__c", "__object_record_type_name": "${门店进货激励}", "__object_layout_api_name": "layout_code_activity_reward_TPMActivityProductRangeObj__c", "__init_fields": ["manufacture_date_end", "manufacture_date_start", "match_method", "to_be_expired_days"]}]}]}, {"_id": "reward.big_date", "name": "${产品代售}", "template_name": "${产品代售}", "description": "<div>${通过<span>「消费者扫码」</span>，以<span>「减价支付」</span>激励<span>「消费者」</span>}</div>", "activity_node_list": [{"type": "plan", "object_record_type": "big_date__c", "__init_fields": ["product_range_fresh_standard"], "activity_plan_config": {"enable_activity_cycle_control": true, "enable_plan_template": false, "account_scope_config": {"account_scope_api_name": "TPMActivityStoreObj", "ref_account_field_api_name": "store_id", "ref_master_field_api_name": "activity_id"}}, "__object_record_type_name": "${产品代售}", "__object_layout_api_name": "layout_big_date_TPMActivityObj__c", "__object_edit_layout_api_name": "edit_layout_big_date_TPMActivityObj__c", "__detail_objects": [{"__object_api_name": "TPMActivityProductRangeObj", "__object_record_type_api_name": "big_date__c", "__object_record_type_name": "${产品代售}", "__object_layout_api_name": "layout_big_date_TPMActivityProductRangeObj__c", "__init_fields": ["manufacture_date_end", "manufacture_date_start", "match_method", "to_be_expired_days", "activity_deduct_amount", "consumer_retail_price"]}]}]}, {"_id": "reward.store_scan_code_reward", "name": "${门店开箱有礼}", "template_name": "${门店开箱有礼}", "description": "<div>${通过<span>「门店扫箱码」</span>，任选<span>「奖励方式」</span>激励<span>「门店」</span>}</div>", "activity_node_list": [{"type": "plan", "object_record_type": "store_scan_code_reward__c", "activity_plan_config": {"enable_activity_cycle_control": true, "enable_plan_template": false, "account_scope_config": {"account_scope_api_name": "TPMActivityStoreObj", "ref_account_field_api_name": "store_id", "ref_master_field_api_name": "activity_id"}}, "__object_record_type_name": "${门店开箱有礼}", "__object_layout_api_name": "layout_store_scan_code_reward_TPMActivityObj__c", "__object_edit_layout_api_name": "edit_layout_store_scan_code_reward_TPMActivityObj__c", "__detail_objects": [{"__object_api_name": "TPMActivityProductRangeObj", "__object_record_type_api_name": "self_define_reward__c", "__object_record_type_name": "${门店开箱有礼}", "__object_layout_api_name": "layout_store_scan_code_reward_TPMActivityProductRangeObj__c", "__init_fields": ["manufacture_date_end", "manufacture_date_start", "match_method", "to_be_expired_days"]}]}]}, {"_id": "reward.self_define_reward", "name": "${自定义激励}", "template_name": "${自定义激励}", "description": "<div>${通过触达门店的<span>「扫码动作」</span>，自由配置激励策略}</div>", "activity_node_list": [{"type": "plan", "object_record_type": "self_define_reward__c", "activity_plan_config": {"enable_activity_cycle_control": true, "enable_plan_template": false, "account_scope_config": {"account_scope_api_name": "TPMActivityStoreObj", "ref_account_field_api_name": "store_id", "ref_master_field_api_name": "activity_id"}}, "__object_record_type_name": "${自定义激励}", "__object_layout_api_name": "layout_self_define_reward_TPMActivityObj__c", "__object_edit_layout_api_name": "edit_layout_self_define_reward_TPMActivityObj__c", "__detail_objects": [{"__object_api_name": "TPMActivityProductRangeObj", "__object_record_type_api_name": "self_define_reward__c", "__object_record_type_name": "${自定义激励}", "__object_layout_api_name": "layout_self_define_reward_TPMActivityProductRangeObj__c", "__init_fields": ["manufacture_date_end", "manufacture_date_start", "match_method", "to_be_expired_days"]}]}]}]}, {"_id": "custom", "name": "${自定义新建}", "description": "<div>${可按需自定义管理节点及对各种范围的定义}</div>", "templates": [{"_id": "custom.empty", "name": "${自定义}", "template_name": "${自定义}", "description": "", "activity_node_list": [{"type": "plan", "object_record_type": "default__c"}, {"type": "agreement", "object_record_type": "default__c", "activity_agreement_config": {"signing_mode": "normal", "begin_date_setting": {"type": "before_begin_date", "value": 0}, "end_date_setting": {"type": "after_end_date", "value": 0}}}, {"type": "proof", "object_record_type": "default__c", "activity_proof_config": {"cost_calculate_config": {"calculate_type": "default", "ratio": "1"}, "frequency_config": {"frequency_limit": "0", "frequency_type": "activity", "limit_days": [], "limit_hours": []}}}, {"type": "audit", "object_record_type": "default__c", "activity_proof_audit_config": {"audit_source_config": {"use_complex_mode": true, "master_api_name": "TPMActivityProofObj", "master_record_type": "default__c", "reference_audit_source_field_api_name": "activity_proof_id", "reference_audit_source_detail_field_api_name": "activity_proof_detail_id", "reference_activity_field_api_name": "activity_id", "detail_api_name": "TPMActivityProofDetailObj", "master_detail_field_api_name": "activity_proof_id", "dealer_field_api_name": "dealer_id", "account_field_api_name": "store_id", "cost_field_api_name": "actual_total", "audit_status_api_name": "audit_status", "display_field_api_names_of_master": ["proof_images", "actual_total"], "display_field_api_names_of_detail": ["proof_item", "proof_detail_cost_standard", "amount", "subtotal"], "display_field_api_names_of_audit_master": ["audit_images", "audit_total", "opinion"], "display_field_api_names_of_audit_detail": ["audit_amount", "audit_subtotal"], "activity_item_field_api_name": ""}, "audit_mode_config": {"audit_mode": "all"}}}, {"type": "write_off", "object_record_type": "default__c", "activity_write_off_config": {"write_off_source_config": {"calculate_type": "store_cost_total", "api_name": "TPMActivityProofAuditObj", "record_type": "default__c", "reference_write_off_field_api_name": "dealer_activity_cost_id", "dealer_field_api_name": "dealer_id", "reference_activity_field_api_name": "activity_id", "account_field_api_name": "store_id", "cost_field_api_name": "audit_total", "display_field_api_names": ["activity_proof_id", "proof_images", "total", "audit_status", "audit_total"]}, "charge_up_config": {"charge_up_account_id": "", "charge_up_account_name": "", "charge_up_account_status": false}, "enable_over_write_off": false}}]}]}]}