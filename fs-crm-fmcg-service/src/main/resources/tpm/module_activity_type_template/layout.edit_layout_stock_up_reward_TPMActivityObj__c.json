{"buttons": [], "components": [{"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "activity_unified_case_id"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "name"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "activity_type"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "dealer_id"}, {"is_readonly": false, "is_required": false, "is_tiled": true, "render_type": "select_one", "field_name": "customer_type"}, {"is_readonly": false, "is_required": false, "render_type": "department_many", "field_name": "multi_department_range"}, {"is_readonly": false, "is_required": false, "render_type": "date", "field_name": "begin_date"}, {"is_readonly": false, "is_required": false, "render_type": "date", "field_name": "end_date"}, {"is_readonly": false, "is_required": false, "is_tiled": true, "render_type": "select_one", "field_name": "product_range_fresh_standard"}, {"is_readonly": false, "is_required": false, "render_type": "file_attachment", "field_name": "attachment"}, {"is_readonly": false, "full_line": true, "is_required": false, "render_type": "long_text", "field_name": "description"}, {"is_readonly": false, "full_line": true, "is_required": false, "render_type": "long_text", "field_name": "remarks"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息", "collapse": false}, {"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "currency", "field_name": "activity_amount"}, {"is_readonly": false, "is_required": false, "render_type": "currency", "field_name": "activity_actual_amount"}, {"is_readonly": true, "is_required": false, "render_type": "formula", "field_name": "remaining_write_off_amount"}, {"is_readonly": true, "is_required": false, "render_type": "count", "field_name": "recorded_amount"}], "api_name": "group_activity_amount__c", "tab_index": "ltr", "column": 2, "header": "费用申请信息", "collapse": false}, {"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "data_own_department"}], "api_name": "group_sys_info__c", "tab_index": "ltr", "column": 2, "header": "系统信息", "collapse": true}], "buttons": [], "api_name": "form_component", "related_list_name": "", "column": 2, "header": "表单组件", "nameI18nKey": "paas.udobj.form_component", "type": "form", "grayLimit": 1, "order": 4}, {"components": [["TPMActivityStoreObj_md_group_component"], ["TPMActivityProductRangeObj_md_group_component"]], "buttons": [], "api_name": "tabs_component", "tabs": [{"api_name": "tab_TPMActivityStoreObj_md_group_component", "header": "参与活动客户", "nameI18nKey": "TPMActivityStoreObj.field.activity_id.reference_label"}, {"api_name": "tab_TPMActivityProductRangeObj_md_group_component", "header": "活动品项", "nameI18nKey": "TPMActivityProductRangeObj.field.activity_id.reference_label"}], "header": "页签容器", "type": "tabs", "order": 5}, {"field_section": [], "buttons": [], "api_name": "head_info", "related_list_name": "", "button_info": [{"hidden": [], "page_type": "create", "render_type": "normal", "order": ["Add_Save_button_default", "Add_Save_Continue_button_default", "Add_Save_Draft_button_default"]}, {"hidden": [], "page_type": "edit", "render_type": "normal", "order": ["Edit_Save_button_default"]}], "header": "标题和按钮", "nameI18nKey": "paas.udobj.head_info", "type": "simple", "grayLimit": 1, "order": 3}, {"buttons": [], "child_components": [], "type": "multi_table", "api_name": "TPMActivityStoreObj_md_group_component", "header": "参与活动客户", "ref_object_api_name": "TPMActivityStoreObj", "related_list_name": "target_related_list_TPMActivityStoreObj_TPMActivityObj__c", "field_api_name": "activity_id", "nameI18nKey": "TPMActivityStoreObj.field.activity_id.reference_label", "limit": 1, "button_info": [{"hidden": [], "render_type": "list_normal", "order": ["Single_Add_button_default", "Batch_Lookup_Add_button_store_id"]}, {"hidden": [], "render_type": "list_batch", "order": ["Batch_Edit_button_default", "Delete_button_default", "Clone_button_default"]}, {"hidden": [], "render_type": "list_single", "order": ["Delete_button_default", "Clone_button_default"]}], "render_type": "card", "order": 6}, {"buttons": [], "child_components": [], "type": "multi_table", "api_name": "TPMActivityProductRangeObj_md_group_component", "header": "活动品项", "ref_object_api_name": "TPMActivityProductRangeObj", "related_list_name": "target_related_list_TPMActivityProductRangeObj_TPMActivityObj__c", "field_api_name": "activity_id", "nameI18nKey": "TPMActivityProductRangeObj.field.activity_id.reference_label", "limit": 1, "button_info": [{"hidden": [], "render_type": "list_normal", "order": ["Single_Add_button_default", "Batch_Lookup_Add_button_field_tdSr9__c", "Batch_Lookup_Add_button_product_id"]}, {"hidden": [], "render_type": "list_batch", "order": ["Batch_Edit_button_default", "Delete_button_default", "Clone_button_default"]}, {"hidden": [], "render_type": "list_single", "order": ["Delete_button_default", "Clone_button_default"]}], "render_type": "card", "order": 7}], "is_deleted": false, "layout_description": "", "api_name": "edit_layout_stock_up_reward_TPMActivityObj__c", "default_component": "form_component", "layout_structure": {"layout": [{"components": [["head_info"]], "columns": [{"width": "100%"}]}, {"components": [["form_component", "tabs_component"]], "columns": [{"width": "100%"}]}], "is_tile_help_text": false}, "display_name": "门店进货激励(新建/编辑页)", "is_default": false, "layout_type": "edit", "package": "CRM", "ref_object_api_name": "TPMActivityObj", "ui_event_ids": [], "hidden_buttons": [], "hidden_components": ["TPMActivityMaterialObj_md_group_component", "TPMActivityVenueObj_md_group_component", "TPMActivityDetailObj_md_group_component"], "enable_mobile_layout": false, "events": []}