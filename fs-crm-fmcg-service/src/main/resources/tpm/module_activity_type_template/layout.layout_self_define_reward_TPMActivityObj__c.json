{"buttons": [], "components": [{"field_section": [], "buttons": [{"action_type": "default", "api_name": "Edit_button_default", "label": "编辑"}, {"action_type": "default", "api_name": "SaleRecord_button_default", "label": "销售记录"}, {"action_type": "default", "api_name": "Dial_button_default", "label": "打电话"}, {"action_type": "default", "api_name": "ChangeOwner_button_default", "label": "更换负责人"}, {"action_type": "default", "api_name": "StartBPM_button_default", "label": "发起业务流程"}, {"action_type": "default", "api_name": "Abolish_button_default", "label": "作废"}, {"action_type": "default", "api_name": "StartStagePropellor_button_default", "label": "发起阶段推进器"}, {"action_type": "default", "api_name": "Lock_button_default", "label": "锁定"}, {"action_type": "default", "api_name": "Unlock_button_default", "label": "解锁"}, {"action_type": "default", "api_name": "Clone_button_default", "label": "复制"}, {"action_type": "default", "api_name": "ChangePartnerOwner_button_default", "label": "更换外部负责人"}, {"action_type": "default", "api_name": "SendMail_button_default"}, {"action_type": "default", "api_name": "Discuss_button_default"}, {"action_type": "default", "api_name": "Remind_button_default"}, {"action_type": "default", "api_name": "Schedule_button_default"}, {"action_type": "default", "api_name": "Print_button_default"}, {"action_type": "default", "api_name": "CloseTPMActivity_button_default", "action": "CloseTPMActivity", "label": "结案", "isActive": true}], "api_name": "head_info", "related_list_name": "", "header": "标题和按钮", "nameI18nKey": "paas.udobj.head_info", "exposedButton": 3, "type": "simple", "grayLimit": 1, "_id": "head_info"}, {"field_section": [], "buttons": [], "api_name": "top_info", "related_list_name": "", "header": "摘要信息", "type": "top_info", "grayLimit": 1, "nameI18nKey": "paas.udobj.summary_info", "_id": "top_info"}, {"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "activity_unified_case_id"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "name"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "activity_type"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "dealer_id"}, {"is_readonly": false, "is_required": false, "is_tiled": true, "render_type": "select_one", "field_name": "customer_type"}, {"is_readonly": false, "is_required": false, "render_type": "department_many", "field_name": "multi_department_range"}, {"is_readonly": false, "is_required": false, "render_type": "date", "field_name": "begin_date"}, {"is_readonly": false, "is_required": false, "render_type": "date", "field_name": "end_date"}, {"is_readonly": false, "is_required": false, "is_tiled": true, "render_type": "select_one", "field_name": "product_range_fresh_standard"}, {"is_readonly": false, "is_required": false, "render_type": "file_attachment", "field_name": "attachment"}, {"is_readonly": false, "full_line": true, "is_required": false, "render_type": "long_text", "field_name": "description"}, {"is_readonly": false, "full_line": true, "is_required": false, "render_type": "long_text", "field_name": "remarks"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息", "collapse": false}, {"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "currency", "field_name": "activity_amount"}, {"is_readonly": false, "is_required": false, "render_type": "currency", "field_name": "activity_actual_amount"}, {"is_readonly": true, "is_required": false, "render_type": "formula", "field_name": "remaining_write_off_amount"}, {"is_readonly": true, "is_required": false, "render_type": "count", "field_name": "recorded_amount"}], "api_name": "group_activity_amount__c", "tab_index": "ltr", "column": 2, "header": "费用申请信息", "collapse": false}, {"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "data_own_department"}], "api_name": "group_sys_info__c", "tab_index": "ltr", "column": 2, "header": "系统信息", "collapse": true}], "buttons": [], "api_name": "form_component", "related_list_name": "", "column": 2, "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "type": "form", "grayLimit": 1, "_id": "form_component"}, {"components": [["form_component"], ["operation_log"], ["Approval_related_list"], ["BPM_related_list"]], "buttons": [], "api_name": "tabs_display__c", "tabs": [{"api_name": "form_component_t5Uv9__c", "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info"}, {"api_name": "operation_log_1684900032969", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log"}, {"api_name": "Approval_related_list_1684900035361", "header": "审批流程", "nameI18nKey": "paas.udobj.approvalflow"}, {"api_name": "BPM_related_list_1684900037639", "header": "流程列表", "nameI18nKey": "paas.udobj.process_list"}], "header": "页签容器", "type": "tabs", "_id": "tabs_display__c"}, {"field_section": [], "buttons": [], "api_name": "operation_log", "related_list_name": "", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log", "type": "related_record", "grayLimit": 1, "_id": "operation_log"}, {"type": "relatedlist", "buttons": [], "define_type": "general", "api_name": "BPM_related_list", "header": "流程列表", "nameI18nKey": "paas.udobj.process_list", "ref_object_api_name": "BPM", "related_list_name": "", "limit": 1, "field_section": [], "grayLimit": 1, "_id": "BPM_related_list"}, {"type": "relatedlist", "buttons": [], "define_type": "general", "api_name": "Approval_related_list", "header": "审批流程", "nameI18nKey": "paas.udobj.approvalflow", "ref_object_api_name": "Approval", "related_list_name": "", "limit": 1, "field_section": [], "grayLimit": 1, "_id": "Approval_related_list"}], "is_deleted": false, "layout_description": "", "api_name": "layout_self_define_reward_TPMActivityObj__c", "default_component": "form_component", "layout_structure": {"layout": [{"components": [["head_info"]], "columns": [{"width": "100%"}]}, {"components": [["top_info", "tabs_display__c"]], "columns": [{"width": "auto"}]}], "layout_structure_type": 1}, "display_name": "自定义激励", "is_default": false, "layout_type": "detail", "package": "CRM", "ref_object_api_name": "TPMActivityObj", "ui_event_ids": [], "hidden_buttons": [], "hidden_components": ["relevant_team_component", "sale_log", "biDashboardCom"], "enable_mobile_layout": false, "events": []}