{"expression_type": "js", "return_type": "number", "describe_api_name": "PurchaseOrderObj", "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "type": "formula", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "expression": "$total_money$-MAX($pay_amount$,$pay_match_amount$)", "is_encrypted": false, "label": "待付款金额", "api_name": "pre_pay_amount", "is_show_mask": false, "help_text": "", "status": "new"}