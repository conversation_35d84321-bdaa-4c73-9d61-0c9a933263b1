{"expression_type": "js", "return_type": "number", "describe_api_name": "ReturnedGoodsInvoiceObj", "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "type": "formula", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "expression": "($returned_goods_inv_amount$-MAX(IF($refund_amount$<0, -$refund_amount$, $refund_amount$),IF($total_settled_amount$<0, -$total_settled_amount$, $total_settled_amount$)))*-1", "is_encrypted": false, "label": "待退款金额", "api_name": "pending_refund_amount", "is_show_mask": false, "help_text": "", "status": "new"}