{"return_type": "currency", "describe_api_name": "PurchaseOrderObj", "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "type": "count", "decimal_places": 2, "sub_object_describe_apiname": "PayDetailObj", "is_required": false, "wheres": [], "define_type": "package", "is_single": false, "field_api_name": "purchase_order_id", "is_index": true, "default_result": "d_zero", "is_active": true, "is_encrypted": false, "count_type": "sum", "count_field_api_name": "pay_amount", "label": "付款金额", "count_to_zero": true, "api_name": "pay_amount", "count_field_type": "currency", "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}