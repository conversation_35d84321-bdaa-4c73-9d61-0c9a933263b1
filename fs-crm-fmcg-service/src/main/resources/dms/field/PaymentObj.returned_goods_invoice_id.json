{"describe_api_name": "PaymentObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "enable_clone": true, "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "换货单", "target_api_name": "ReturnedGoodsInvoiceObj", "target_related_list_name": "target_related_list_PaymentObj_ReturnedGoodsInvoiceObj__c", "target_related_list_label": "回款单", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "returned_goods_invoice_id", "help_text": "", "status": "new"}