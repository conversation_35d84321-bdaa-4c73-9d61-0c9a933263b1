{"return_type": "currency", "describe_api_name": "ReturnedGoodsInvoiceObj", "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "count", "decimal_places": 2, "sub_object_describe_apiname": "OrderPaymentObj", "is_required": false, "wheres": [{"connector": "OR", "filters": [{"value_type": 0, "operator": "EQ", "field_name": "life_status", "field_values": ["under_review"]}]}], "define_type": "package", "is_single": false, "field_api_name": "returned_goods_invoice_id", "is_index": true, "default_result": "d_zero", "is_active": true, "is_encrypted": false, "count_type": "sum", "count_field_api_name": "payment_amount", "label": "待确认退款金额", "count_to_zero": true, "api_name": "refund_amount_to_be_confirmed", "count_field_type": "currency", "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}