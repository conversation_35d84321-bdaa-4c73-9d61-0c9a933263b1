{"components": [{"field_section": [], "buttons": [{"action_type": "default", "api_name": "Edit_button_default", "label": "编辑"}, {"action_type": "default", "api_name": "SaleRecord_button_default", "label": "销售记录"}, {"action_type": "default", "api_name": "Dial_button_default", "label": "打电话"}, {"action_type": "default", "api_name": "ChangeOwner_button_default", "label": "更换负责人"}, {"action_type": "default", "api_name": "StartBPM_button_default", "label": "发起业务流程"}, {"action_type": "default", "api_name": "Abolish_button_default", "label": "作废"}, {"action_type": "default", "api_name": "StartStagePropellor_button_default", "label": "发起阶段推进器"}, {"action_type": "default", "api_name": "Lock_button_default", "label": "锁定"}, {"action_type": "default", "api_name": "Unlock_button_default", "label": "解锁"}, {"action_type": "default", "api_name": "Clone_button_default", "label": "复制"}, {"action_type": "default", "api_name": "ChangePartnerOwner_button_default", "label": "更换外部负责人"}, {"action_type": "default", "api_name": "SendMail_button_default"}, {"action_type": "default", "api_name": "Discuss_button_default"}, {"action_type": "default", "api_name": "Remind_button_default"}, {"action_type": "default", "api_name": "Schedule_button_default"}, {"action_type": "default", "api_name": "Print_button_default"}], "api_name": "head_info", "related_list_name": "", "header": "标题和按钮", "nameI18nKey": "paas.udobj.head_info", "exposedButton": 3, "type": "simple", "isSticky": false, "grayLimit": 1}, {"field_section": [{"field_name": "owner"}, {"field_name": "owner_department"}, {"field_name": "last_modified_time"}, {"field_name": "record_type"}], "buttons": [], "api_name": "top_info", "related_list_name": "", "header": "摘要信息", "nameI18nKey": "paas.udobj.summary_info", "type": "top_info", "isSticky": false, "grayLimit": 1}, {"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "is_required": true, "render_type": "auto_number", "field_name": "name"}, {"is_readonly": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "supplier_id"}, {"is_readonly": false, "is_required": false, "render_type": "date", "field_name": "pay_date"}, {"is_readonly": false, "is_required": false, "render_type": "currency", "field_name": "pay_amount"}, {"is_readonly": true, "is_required": false, "render_type": "formula", "field_name": "available_amount"}, {"is_readonly": false, "is_required": false, "render_type": "true_or_false", "field_name": "base"}, {"is_readonly": false, "is_required": false, "render_type": "true_or_false", "field_name": "pre_pay"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "pay_type"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "pay_term"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "purpose"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "contact_object"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "enterprise_fund_account_id"}, {"is_readonly": false, "is_required": false, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "owner_department"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "life_status"}, {"is_readonly": false, "full_line": true, "is_required": false, "render_type": "long_text", "field_name": "remarks"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "field_HCznw"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息", "collapse": false}, {"show_header": true, "form_fields": [{"is_readonly": true, "is_required": false, "render_type": "count", "field_name": "pre_match_amount"}, {"is_readonly": true, "is_required": false, "render_type": "formula", "field_name": "no_match_amount"}, {"is_readonly": true, "is_required": false, "render_type": "count", "field_name": "match_amount"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "match_status"}], "api_name": "group_1DJis__c", "tab_index": "ltr", "column": 2, "header": "核销信息", "collapse": false}, {"show_header": true, "form_fields": [{"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}], "api_name": "group_86lot__c", "tab_index": "ltr", "column": 2, "header": "系统信息", "collapse": true}], "buttons": [], "api_name": "form_component", "_id": "form_component", "related_list_name": "", "column": 2, "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "type": "form", "isSticky": false, "grayLimit": 1}, {"field_section": [], "buttons": [], "api_name": "bpm_component", "related_list_name": "", "header": "业务流组件", "nameI18nKey": "paas.udobj.bpm_component", "type": "bpm_component", "isSticky": false, "grayLimit": 1}, {"field_section": [], "buttons": [], "api_name": "stage_component", "related_list_name": "", "header": "阶段推进器组件", "nameI18nKey": "paas.udobj.stage_component", "type": "stage_component", "isSticky": false, "grayLimit": 1}, {"field_section": [], "buttons": [], "api_name": "approval_component", "related_list_name": "", "header": "审批流组件", "nameI18nKey": "paas.udobj.approval_component", "type": "approval_component", "isSticky": false, "grayLimit": 1}, {"components": [["form_component"], ["operation_log"]], "buttons": [], "api_name": "tabs_n1fnI__c", "tabs": [{"api_name": "form_component_bnbuQ__c", "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info"}, {"api_name": "operation_log_2bqVd__c", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log"}], "header": "页签容器", "type": "tabs", "isSticky": false}], "ref_object_api_name": "PayObj", "layout_type": "detail", "hidden_buttons": [], "ui_event_ids": [], "is_deleted": false, "default_component": "form_component", "layout_structure": {"layout": [{"components": [["head_info"]], "columns": [{"width": "100%"}]}, {"components": [["stage_component", "bpm_component", "top_info", "tabs_n1fnI__c"], ["approval_component"]], "columns": [{"width": "auto"}, {"width": "500px", "retractable": true}]}]}, "buttons": [], "package": "CRM", "display_name": "无明细布局", "is_default": false, "version": 4, "api_name": "layout_default_no_details_PayObj__c", "layout_description": ""}