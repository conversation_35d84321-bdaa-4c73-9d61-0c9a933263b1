CREATE TABLE IF NOT EXISTS fmcg_enterprise_fund_account_adjustment (
	id  VARCHAR(64) NOT NULL,
	tenant_id  VARCHAR(32) NOT NULL,
	name  VARCHAR(128),
	expenditure_account VARCHAR(64),
	expense_type VARCHAR(64),
	service_charge NUMERIC,
	revenue_amount NUMERIC,
	revenue_account VARCHAR(64),
	expense_amount NUMERIC,
	transfer_amount NUMERIC,
	revenue_type VARCHAR(64),
	remarks VARCHAR(4000),
	adjustment_date NUMERIC,
	owner  VARCHA<PERSON>(64),
	display_name  VA<PERSON>HA<PERSON>(128),
	lock_status  VARCHAR(64),
	life_status  VARCHAR(64),
	record_type  VARCHAR(64),
	created_by  VARCHAR(64),
	create_time BIGINT,
	last_modified_by  VARC<PERSON>R(64),
	last_modified_time BIGINT,
	extend_obj_data_id  VARCHAR(128),
	package  VARCHAR(64),
	object_describe_id  VARCHAR(128),
	object_describe_api_name  <PERSON><PERSON><PERSON><PERSON>(128),
	version INT,
	lock_user  VARCHAR(128),
	lock_rule  VARCHAR(128),
	life_status_before_invalid  VARCHAR(64),
	is_deleted INT,
	out_tenant_id  VARCHAR(50),
	out_owner  VARCHA<PERSON>(50),
	data_own_department  VARCHAR(32),
	data_own_organization  VARCHAR(32),
	out_data_own_department  VARCHAR(64),
	out_data_own_organization  VARCHAR(64),
	data_auth_code  VARCHAR(64),
	change_type INT,
	out_data_auth_code  VARCHAR(64),
	order_by INT,
	data_auth_id INT,
	out_data_auth_id INT,
	sys_modified_time BIGINT,
	origin_source varchar(128),
	dimension_d1 VARCHAR(2000)[64],
	dimension_d2 VARCHAR(2000)[64],
	dimension_d3 VARCHAR(2000)[64],
	mc_currency VARCHAR(128) ,
	mc_exchange_rate NUMERIC,
	mc_functional_currency VARCHAR(128),
	mc_exchange_rate_version VARCHAR(64) ,
	CONSTRAINT fmcg_enterprise_fund_account_adjustment_pk PRIMARY KEY (id, tenant_id)
);

CREATE INDEX  IF NOT EXISTS fmcg_enterprise_fund_account_adjustment_id_name_delete_describe_index ON fmcg_enterprise_fund_account_adjustment USING btree(tenant_id,name, is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_enterprise_fund_account_adjustment_ti_dn_id_odan_index ON fmcg_enterprise_fund_account_adjustment USING btree(tenant_id,display_name,is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_enterprise_fund_account_adjustment_eodi_ti_index ON fmcg_enterprise_fund_account_adjustment USING btree(extend_obj_data_id,tenant_id);
CREATE INDEX  IF NOT EXISTS fmcg_enterprise_fund_account_adjustment_lmt_ti_id_odan_index ON fmcg_enterprise_fund_account_adjustment USING btree(last_modified_time desc,tenant_id,is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_enterprise_fund_account_adjustment_ct_ti_id_odan_index ON fmcg_enterprise_fund_account_adjustment USING btree(create_time desc,tenant_id, is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_enterprise_fund_account_adjustment_ti_o_id_odan_index ON fmcg_enterprise_fund_account_adjustment USING btree(tenant_id,owner, is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_enterprise_fund_account_adjustment_ti_dod_id_odan_index ON fmcg_enterprise_fund_account_adjustment USING btree(tenant_id,data_own_department, is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_enterprise_fund_account_adjustment_dac_index ON fmcg_enterprise_fund_account_adjustment USING btree(data_auth_code);
CREATE INDEX  IF NOT EXISTS fmcg_enterprise_fund_account_adjustment_odac_index ON fmcg_enterprise_fund_account_adjustment USING btree(out_data_auth_code);
CREATE INDEX  IF NOT EXISTS fmcg_enterprise_fund_account_adjustment_out_id_ti_index ON fmcg_enterprise_fund_account_adjustment USING btree(out_tenant_id,is_deleted,tenant_id);
CREATE INDEX  IF NOT EXISTS fmcg_enterprise_fund_account_adjustment_smt_ti_id_odan_index ON fmcg_enterprise_fund_account_adjustment USING btree(sys_modified_time desc,tenant_id, is_deleted, object_describe_api_name);
DROP TRIGGER IF EXISTS x_audit_changes ON fmcg_enterprise_fund_account_adjustment;
DROP TRIGGER IF EXISTS x_system_changes ON fmcg_enterprise_fund_account_adjustment;
CREATE TRIGGER x_system_changes BEFORE INSERT OR UPDATE ON fmcg_enterprise_fund_account_adjustment FOR EACH ROW EXECUTE PROCEDURE f_system_change();
CREATE TRIGGER x_audit_changes AFTER INSERT OR UPDATE OR DELETE ON fmcg_enterprise_fund_account_adjustment FOR EACH ROW EXECUTE PROCEDURE f_change_detail('id','tenant_id','object_describe_api_name');


CREATE INDEX CONCURRENTLY IF NOT EXISTS fmcg_enterprise_fund_account_adjustment_ra_ti_id_idx ON fmcg_enterprise_fund_account_adjustment USING BTREE (revenue_account,tenant_id,is_deleted);
CREATE INDEX CONCURRENTLY IF NOT EXISTS fmcg_enterprise_fund_account_adjustment_ea_ti_id_idx ON fmcg_enterprise_fund_account_adjustment USING BTREE (expenditure_account,tenant_id,is_deleted);
