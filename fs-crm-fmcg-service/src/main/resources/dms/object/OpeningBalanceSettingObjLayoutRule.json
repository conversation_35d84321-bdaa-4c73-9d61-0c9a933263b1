{"api_name": "layout_rule_OpeningBalanceSettingObj_default__c", "label": "期初设置预置布局规则", "layout_api_name": "layout_default_OpeningBalanceSettingObj__c", "object_describe_api_name": "OpeningBalanceSettingObj", "description": "", "main_field": "opening_type", "main_field_branches": [{"branches": [{"result": {"required_field": [{"field_api_name": "account_id"}, {"field_api_name": "due_date"}], "show_field": [], "readonly_field": []}, "conditions": []}], "main_field_filter": {"value_type": 0, "operator": "EQ", "field_name": "opening_type", "field_values": ["accounts_receivable"]}}, {"branches": [{"result": {"required_field": [{"field_api_name": "account_id"}], "show_field": [], "readonly_field": []}, "conditions": []}], "main_field_filter": {"value_type": 0, "operator": "EQ", "field_name": "opening_type", "field_values": ["payment"]}}, {"branches": [{"result": {"required_field": [{"field_api_name": "supplier_id"}], "show_field": [], "readonly_field": []}, "conditions": []}], "main_field_filter": {"value_type": 0, "operator": "EQ", "field_name": "opening_type", "field_values": ["accounts_payable"]}}, {"branches": [{"result": {"required_field": [{"field_api_name": "supplier_id"}], "show_field": [], "readonly_field": []}, "conditions": []}], "main_field_filter": {"value_type": 0, "operator": "EQ", "field_name": "opening_type", "field_values": ["pay"]}}], "status": 0, "type": "field", "page_branches": null, "page_trigger_mode": null, "define_type": "system"}