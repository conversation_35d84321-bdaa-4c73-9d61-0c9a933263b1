{"components": [{"field_section": [], "buttons": [{"action_type": "default", "api_name": "Edit_button_default", "label": "编辑"}, {"action_type": "default", "api_name": "Dial_button_default", "label": "打电话"}, {"action_type": "default", "api_name": "ChangeOwner_button_default", "label": "更换负责人"}, {"action_type": "default", "api_name": "StartBPM_button_default", "label": "发起业务流程"}, {"action_type": "default", "api_name": "Abolish_button_default", "label": "作废"}, {"action_type": "default", "api_name": "StartStagePropellor_button_default", "label": "发起阶段推进器"}, {"action_type": "default", "api_name": "Lock_button_default", "label": "锁定"}, {"action_type": "default", "api_name": "Unlock_button_default", "label": "解锁"}, {"action_type": "default", "api_name": "Clone_button_default", "label": "复制"}, {"action_type": "default", "api_name": "ChangePartnerOwner_button_default", "label": "更换外部负责人"}, {"action_type": "default", "api_name": "EnterAccount_button_default", "label": "入账"}, {"action_type": "default", "api_name": "CancelEntry_button_default", "label": "取消入账"}, {"action_type": "default", "api_name": "SendMail_button_default"}, {"action_type": "default", "api_name": "Discuss_button_default"}, {"action_type": "default", "api_name": "Remind_button_default"}, {"action_type": "default", "api_name": "Schedule_button_default"}, {"action_type": "default", "api_name": "Print_button_default"}, {"action_type": "system", "api_name": "Unfollow_button_default", "action": "Unfollow", "label": "取消关注"}, {"action_type": "system", "api_name": "Follow_button_default", "action": "Follow", "label": "关注"}], "api_name": "head_info", "related_list_name": "", "header": "标题和按钮", "nameI18nKey": "paas.udobj.head_info", "exposedButton": 3, "type": "simple", "isSticky": false, "grayLimit": 1}, {"field_section": [{"field_name": "owner"}, {"field_name": "owner_department"}, {"field_name": "last_modified_time"}, {"field_name": "record_type"}], "buttons": [], "api_name": "top_info", "related_list_name": "", "header": "摘要信息", "nameI18nKey": "paas.udobj.summary_info", "type": "top_info", "isSticky": false, "grayLimit": 1}, {"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "full_line": false, "is_required": true, "render_type": "auto_number", "field_name": "name"}, {"is_readonly": false, "full_line": false, "is_required": true, "render_type": "date", "field_name": "adjustment_date"}, {"is_readonly": false, "full_line": false, "is_required": true, "render_type": "object_reference", "field_name": "revenue_account"}, {"is_readonly": false, "full_line": false, "is_required": true, "render_type": "object_reference", "field_name": "expenditure_account"}, {"is_readonly": false, "full_line": false, "is_required": true, "render_type": "currency", "field_name": "transfer_amount"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "currency", "field_name": "service_charge"}, {"is_readonly": true, "full_line": false, "is_required": false, "render_type": "currency", "field_name": "revenue_amount"}, {"is_readonly": true, "full_line": false, "is_required": false, "render_type": "currency", "field_name": "expense_amount"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "department", "field_name": "data_own_department"}, {"is_readonly": false, "full_line": true, "is_required": false, "render_type": "long_text", "field_name": "remarks"}], "api_name": "base_field_section__c", "tab_index": "ltr", "defaultValue": "基本信息", "column": 2, "header": "基本信息", "collapse": false}, {"show_header": true, "form_fields": [{"is_readonly": false, "full_line": false, "is_required": false, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "full_line": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}, {"is_readonly": false, "full_line": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "life_status"}], "api_name": "group_gsq8s__c", "tab_index": "ltr", "defaultValue": "系统信息", "column": 2, "header": "系统信息", "collapse": true}], "buttons": [], "api_name": "form_component", "related_list_name": "", "i18nInfoList": [{"customKey": "layout.EnterpriseFundAccountAdjustmentObj__c.detail.layout_p2Hfe__c.group.base_field_section__c.header", "apiName": "base_field_section__c", "defaultValue": "基本信息", "preKey": "paas.metadata.layout.base_info", "languageInfo": {"de": "Grundlegende Informationen", "pt": "Informações Básicas", "sw-TZ": "Makundi ya Makundi", "zh-TW": "基本資訊", "ko-KR": "기본 정보", "pt-BR": "Informações básicas", "en": "Basic Information", "es-ES": "Información básica", "kk-KZ": "Басқару құралдары", "zh-CN": "基本信息", "ur-PK": "بیچارہ معلومات", "it-IT": "Informazioni di base", "ar": "البيانات الأساسية", "pl-PL": "Podstawowe informacje", "ru-RU": "Основная информация", "nl-NL": "Basisinformatie", "id-ID": "Informasi <PERSON>", "tr-TR": "<PERSON><PERSON> bilgiler", "fr-FR": "Informations de base", "vi-VN": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "ja-JP": "基本情報", "th-TH": "ข้อมูลพื้นฐาน"}, "value": "基本信息"}, {"customKey": "layout.EnterpriseFundAccountAdjustmentObj__c.detail.layout_p2Hfe__c.group.group_gsq8s__c.header", "apiName": "group_gsq8s__c", "defaultValue": "系统信息", "value": "系统信息"}], "column": 2, "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "type": "form", "isSticky": false, "grayLimit": 1}, {"field_section": [], "buttons": [], "api_name": "bpm_component", "related_list_name": "", "header": "业务流组件", "nameI18nKey": "paas.udobj.bpm_component", "type": "bpm_component", "isSticky": false, "grayLimit": 1}, {"field_section": [], "buttons": [], "api_name": "stage_component", "related_list_name": "", "header": "阶段推进器组件", "nameI18nKey": "paas.udobj.stage_component", "type": "stage_component", "isSticky": false, "grayLimit": 1}, {"field_section": [], "buttons": [], "api_name": "approval_component", "related_list_name": "", "header": "审批流组件", "nameI18nKey": "paas.udobj.approval_component", "type": "approval_component", "isSticky": false, "grayLimit": 1}, {"components": [["form_component"], ["operation_log"]], "buttons": [], "api_name": "tabs_2fmOT__c", "tabs": [{"api_name": "form_component_ou9fu__c", "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info"}, {"api_name": "operation_log_2bqVb__c", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log"}], "header": "页签容器", "type": "tabs", "isSticky": false}], "ref_object_api_name": "EnterpriseFundAccountAdjustmentObj", "layout_type": "detail", "hidden_buttons": [], "ui_event_ids": [], "is_deleted": false, "default_component": "form_component", "layout_structure": {"layout": [{"components": [["head_info"]], "columns": [{"width": "100%"}]}, {"components": [["stage_component", "bpm_component", "top_info", "tabs_2fmOT__c"], ["approval_component"]], "columns": [{"width": "auto"}, {"width": "500px", "retractable": true}]}]}, "buttons": [], "package": "CRM", "display_name": "银行转账单", "is_default": true, "version": 16, "api_name": "layout_bank_transfer_document__c", "layout_description": ""}