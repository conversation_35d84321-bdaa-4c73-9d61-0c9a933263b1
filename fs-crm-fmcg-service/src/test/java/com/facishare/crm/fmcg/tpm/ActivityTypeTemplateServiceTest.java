package com.facishare.crm.fmcg.tpm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.business.enums.ActivityTemplateI18nEnum;
import com.facishare.crm.fmcg.tpm.web.contract.ActivityTypeTemplateGet;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IActivityTypeTemplateService;
import com.facishare.paas.appframework.metadata.RecordTypeLogicServiceImpl;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.describe.IObjectRelationMatch;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.commons.text.StringSubstitutor;
import org.apache.logging.log4j.util.Strings;
import org.junit.Test;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;
import java.util.Map;

public class ActivityTypeTemplateServiceTest extends BaseTest {

    @Resource
    private IActivityTypeTemplateService activityTypeTemplateService;
    @Resource
    private RecordTypeLogicServiceImpl recordTypeLogicService;

    @Test
    public void getTest() {
        ActivityTypeTemplateGet.Arg arg = new ActivityTypeTemplateGet.Arg();
        arg.setId("online.simple");
        ActivityTypeTemplateGet.Result result = activityTypeTemplateService.get(arg);
        assert result != null;
    }

    @Test
    public void closeAndReopenEditLayout() {
        List<IObjectRelationMatch> matches = recordTypeLogicService.findMatchRecordTypeRelation("84931", ApiNames.TPM_ACTIVITY_OBJ, "tasting__c", ApiNames.TPM_ACTIVITY_PRODUCT_RANGE_OBJ);

        assert matches != null;
    }


    @Test
    public void reloadTemplate() {
        String json = loadResource();
        JSON.parseObject(json);

        Gson gson = new Gson();
        JsonElement element = gson.fromJson(json, JsonElement.class);
        traverseJson(element, "fmcg.crm.fmcg.tpm", 0);

    }

    private String loadResource() {
        String json;
        try {
            File file = ResourceUtils.getFile("classpath:tpm/module_activity_type_template/activity_type_template.json");
            json = new String(Files.readAllBytes(file.toPath()));

        } catch (FileNotFoundException e) {
            throw new MetaDataBusinessException("activity type template resource file not found.");
        } catch (IOException ex) {
            throw new MetaDataBusinessException("read activity type template resource failed.");
        }
        return json;
    }


    private static void traverseJson(JsonElement element, String key, int count) {
        if (element instanceof JsonObject) {
            String innerKey = count > 0 ? key + "." + count : key;
            for (Map.Entry<String, JsonElement> entry : ((JsonObject) element).entrySet()) {
                innerKey = innerKey + "." + entry.getKey();
                traverseJson(entry.getValue(), innerKey, count);
                //System.out.println("Key: " + innerKey);

            /*    JsonElement value = entry.getValue();
                if (!value.isJsonPrimitive() && !value.isJsonArray()) {
                    traverseJson(entry.getValue(), innerKey, count);
                } else {
                    boolean containsChineseCharacter = entry.getValue().getAsString().matches(".*[\u4e00-\u9fa5]+.*");
                    if (containsChineseCharacter) {
                        //System.out.println("Key: " + key);
                        System.out.println("Value: " + element);

                        if (!value.getAsString().contains("div")) {
                            String vaStr = "&{" + value + "}";
                            Gson gson = new Gson();
                            entry.setValue(gson.fromJson(vaStr, JsonElement.class));
                        }
                    }
                }*/
            }
        } else if (element instanceof JsonArray) {
            count = 0;
            for (JsonElement arrayElement : ((JsonArray) element)) {
                count++;
                traverseJson(arrayElement, key, count);
            }
        } else {
            boolean containsChineseCharacter = element.getAsString().matches(".*[\u4e00-\u9fa5]+.*");
            if (containsChineseCharacter) {
                //System.out.println("Key: " + key);
                System.out.println("Value: " + element);
            }
        }
    }
}