package com.facishare.crm.fmcg.dms;

import com.facishare.crm.fmcg.dms.business.enums.AccountPayableSwitchEnum;
import com.facishare.crm.fmcg.dms.constants.AccountsReceivableConstants;
import com.facishare.crm.fmcg.dms.model.EnableReceivableAutoMatch;
import com.facishare.crm.fmcg.dms.model.QueryReceivableAutoMatchStatus;
import com.facishare.crm.fmcg.dms.web.abstraction.IAccountsReceivableNoteService;
import com.facishare.crm.fmcg.tpm.BaseTest;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.fxiaoke.bizconf.arg.ConfigArg;
import com.fxiaoke.bizconf.arg.QueryConfigByRankArg;
import com.fxiaoke.bizconf.bean.Rank;
import com.fxiaoke.bizconf.bean.ValueType;
import com.fxiaoke.bizconf.factory.BizConfClient;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Objects;

public class ReceivableTest extends BaseTest {

    @Resource
    private IAccountsReceivableNoteService accountsReceivableNoteService;
    @Resource
    private BizConfClient bizConfClient;

    @Test
    public void enableTest() {
//        EnableReceivableAutoMatch.Arg arg = new EnableReceivableAutoMatch.Arg();
//        accountsReceivableNoteService.enableAutoMatch(arg);

        updateReceivabelAutoMatchButtonStatus(User.systemUser("93387"), AccountPayableSwitchEnum.NOT_OPEN.getStatus());

    }

    @Test
    public void queryTest() {
        QueryReceivableAutoMatchStatus.Arg arg = new QueryReceivableAutoMatchStatus.Arg();
        accountsReceivableNoteService.queryAutoMatchStatus(arg);

    }

    protected int updateReceivabelAutoMatchButtonStatus(User user, int status) {
        try {
            int result;
            String config = bizConfClient
                    .queryConfigByRank(QueryConfigByRankArg.builder().rank(Rank.TENANT).key(AccountsReceivableConstants.ACCOUNTS_RECEIVABLE_AUTO_MATCH_BUTTON_SWITCH_KEY).tenantId(user.getTenantId()).pkg(AccountsReceivableConstants.SWITCH_PKG).build());
            if (StringUtils.isBlank(config)) {
                ConfigArg configArg = buildConfigArg(user, AccountsReceivableConstants.ACCOUNTS_RECEIVABLE_AUTO_MATCH_BUTTON_SWITCH_KEY, String.valueOf(status));
                result = bizConfClient.createConfig(configArg);

            } else {
                if (Objects.equals(String.valueOf(status), config)) {
                    return AccountPayableSwitchEnum.OPENING.getStatus();
                }
                result = bizConfClient.updateConfig(buildConfigArg(user, AccountsReceivableConstants.ACCOUNTS_RECEIVABLE_AUTO_MATCH_BUTTON_SWITCH_KEY, String.valueOf(status)));
            }
            return result;
        } catch (Exception e) {

            throw new MetaDataBusinessException("updateReceivabelAutoMatchButtonStatus error");
        }
    }
    public ConfigArg buildConfigArg(User user, String key, String value) {
        return ConfigArg.builder().key(key).operator(user.getUserId()).pkg("CRM").userId(user.getUserId()).tenantId(user.getTenantId()).valueType(ValueType.STRING).value(value).rank(Rank.TENANT).build();
    }
}
