package com.facishare.crm.fmcg.tpm;

import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountFields;
import com.facishare.crm.fmcg.tpm.business.BudgetCalculateService;
import com.facishare.paas.appframework.core.model.User;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

public class BudgetAccountTest extends BaseTest {

    @Resource
    private BudgetCalculateService budgetCalculateService;


    @Test
    public void testUpdateAmount(){
        Map<String, BigDecimal> map = new HashMap<>();
        map.put(TPMBudgetAccountFields.FROZEN_AMOUNT,new BigDecimal(100));
        budgetCalculateService.updateBudgetAmountFields(User.systemUser("84931"),"64058756cbcf4b0001b5234e",map);
    }
}
