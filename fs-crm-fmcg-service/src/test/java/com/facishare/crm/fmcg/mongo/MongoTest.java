package com.facishare.crm.fmcg.mongo;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.BaseTest;
import com.facishare.crm.fmcg.tpm.business.dto.FilterDTO;
import com.facishare.crm.fmcg.tpm.dao.mongo.CommonDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RetryTaskPO;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Author: linmj
 * Date: 2023/10/31 17:44
 */
public class MongoTest extends BaseTest {

    @Resource
    private CommonDAO commonDAO;


    @Test
    public void query() {
        List<FilterDTO> filterList = new ArrayList<>();
        FilterDTO filter = new FilterDTO();
        filter.setFieldName(RetryTaskPO.F_NAME);
        filter.setOperator(Operator.EQ.name());
        filter.setFieldValues(Lists.newArrayList("更新奖励记录状态任务_RedPacketRecordObj_6530958ddc29a60001bb475c"));
        filterList.add(filter);

        System.out.println(JSON.toJSONString(commonDAO.query("RetryTaskPO", filterList, 10, 0)));
    }
}
