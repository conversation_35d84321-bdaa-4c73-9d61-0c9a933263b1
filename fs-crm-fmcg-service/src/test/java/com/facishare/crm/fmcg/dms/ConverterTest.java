package com.facishare.crm.fmcg.dms;

import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.crm.fmcg.dms.service.converter.AutoReceivableConverter;
import com.facishare.crm.fmcg.tpm.BaseTest;
import org.junit.Test;

import javax.annotation.Resource;

public class ConverterTest extends BaseTest {

    @Resource
    private AutoReceivableConverter autoReceivableConverter;

    @Test
    public void convertTest() {
        FinancialBill bill = FinancialBill.builder()
                .tenantId("89150")
                .apiName("GoodsReceivedNoteObj")
                .id("67c00b1bae5c3d00016b51b5")
                .build();

        autoReceivableConverter.convert(bill);
    }
}
