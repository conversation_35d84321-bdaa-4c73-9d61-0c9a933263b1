package com.facishare.crm.fmcg.tpm;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetStatisticTableService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.Uninterruptibles;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @date 2021/12/28 上午11:51
 */
public class BudgetStatisticsTableTest extends BaseTest {

    @Autowired
    private ServiceFacade serviceFacade;

    @Resource
    private IBudgetStatisticTableService budgetStatisticTableService;

    @Test
    public void asyncDoStatisticTest() {

        Uninterruptibles.sleepUninterruptibly(5, TimeUnit.SECONDS);
        List<String> ids = Lists.newArrayList("63ef2fe2ebb75e0001ec392b");
        List<IObjectData> objectDataByIds = serviceFacade.findObjectDataByIds("85494", ids, ApiNames.TPM_BUDGET_ACCOUNT);
        budgetStatisticTableService.asyncDoStatistic("85494", objectDataByIds);
    }
}
