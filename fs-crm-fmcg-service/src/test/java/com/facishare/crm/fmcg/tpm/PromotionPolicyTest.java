package com.facishare.crm.fmcg.tpm;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.web.contract.StatisticPolicyOccupy;
import com.facishare.crm.fmcg.tpm.web.service.PromotionPolicyService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * author: wuyx
 * description:
 * createTime: 2023/6/30 11:09
 */
public class PromotionPolicyTest extends BaseTest {


    @Resource
    private PromotionPolicyService promotionPolicyService;

    @Resource
    protected ServiceFacade serviceFacade;

    @Test
    public void isOpenPromotionPolicyTest() {
        Boolean promotionPolicy = promotionPolicyService.isOpenPromotionPolicy("80063");
        System.out.println(promotionPolicy);
    }

    @Test
    public void findObjectDataIncludeDeletedTest() {

        List<IObjectData> activity = serviceFacade.findObjectDataByIdsIncludeDeleted(User.systemUser("84931"), Lists.newArrayList("64b636b1763d880001d0fdc1"), ApiNames.TPM_ACTIVITY_OBJ);
        System.out.println(activity);
    }

    @Test
    public void statisticPolicyOccupyTest() {

        String data = "{\n" +
                "    \"dataList\":[\n" +
                "        {\n" +
                "            \"accountId\":\"64b7a1c7ff8e0100013d08d8\",\n" +
                "            \"activityId\":\"650014276655750001b7245f\",\n" +
                "            \"occupy\":1,\n" +
                "            \"limitObjType\":\"all\",\n" +
                "            \"detailLimitId\":\"650014a7f395b80001244f41\",\n" +
                "            \"accountMode\":\"EQUAL\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"tenantId\":\"84931\",\n" +
                "    \"userId\":\"1001\"\n" +
                "}";

        StatisticPolicyOccupy.Arg policyOccupyObj = JSON.parseObject(data.getBytes(), StatisticPolicyOccupy.Arg.class);

        StatisticPolicyOccupy.Arg arg = new StatisticPolicyOccupy.Arg();
        arg.setDataList(policyOccupyObj.getDataList());

        StatisticPolicyOccupy.Result result = promotionPolicyService.statisticPolicyOccupy(arg);
        System.out.println(result);
    }
}

