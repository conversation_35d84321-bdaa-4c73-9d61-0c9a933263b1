package com.facishare.crm.fmcg.tpm;

import com.facishare.crm.fmcg.tpm.web.contract.UpdateActivityType;
import com.facishare.crm.fmcg.tpm.web.contract.UpdateRedPacketExpire;
import com.facishare.crm.fmcg.tpm.web.manager.BudgetConsumeRuleManager;
import com.facishare.crm.fmcg.tpm.web.tools.ModuleInitializationService;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/6/5 22:12
 */
public class ModuleInitializationServiceTest extends BaseTest {

    @Resource
    private ModuleInitializationService moduleInitializationService;

    @Resource
    private BudgetConsumeRuleManager budgetConsumeRuleManager;

    @Test
    public void addClosureComponentsTest() {
        budgetConsumeRuleManager.addClosureComponents("85494", 1000, "object_NjN3C__c");
    }

    @Test
    public void deletePlugin() {

        budgetConsumeRuleManager.deleteUseLessPlugin("84931", "tpm_budget_consume");
    }

    @Test
    public void disableAgreementStoreConfirmModuleTest() {
        moduleInitializationService.disableAgreementStoreConfirmModule("80063");
    }

    @Test
    public void upgradeFor810Test() {
        moduleInitializationService.upgradeFor810("84931");
    }

    @Test
    public void rollbackFor810Test() {
        moduleInitializationService.rollbackFor810("84931");
    }

    @Test
    public void disableSalesOrderConfirmModuleTest() {
        moduleInitializationService.disableSalesOrderConfirmModule("80063");
    }

    @Test
    public void initSalesOrderProductBatchFieldsTest() {
        moduleInitializationService.initSalesOrderTPMFields("80063");
    }

    @Test
    public void initAllTenantSalesOrderTPMFieldsTest() {
        moduleInitializationService.initAllTenantSalesOrderTPMFields();
    }

    @Test
    public void initSalesOrderTPM815FieldsTest() {
        moduleInitializationService.initSalesOrderTPM815Fields("84931");
    }

    @Test
    public void initAllTenantSalesOrderTPM815FieldsTest() {
        moduleInitializationService.initAllTenantSalesOrderTPM815Fields();
    }

    @Test
    public void disableBudgetObjectDescribeTest() {
        moduleInitializationService.disableBudgetObjectDescribe("80063");
    }

    @Test
    public void oneMoreOrderTest() {
        moduleInitializationService.initOneMoreOrderModule("79568");
    }

    @Test
    public void fixActivityStatus() {
        List<String> objects = Lists.newArrayList();
        objects.add("64c863d3a6f7d200015a46a9");
        //in_progress
        moduleInitializationService.fixActivityStatus("84931", objects, "in_progress");
    }


    @Test
    public void updateActivityType() {
        UpdateActivityType.Arg arg = new UpdateActivityType.Arg();
        arg.setTenantId("82958");
        arg.setEmployeeId(1000);
        arg.setDataIds(Lists.newArrayList("6593ab60628d4700017e984c"));
        arg.setParam("0");
        //in_progress
        moduleInitializationService.updateActivityType(arg);
    }

    @Test
    public void updateRedPacketExpire() {
        UpdateRedPacketExpire.Arg arg = new UpdateRedPacketExpire.Arg();
        arg.setTenantId("82958");
        arg.setActivityId("6627668093bca90001f56ce1");
        // 1725465600000 9月 5 号 0点
        arg.setExpireTime(1725120000000L);  // 1 号 0点
        arg.setStatus("expired");
        arg.setDataIds(Lists.newArrayList("662785d9eb74090001db35e4"));
        //in_progress
        moduleInitializationService.updateRedPacketExpire(arg);
    }


}
