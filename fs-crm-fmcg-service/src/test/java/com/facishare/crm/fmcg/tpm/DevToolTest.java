package com.facishare.crm.fmcg.tpm;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.web.contract.CopyActivityType;
import com.facishare.crm.fmcg.tpm.web.contract.FixProofDetail;
import com.facishare.crm.fmcg.tpm.web.contract.QueryDataBySql;
import com.facishare.crm.fmcg.tpm.web.contract.UpdateDataDescribe;
import com.facishare.crm.fmcg.tpm.web.tools.abstraction.IDevToolService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.license.Result.LicenseVersionResult;
import com.facishare.paas.license.arg.QueryProductArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.http.LicenseClient;
import com.fxiaoke.enterpriserelation2.arg.CreateChannelWebNavigationArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.service.ChannelWebNavigationService;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * author: wuyx
 * description:
 * createTime: 2023/8/3 20:46
 */
public class DevToolTest extends BaseTest {

    @Resource
    private IDevToolService devToolService;
    @Resource
    private ChannelWebNavigationService channelWebNavigationService;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private LicenseClient licenseClient;

    @Test
    public void grayManyAbstractLayoutFilterConfigTest() {
        String s = devToolService.grayManyAbstractLayoutFilterConfig();
        System.out.println(s);
    }

    @Test
    public void calculateCountTest(){
//        FieldResult fieldDescribe = serviceFacade.findCustomFieldDescribe("84931", ApiNames.TPM_ACTIVITY_PROOF_OBJ, TPMActivityProofFields.TOTAL);
//
//        CountFieldDescribe totalDesc = new CountFieldDescribe(ObjectFieldDescribeDocument.of(fieldDescribe.getField()));
//
//        Map<String, Object> calculateMap = serviceFacade.calculateCountField(User.systemUser("84931"), ApiNames.TPM_ACTIVITY_PROOF_OBJ, "65a664ff9ace400001c4c53d", Lists.newArrayList(totalDesc));
//
//        System.out.println(calculateMap);
//
//        User user = User.systemUser("84931");
//        IObjectData objectData = serviceFacade.findObjectData(user, "65a8d5ac8e19026804b8aea4", "TPMActivityProofDetailObj");
//        serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(Lists.newArrayList(objectData), user);



        FixProofDetail.Arg arg = new FixProofDetail.Arg();
        arg.setTenantIds(Lists.newArrayList("84931"));
//        devToolService.fixProofDetail(arg);

        devToolService.fixAgreementStatus(arg);

    }

    @Test
    public void queryDataBySql(){
        QueryDataBySql.Arg arg = new QueryDataBySql.Arg();
        arg.setTenantIds(Lists.newArrayList("84931"));

        String sql2 = "select count(1) \n" +
                "\tfrom fmcg_tpm_activity_proof\n" +
                "\twhere is_deleted = 0 and tenant_id = '#{tenant_id}'";
        arg.setSql(sql2);
        String s = devToolService.queryDataBySql(arg);
        System.out.println(s);

    }



    @Test
    public void initChannelWebNavigationWithLinkAppOpenTest() {

        String linkAppId = ConfigFactory.getConfig("fs-appserver-openproxy-appinfo").get("tpm_link_app_id");

        CreateChannelWebNavigationArg initChannelsArg = new CreateChannelWebNavigationArg();
        initChannelsArg.setName("web端默认导航");
        initChannelsArg.setPriorityLevel(9);
        initChannelsArg.setNavigationType(1);
        initChannelsArg.setShowAppName(true);

        CreateChannelWebNavigationArg.UseScopeData useScopeData = new CreateChannelWebNavigationArg.UseScopeData();
        useScopeData.setIsAll(false);
        useScopeData.setOuterRoleIds(Lists.newArrayList(new CreateChannelWebNavigationArg.UseScopeData.ScopeData("00000000000000000000000000123456", "TPM厂商人员")));
        initChannelsArg.setUseScope(useScopeData);

        CreateChannelWebNavigationArg.LinkAppData linkAppData = new CreateChannelWebNavigationArg.LinkAppData();
        linkAppData.setAppId(linkAppId);
        linkAppData.setAppName("营销活动");
        linkAppData.setMenuName("营销活动");
        linkAppData.setAppType(1);
        linkAppData.setOrder(0);
        linkAppData.setIsDefaultMenu(1);
        initChannelsArg.setBindAppList(Lists.newArrayList(linkAppData));

        initChannelsArg.setShowMoreApp(0);

        RestResult<Boolean> result = channelWebNavigationService.initChannelWebNavigationWithLinkAppOpen(HeaderObj.newInstance(84931), initChannelsArg);
        System.out.println(result);
    }

    @Test
    public void fsPaasFilterConfigFmcgTest() {
        String fileStr = "{\n" +
                "  \"businessFilterConfig\": {\n" +
                "    \"TPMDealerActivityCostObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"is_master\": 1,\n" +
                "            \"ui_event\": 1,\n" +
                "            \"layout_rule_page\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"AreaManageObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"ui_event\": 1,\n" +
                "            \"mobile_list_program\": 1,\n" +
                "            \"mobile_list_layout\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"ProjectStandardsObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"TPMBudgetAccountObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [\n" +
                "            \"84931\"\n" +
                "          ],\n" +
                "          \"data\": {\n" +
                "            \"ui_event\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"TPMActivityItemCostStandardObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"ui_event\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"TPMStoreWriteOffObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"is_master\": 1,\n" +
                "            \"ui_event\": 1,\n" +
                "            \"mobile_list_layout\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"ExpenseClaimFormObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"is_master\": 1,\n" +
                "            \"ui_event\": 1,\n" +
                "            \"mobile_list_layout\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"ExpenseClaimFormDetailObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"is_master\": 1,\n" +
                "            \"ui_event\": 1,\n" +
                "            \"mobile_list_layout\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"PointsGoodsObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"ui_event\": 1,\n" +
                "            \"mobile_list_layout\": 1,\n" +
                "            \"display_name\": 1,\n" +
                "            \"layout_rule_page\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"filters\": {\n" +
                "        \"submodules\": {\n" +
                "          \"button_field_update\": [\n" +
                "            {\n" +
                "              \"tenants\": [],\n" +
                "              \"data\": {\n" +
                "                \"label\": \"按钮字段变更的所需字段\",\n" +
                "                \"rule_outside\": [\n" +
                "                  {\n" +
                "                    \"value_type\": \"api_name\",\n" +
                "                    \"contain\": true,\n" +
                "                    \"value\": [\n" +
                "                      \"state\",\n" +
                "                      \"price\"\n" +
                "                    ]\n" +
                "                  }\n" +
                "                ]\n" +
                "              },\n" +
                "              \"group\": \"default\"\n" +
                "            }\n" +
                "          ]\n" +
                "        }\n" +
                "      }\n" +
                "    },\n" +
                "    \"TPMActivityStoreObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"ui_event\": 1,\n" +
                "            \"layout_rule_page\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"TPMActivityProofAuditObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"is_master\": 1,\n" +
                "            \"ui_event\": 1,\n" +
                "            \"layout_rule_page\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"TPMActivityProofObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"is_master\": 1,\n" +
                "            \"ui_event\": 1,\n" +
                "            \"layout_rule_page\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"TPMActivityBudgetObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"form_many\": 1,\n" +
                "            \"is_master\": 1,\n" +
                "            \"ui_event\": 1,\n" +
                "            \"mobile_list_program\": 1,\n" +
                "            \"field_tag\": 1,\n" +
                "            \"mobile_list_layout\": 1,\n" +
                "            \"display_name\": 1,\n" +
                "            \"layout_rule_page\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"TPMActivityBudgetDetailObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"ui_event\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"ProductCollectionObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"ui_event\": 1,\n" +
                "            \"layout_rule_page\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"StockReportingDetailsObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"ShiftsObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"is_master\": 1,\n" +
                "            \"ui_event\": 1,\n" +
                "            \"layout_rule_page\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"fieldConfig\": {\n" +
                "        \"ShiftsObj\": {\n" +
                "          \"owner\": [\n" +
                "            {\n" +
                "              \"data\": {\n" +
                "                \"ui_event\": 1\n" +
                "              }\n" +
                "            }\n" +
                "          ]\n" +
                "        }\n" +
                "      }\n" +
                "    },\n" +
                "    \"TPMActivityBudgetAdjustObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"is_master\": 1,\n" +
                "            \"ui_event\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"TPMActivityItemObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"is_master\": 1,\n" +
                "            \"ui_event\": 1,\n" +
                "            \"layout_rule_page\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"SuccessfulStoreRangeObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"ui_event\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"TPMDealerActivityObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"is_master\": 1,\n" +
                "            \"layout_rule_page\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"TPMActivityDetailObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"ui_event\": 1,\n" +
                "            \"layout_rule_page\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"TPMActivityProofDetailObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"ui_event\": 1,\n" +
                "            \"layout_rule_page\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"CheckinsObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"ui_event\": 1,\n" +
                "            \"display_name\": 1,\n" +
                "            \"layout_rule_page\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"filters\": {\n" +
                "        \"submodules\": {\n" +
                "          \"button_field_update\": [\n" +
                "            {\n" +
                "              \"tenants\": [\n" +
                "                \"85023\",\n" +
                "                \"83150\"\n" +
                "              ],\n" +
                "              \"data\": {\n" +
                "                \"label\": \"按钮入参字段\",\n" +
                "                \"rule_outside\": [\n" +
                "                  {\n" +
                "                    \"contain\": true,\n" +
                "                    \"value_type\": \"define_type\",\n" +
                "                    \"value\": [\n" +
                "                      \"system\"\n" +
                "                    ]\n" +
                "                  },\n" +
                "                  {\n" +
                "                    \"contain\": true,\n" +
                "                    \"value_type\": \"define_type\",\n" +
                "                    \"value\": [\n" +
                "                      \"package\"\n" +
                "                    ]\n" +
                "                  }\n" +
                "                ]\n" +
                "              },\n" +
                "              \"group\": \"default\"\n" +
                "            }\n" +
                "          ]\n" +
                "        }\n" +
                "      }\n" +
                "    },\n" +
                "    \"TPMActivityAgreementDetailObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"ui_event\": 1,\n" +
                "            \"mobile_list_layout\": 1,\n" +
                "            \"layout_rule_page\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"ShelfReportObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"TPMActivityAgreementObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [\n" +
                "            78612\n" +
                "          ],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"is_master\": 1,\n" +
                "            \"ui_event\": 1,\n" +
                "            \"layout_rule_page\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"is_master\": 1,\n" +
                "            \"ui_event\": 1,\n" +
                "            \"mobile_list_layout\": 1,\n" +
                "            \"layout_rule_page\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"PurchaseReportingObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"ChannelObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"display_name\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"TPMActivityObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"is_master\": 1,\n" +
                "            \"ui_event\": 1,\n" +
                "            \"layout_rule_page\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"MustDistributeProductsObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"StockReportingObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"ui_event\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"ShelfReportDetailObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"TPMActivityProofAuditDetailObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"ui_event\": 1,\n" +
                "            \"layout_rule_page\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"PurchaseDetailsObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"PointsExchangeRecordObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"ui_event\": 1,\n" +
                "            \"mobile_list_layout\": 1,\n" +
                "            \"display_name\": 1,\n" +
                "            \"layout_rule_page\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"filters\": {\n" +
                "        \"submodules\": {\n" +
                "          \"button_field_update\": [\n" +
                "            {\n" +
                "              \"tenants\": [],\n" +
                "              \"data\": {\n" +
                "                \"label\": \"按钮字段变更的所需字段\",\n" +
                "                \"rule_outside\": [\n" +
                "                  {\n" +
                "                    \"value_type\": \"api_name\",\n" +
                "                    \"contain\": true,\n" +
                "                    \"value\": [\n" +
                "                      \"order_state\"\n" +
                "                    ]\n" +
                "                  }\n" +
                "                ]\n" +
                "              },\n" +
                "              \"group\": \"default\"\n" +
                "            }\n" +
                "          ]\n" +
                "        }\n" +
                "      }\n" +
                "    },\n" +
                "    \"TPMBudgetBusinessSubjectObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"display_name\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"TPMBudgetAccountDetailObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"ui_event\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"TPMBudgetStatisticTableObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"ui_event\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"TPMBudgetAccrualObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"ui_event\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"TPMBudgetDisassemblyObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"ui_event\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"TPMBudgetTransferDetailObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"ui_event\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"TPMActivityUnifiedCaseObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"is_master\": 1,\n" +
                "            \"edit_layout\": 1,\n" +
                "            \"ui_event\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"TPMBudgetCarryForwardObj\": {\n" +
                "      \"business\": [\n" +
                "        {\n" +
                "          \"tenants\": [],\n" +
                "          \"data\": {\n" +
                "            \"is_master\": 1,\n" +
                "            \"ui_event\": 1\n" +
                "          },\n" +
                "          \"group\": \"default\"\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  }\n" +
                "}";
        String s = devToolService.fsPaasFilterConfigFmcg(fileStr);
        System.out.println(s);
    }

    @Test
    public void deleteAllActivityTypeOptionTest() {
        devToolService.deleteAllActivityTypeOption("84846");
    }

    @Test
    public void updateActivityLayoutWithDetail() {
        devToolService.updateActivityUnifiedCaseLayoutWithDetail("89150", "TPMActivityUnifiedCaseObj", Lists.newArrayList("参与方案经销商范围", "方案兑付产品范围"));
    }

    @Test
    public void deleteObjByApiNameTest() {
        String s = devToolService.deleteObjByApiName("89405", Lists.newArrayList());
        System.out.println(s);
    }

    @Test
    public void deleteTPMObjDataTest() {
        String s = devToolService.deleteTPMObjData("89405");
        System.out.println(s);
    }

    @Test
    public void copyActivityTypeTest() {
        CopyActivityType.Arg arg = new CopyActivityType.Arg();
        arg.setFromTenantId("90100");
        arg.setToTenantIds(Lists.newArrayList(""));
        arg.setActivityTypeId("657179dcc153220001594945");
        arg.setMengniuTenantId("84931");

        devToolService.copyActivityType(arg);
    }

    @Test
    public void queryProductVersionTest() {

        QueryProductArg queryProductVersion = new QueryProductArg();
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setTenantId("");
        licenseContext.setAppId("");
        licenseContext.setUserId("");
        licenseContext.setProperties(Maps.newHashMap());
        licenseContext.setObjectProperties(Maps.newHashMap());

        queryProductVersion.setLicenseContext(licenseContext);
        queryProductVersion.setProductVersion("");
        queryProductVersion.setModuleCode("trade_promotion_management_app");
        queryProductVersion.setParaKeys(Sets.newHashSet());

        LicenseVersionResult result = licenseClient.queryProductVersion(queryProductVersion);
        System.out.println(JSON.toJSONString(result.getResult()));
    }

    @Test
    public void updateLayoutTest() {
        //ILayout iLayout = serviceFacade.deleteLayout(User.builder().tenantId("85494").build(), "66f13a1f8582140001f6747b");

        ILayout layout = serviceFacade.getLayoutLogicService().findDefaultLayout(User.systemUser("85494"),"list_layout","TPMBudgetBusinessSubjectObj");
        System.out.println(layout);

        ILayout layout84931 = serviceFacade.getLayoutLogicService().findDefaultLayout(User.systemUser("84931"),"list_layout","TPMBudgetBusinessSubjectObj");
        System.out.println(layout84931);

        String components = "{\n" +
                "            \"type\": \"list\",\n" +
                "            \"api_name\": \"list_component\",\n" +
                "            \"header\": \"列表页\",\n" +
                "            \"nameI18nKey\": \"paas.udobj.list_page\",\n" +
                "            \"view_info\": [\n" +
                "                {\n" +
                "                    \"name\": \"list_view\",\n" +
                "                    \"is_default\": true\n" +
                "                },\n" +
                "                {\n" +
                "                    \"name\": \"split_view\",\n" +
                "                    \"is_default\": false\n" +
                "                }\n" +
                "            ],\n" +
                "            \"filters_info\": [],\n" +
                "            \"button_info\": [\n" +
                "                {\n" +
                "                    \"hidden\": [],\n" +
                "                    \"page_type\": \"list\",\n" +
                "                    \"render_type\": \"list_batch\",\n" +
                "                    \"order\": []\n" +
                "                },\n" +
                "                {\n" +
                "                    \"hidden\": [],\n" +
                "                    \"page_type\": \"list\",\n" +
                "                    \"render_type\": \"list_normal\",\n" +
                "                    \"order\": []\n" +
                "                },\n" +
                "                {\n" +
                "                    \"hidden\": [],\n" +
                "                    \"page_type\": \"list\",\n" +
                "                    \"render_type\": \"list_single\",\n" +
                "                    \"order\": []\n" +
                "                }\n" +
                "            ]\n" +
                "        }";



        String structure = "{\n" +
                "        \"layout\": [\n" +
                "            {\n" +
                "                \"columns\": [\n" +
                "                    {\n" +
                "                        \"width\": \"100%\"\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"components\": [\n" +
                "                    []\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"columns\": [\n" +
                "                    {\n" +
                "                        \"width\": \"100%\"\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"components\": [\n" +
                "                    [\n" +
                "                        \"list_component\"\n" +
                "                    ]\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }";
        layout.setLayoutStructure(JSON.parseObject(structure));
        ILayout iLayout1 = serviceFacade.getLayoutLogicService().updateLayout(User.builder().tenantId("85494").build(), layout);
        System.out.println(iLayout1);
    }
}
