package com.facishare.crm.fmcg.tf;

import com.facishare.crm.fmcg.tongfu.AuthService;
import com.facishare.crm.fmcg.tongfu.model.Auth;
import com.facishare.crm.fmcg.tpm.BaseTest;
import org.junit.Test;

import javax.annotation.Resource;

public class AuthTest extends BaseTest {

    @Resource
    private AuthService authService;

    @Test
    public void auth() {
        Auth.Arg arg = new Auth.Arg();
        Auth.Result result = authService.auth(arg);
        assert result != null;
    }
}
