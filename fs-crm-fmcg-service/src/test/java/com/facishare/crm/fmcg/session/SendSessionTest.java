package com.facishare.crm.fmcg.session;

import com.fxiaoke.model.TextCardElement;
import com.fxiaoke.model.*;
import com.fxiaoke.Utils.ReceiverChannelUtils;
import com.fxiaoke.constant.ReceiverChannelType;

import com.facishare.crm.fmcg.tpm.BaseTest;
import com.facishare.crm.fmcg.tpm.session.SendMessageService;
import com.facishare.crm.fmcg.tpm.session.SessionSendService;
import com.facishare.crm.fmcg.tpm.session.model.SessionContentSyncErrorActivity;
import com.facishare.crm.fmcg.tpm.task.ActivityNodeTemplateValidationTaskService;
import com.facishare.crm.fmcg.tpm.task.ActivityTypeValidationTaskService;
import com.facishare.crm.fmcg.tpm.web.contract.ActivityTask;
import com.fxiaoke.model.message.SendTextCardMessageArg;
import com.fxiaoke.model.message.SendTextMessageArg;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeableConfig;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;

/**
 * @author: wuyx
 * @description:
 * @createTime: 2022/1/7 10:27
 */
public class SendSessionTest extends BaseTest {

    @Resource
    private SessionSendService sessionSendService;

    @Resource
    private SendMessageService sendMessageService;

    @Autowired
    private ActivityNodeTemplateValidationTaskService activityNodeTemplateValidationTaskService;

    @Autowired
    private ActivityTypeValidationTaskService activityTypeValidationTaskService;

    @Test
    public void queryActivityTypeStatisticsDataTest() {
        List<Integer> employeeIds = Lists.newArrayList(1000);
        sessionSendService.sendTextMessageDefault("test发送消息001", "80063", employeeIds);
    }

    @Test
    public void doSendSessionTest() {
        List<Integer> employeeIds = Lists.newArrayList(1000);

        String nodeName = "自定义举证";
        String objectDisplayName = "自定义举证";
        String referenceFieldApiName = "活动方案";
        String errorMessage = "关联字段被禁用";
        // 发企信消息
        String message = String.format("%s活动节点中的【%s】关联【%s】对象的%s，" +
                        "导致该节点暂时无法使用。节点异常，会影响活动类型及活动方案的正常使用，请及时处理",
                nodeName, objectDisplayName, referenceFieldApiName, errorMessage);
        SessionContentSyncErrorActivity sessionContentSyncErrorActivity = new SessionContentSyncErrorActivity(
                nodeName,
                objectDisplayName,
                referenceFieldApiName,
                "",
                false,
                null);
        sessionContentSyncErrorActivity.setFirst(message);
        //sessionSendService.doSendSession("80063", employeeIds, sessionContentSyncErrorActivity, 0);
        sessionSendService.doSendSessionToSystemAdmins("80063", sessionContentSyncErrorActivity, 3);
    }

    @Test
    public void activityNodeTemplateListValidationTest() {
        ActivityTask.Arg arg = new ActivityTask.Arg();
        activityNodeTemplateValidationTaskService.activityNodeTemplateListValidation(arg);
    }

    @Test
    public void activityTypeListValidationTest() {
        ActivityTask.Arg arg = new ActivityTask.Arg();
        activityTypeValidationTaskService.activityTypeValidation(arg);
    }

    @Test
    public void getConfigTest() {
        IChangeableConfig config = ConfigFactory.getConfig("checkins-v2-config");
        System.out.println(config.get("xxl.job.app.name.tpm"));
    }

    @Test
    public void sendTextMessageTest() {

        String uuid = UUID.randomUUID().toString();
        SendTextMessageArg arg = new SendTextMessageArg();
        arg.setUuid(uuid);
        arg.setEi(80063);
        // 应用通知
        arg.setReceiverChannelType(ReceiverChannelType.NOTICE);
        arg.setReceiverChannelData(ReceiverChannelUtils.buildNoticeChannelData("TPM"));

        arg.setMessageContent("wuyx test Message");
        arg.setReceiverIds(Lists.newArrayList(1000));

        sendMessageService.sendTextMessage(arg);
    }

    @Test
    public void sendTextCardMessageTest() {

        String uuid = UUID.randomUUID().toString();
        SendTextCardMessageArg arg = new SendTextCardMessageArg();
        arg.setUuid(uuid);
        arg.setEi(80063);
        // 应用通知
        arg.setReceiverChannelType(ReceiverChannelType.NOTICE);
        arg.setReceiverChannelData(ReceiverChannelUtils.buildNoticeChannelData("TPM"));

        TextCardMessage textCardMessage = new TextCardMessage();

        TextCardMessageHead head = new TextCardMessageHead();
        head.setTitleElement(new TextCardElement("错误提醒", "#333333", ""));
        textCardMessage.setHead(head);

        TextCardMessageBody body = new TextCardMessageBody();
        body.setContentElement(new TextCardElement("活动类型或活动节点错误", "", ""));

        List<KeyValueItem> keyValueItems = Lists.newArrayList();
        keyValueItems.add(new KeyValueItem(new TextCardElement("报表名称：", "", ""), new TextCardElement("时间段范围", "", "")));
        keyValueItems.add(new KeyValueItem(new TextCardElement("运行时间：", "", ""), new TextCardElement("2019-03-06 02:45:08", "", "")));
        body.setForm(keyValueItems);
        body.setRemarkElement(new TextCardElement("订单备注", "", ""));

        textCardMessage.setBody(body);

        TextCardMessageFoot foot = new TextCardMessageFoot();
        foot.setFootElement(new TextCardElement("点击查看详情", "", ""));

        textCardMessage.setFoot(foot);
        textCardMessage.setInnerPlatformWebUrl("www.baidu.com");
        textCardMessage.setInnerPlatformMobileUrl("www.baidu.com");
        textCardMessage.setOutPlatformUrl("www.baidu.com");

        arg.setTextCardMessage(textCardMessage);
        arg.setReceiverIds(Lists.newArrayList(1000));

        sendMessageService.sendTextCardMessage(arg);
    }
}
