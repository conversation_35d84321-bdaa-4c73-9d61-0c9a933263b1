package com.facishare.crm.fmcg.tpm;


import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.web.contract.ListBudgetType;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IBudgetTypeService;
import org.junit.Test;

import javax.annotation.Resource;

public class BudgetTypeTest extends BaseTest {

    @Resource
    private IBudgetTypeService budgetTypeService;

    @Test
    public void listTest() {
        ListBudgetType.Arg arg = new ListBudgetType.Arg();
        arg.setScene(ApiNames.TPM_BUDGET_ACCOUNT);
        arg.setKeyword("");
        ListBudgetType.Result result = budgetTypeService.list(arg);
        assert result != null;
    }
}
