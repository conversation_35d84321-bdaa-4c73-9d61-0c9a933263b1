package com.facishare.crm.fmcg.tpm;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.RedPacketRecordObjFields;
import com.facishare.crm.fmcg.tpm.api.activity.TPMActivityScript;
import com.facishare.crm.fmcg.tpm.api.proof.BatchEnableV2;
import com.facishare.crm.fmcg.tpm.api.proof.CorrectActivityStatus;
import com.facishare.crm.fmcg.tpm.api.proof.Enable;
import com.facishare.crm.fmcg.tpm.controller.TPMActivityObjCorrectStatusController;
import com.facishare.crm.fmcg.tpm.controller.TPMActivityObjScriptController;
import com.facishare.crm.fmcg.tpm.controller.TPMActivityProofObjBatchEnableController;
import com.facishare.crm.fmcg.tpm.controller.TPMActivityProofObjEnableController;
import com.facishare.crm.fmcg.tpm.service.RedPacketRecordService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/6/20 13:22
 */
public class EnableControllerTest extends BaseTest {

    @Resource
    private ServiceFacade serviceFacade;

    private static final List<String> RESULT_FIELDS = Lists.newArrayList("tenant_id", "object_describe_api_name", "_id");
    private static final int MAX_QUERY_TIMES = 2000;

    @Resource
    private RedPacketRecordService redPacketRecordService;

    @Test
    public void accountUpdateTest() {
        RequestContext context = RequestContext.builder()
                .tenantId("80063")
                .ea("80063")
                .user(new User("80063", "1000"))
                .build();

/*        TPMActivityObjAccountUpdateController controller = new TPMActivityObjAccountUpdateController();
        controller.setServiceFacade(serviceFacade);
        controller.setControllerContext(new ControllerContext(context, ApiNames.TPM_ACTIVITY_OBJ, "AccountUpdate"));

        AccountUpdate.Arg arg = new AccountUpdate.Arg();
        arg.setName("test0");
        AccountUpdate.Result result = controller.service(arg);
        assert result != null;*/
    }

    @Test
    public void enableTest() {
        RequestContext context = RequestContext.builder()
                .tenantId("80063")
                .ea("80063")
                .user(new User("80063", "1000"))
                .build();

        TPMActivityProofObjEnableController controller = new TPMActivityProofObjEnableController();
        controller.setServiceFacade(serviceFacade);
        controller.setControllerContext(new ControllerContext(context, ApiNames.TPM_ACTIVITY_PROOF_OBJ, "Enable"));

        Enable.Arg arg = new Enable.Arg();
        arg.setStoreId("5fa6329eed75890001545656");
        arg.setUseCache(false);
        Enable.Result result = controller.service(arg);
        assert result.isEnable();
    }

    @Test
    public void batchEnableV2Test() {
        RequestContext context = RequestContext.builder()
                .tenantId("80063")
                .ea("80063")
                .user(new User("80063", "1000"))
                .build();

        TPMActivityProofObjBatchEnableController controller = new TPMActivityProofObjBatchEnableController();
        controller.setServiceFacade(serviceFacade);
        controller.setControllerContext(new ControllerContext(context, ApiNames.TPM_ACTIVITY_PROOF_OBJ, "BatchEnableV2"));

        BatchEnableV2.Arg arg = new BatchEnableV2.Arg();
        arg.setStoreIds(Lists.newArrayList("5fa6329eed75890001545656"));
        BatchEnableV2.Result result = controller.service(arg);
        assert result != null;
    }

    @Test
    public void correctStatusTest() {
        RequestContext context = RequestContext.builder()
                .tenantId("82958")
                .ea("82958")
                .user(new User("82958", "1000"))
                .build();

        TPMActivityObjCorrectStatusController controller = new TPMActivityObjCorrectStatusController();
        controller.setServiceFacade(serviceFacade);
        controller.setControllerContext(new ControllerContext(context, ApiNames.TPM_ACTIVITY_OBJ, "CorrectActivityStatus"));

        CorrectActivityStatus.Arg arg = new CorrectActivityStatus.Arg();
        CorrectActivityStatus.Result result = controller.service(arg);
        assert result != null;
    }


    @Test
    public void activityObjScriptTest() {
        RequestContext context = RequestContext.builder()
                .tenantId("84931")
                .ea("84931")
                .user(new User("84931", "1000"))
                .build();

        TPMActivityObjScriptController controller = new TPMActivityObjScriptController();
        controller.setServiceFacade(serviceFacade);
        controller.setControllerContext(new ControllerContext(context, ApiNames.TPM_ACTIVITY_OBJ, "Script"));

        TPMActivityScript.Arg arg = new TPMActivityScript.Arg();
        arg.setTenantIds(Lists.newArrayList("84931"));
        arg.setModule("init_reuse_provision_occupy_button");

        TPMActivityScript.Result result = controller.service(arg);
        assert result != null;
    }

    @Test
    public void test() {

        long now = System.currentTimeMillis();
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(RedPacketRecordObjFields.RED_PACKET_STATUS);
        statusFilter.setOperator(Operator.EQ);
        statusFilter.setFieldValues(Lists.newArrayList(RedPacketRecordObjFields.RedPacketStatus.EFFECTUATE));

        Filter expirationTimeFilter = new Filter();
        expirationTimeFilter.setFieldName(RedPacketRecordObjFields.EXPIRATION_TIME);
        expirationTimeFilter.setOperator(Operator.LT);
        expirationTimeFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        query.setFilters(Lists.newArrayList(expirationTimeFilter, statusFilter));
        User user = User.systemUser("82958");
        CommonUtils.queryDataWithoutOffset(serviceFacade, user, ApiNames.RED_PACKET_RECORD_OBJ, query, RESULT_FIELDS, MAX_QUERY_TIMES, dataList -> {
            redPacketRecordService.batchUpdateRedPacketStatus(user, dataList);
        });
    }

}
