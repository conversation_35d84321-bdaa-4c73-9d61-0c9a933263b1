package com.facishare.crm.fmcg.dms;

import org.junit.Test;

public class CodeTest {

    @Test
    public void convertTest() {
        String a = "https://m.mengniu.cn/CB/313231JPJ4FDDQT3P6S1B54GKCVF3TE6EF";
        String b = "https://m.mengniu.cn/CB/313231JPJ4FDDQT3P6S1B54GKCVF3TE6EF/";
        String c = "https://m.mengniu.cn/CB/111111111111111111";
        String d = "https://m.mengniu.cn/CB/111111111111111111/";

        System.out.println(fetchCode(a));
        System.out.println(fetchCode(b));
        System.out.println(fetchCode(c));
        System.out.println(fetchCode(d));
    }

    private String fetchCode(String code) {
        if (code.endsWith("/")) {
            code = code.substring(0, code.length() - 1);
        }
        return code.substring(code.lastIndexOf("/") + 1);
    }
}
