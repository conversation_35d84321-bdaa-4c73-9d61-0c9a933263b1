package com.facishare.crm.fmcg.tpm;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.NodeType;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;

import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.web.contract.*;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IActivityNodeTemplateService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wuyx
 * @description: test
 * @createTime: 2021/12/22 19:40
 */
public class ActivityNodeTemplateServiceTest extends BaseTest {
    @Resource
    private IActivityNodeTemplateService activityNodeTemplateService;

    @Autowired
    ServiceFacade serviceFacade;

    @Test
    public void addTest() {
        String js = "{\"activity_node_template\":{\"name\":\"活动协议22\",\"description\":\"\",\"object_api_name\":\"TPMActivityAgreementObj\",\"reference_field_api_name\":\"activity_id\",\"version\":\"0\"}}";
        AddActivityNodeTemplate.Arg arg = JSON.toJavaObject(JSON.parseObject(js),AddActivityNodeTemplate.Arg.class);
        System.out.println(JSON.toJSONString(activityNodeTemplateService.add(arg)));
    }

    @Test
    public void deleteTest() {
        DeleteActivityNodeTemplate.Arg arg = new DeleteActivityNodeTemplate.Arg();
        arg.setId("");
        System.out.println(JSON.toJSONString(activityNodeTemplateService.delete(arg)));
    }

    @Test
    public void editTest() {
        String js = "{\"activity_node_template\":{\"name\":\"活动协议\",\"description\":\"\",\"object_api_name\":\"TPMActivityAgreementObj\",\"reference_field_api_name\":\"activity_id\",\"version\":\"\",\"_id\":\"623b0c4e04e8f203db7c4f2c\"}}";
        EditActivityNodeTemplate.Arg arg = JSON.toJavaObject(JSON.parseObject(js),EditActivityNodeTemplate.Arg.class);
        System.out.println(JSON.toJSONString(activityNodeTemplateService.edit(arg)));
    }

    @Test
    public void getTest() {

        GetActivityNodeTemplate.Arg arg = new GetActivityNodeTemplate.Arg();
        arg.setId("623b048604e8f203db7c4f2b");
        System.out.println(JSON.toJSONString(activityNodeTemplateService.get(arg)));
    }

    @Test
    public void listTest() {
        ListActivityNodeTemplate.Arg arg = new ListActivityNodeTemplate.Arg();
        arg.setKeyword("");
        arg.setLimit(0);
        arg.setOffset(0);
        System.out.println(JSON.toJSONString(activityNodeTemplateService.list(arg)));
    }

    @Test
    public void enableObjects() {
        ActivityNodeTemplateEnableObjects.Arg arg = new ActivityNodeTemplateEnableObjects.Arg();
        System.out.println(JSON.toJSONString(activityNodeTemplateService.enableObjects(arg)));
    }

    @Test
    public void testFindBySearchQuery() {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
        activityFilter.setFieldValues(Lists.newArrayList("61c429f4c1e24312537c5af4"));
        activityFilter.setOperator(Operator.EQ);

        query.setFilters(Lists.newArrayList(activityFilter));
        query.setOrders(Lists.newArrayList());

        List<IObjectData> proofs = serviceFacade.findBySearchQuery(User.systemUser("80063"), ApiNames.TPM_ACTIVITY_OBJ, query).getData();
        System.out.println(JSON.toJSONString(proofs));

    }

    @Test
    public void testEnum(){
        NodeType[] values = NodeType.values();
        for (int i = 0; i < values.length; i++) {
            System.out.println(values[i].value());
        }
    }
}
