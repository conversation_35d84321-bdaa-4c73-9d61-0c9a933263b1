package com.facishare.crm.fmcg.tpm;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Sets;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.web.contract.*;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.service.ActivityTypeService;
import com.fmcg.framework.http.PaasLayoutProxy;
import com.fmcg.framework.http.contract.paas.layout.PaasFindAssignedLayout;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/21 17:59
 */
public class ActivityTypeServiceTest extends BaseTest {

    @Resource
    private ActivityTypeService activityTypeService;
    @Resource
    private IActivityTypeManager activityTypeManager;
    @Resource
    private PaasLayoutProxy paasLayoutProxy;

    @Test
    public void addTest() {

        String jsonStr = "{\"activity_type\":{\"_id\":\"\",\"name\":\"活动类型wyxtest001\",\"api_name\":\"type_jab5K__c\",\"status\":\"normal\",\"package\":\"custom\",\"description\":\"\",\"role_ids\":[],\"employee_ids\":[],\"department_ids\":[999999],\"activity_node_list\":[{\"name\":\"活动方案\",\"description\":\"\",\"package\":\"system\",\"object_api_name\":\"TPMActivityObj\",\"object_display_name\":\"活动方案\",\"reference_field_api_name\":\"\",\"type\":\"plan\",\"status\":\"normal\",\"exception_status\":\"normal\",\"version\":5,\"object_is_invalid\":false,\"reference_field_is_invalid\":false,\"related_activity_types\":[\"陈列活动类型\",\"SJ非协议类活动类型\",\"SJ特殊执行活动类型\",\"默认活动类型\",\"SJ按协议核销活动类型\",\"SJ抽检活动类型\",\"SJ标准个案活动类型\",\"SJ极简活动类型\",\"测试是否启用\",\"测试是否用002\",\"0113标准全流程活动类型\",\"0113特殊检核活动类型\",\"活动类型1\",\"测试类型异常专用\",\"SJ按场执行活动类型\",\"hw活动类型\",\"ycr测试全检活动类型t\",\"ycr测试抽检活动类型\",\"syf\",\"ycr平均值活动类型\",\"平均值活动类型\",\"活动申请费用活动类型\",\"函数计算费用核销活动类型\",\"函数计算活动类型\",\"SJ品鉴会活动类型\",\"活动类型1010\",\"无费用核销活动类型\",\"无核销依据的节点活动类型\",\"无费用核销的节点\",\"全检测试活动类型\",\"AFS活动类型\",\"AFS活动类型3\",\"抽检1\",\"活动类型test001\",\"活动类型test002\",\"活动类型\",\"SJ检核自定义对象的活动类型\",\"SJ0210极简活动类型\",\"znf活动类型F\",\"znf活动类型G\",\"znf活动类型J\",\"znf活动类型K\",\"znf活动类型L\",\"znf活动类型M\",\"znf活动类型N\",\"znf活动类型O\",\"活动类型123\",\"庆飞01\",\"庆飞03\",\"znf活动类型P\"],\"tenant_id\":\"80063\",\"creator\":1000,\"create_time\":1641973282221,\"last_updater\":1000,\"last_update_time\":1645760602440,\"is_deleted\":false,\"object_record_type\":\"default__c\",\"template_id\":\"61de8622b70f6a0eb8e5da1d\"},{\"name\":\"活动协议\",\"description\":\"预置\",\"package\":\"system\",\"object_api_name\":\"TPMActivityAgreementObj\",\"object_display_name\":\"活动协议\",\"reference_field_api_name\":\"activity_id\",\"type\":\"agreement\",\"status\":\"normal\",\"exception_status\":\"normal\",\"version\":6,\"object_is_invalid\":false,\"reference_field_is_invalid\":false,\"related_activity_types\":[\"陈列活动类型\",\"默认活动类型\",\"SJ按协议核销活动类型\",\"测试是否启用\",\"测试是否用002\",\"0113标准全流程活动类型\",\"测试类型异常专用\",\"hw活动类型\",\"ycr测试全检活动类型t\",\"ycr测试抽检活动类型\",\"syf\",\"ycr平均值活动类型\",\"平均值活动类型\",\"活动申请费用活动类型\",\"函数计算费用核销活动类型\",\"函数计算活动类型\",\"活动类型1010\",\"无费用核销活动类型\",\"无核销依据的节点活动类型\",\"无费用核销的节点\",\"全检测试活动类型\",\"AFS活动类型\",\"抽检1\",\"活动类型test001\",\"活动类型test002\",\"活动类型\",\"znf活动类型G\",\"活动类型123\"],\"tenant_id\":\"80063\",\"creator\":1000,\"create_time\":1641973282220,\"last_updater\":1000,\"last_update_time\":1645004476059,\"is_deleted\":false,\"object_record_type\":\"default__c\",\"template_id\":\"61de8622b70f6a0eb8e5da1e\"},{\"name\":\"活动举证\",\"description\":\"\",\"package\":\"system\",\"object_api_name\":\"TPMActivityProofObj\",\"object_display_name\":\"活动举证\",\"reference_field_api_name\":\"activity_id\",\"type\":\"proof\",\"status\":\"normal\",\"version\":0,\"object_is_invalid\":false,\"reference_field_is_invalid\":false,\"related_activity_types\":[\"陈列活动类型\",\"SJ非协议类活动类型\",\"默认活动类型\",\"SJ抽检活动类型\",\"SJ标准个案活动类型\",\"测试是否启用\",\"测试是否用002\",\"0113标准全流程活动类型\",\"测试类型异常专用\",\"SJ按场执行活动类型\",\"hw活动类型\",\"ycr测试全检活动类型t\",\"ycr测试抽检活动类型\",\"syf\",\"ycr平均值活动类型\",\"平均值活动类型\",\"活动申请费用活动类型\",\"函数计算费用核销活动类型\",\"函数计算活动类型\",\"SJ品鉴会活动类型\",\"活动类型1010\",\"无费用核销活动类型\",\"无核销依据的节点活动类型\",\"无费用核销的节点\",\"全检测试活动类型\",\"AFS活动类型\",\"抽检1\",\"活动类型test001\",\"活动类型test002\",\"活动类型\",\"znf活动类型F\",\"znf活动类型K\",\"znf活动类型L\",\"znf活动类型M\",\"znf活动类型N\",\"庆飞03\"],\"tenant_id\":\"80063\",\"creator\":1000,\"create_time\":1641973282219,\"last_updater\":1000,\"last_update_time\":1641973282219,\"is_deleted\":false,\"object_record_type\":\"default__c\",\"template_id\":\"61de8622b70f6a0eb8e5da1f\",\"activity_proof_config\":{\"frequency_config\":{\"frequency_type\":\"activity\",\"frequency_limit\":\"0\",\"limit_days\":[],\"limit_hours\":[]},\"cost_calculate_config\":{\"calculate_type\":\"default\",\"ratio\":1}}},{\"name\":\"活动检核\",\"description\":\"\",\"package\":\"system\",\"object_api_name\":\"TPMActivityProofAuditObj\",\"object_display_name\":\"活动举证检核\",\"reference_field_api_name\":\"activity_id\",\"type\":\"audit\",\"status\":\"normal\",\"version\":0,\"object_is_invalid\":false,\"reference_field_is_invalid\":false,\"related_activity_types\":[\"陈列活动类型\",\"SJ非协议类活动类型\",\"SJ特殊执行活动类型\",\"默认活动类型\",\"SJ抽检活动类型\",\"SJ标准个案活动类型\",\"测试是否启用\",\"测试是否用002\",\"0113标准全流程活动类型\",\"0113特殊检核活动类型\",\"测试类型异常专用\",\"SJ按场执行活动类型\",\"hw活动类型\",\"ycr测试全检活动类型t\",\"ycr测试抽检活动类型\",\"syf\",\"ycr平均值活动类型\",\"平均值活动类型\",\"活动申请费用活动类型\",\"函数计算费用核销活动类型\",\"函数计算活动类型\",\"SJ品鉴会活动类型\",\"活动类型1010\",\"无费用核销活动类型\",\"无核销依据的节点活动类型\",\"无费用核销的节点\",\"全检测试活动类型\",\"AFS活动类型\",\"抽检1\",\"活动类型test001\",\"活动类型test002\",\"活动类型\",\"SJ检核自定义对象的活动类型\",\"znf活动类型F\",\"znf活动类型K\",\"znf活动类型L\",\"znf活动类型M\",\"活动类型123\",\"庆飞03\",\"znf活动类型P\"],\"tenant_id\":\"80063\",\"creator\":1000,\"create_time\":1641973282218,\"last_updater\":1000,\"last_update_time\":1641973282218,\"is_deleted\":false,\"object_record_type\":\"default__c\",\"template_id\":\"61de8622b70f6a0eb8e5da20\",\"activity_proof_audit_config\":{\"audit_source_config\":{\"reference_audit_source_field_api_name\":\"activity_proof_id\",\"reference_audit_source_detail_field_api_name\":\"activity_proof_detail_id\",\"master_api_name\":\"TPMActivityProofObj\",\"detail_object_api_name\":\"TPMActivityProofDetailObj\",\"display_field_api_names_of_master\":[\"activity_agreement_id\",\"proof_images\",\"actual_total\"],\"display_field_api_names_of_detail\":[\"proof_item\",\"proof_detail_cost_standard\",\"amount\",\"subtotal\"],\"display_field_api_names_of_audit_detail\":[\"audit_amount\",\"audit_subtotal\"],\"display_field_api_names_of_audit_master\":[\"audit_images\",\"audit_total\",\"opinion\"],\"agreement_field_api_name\":\"activity_agreement_id\",\"dealer_field_api_name\":\"\",\"account_field_api_name\":\"\",\"activity_item_field_api_name\":\"\",\"use_complex_mode\":true,\"template_id\":\"61de8622b70f6a0eb8e5da1f\"},\"audit_mode_config\":{\"audit_mode\":\"all\"}}},{\"name\":\"费用核销\",\"description\":\"\",\"package\":\"system\",\"object_api_name\":\"TPMDealerActivityCostObj\",\"object_display_name\":\"费用核销\",\"reference_field_api_name\":\"activity_id\",\"type\":\"write_off\",\"status\":\"normal\",\"version\":0,\"object_is_invalid\":false,\"reference_field_is_invalid\":false,\"related_activity_types\":[\"陈列活动类型\",\"SJ非协议类活动类型\",\"SJ特殊执行活动类型\",\"默认活动类型\",\"SJ按协议核销活动类型\",\"SJ抽检活动类型\",\"SJ标准个案活动类型\",\"SJ极简活动类型\",\"测试是否启用\",\"测试是否用002\",\"0113标准全流程活动类型\",\"0113特殊检核活动类型\",\"活动类型1\",\"测试类型异常专用\",\"SJ按场执行活动类型\",\"hw活动类型\",\"ycr测试全检活动类型t\",\"ycr测试抽检活动类型\",\"syf\",\"ycr平均值活动类型\",\"平均值活动类型\",\"活动申请费用活动类型\",\"函数计算活动类型\",\"SJ品鉴会活动类型\",\"活动类型1010\",\"无核销依据的节点活动类型\",\"全检测试活动类型\",\"AFS活动类型\",\"抽检1\",\"活动类型test001\",\"活动类型test002\",\"活动类型\",\"SJ检核自定义对象的活动类型\",\"SJ0210极简活动类型\",\"znf活动类型F\",\"znf活动类型G\",\"znf活动类型J\",\"znf活动类型K\",\"znf活动类型L\",\"znf活动类型M\",\"znf活动类型N\",\"庆飞01\"],\"tenant_id\":\"80063\",\"creator\":1000,\"create_time\":*************,\"last_updater\":1000,\"last_update_time\":*************,\"is_deleted\":false,\"object_record_type\":\"default__c\",\"template_id\":\"61de8622b70f6a0eb8e5da21\",\"activity_write_off_config\":{\"write_off_source_config\":{\"calculate_type\":\"store_cost_total\",\"api_name\":\"TPMActivityProofAuditObj\",\"reference_write_off_field_api_name\":\"\",\"dealer_field_api_name\":\"\",\"account_field_api_name\":\"\",\"cost_field_api_name\":\"\",\"display_field_api_names\":[\"activity_proof_id\",\"proof_images\",\"total\",\"audit_status\",\"audit_total\"],\"template_id\":\"61de8622b70f6a0eb8e5da20\"},\"charge_up_config\":{\"charge_up_account_status\":false,\"charge_up_account_id\":\"\",\"charge_up_account_name\":\"\"}}}]}}";
        AddActivityType.Arg arg = JSON.toJavaObject(JSON.parseObject(jsonStr), AddActivityType.Arg.class);

        AddActivityType.Result result = activityTypeService.add(arg);
        Assert.assertNotNull(result);
    }

    @Test
    public void editTest() {
        String jsonStr = "{\"activity_type\":{\"name\":\"znf活动类型P\",\"api_name\":\"type_okpTw__c\",\"scope_description\":\"部门：全公司;\",\"description\":\"\",\"status\":\"normal\",\"exception_status\":\"error\",\"version\":13,\"package\":\"custom\",\"_id\":\"6213a66f0c96b702ea7b86dd\",\"tenant_id\":\"80063\",\"creator\":1000,\"create_time\":*************,\"last_updater\":1000,\"last_update_time\":*************,\"is_deleted\":false,\"role_ids\":[],\"employee_ids\":[],\"department_ids\":[999999],\"activity_node_list\":[{\"name\":\"活动方案\",\"template_id\":\"61de8622b70f6a0eb8e5da1d\",\"package\":\"system\",\"type\":\"plan\",\"object_api_name\":\"TPMActivityObj\",\"object_display_name\":\"活动方案\",\"object_record_type\":\"default__c\",\"description\":\"\",\"exception_status\":\"normal\",\"object_record_type_name\":\"统案活动\",\"reference_activity_field_api_name\":\"\",\"node_exception_info\":{\"object_reference_activity\":\"normal\",\"object_pre_node\":\"normal\"}},{\"name\":\"znf自定义节点\",\"template_id\":\"62022623f2da772f50ca9acd\",\"package\":\"custom\",\"type\":\"custom\",\"object_api_name\":\"object_5hzDL__c\",\"object_display_name\":\"znf自定义对象TPM\",\"object_record_type\":\"default__c\",\"description\":\"自定义\",\"exception_status\":\"normal\",\"object_record_type_name\":\"预设业务类型\",\"reference_activity_field_api_name\":\"field_ynW8U__c\",\"node_exception_info\":{\"object_reference_activity\":\"normal\",\"object_pre_node\":\"normal\"}},{\"name\":\"活动检核\",\"template_id\":\"61de8622b70f6a0eb8e5da20\",\"package\":\"system\",\"type\":\"audit\",\"object_api_name\":\"TPMActivityProofAuditObj\",\"object_display_name\":\"活动举证检核\",\"object_record_type\":\"default__c\",\"description\":\"\",\"exception_status\":\"error\",\"object_record_type_name\":\"预设业务类型\",\"reference_activity_field_api_name\":\"activity_id\",\"activity_proof_audit_config\":{\"audit_source_config\":{\"template_id\":\"62022623f2da772f50ca9acd\",\"use_complex_mode\":true,\"master_api_name\":\"object_5hzDL__c\",\"master_record_type\":\"default__c\",\"reference_activity_field_api_name\":\"field_ynW8U__c\",\"reference_audit_source_field_api_name\":\"field_Karbc__c\",\"reference_audit_source_detail_field_api_name\":\"\",\"detail_object_api_name\":\"\",\"display_field_api_names_of_master\":[\"create_time\",\"name\",\"field_ynW8U__c\",\"field_30bq9__c\",\"created_by\",\"field_QJN14__c\",\"field_k0r85__c\"],\"display_field_api_names_of_detail\":[\"proof_item\",\"proof_detail_cost_standard\",\"amount\",\"subtotal\"],\"display_field_api_names_of_audit_detail\":[\"audit_amount\",\"audit_subtotal\"],\"dealer_field_api_name\":\"\",\"agreement_field_api_name\":\"\",\"account_field_api_name\":\"field_30bq9__c\",\"audit_status_api_name\":\"audit_status__c\",\"activity_item_field_api_name\":\"\",\"display_field_api_names_of_audit_master\":[]},\"audit_mode_config\":{\"audit_mode\":\"all\"}},\"node_exception_info\":{\"object_reference_activity\":\"normal\",\"object_pre_node\":\"normal\"}}]}}";
        EditActivityType.Arg arg = JSON.toJavaObject(JSON.parseObject(jsonStr), EditActivityType.Arg.class);

        EditActivityType.Result result = activityTypeService.edit(arg);
        Assert.assertNotNull(result);
    }

    @Test
    public void getTest() {
        ApiContext context = ApiContextManager.getContext();
        context.setTenantId("83150");
//
//        GetActivityType.Arg arg = new GetActivityType.Arg();
//        arg.setId("6498fc7249a39d0001c748e8");
//
//        GetActivityType.Result result = activityTypeService.get(arg);

        ListActivityTypeForProofAi.Arg arg2 = new ListActivityTypeForProofAi.Arg();
        ListActivityTypeForProofAi.Result result1 = activityTypeService.listForPoofAi(arg2);
        System.out.println(result1);


//        Assert.assertNotNull(result);
    }

    @Test
    public void listEnableProofAI() {
        ApiContext context = ApiContextManager.getContext();
        context.setTenantId("83921");

        ActivityTypeForProofAiEnable.Arg arg2 = new ActivityTypeForProofAiEnable.Arg();
        arg2.setIds(Lists.newArrayList("67dce38089e24a0001297a56","681dd1b671894b000160e999","64425bb99dedae0001aec3e8"));
        ActivityTypeForProofAiEnable.Result result1 = activityTypeService.listEnableProofAI(arg2);
        System.out.println(result1);


//        Assert.assertNotNull(result);
    }


    @Test
    public void loadDesignerConfigTest() {
        LoadDesignerConfig.Arg arg = new LoadDesignerConfig.Arg();
//        arg.setId("61dba6d2279c5b5c562e61db");
        arg.setId("61dc265274112b62ffdb6432");
        LoadDesignerConfig.Result result = activityTypeService.loadDesignerConfig(arg);
        Assert.assertNotNull(result);
    }

    @Test
    public void findLayoutByRecordTypeTest() {
        ApiContextManager.getContext().setTenantId("85494");

        ValidActivityDateByRecordType.Arg arg = new ValidActivityDateByRecordType.Arg();
        arg.setObjectApiName("TPMActivityObj");
        arg.setRecordType("default__c");
        arg.setSlaveApiName("TPMActivityStoreObj");
        activityTypeService.validActivityDateByRecordType(arg);
    }

    @Test
    public void testFindLayoutByRecordType() {
        PaasFindAssignedLayout.Arg arg = new PaasFindAssignedLayout.Arg();
        arg.setDescribeApiName("TPMActivityObj");
        arg.setLayoutType("edit");
        PaasFindAssignedLayout.Result result = paasLayoutProxy.findAssignedLayout(84931, 1000, arg);
        PaasFindAssignedLayout.Content data = result.getData();
        String recordType = "";
        Set<String> layoutApiNames = Sets.newHashSet();
        for (PaasFindAssignedLayout.Role role : data.getRoleList()) {
            role.getRecordLayouts().stream().filter(v -> v.getRecordApiName().equals(recordType))
                    .map(PaasFindAssignedLayout.RecordLayout::getLayoutApiName).findFirst().ifPresent(layoutApiNames::add);
        }

        System.out.println(JSON.toJSONString(result));
    }


    @Test
    public void dataTypeTest() {
        DataActivityType.Arg arg = new DataActivityType.Arg();
//        arg.setId("61dba6d2279c5b5c562e61db");

        DataActivityType.Result result = activityTypeService.dataActivityType(arg);
        Assert.assertNotNull(result);
    }

    @Test
    public void deleteTest() {
        DeleteActivityType.Arg arg = new DeleteActivityType.Arg();
        arg.setId("61dc0b98db5eb44db2d85573");
        activityTypeService.delete(arg);
    }

    @Test
    public void tryInitSystemTemplateTest() {
        List<String> systemTemplate = activityTypeManager.tryInitSystemTemplate("89724", 1000);
        Assert.assertNotNull(systemTemplate);
    }

    @Test
    public void validActivityTypeTest() {
        ValidActivityType.Arg arg = new ValidActivityType.Arg();
        arg.setObjectApiName("TPMActivityObj");
        ValidActivityType.Result result = activityTypeService.validActivityType(arg);
        Assert.assertNotNull(result);
    }
}
