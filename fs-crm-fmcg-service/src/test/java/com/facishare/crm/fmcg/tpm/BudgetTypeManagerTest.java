package com.facishare.crm.fmcg.tpm;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetDimensionEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IBudgetTypeManager;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Scanner;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/6/5 22:12
 */
public class BudgetTypeManagerTest extends BaseTest {

    @Resource
    private IBudgetTypeManager budgetTypeManager;

    @Test
    public void publishSyncBudgetTypeFieldTaskTest() {
        budgetTypeManager.publishSyncBudgetTypeFieldTask("85494");
    }

    @Test
    public void createBudgetTypeReferenceTest() {
        BudgetDimensionEntity budgetDimension = new BudgetDimensionEntity();
        budgetDimension.setApiName("product_line__c");

        BudgetTypeNodeEntity budgetTypeNode = new BudgetTypeNodeEntity();
        budgetTypeNode.setDimensions(Lists.newArrayList(budgetDimension));
        budgetTypeNode.setControlDimensions(Lists.newArrayList(budgetDimension));

        BudgetTypePO budgetType = new BudgetTypePO();
        budgetType.setName("yx_test");
        budgetType.setApiName("yyds__c");
        budgetType.setNodes(Lists.newArrayList(budgetTypeNode));

        budgetTypeManager.publishSyncCreateBudgetTypeReference("84931", budgetType);
    }

    @Test
    public void deleteBudgetTypeReferenceTest() {

        BudgetDimensionEntity budgetDimension = new BudgetDimensionEntity();
        budgetDimension.setApiName("product_line__c");

        BudgetTypeNodeEntity budgetTypeNode = new BudgetTypeNodeEntity();
        budgetTypeNode.setDimensions(Lists.newArrayList(budgetDimension));
        budgetTypeNode.setControlDimensions(Lists.newArrayList(budgetDimension));

        BudgetTypePO budgetType = new BudgetTypePO();
        budgetType.setName("yx_test");
        budgetType.setApiName("yyds__c");
        budgetType.setNodes(Lists.newArrayList(budgetTypeNode));

        budgetTypeManager.publishSyncDeleteBudgetTypeReference("84931", budgetType);
    }

    @Test
    public void testPublish() throws InterruptedException {
        budgetTypeManager.publishSyncBudgetNodeFieldTask("84931");
        //Thread.sleep(100000000L);
    }
}
