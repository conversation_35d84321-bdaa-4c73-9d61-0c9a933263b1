package com.facishare.crm.fmcg.dms;

import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.crm.fmcg.dms.service.mapper.MatchableBillMapper;
import com.facishare.crm.fmcg.dms.service.matcher.AutoMatcher;
import com.facishare.crm.fmcg.tpm.BaseTest;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

public class MatcherTest extends BaseTest {

    @Resource
    private AutoMatcher autoMatcher;
    @Resource
    private MatchableBillMapper matchableBillMapper;

    @Test
    public void matchTest() {
        FinancialBill bill = FinancialBill.builder()
                .tenantId("89150")
                .apiName("AccountTransactionFlowObj")
                .id("650309abfa0df90001b998e4")
                .build();

        autoMatcher.match(bill);
    }

    @Test
    public void mapperTest() {
        FinancialBill bill = FinancialBill.builder()
                .tenantId("89275")
                .apiName("AccountsReceivableNoteObj")
                .id("6504358ae6d0e700015e91a2")
                .build();

        List<FinancialBill> bills = matchableBillMapper.map(bill);
        for (FinancialBill matchableBill : bills) {
            autoMatcher.match(matchableBill);
        }
    }
}