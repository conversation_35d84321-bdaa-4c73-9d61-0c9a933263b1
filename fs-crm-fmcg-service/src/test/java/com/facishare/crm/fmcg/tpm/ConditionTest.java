package com.facishare.crm.fmcg.tpm;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.web.condition.ConditionAdapter;
import com.facishare.crm.fmcg.tpm.web.condition.model.ConditionDto;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/2 上午11:01
 */
public class ConditionTest extends BaseTest {

    @Resource
    private ConditionAdapter conditionAdapter;

    @Resource
    private ServiceFacade serviceFacade;

    @Test
    public void testPattern() {

        ConditionDto conditionDto = new ConditionDto();
        conditionDto.setFieldName("field_da2OF__c");
        conditionDto.setOperator("IN");
        conditionDto.setRowNo(0);
        conditionDto.setValues(Lists.newArrayList("f1529L8Du"));
        System.out.println(conditionAdapter.publish(84931, -10000, "AccountObj", "(0)", Lists.newArrayList(conditionDto)));
    }

    @Test
    public void testFind() {
        System.out.println(JSON.toJSONString(conditionAdapter.find(84931, -10000, "object_7uVXk__c", "63e2767957243151667702ab")));
    }

    @Test
    public void testFilter() {
        String tenantId = "84931";
        String apiName = "TPMActivityObj";
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(0);
        query.setOffset(0);
        Filter filter = new Filter();
        filter.setFieldName(TPMActivityFields.STORE_RANGE);
        filter.setOperator(Operator.CONTAINS);
        filter.setFieldValues(com.google.common.collect.Lists.newArrayList("CONDITION"));
        query.setFilters(com.google.common.collect.Lists.newArrayList(filter));
        List<IObjectData> allData = CommonUtils.queryAllDataInFields(serviceFacade, User.systemUser(tenantId), apiName, query, com.google.common.collect.Lists.newArrayList(CommonFields.TENANT_ID, CommonFields.OBJECT_DESCRIBE_API_NAME, TPMActivityFields.STORE_RANGE));
        System.out.println(allData);
    }
}
