package com.facishare.crm.fmcg.tpm;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.expression.SimpleExpression;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/10 下午6:26
 */
public class CalculateExpresionTest extends BaseTest {


    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private ExpressionCalculateLogicService expressionCalculateLogicService;

    @Test
    public void testExpresion() {
        User user = User.systemUser("84931");
        IObjectData data = serviceFacade.findObjectData(user, "6335517cad5281000110c949", "TPMBudgetBusinessSubjectObj");
        IObjectDescribe describe = serviceFacade.findDescribeAndLayout(user, data.getDescribeApiName(), false, null).getObjectDescribe();
        SimpleExpression simpleExpression = new SimpleExpression();
        simpleExpression.setId("jie_ge_niu_bi__c");
        simpleExpression.setDecimalPlaces(2);
        simpleExpression.setNullAsZero(true);
        simpleExpression.setReturnType("number");
        simpleExpression.setExpression("$subject_level$");
        List a = Lists.newArrayList(data);
        expressionCalculateLogicService.bulkCalculateWithExpression(describe, a, Lists.newArrayList(simpleExpression));
        System.out.println(JSON.toJSONString(a));
    }
}
