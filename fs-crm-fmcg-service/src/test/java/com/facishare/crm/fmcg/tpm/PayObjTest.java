package com.facishare.crm.fmcg.tpm;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.dms.action.PayObjAddAction;
import com.facishare.crm.fmcg.fesco.model.apl.AplCreateTask;
import com.facishare.crm.fmcg.fesco.model.fesco.*;
import com.facishare.crm.fmcg.fesco.service.FescoService;
import com.facishare.crm.fmcg.fesco.service.RedSunAplService;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.checkerframework.checker.units.qual.C;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;


public class PayObjTest extends BaseTest {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private InfraServiceFacade infraServiceFacade;

    @Resource
    private FescoService fescoService;

    @Resource
    private RedSunAplService redSunAplService;


    @Test
    public void addActionTest() {
        PayObjAddAction action = new PayObjAddAction();
        action.setServiceFacade(serviceFacade);
        action.setInfraServiceFacade(infraServiceFacade);


        RequestContext requestContext = RequestContext.builder()
                .tenantId("90265")
                .ea("90265")
                .user(User.systemUser("90265"))
                .build();
        action.setActionContext(new ActionContext(requestContext, "PayObj", "Add"));

        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(new ObjectDataDocument());
        String objData = "{\"object_describe_api_name\":\"PayObj\",\"record_type\":\"default__c\",\"created_by\":[\"1000\"],\"owner\":[\"1000\"],\"data_own_department\":[\"999998\"],\"data_own_department__r\":\"待分配\",\"purpose\":\"purchase\",\"pay_type\":\"red\",\"contact_object\":\"supplier\",\"base\":false,\"pre_match_amount\":\"-37.32\",\"match_amount\":\"0.00\",\"available_amount\":\"0.00\",\"no_match_amount\":\"-37.32\",\"match_status\":\"no_match\",\"pay_type__o\":\"\",\"purpose__o\":\"\",\"contact_object__o\":\"\",\"match_status__o\":\"\",\"object_describe_id\":\"65967c93c4d3a30001708d27\",\"supplier_id\":\"6596607afb05460001430b02\",\"pay_date\":*************,\"pay_amount\":\"-37.32\",\"pay_term\":\"\",\"enterprise_fund_account_id\":\"65967ed299e5520001c63906\",\"remarks\":\"\",\"requestId\":\"73a13b6b8b6f4e43962fa88b62fddfca\"}";

        arg.getObjectData().putAll(JSON.parseObject(objData));


        arg.setDetails(Maps.newHashMap());

//        String detail = "{\"record_type\":\"default__c\",\"object_describe_id\":\"6300967ab2b0380001b12d1e\",\"object_describe_api_name\":\"TPMBudgetDisassemblyNewDetailObj\",\"lock_rule\":\"default_lock_rule\",\"lock_status\":\"0\",\"life_status\":\"normal\",\"dealer_id\":\"62f5f51a2c477d0001a13f93\",\"dealer_id__r\":\"消费2\",\"take_apart_in_amount\":\"100.00\",\"name\":\"测试拆解04\",\"budget_department\":[\"1000\"],\"budget_period_year\":*************,\"__tbIndex\":0,\"amount\":\"100\"}";
//        List<ObjectDataDocument> details = Lists.newArrayList();
//        ObjectDataDocument objectDataDocument = new ObjectDataDocument();
//        objectDataDocument.putAll(JSON.parseObject(detail));
//        details.add(objectDataDocument);
//
//        arg.getDetails().put("TPMBudgetDisassemblyNewDetailObj", details);

        String detail = "{\"record_type\":\"default__c\",\"object_describe_api_name\":\"PayDetailObj\",\"rowId\":\"1704440217892765\",\"object_describe_id\":\"65967c94c4d3a30001708d73\",\"lock_rule\":\"default_lock_rule\",\"lock_status\":\"0\",\"life_status\":\"normal\",\"supplier_id\":\"隔离企业-82958\",\"supplier_id__r\":\"6596607afb05460001430b02\",\"pay_date\":\"2024-01-05\",\"pay_amount\":\"-37.32\",\"purchase_order_id\":\"\",\"purchase_order_id__r\":\"\",\"purchase_return_note_id\":\"6597aff04d3769000169e744\",\"purchase_return_note_id__r\":\"24-01-0500002\",\"__operateWidth\":88,\"dataIndex\":0}";
        List<ObjectDataDocument> details = Lists.newArrayList();
        ObjectDataDocument objectDataDocument = new ObjectDataDocument();
        objectDataDocument.putAll(JSON.parseObject(detail));
        details.add(objectDataDocument);

        arg.getDetails().put("PayDetailObj", details);

        action.setArg(arg);

        BaseObjectSaveAction.Result result = action.act(arg);

        assert result != null;
    }

    @Test
    public void test() {
        String taskId = String.format("T.%s.%s", "82958", "ceshi112-002");
        String subTaskId = String.format("CT.%s.%s.%s", "82958", "A18236158763C", "1000");
        CreateAsyncTask.ChildTaskArg childTaskArg = new CreateAsyncTask.ChildTaskArg();
        childTaskArg.setThirdSubId(subTaskId);
        childTaskArg.setName("李京松");
        childTaskArg.setPhone("18236158763");
        childTaskArg.setMoney(new BigDecimal("1"));

        CreateAsyncTask.Arg arg = new CreateAsyncTask.Arg();
        arg.setThirdTaskId(taskId);
        arg.setChildTaskList(Lists.newArrayList(childTaskArg));

        System.out.println(fescoService.createAsyncTask2(arg));



    }

    @Test
    public void testCreateTask(){

        ApiContext context = ApiContextManager.getContext();
        context.setTenantId("78612");

        AplCreateTask.Arg arg = new AplCreateTask.Arg();
        arg.setId("661f955dbf17740001904913");

        redSunAplService.createTask(arg);
    }

    @Test
    public void testUserAuth(){
        GetUserAuth.Arg arg = new GetUserAuth.Arg();
        arg.setPhone("18236158763");

        fescoService.getUserAuth(arg);
    }

    @Test
    public void token(){

        System.out.println(fescoService.token());

    }


    @Test
    public void testCancelUser(){
        CancelUser.Arg arg = new CancelUser.Arg();
        arg.setPhone("18236158763");
        arg.setReason("测试人员");

        fescoService.cancelUser(arg);
    }

    @Test
    public void testCancelTask(){
        CancelTask.Arg arg = new CancelTask.Arg();

        arg.setThirdTaskId("82958.6613B8C40DC821DC7DF123D3");

        fescoService.cancelTask(arg);
    }

    @Test
    public void testQueryTask(){
        QueryTask.Arg arg = new QueryTask.Arg();
        String taskId = String.format("T.%s.%s", "78612", "661fad4becec7d53e5d105d5").toUpperCase();
        arg.setThirdTaskId(taskId);
        arg.setPageNumber(1);
        arg.setPageSize(1);

        fescoService.queryTask(arg);
    }

    @Test
    public void testCallBack(){
        TaskCallback.SubTask subTask = new TaskCallback.SubTask();
        subTask.setThirdSubId("78612.661E75C33CD4F70001260F43.1132");
        subTask.setState("1");
        TaskCallback.Arg arg = new TaskCallback.Arg();
        arg.setCode(200);
        arg.setData(subTask);
        arg.setSuccess(true);

        String sss = "LUa8CX97M1Au9RH1xPB/aAA7uWQsGBfMfLVDWuj4fW7uEkEJpbyYTpOhkCtYqUjcY5a6fY88aP9d7hiNM3N8MfIeOSGvlyySm3DDPKTU2sj9Gy+K73RYfyLNbH7v0gDJNw53o+lhchxDLghh+dd/2fsfuzxU1fv1rq3/Cg0cAx4NKJ5yq7bQ1tmWg0N4se9qoLEkFK5nNf7YfULi8V4SveB5Fw61XnmxpXgi61yTeHirOaRmamJJ8qeCfkc3tn2QWurzkDgMcCITHMoKHqajaTkAQqI3TkhTjwxWy2l9Jgz+rRDQsNjG/GG6B3C0P/VE5BR8LJ3RVaz0FVUVOLTY4E9hD5ESUWlZFB6IA9Y4zkmFQpVN1wegFnn0AfecpKurOI9q0Cyc7v63lyKtEeRJZAz25yzp/g5ERNyDe0RgxtGKOU913XieC0s0Qd0Ilx8IgdK+eQLLjX1JTpv7t/XgAiZzGGinpMq4CbVLYkjlA9hesji7/TVfYWpWu0b8YXO+JIOFlB4/g+C/QlWyIVmEyQ==";

        redSunAplService.taskCallback(sss);
    }

    @Test
    public void testAsyncCallBack(){
        TaskCallback.SubTask subTask = new TaskCallback.SubTask();
        subTask.setThirdSubId("78612.661E75C33CD4F70001260F43.1132");
        subTask.setState("1");
        TaskCallback.Arg arg = new TaskCallback.Arg();
        arg.setCode(200);
        arg.setData(subTask);
        arg.setSuccess(true);

//        String sss = "LUa8CX97M1Au9RH1xPB/aAA7uWQsGBfMfLVDWuj4fW7uEkEJpbyYTpOhkCtYqUjcY5a6fY88aP9d7hiNM3N8MfIeOSGvlyySm3DDPKTU2sj9Gy+K73RYfyLNbH7v0gDJNw53o+lhchxDLghh+dd/2fsfuzxU1fv1rq3/Cg0cAx4NKJ5yq7bQ1tmWg0N4se9qoLEkFK5nNf7YfULi8V4SveB5Fw61XnmxpXgi61yTeHirOaRmamJJ8qeCfkc3tn2QWurzkDgMcCITHMoKHqajaTkAQqI3TkhTjwxWy2l9Jgz+rRDQsNjG/GG6B3C0P/VE5BR8LJ3RVaz0FVUVOLTY4E9hD5ESUWlZFB6IA9Y4zkmFQpVN1wegFnn0AfecpKurOI9q0Cyc7v63lyKtEeRJZAz25yzp/g5ERNyDe0RgxtGKOU913XieC0s0Qd0Ilx8IgdK+eQLLjX1JTpv7t/XgAiZzGGinpMq4CbVLYkjlA9hesji7/TVfYWpWu0b8YXO+JIOFlB4/g+C/QlWyIVmEyQ==";

        String sss = "yb39CanJBZOq18b8FziaPNfEebuJhpca1BLDyHg8hATAzNrTHFbAxG5If9WKRr9SYf0jdz3WkO2s1+QtNnurjhBbnOQUp6gekijQ5kKOL8z/B7GukvmrkblXu8xQrUcC762mRi4j6BQVx9XKif1iGKsmLW3B5n2039mCT68AGVqrKb2wyA5zUfoga4ZRCh9rF+Xg9nGrACOoEeQPHl30pRyNwAvb6nrRLfJiDXZK9cvpsjZ+Tvx32OSVsACxUDe03F06RRodk6uDM7PkfRaLw2gHtLq/qlei0ZkhfC1g4Zu3ABIufGMWx0Piicv6AY3UkLePbYvl5gWesADezd7Z8TsrMtX0g/mRsSs3jbr2J+TtEuSrz15hgiOwTWhsGdOcUjwHUcimlJ9IbQ6OSND0hRqybmMxkYhiYy9KLZiitR3vd+680Pguf/F9hYQeRL6mKVE55nlcnQWOG6EAfckDnPk8vQloHNGPwnPLDo+m8KifMIT7McBOAC7IagqAUCDXrLneGsXS8L+Ms7Pm8761+Z95AkHgWFkckxHfKPJN6mSyC5eJwezrvfxdpH9pY8XZJ2YI30wMWHTkITYieRhKrTuUyX4YsdQsh0UGXvA5E3yMn9Nxj/JsLYevc0firnRvh1VdbzXv8F2jTpXAhTphhv23xcYpYosBZoAmEkNwm2ggDg0gGCm7FLqjkQFtCuHLHFh4YK3lgKraPlKuWBi9HrPSF54jx+lK80KjtW+SkuhjcvWZpwXpXH1HqLTvv8+8SW4jmOJqpCURN9o5ZkD5NL56DaRVbK2ojmoOwlj/g2SUQKVNIX7BW4GLj7yROdiQ7jnVXUVm34z5sXUzvWgbExg/7s48FN8WD4EyzUUz02NQ8QExsryKz9z8onoRgMdKId068xNvAgLoEN2xh+teL8Rp44Hf39a5+4j9155dqLP7hSave/tlWRQCeNY6UHLyxC7SNbuNc8FL9IfjaAyeykfo82wKMLnKUxFst30NvK0WY1YTmcpfDcdxiBuWXIpdvDTyyKD/V64QnvF4dgJFZVqd54YyRF7FflO+45pKIO00+zn2mrfgu1UnLw2aPyncDRAht4Qr5pmvuX81az/wAWv0SMO2KwkkHh3eedmtCO5fkl7p3GcCggBwEWPdTAB0zcW95JCBPSvGQkuz0OmnYwuf+/r1oRyh7S+wTdrUaTnxxo5eN+SGWnwnXGbmKdB9zssn3/x4ec3nLMUF4kHdNllnWE71z8/UHYS8OqLQ+pAOCcUxaKhENke0D3iuIEx6";

        String sss2 = "eUjZdLNKlsBofaSF3KxJBDyQH3zlc5eomdj12TFlm51NH44VRrngGkCSg+ap5SXr3ALaqsZV6rDkGES/HuiEBoMiSPoEDHtNMhXtJNFGCRiekLydKqSeBpvaYfg4bS/HzleH7m+w6nWm+6FP6sZqoxyf9dsAfLF/+BtGx9AKrr2NBb89GRB1WYT4sNC/TuAt";


        redSunAplService.asyncCreateTaskCallBack(sss2);
    }



}
