package com.facishare.crm.fmcg.dms;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.dms.business.DMSScriptHandlerFactory;
import com.facishare.crm.fmcg.dms.business.abstraction.IDMSScriptHandler;
import com.facishare.crm.fmcg.dms.business.enums.ScriptHandlerNameEnum;
import com.facishare.crm.fmcg.dms.errors.AbandonActionException;
import com.facishare.crm.fmcg.dms.model.AccountsPayableEnable;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.crm.fmcg.dms.service.converter.AutoConverterActuator;
import com.facishare.crm.fmcg.dms.service.mapper.MatchableBillMapper;
import com.facishare.crm.fmcg.dms.service.matcher.AutoMatcher;
import com.facishare.crm.fmcg.dms.web.abstraction.IAccountsPayableService;
import com.facishare.crm.fmcg.tpm.BaseTest;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

public class PayableTest extends BaseTest {

    @Resource
    private IAccountsPayableService accountsPayableService;


    @Resource
    private AutoMatcher autoMatcher;
    @Resource
    private MatchableBillMapper matchableBillMapper;
    @Resource
    private AutoConverterActuator autoConverterActuator;
    @Resource
    private DMSScriptHandlerFactory dmsScriptHandlerFactory;

    @Test
    public void enableTest() {

        accountsPayableService.accountsPayableEnable(new AccountsPayableEnable.Arg());
    }

    @Test
    public void enableTest2() {

        IDMSScriptHandler accountsPayableEnableHandler = dmsScriptHandlerFactory.resolve(ScriptHandlerNameEnum.ACCOUNTS_PAYABLE_ENABLE.getHandlerName());
        accountsPayableEnableHandler.initObjectDescribe("89150");

    }

    @Test
    public void matchTest() {

//        FinancialBill bill = FinancialBill.builder().tenantId("89150").apiName(ApiNames.PAY_OBJ).id("65800af6d651d900013fb04e").build();
        FinancialBill bill = FinancialBill.builder().tenantId("89150").apiName(ApiNames.PAY_OBJ).id("6585454d911ce3000127ea5c").build();
        List<FinancialBill> matchableBills = matchableBillMapper.map(bill);
        for (FinancialBill matchableBill : matchableBills) {
            autoMatcher.match(matchableBill);
        }
    }

    @Test
    public void convertTest() {
        try {
            FinancialBill bill = FinancialBill.builder().tenantId("89150").apiName(ApiNames.OUTBOUND_DELIVERY_NOTE_OBJ).id("67c00b1dae5c3d00016b54be").build();
            autoConverterActuator.convert(bill);
        } catch (AbandonActionException ex) {
            System.out.println(1);
        } catch (Exception ex) {
            System.out.println(2);
        }

    }
}
