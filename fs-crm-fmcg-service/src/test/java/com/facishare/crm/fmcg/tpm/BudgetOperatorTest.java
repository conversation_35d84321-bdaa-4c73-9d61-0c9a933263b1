package com.facishare.crm.fmcg.tpm;

import com.facishare.crm.fmcg.tpm.business.BudgetOperatorFactory;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetOperator;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.tpm.web.custom.CustomBudgetService;
import com.facishare.paas.appframework.core.model.User;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * author: wuyx
 * description:
 * createTime: 2022/3/30 17:05
 */
public class BudgetOperatorTest extends BaseTest {

    @Resource
    private CustomBudgetService customBudgetService;

    @Test
    public void validateOperableAmountTest() {
        IBudgetOperator operator = BudgetOperatorFactory.initOperator(BizType.CONSUME, User.systemUser("85494"), "634662e6527df10001b9c036", "A", "B");
        operator.validateOperableAmount(new BigDecimal("10"));
    }

    @Test
    public void validateConsumableAmount(){
        IBudgetOperator operator = BudgetOperatorFactory.initOperator(BizType.CONSUME, User.systemUser("85494"), "6406aa7f6de18e00012dbbfb", "A", "B");
        operator.validateConsumableAmount(new BigDecimal("100000"));
    }
}
