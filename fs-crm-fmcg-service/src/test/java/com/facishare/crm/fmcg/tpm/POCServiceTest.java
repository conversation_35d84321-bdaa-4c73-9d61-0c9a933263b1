package com.facishare.crm.fmcg.tpm;

import com.facishare.crm.fmcg.service.web.task.POCTaskController;
import com.facishare.crm.fmcg.tpm.web.poc.abstraction.IPOCActivityService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.xxl.job.core.biz.model.TriggerParam;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/24 16:53
 */
public class POCServiceTest extends BaseTest {


    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private IPOCActivityService pocActivityService;
    @Resource
    private POCTaskController pocTaskController;

    @Test
    public void test() {

//        pocActivityService.init(new POCInitActivityType.Arg ());
//
//        POCInitActivity.Arg arg = new POCInitActivity.Arg();
//        arg.setTenantId("88181");
//        pocActivityService.initWholeActivity(arg);
//
        pocTaskController.execute(new TriggerParam());
//
//        POCInitProof.Arg arg = new POCInitProof.Arg();
//        arg.setIncrement(true);
//        arg.setEi("88181");
//        arg.setEa("88181");
//        pocActivityService.initProof(arg);
    }

}

