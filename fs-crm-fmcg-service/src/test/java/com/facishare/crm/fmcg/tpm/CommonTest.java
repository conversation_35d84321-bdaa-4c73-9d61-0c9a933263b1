package com.facishare.crm.fmcg.tpm;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.matheclipse.core.expression.F;

/**
 * Author: linmj
 * Date: 2023/5/22 15:15
 */
public class CommonTest {

    public static void printWhere(){
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(200);
        Wheres where1 = new Wheres();
        Filter filter1 = new Filter();
        filter1.setFieldName("name");
        filter1.setOperator(Operator.CONTAINS);
        filter1.setFieldValues(Lists.newArrayList("1"));
        where1.setFilters(Lists.newArrayList(filter1));

        Wheres where2 = new Wheres();
        Filter filter2 = new Filter();
        filter2.setFieldName("name");
        filter2.setOperator(Operator.CONTAINS);
        filter2.setFieldValues(Lists.newArrayList("2"));
        where2.setFilters(Lists.newArrayList(filter2));

        query.setWheres(Lists.newArrayList(where2,where1));

        System.out.println(JSON.toJSONString(query));
    }

    public static void main(String[] args) {
        printWhere();
    }
}
