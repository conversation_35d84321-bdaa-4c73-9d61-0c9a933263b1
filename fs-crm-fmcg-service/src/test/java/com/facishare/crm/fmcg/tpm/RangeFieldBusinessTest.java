package com.facishare.crm.fmcg.tpm;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.ActivityCustomerTypeEnum;
import com.facishare.crm.fmcg.tpm.business.RangeFieldBusiness;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.web.condition.ConditionAdapter;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * Author: linmj
 * Date: 2023/4/18 16:23
 */
public class RangeFieldBusinessTest extends BaseTest {

    @Resource
    private RangeFieldBusiness rangeFieldBusiness;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private ConditionAdapter conditionAdapter;

    @Test
    public void testFormCode() {
        String code = rangeFieldBusiness.formActivityStoreRangeRuleCode("84931", ActivityCustomerTypeEnum.DEALER.value(),"[{\"filters\":[{\"field_name\":\"dealer_id.name\",\"operator\":\"EQ\",\"field_values\":[\"统案经销商\"],\"connector\":\"AND\",\"type\":\"object_reference\"}],\"connector\":\"OR\"}]", "123");
        System.out.println(code);
        //System.out.println(conditionAdapter.isDataMatchRuleCodes(84931, -10000, "AccountObj", "6322ef65bab52b0001f09a61", Sets.newSet("64488e62ccd22a6b7abad398")));
    }


    @Test
    public void testFromCode() {
        String tenantId = "88522";
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(-1);
        List<IObjectData> unifiedActivity = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ, query);
        long time = System.currentTimeMillis();
        System.out.println(rangeFieldBusiness.judgeDealerInActivitiesDealerRange(tenantId, "643f9206f6a9160001cd9617", unifiedActivity));
        System.out.println(System.currentTimeMillis() - time);
    }
}
