package com.facishare.crm.fmcg.tpm;

import com.facishare.crm.fmcg.tpm.action.TPMBudgetCarryForwardObjAddActionBak;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetCarryForwardFields;
import com.facishare.crm.fmcg.tpm.business.CarryForwardActionService;
import com.facishare.crm.fmcg.tpm.controller.TPMBudgetCarryForwardObjLoadDetailDataController;
import com.facishare.crm.fmcg.tpm.controller.TPMBudgetCarryForwardObjWebDetailController;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDetailController;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/6/5 22:12
 */
public class BudgetCarryForwardObjTest extends BaseTest {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private InfraServiceFacade infraServiceFacade;
    @Resource
    private CarryForwardActionService carryForwardActionService;

    @Test
    public void loadDetailTest() {
        TPMBudgetCarryForwardObjLoadDetailDataController controller = new TPMBudgetCarryForwardObjLoadDetailDataController();
        controller.setServiceFacade(serviceFacade);

        RequestContext requestContext = RequestContext.builder().tenantId("84931").ea("84931").user(User.systemUser("84931")).build();
        controller.setControllerContext(new ControllerContext(requestContext, "TPMBudgetCarryForwardObj", "LoadDetailData"));

        TPMBudgetCarryForwardObjLoadDetailDataController.Arg arg = new TPMBudgetCarryForwardObjLoadDetailDataController.Arg();
        arg.setBudgetTemplateId("62f1f9288cca033bbd82fbc1");
        arg.setSourcePeriod(1640966400000L);
        arg.setTargetPeriod(1672502400000L);

        controller.setArg(arg);

        TPMBudgetCarryForwardObjLoadDetailDataController.Result result = controller.service(arg);

        assert result != null;
    }

    @Test
    public void freezeTest() {
        carryForwardActionService.freeze(User.systemUser("84931"), "64242dbd073f3a000128ae6a");
    }

    @Test
    public void initRetryButtonTest() {
        carryForwardActionService.initRetryButton("84931");
    }

    @Test
    public void webDetailTest() {
        TPMBudgetCarryForwardObjWebDetailController controller = new TPMBudgetCarryForwardObjWebDetailController();

        controller.setServiceFacade(serviceFacade);
        controller.setInfraServiceFacade(infraServiceFacade);

        AbstractStandardDetailController.Arg arg = new AbstractStandardDetailController.Arg();
        arg.setObjectDataId("646f3279444f4d00013f8bc1");
        arg.setObjectDescribeApiName("TPMBudgetCarryForwardObj");
        arg.setFromRecycleBin(false);
        arg.setIncludeLayout(true);
        Map<String, Integer> versionMap = Maps.newHashMap();
        versionMap.put("TPMBudgetCarryForwardObj", 40);
        arg.setDescribeVersionMap(versionMap);
        arg.setIncludeDescribe(true);
        arg.setLayoutType("detail");
        arg.setLayoutVersion("V3");
        controller.setArg(arg);

        RequestContext requestContext = RequestContext.builder()
                .tenantId("84931")
                .ea("84931")
                .user(User.systemUser("84931")).build();
        RequestContextManager.setContext(requestContext);
        controller.setControllerContext(new ControllerContext(requestContext, "TPMBudgetCarryForwardObj", "WebDetail"));

        AbstractStandardDetailController.Result result = controller.service(arg);

        assert result != null;
    }

    @Test
    public void addActionTest() {
        TPMBudgetCarryForwardObjAddActionBak action = new TPMBudgetCarryForwardObjAddActionBak();
        action.setServiceFacade(serviceFacade);
        // action.setStopWatch(StopWatch.create("TPMBudgetCarryForwardObjAddAction"));

        RequestContext requestContext = RequestContext.builder().tenantId("84931").ea("84931").user(User.systemUser("84931")).build();
        action.setActionContext(new ActionContext(requestContext, "TPMBudgetCarryForwardObj", "Add"));

        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();

        arg.setObjectData(new ObjectDataDocument());
        arg.getObjectData().put(CommonFields.OWNER, Lists.newArrayList("-10000"));
        arg.getObjectData().put(TPMBudgetCarryForwardFields.SOURCE_QUARTER, 1656604800000L);
        arg.getObjectData().put(TPMBudgetCarryForwardFields.TARGET_QUARTER, 1664553600000L);

        arg.setDetails(Maps.newHashMap());

        List<ObjectDataDocument> details = Lists.newArrayList();
        ObjectDataDocument detail = new ObjectDataDocument();
        detail.put("source_budget_account_id", "62e23425acd6e80001fdcfd0");
        detail.put("target_budget_account_id", "62e234f9acd6e80001fdd2c6");
        detail.put("amount", "1000.00");
        detail.put("after_carry_forward_amount", "1000.00");
        details.add(detail);

        arg.getDetails().put("TPMBudgetCarryForwardDetailObj", details);

        action.setArg(arg);

        BaseObjectSaveAction.Result result = action.act(arg);

        assert result != null;
    }
}
