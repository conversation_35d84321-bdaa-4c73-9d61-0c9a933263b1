package com.facishare.crm.fmcg.tpm;

import com.facishare.crm.fmcg.tpm.service.PaasReferenceService;
import com.fmcg.framework.http.contract.reference.PaasReferenceBatchDelete;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/22 14:46
 */
public class ActivityTypeReferenceTest extends BaseTest {

    @Resource
    private PaasReferenceService paasReferenceService;

    @Test
    public void queryActivityTypeStatisticsDataTest() {

        List<PaasReferenceBatchDelete.BatchDeleteArg> reference = Lists.newArrayList();

        PaasReferenceBatchDelete.BatchDeleteArg referenceCommonArg = new PaasReferenceBatchDelete.BatchDeleteArg();
        referenceCommonArg.setSourceType("TPMDesigner");
        referenceCommonArg.setSourceValue("type_yMl26__c--custom--agreement--activity_agreement_id__c");
        referenceCommonArg.setTargetType("Describe.Field");
        referenceCommonArg.setTargetValue("object_1t6fo__c.activity_agreement_id__c");
        reference.add(referenceCommonArg);
        paasReferenceService.deleteReference("80063", reference);
    }
}
