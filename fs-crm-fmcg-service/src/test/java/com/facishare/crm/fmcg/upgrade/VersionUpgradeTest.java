package com.facishare.crm.fmcg.upgrade;

import com.facishare.crm.fmcg.tpm.BaseTest;
import com.facishare.crm.fmcg.upgrade.api.Upgrade;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;

public class VersionUpgradeTest extends BaseTest {

    @Resource
    private VersionUpgradeHandler versionUpgradeHandler;

    @Test
    public void upgradeTest() {
        Upgrade.Arg arg = new Upgrade.Arg();
        arg.setTenantIds(Lists.newArrayList("84931"));
        arg.setVersion("V860");

        Upgrade.Result result = versionUpgradeHandler.upgrade(arg);

        assert result != null;
    }
}
