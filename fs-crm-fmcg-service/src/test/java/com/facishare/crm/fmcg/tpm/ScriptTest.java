package com.facishare.crm.fmcg.tpm;

import com.facishare.crm.fmcg.tpm.service.abstraction.ScriptService;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * Author: linmj
 * Date: 2023/8/30 15:00
 */
public class ScriptTest extends BaseTest{

    @Resource
    private ScriptService service;

    @Resource
    private ScriptService scriptService;


    @Test
    public void testUnique(){

        service.changeObjectFieldSupportRepeat("84788","TPMBudgetBusinessSubjectObj","name");
    }

    @Test
    public void testMongo(){
        scriptService.copyMongo("89150","82958");
    }

    @Test
    public void scriptTest(){
        scriptService.filterActivityByAccount("84931","","64b8cf458a45a700016f24b0", Lists.newArrayList("65a898fc97a60a00015f01e4"),Lists.newArrayList());
    }
}
