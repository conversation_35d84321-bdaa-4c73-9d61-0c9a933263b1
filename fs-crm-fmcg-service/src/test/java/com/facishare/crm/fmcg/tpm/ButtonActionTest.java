package com.facishare.crm.fmcg.tpm;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.business.ButtonActionService;
import com.facishare.crm.fmcg.tpm.common.constant.ButtonConstants;
import com.facishare.crm.fmcg.tpm.controller.TPMBudgetBusinessSubjectObjInnerDescribeController;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.metadata.api.IUdefAction;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/15 下午5:38
 */
public class ButtonActionTest extends BaseTest {

    @Resource
    private ButtonActionService buttonActionService;

    @Resource
    private ServiceFacade serviceFacade;

    @Test
    public void testAddAction() {

        RequestContext requestContext = RequestContext.builder().tenantId("84931").user(new User("84931", "1000")).build();

        ServiceContext serviceContext = new ServiceContext(requestContext, null, null);

        List<IUdefAction> actionList = Lists.newArrayList(buttonActionService.formUDefAction("testWhate__c", "testPostAction", ButtonConstants.ActionStage.POST, ButtonConstants.ActionBizKey.TEST_POST_BIZ, null, null));
        buttonActionService.addActionsToButton(serviceContext, "testWhate__c", "Add_button_default", actionList);
    }

    @Test
    public void testTriggerController() {
        TPMBudgetBusinessSubjectObjInnerDescribeController.Arg arg = new TPMBudgetBusinessSubjectObjInnerDescribeController.Arg();
        arg.setDescribeData("{\"store_table_name\":\"fmcg_tpm_budget_business_subject\",\"package\":\"CRM\",\"is_active\":true,\"description\":\"\",\"display_name\":\"预算科目\",\"version\":2,\"is_open_display_name\":false,\"index_version\":1,\"icon_index\":0,\"is_deleted\":false,\"api_name\":\"TPMBudgetBusinessSubjectObj\",\"icon_path\":\"\",\"is_udef\":true,\"define_type\":\"package\",\"short_name\":\"bbs\",\"is_support_tree_view\":true,\"fields\":{\"tenant_id\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":false,\"is_active\":true,\"pattern\":\"\",\"description\":\"tenant_id\",\"is_unique\":false,\"label\":\"tenant_id\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":true,\"api_name\":\"tenant_id\",\"define_type\":\"system\",\"status\":\"released\",\"max_length\":200},\"lock_rule\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":false,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"description\":\"锁定规则\",\"default_value\":\"default_lock_rule\",\"rules\":[],\"label\":\"锁定规则\",\"type\":\"lock_rule\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"lock_rule\",\"define_type\":\"package\",\"is_single\":false,\"status\":\"new\"},\"subject_number\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"prefix\":\"{yyyy}-{mm}-{dd}-\",\"auto_adapt_places\":false,\"is_unique\":true,\"description\":\"\",\"start_number\":1,\"type\":\"auto_number\",\"is_required\":false,\"define_type\":\"package\",\"postfix\":\"\",\"is_single\":false,\"is_index\":true,\"auto_number_type\":\"normal\",\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"{yyyy}-{mm}-{dd}-0001\",\"serial_number\":4,\"label\":\"科目编号\",\"condition\":\"DAY\",\"api_name\":\"subject_number\",\"func_api_name\":\"\",\"status\":\"new\",\"help_text\":\"\"},\"parent_subject_id\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"description\":\"\",\"where_type\":\"field\",\"type\":\"object_reference\",\"relation_outer_data_privilege\":\"not_related\",\"related_where_type\":\"\",\"wheres\":[],\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"target_api_name\":\"TPMBudgetBusinessSubjectObj\",\"label\":\"上级科目\",\"target_related_list_name\":\"target_related_list_TPMBudgetBusinessSubjectObj_TPMBudgetBusinessSubjectObj__c\",\"target_related_list_label\":\"预算科目\",\"action_on_target_delete\":\"cascade_delete\",\"related_wheres\":[],\"api_name\":\"parent_subject_id\",\"status\":\"new\",\"tree_view_field\":\"tree_view\"},\"tree_view\":{\"is_index\":false,\"is_active\":true,\"pattern\":\"\",\"description\":\"树形视图层级\",\"is_unique\":false,\"label\":\"树形视图层级\",\"type\":\"tree_path\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"tree_view\",\"define_type\":\"package\",\"is_index_field\":false,\"is_single\":false,\"status\":\"released\"},\"lock_user\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":false,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"description\":\"加锁人\",\"label\":\"加锁人\",\"type\":\"employee\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"lock_user\",\"define_type\":\"package\",\"is_single\":true,\"status\":\"new\"},\"extend_obj_data_id\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":true,\"is_required\":false,\"api_name\":\"extend_obj_data_id\",\"is_unique\":true,\"description\":\"连接通表的记录ID,扩展字段用\",\"define_type\":\"system\",\"label\":\"扩展字段在mt_data中的记录ID\",\"type\":\"text\",\"status\":\"released\",\"max_length\":64},\"is_deleted\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":false,\"description\":\"is_deleted\",\"is_unique\":false,\"default_value\":\"false\",\"label\":\"is_deleted\",\"type\":\"true_or_false\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"is_deleted\",\"define_type\":\"system\",\"status\":\"released\"},\"enable\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"description\":\"\",\"default_value\":\"true\",\"label\":\"是否启用\",\"type\":\"true_or_false\",\"is_required\":false,\"api_name\":\"enable\",\"options\":[{\"label\":\"是\",\"value\":true},{\"label\":\"否\",\"value\":false}],\"define_type\":\"package\",\"is_single\":false,\"status\":\"new\",\"help_text\":\"\"},\"life_status_before_invalid\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":false,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"is_unique\":false,\"description\":\"作废前生命状态\",\"label\":\"作废前生命状态\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"life_status_before_invalid\",\"define_type\":\"package\",\"is_single\":false,\"status\":\"new\",\"max_length\":256},\"object_describe_api_name\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":false,\"is_active\":true,\"pattern\":\"\",\"description\":\"object_describe_api_name\",\"is_unique\":false,\"label\":\"object_describe_api_name\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":true,\"api_name\":\"object_describe_api_name\",\"define_type\":\"system\",\"status\":\"released\",\"max_length\":200},\"subject_level\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"description\":\"\",\"type\":\"number\",\"decimal_places\":0,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"max_length\":14,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"step_value\":1,\"display_style\":\"input\",\"length\":14,\"default_value\":\"\",\"label\":\"科目层级\",\"api_name\":\"subject_level\",\"round_mode\":4,\"status\":\"new\",\"help_text\":\"\"},\"out_owner\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":false,\"is_active\":true,\"is_unique\":false,\"label\":\"外部负责人\",\"type\":\"employee\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"out_owner\",\"define_type\":\"system\",\"is_single\":true,\"status\":\"released\"},\"owner_department\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"is_unique\":false,\"description\":\"\",\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":true,\"max_length\":100,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"负责人主属部门\",\"is_need_convert\":false,\"api_name\":\"owner_department\",\"status\":\"new\",\"help_text\":\"\"},\"activity_item_ids\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"description\":\"\",\"where_type\":\"field\",\"type\":\"object_reference_many\",\"wheres\":[],\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"target_api_name\":\"TPMActivityItemObj\",\"label\":\"关联费用项目\",\"target_related_list_name\":\"target_related_list_TPMBudgetBusinessSubjectObj_TPMActivityItemObj__c\",\"target_related_list_label\":\"预算科目\",\"action_on_target_delete\":\"set_null\",\"api_name\":\"activity_item_ids\",\"status\":\"new\",\"help_text\":\"\"},\"owner\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"description\":\"\",\"where_type\":\"field\",\"label\":\"负责人\",\"type\":\"employee\",\"is_need_convert\":false,\"wheres\":[],\"is_required\":true,\"api_name\":\"owner\",\"define_type\":\"package\",\"is_single\":true,\"status\":\"new\",\"help_text\":\"\"},\"last_modified_time\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":true,\"description\":\"last_modified_time\",\"is_unique\":false,\"label\":\"最后修改时间\",\"time_zone\":\"\",\"type\":\"date_time\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"last_modified_time\",\"define_type\":\"system\",\"date_format\":\"yyyy-MM-dd HH:mm:ss\",\"status\":\"released\"},\"package\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":false,\"is_active\":true,\"pattern\":\"\",\"description\":\"package\",\"is_unique\":false,\"label\":\"package\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"package\",\"define_type\":\"system\",\"status\":\"released\",\"max_length\":200},\"lock_status\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"description\":\"锁定状态\",\"default_value\":\"0\",\"label\":\"锁定状态\",\"type\":\"select_one\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"lock_status\",\"options\":[{\"label\":\"未锁定\",\"value\":\"0\"},{\"label\":\"锁定\",\"value\":\"1\"}],\"define_type\":\"package\",\"is_single\":false,\"status\":\"new\",\"help_text\":\"\"},\"create_time\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":true,\"description\":\"create_time\",\"is_unique\":false,\"label\":\"创建时间\",\"time_zone\":\"\",\"type\":\"date_time\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"create_time\",\"define_type\":\"system\",\"date_format\":\"yyyy-MM-dd HH:mm:ss\",\"status\":\"released\"},\"life_status\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"description\":\"\",\"default_value\":\"normal\",\"label\":\"生命状态\",\"type\":\"select_one\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"life_status\",\"options\":[{\"label\":\"未生效\",\"value\":\"ineffective\"},{\"label\":\"审核中\",\"value\":\"under_review\"},{\"label\":\"正常\",\"value\":\"normal\"},{\"label\":\"变更中\",\"value\":\"in_change\"},{\"label\":\"作废\",\"value\":\"invalid\"}],\"define_type\":\"package\",\"is_single\":false,\"status\":\"new\",\"help_text\":\"\"},\"last_modified_by\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":true,\"is_active\":true,\"is_unique\":false,\"label\":\"最后修改人\",\"type\":\"employee\",\"is_need_convert\":true,\"is_required\":false,\"api_name\":\"last_modified_by\",\"define_type\":\"system\",\"is_single\":true,\"status\":\"released\"},\"out_tenant_id\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":false,\"is_active\":true,\"pattern\":\"\",\"description\":\"out_tenant_id\",\"is_unique\":false,\"label\":\"外部企业\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"out_tenant_id\",\"define_type\":\"system\",\"status\":\"released\",\"max_length\":200},\"created_by\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":true,\"is_active\":true,\"is_unique\":false,\"label\":\"创建人\",\"type\":\"employee\",\"is_need_convert\":true,\"is_required\":false,\"api_name\":\"created_by\",\"define_type\":\"system\",\"is_single\":true,\"status\":\"released\"},\"version\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":false,\"length\":8,\"description\":\"version\",\"is_unique\":false,\"label\":\"version\",\"type\":\"number\",\"decimal_places\":0,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"version\",\"define_type\":\"system\",\"round_mode\":4,\"status\":\"released\"},\"record_type\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"description\":\"record_type\",\"label\":\"业务类型\",\"type\":\"record_type\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"record_type\",\"options\":[{\"is_active\":true,\"api_name\":\"default__c\",\"description\":\"预设业务类型\",\"label\":\"预设业务类型\"}],\"define_type\":\"package\",\"is_single\":false,\"status\":\"released\",\"help_text\":\"\"},\"relevant_team\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"embedded_fields\":{\"teamMemberEmployee\":{\"is_index\":true,\"is_need_convert\":true,\"is_required\":false,\"api_name\":\"teamMemberEmployee\",\"description\":\"成员员工\",\"define_type\":\"package\",\"is_unique\":false,\"label\":\"成员员工\",\"is_single\":true,\"type\":\"employee\",\"help_text\":\"成员员工\"},\"teamMemberRole\":{\"is_index\":true,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"teamMemberRole\",\"options\":[{\"label\":\"负责人\",\"value\":\"1\"},{\"label\":\"普通成员\",\"value\":\"4\"}],\"description\":\"成员角色\",\"define_type\":\"package\",\"is_unique\":false,\"label\":\"成员角色\",\"type\":\"select_one\",\"help_text\":\"成员角色\"},\"teamMemberPermissionType\":{\"is_index\":true,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"teamMemberPermissionType\",\"options\":[{\"label\":\"只读\",\"value\":\"1\"},{\"label\":\"读写\",\"value\":\"2\"}],\"description\":\"成员权限类型\",\"define_type\":\"package\",\"is_unique\":false,\"label\":\"成员权限类型\",\"type\":\"select_one\",\"help_text\":\"成员权限类型\"}},\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"description\":\"\",\"label\":\"相关团队\",\"type\":\"embedded_object_list\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"relevant_team\",\"define_type\":\"package\",\"is_single\":false,\"status\":\"new\",\"help_text\":\"相关团队\"},\"data_own_department\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"description\":\"\",\"where_type\":\"field\",\"label\":\"归属部门\",\"type\":\"department\",\"is_need_convert\":false,\"wheres\":[],\"is_required\":false,\"api_name\":\"data_own_department\",\"define_type\":\"package\",\"is_single\":true,\"status\":\"new\",\"help_text\":\"\"},\"name\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"is_unique\":true,\"description\":\"name\",\"type\":\"text\",\"default_to_zero\":false,\"is_required\":true,\"define_type\":\"system\",\"input_mode\":\"\",\"is_single\":false,\"max_length\":100,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"科目名称\",\"api_name\":\"name\",\"status\":\"new\",\"help_text\":\"\"},\"order_by\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":false,\"length\":8,\"description\":\"order_by\",\"is_unique\":false,\"label\":\"order_by\",\"type\":\"number\",\"decimal_places\":0,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"order_by\",\"define_type\":\"system\",\"round_mode\":4,\"status\":\"released\"},\"_id\":{\"describe_api_name\":\"TPMBudgetBusinessSubjectObj\",\"is_index\":false,\"is_active\":true,\"pattern\":\"\",\"description\":\"_id\",\"is_unique\":false,\"label\":\"_id\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"_id\",\"define_type\":\"system\",\"status\":\"released\",\"max_length\":200}},\"release_version\":\"6.4\"}");
        ControllerContext controllerContext = new ControllerContext(RequestContext.builder().tenantId("84931").user(User.systemUser("84931")).build(), "TPMBudgetBusinessSubjectObj", "InnerDescribe");
        System.out.println(JSON.toJSONString(serviceFacade.triggerController(controllerContext, arg, TPMBudgetBusinessSubjectObjInnerDescribeController.Result.class)));
    }

    @Test
    public void testFindButtonList() {

        RequestContext requestContext = RequestContext.builder().tenantId("84931").user(new User("84931", "1000")).build();
        ServiceContext context = new ServiceContext(requestContext, null, null);

        List<ButtonDocument> buttonList = buttonActionService.findButtonList(context, "TPMBudgetAccountObj", false);
        System.out.println(buttonList);
    }
}
