package com.facishare.crm.fmcg.tpm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.business.abstraction.IStoreCostService;
import com.facishare.crm.fmcg.tpm.dao.paas.ActivityCalculateMapperImpl;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.web.contract.GetFoundAccountList;
import com.facishare.crm.fmcg.tpm.web.contract.StoreWriteOffBatchData;
import com.facishare.crm.fmcg.tpm.web.contract.WriteOffSourceDataList;
import com.facishare.crm.fmcg.tpm.web.service.StoreWriteOffService;
import com.facishare.crm.fmcg.tpm.web.service.WriteOffService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.api.IdGenerator;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/21 17:59
 */
public class WriteOffSourceServiceTest extends BaseTest {

    @Resource
    private WriteOffService writeOffSourceService;

    @Resource
    private StoreWriteOffService storeWriteOffService;

    @Resource
    private IStoreCostService storeCostService;

    @Resource
    protected ServiceFacade serviceFacade;

    @Resource
    private ActivityCalculateMapperImpl activityCalculateMapper;

    @Test
    public void getTest() {
        WriteOffSourceDataList.Arg arg = new WriteOffSourceDataList.Arg();
        arg.setId("623c46af10bf7200013443f9");
        arg.setAuditStatus(Lists.newArrayList("pass", "reject"));
        arg.setKeyword("");
        arg.setLimit(20);
        arg.setOffset(0);
        arg.setNeedReturnDescribeAndLayout(true);

        WriteOffSourceDataList.Result result = writeOffSourceService.sourceDataList(arg);

        Assert.assertNotNull(result);
    }

    @Test
    public void getID() {
        System.out.println(IdGenerator.get());
    }

    @Test
    public void col() {
        activityCalculateMapper.statisticMoneyByUnifiedActivityId("84931", "63fc1585268b72000119ee2b");

    }


    @Test
    public void batchUpdate() {
        ApiContext context = ApiContextManager.getContext();
        context.setTenantId("80063");

        StoreWriteOffBatchData.Arg arg = new StoreWriteOffBatchData.Arg();
        arg.setIdList(Lists.newArrayList("63282dc601b54200013f7ddd"));
        arg.setId("63282dc601b54200013f7ddd");
        arg.setWriteOffOwner(Lists.newArrayList("1000"));
        String m = "{store_standard: true, confirmed_amount: \"21.00\", remark: \"qqq\", audited_amount: null}";

        arg.setData((JSONObject) JSONObject.parse(m));
        storeWriteOffService.batchUpdate(arg);
    }

    @Test
    public void createWriteOffTest() {

        List<String> ids = Lists.newArrayList("645defb6c1ec6d0001898815", "645def02c1ec6d0001895d85", "6458d15ed5f2050001c6295b");
        List<IObjectData> objectDataByIds = serviceFacade.findObjectDataByIds("84931", ids, ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);
        for (IObjectData objectData : objectDataByIds) {
//            objectData.set("activity_type", "6323f006826a396d8d7102f7");
            storeCostService.addStoreWriteOffV2("84931", ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, objectData);
        }


    }

    @Test
    public void testGetFoundAccount(){
        System.out.println(JSON.toJSONString(writeOffSourceService.getFundListByType(new GetFoundAccountList.Arg())));
    }


}
