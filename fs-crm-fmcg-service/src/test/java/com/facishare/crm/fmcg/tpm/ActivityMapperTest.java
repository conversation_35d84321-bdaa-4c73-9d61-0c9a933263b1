package com.facishare.crm.fmcg.tpm;

import com.facishare.crm.fmcg.tpm.dao.pg.ActivityMapper;
import com.facishare.crm.fmcg.tpm.dao.pg.po.ActivityTypeStatisticsDatumPO;
import org.junit.Test;
import org.spockframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/22 14:46
 */
public class ActivityMapperTest extends BaseTest {

    @Resource
    private ActivityMapper activityMapper;

    @Test
    public void queryActivityTypeStatisticsDataTest() {
        List<ActivityTypeStatisticsDatumPO> data = activityMapper.setTenantId("80063").queryActivityTypeStatisticsData("80063", "61b9b2e925e9ff6bd68582c4");

        Assert.notNull(data);
    }
}
