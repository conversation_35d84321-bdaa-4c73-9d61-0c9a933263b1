package com.facishare.crm.fmcg.budget;

import com.facishare.crm.fmcg.tpm.BaseTest;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.web.service.PromotionPolicyService;
import org.junit.Test;

import javax.annotation.Resource;

public class PromotionTest extends BaseTest {

    @Resource
    private PromotionPolicyService promotionPolicyService;

    @Test
    public void freezeAndCancelTest() {
        promotionPolicyService.bindPluginInstance("84931",1000, ApiNames.PRICE_POLICY_OBJ,"tpm_price_policy");
        promotionPolicyService.bindPluginInstance("84931",1000, ApiNames.PRICE_POLICY_LIMIT_ACCOUNT_OBJ,"tpm_price_policy_limit_account");
    }


}
