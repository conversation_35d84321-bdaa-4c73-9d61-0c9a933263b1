package com.facishare.crm.fmcg.upgrade;

import com.facishare.crm.fmcg.ocr.api.IWinePriceService;
import com.facishare.crm.fmcg.ocr.api.contract.WinePriceDetect;
import com.facishare.crm.fmcg.tpm.BaseTest;
import org.junit.Test;

import javax.annotation.Resource;

public class WinePriceTest extends BaseTest {

    @Resource
    private IWinePriceService winePriceService;

    @Test
    public void detectTest() {
        WinePriceDetect.Arg arg = new WinePriceDetect.Arg();
        arg.setPath("N_202306_30_0c29e3e7cdfc4a33958272203c8786a9");

        WinePriceDetect.Result result = winePriceService.detect(arg);

        assert result != null;
    }
}
