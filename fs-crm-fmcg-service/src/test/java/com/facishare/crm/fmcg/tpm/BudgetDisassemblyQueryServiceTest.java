package com.facishare.crm.fmcg.tpm;

import com.facishare.crm.fmcg.tpm.web.contract.DisassemblyGetDefaultValue;
import com.facishare.crm.fmcg.tpm.web.contract.DisassemblyGetDepartment;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IBudgetDisassemblyQueryService;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/22 14:46
 */
public class BudgetDisassemblyQueryServiceTest extends BaseTest {

    @Resource
    private IBudgetDisassemblyQueryService budgetDisassemblyQueryService;

    @Test
    public void queryActivityTypeStatisticsDataTest() {
        DisassemblyGetDefaultValue.Arg arg = new DisassemblyGetDefaultValue.Arg();
        arg.setParentAccountId("6333ea1c38fe2000018ac599");
        DisassemblyGetDefaultValue.Result result = budgetDisassemblyQueryService.defaultValue(arg);
        assert result != null;
    }

    @Test
    public void getDepartmentForDisassemblyAdd() {
        DisassemblyGetDepartment.Arg arg = new DisassemblyGetDepartment.Arg();
        arg.setSourceBudgetAccountId("6329ade8d8c08a000119024b");
        budgetDisassemblyQueryService.getDepartment(arg);
    }
}
