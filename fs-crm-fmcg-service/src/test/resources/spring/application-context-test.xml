<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.1.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <import resource="classpath:fmcg-framework-http.xml"/>


    <!--  framework import  -->

    <import resource="classpath:spring/metadata.xml"/>
    <import resource="classpath:spring/common.xml"/>
    <import resource="classpath:spring/dubbo.xml"/>
    <import resource="classpath:spring/log.xml"/>
    <import resource="classpath:spring/flow.xml"/>
    <import resource="classpath:spring/privilege.xml"/>
    <import resource="classpath:spring/restdriver.xml"/>
    <import resource="classpath:spring/licence.xml"/>
    <import resource="classpath:spring/fsi.xml"/>
    <import resource="classpath:spring/payment.xml"/>
    <import resource="classpath:spring/config.xml"/>
    <import resource="classpath:spring/function-service.xml"/>
    <import resource="classpath:spring/cores.xml"/>
    <import resource="classpath:privilege-temp.xml"/>
    <import resource="classpath:spring/mongo.xml"/>
    <import resource="classpath:spring/fmcg-dubbo.xml"/>
    <import resource="classpath:spring/tpm-mongo.xml"/>
    <import resource="classpath:spring/action.xml"/>
    <import resource="classpath:fs-organization-cache.xml"/>
    <import resource="classpath:spring/checkins-v2-rest-client.xml"/>
    <import resource="classpath:spring/global-transaction-tcc.xml"/>
    <import resource="classpath:META-INF/spring/fs-fsi-proxy-service.xml"/>
    <import resource="classpath:crmrest/crmrest.xml"/>
    <import resource="classpath:fmcg-sdk-ai.xml"/>
    <import resource="classpath:/META-INF/spring/fs-crm-notify-rest-api.xml"/>

    <bean id="objectResource" class="com.facishare.paas.appframework.resource.ObjectResource"/>
    <bean id="objectInnerAPIResource" class="com.facishare.paas.appframework.resource.ObjectInnerAPIResource"/>
    <bean id="objectRestAPIResource" class="com.facishare.paas.appframework.resource.ObjectRestAPIResource"/>
    <bean id="redissonCmd" class="com.fxiaoke.common.redisson.RedissonFactoryBean" p:configName="checkins-v2-redis"/>

    <!--  framework end  -->

    <context:component-scan
            base-package="com.facishare.paas.appframework,com.facishare.crm,com.fxiaoke.transaction"/>
    <context:annotation-config/>

    <!--银行支付-->
    <import resource="classpath:fs-crm-pay-rest-api.xml"/>

    <!--cep-->
    <import resource="classpath:spring/fs-paas-proxy.xml"/>

    <!--  auto config  -->
    <bean id="fmcgAutoConf"
          class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
          p:fileEncoding="UTF-8"
          p:ignoreResourceNotFound="true"
          p:ignoreUnresolvablePlaceholders="false"
          p:location="classpath:application.properties"
          p:configName="checkins-v2-config dubbo-common fs-crm-add-function-code fs-crm-icon-path fs-crm-java-config fs-crm-java-detailpage-layout-setting fs-crm-java-fsi-proxy fs-crm-java-openapi-black-list fs-crm-java-reference_relation_type fs-crm-java-special-field fs-crm-printconfig fs-crm-privilege fs-crm-sys-variable fs-eservice-cases-crm-config fs-metadata fs-paas-appframework-rest fs-paas-metadata-mongo fs-restful-common new-predefined-com.facishare.crm.privilege.objects new-predefined-object"/>
    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
          c:placeholderConfigurer-ref="fmcgAutoConf"/>
    <!--  auto config end  -->

    <!--privilege temp-->

    <bean id="redisCmd" class="com.github.jedis.support.JedisFactoryBean"
          p:configName="checkins-v2-redis"/>

    <!-- stone proxy api -->
    <bean id="stoneProxyApi" class="com.facishare.restful.client.FRestApiProxyFactoryBean">
        <property name="type" value="com.facishare.stone.sdk.StoneProxyApi"/>
    </bean>

    <bean id="messageServiceV2" class="com.facishare.restful.client.FRestApiProxyFactoryBean">
        <property name="type" value="com.fxiaoke.api.MessageServiceV2"/>
    </bean>
</beans>
