package com.facishare.crm.fmcg.common.adapter.abstraction;

import com.facishare.crm.fmcg.common.adapter.dto.UserInfo;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.BatchCloudTransfer;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.CloudTransfer;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.QueryCloudTransferDetails;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.BatchWXTenantTransfer;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.QueryWXTenantTransferDetail;

/**
 * Author: linmj
 * Date: 2023/7/27 10:54
 */
public interface IPayService {


    /**
     * 云账户转账
     *
     * @param transferArg 转账参数
     * @return 关联id
     */
    CloudTransfer.Result cloudTransfer(UserInfo transferUser, CloudTransfer.Arg transferArg);


    /**
     * 批量云账户转账
     * @param transferUser
     * @param transferArg
     * @return
     */
    BatchCloudTransfer.Result batchCloudTransfer(UserInfo transferUser, BatchCloudTransfer.Arg transferArg);

    /**
     * 批量查询转账详情
     *
     * @param arg
     * @return
     */
    QueryCloudTransferDetails.Result queryCloudTransferDetails(UserInfo queryUser, QueryCloudTransferDetails.Arg arg);

    /**
     * 批量微信商户转账
     * @param transferUser
     * @param transferArg
     * @return
     */
    BatchWXTenantTransfer.Result batchWXTenantTransfer(UserInfo transferUser, BatchWXTenantTransfer.Arg transferArg);

    /**
     * 批量查询微信商户转账详情
     * @param queryUser
     * @param arg
     * @return
     */
    QueryWXTenantTransferDetail.Result queryWXTenantTransferDetails(UserInfo queryUser, QueryWXTenantTransferDetail.Arg arg);
}
