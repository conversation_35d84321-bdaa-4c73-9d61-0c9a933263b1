{"wheres": [{"connector": "OR", "filters": [{"value_type": 0, "connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "N", "isObjectReference": false, "field_name": "store_confirm_status", "field_values": ["confirmed"]}, {"value_type": 0, "connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "EQ", "isObjectReference": false, "field_name": "signing_mode", "field_values": ["agent_signing"]}]}], "param_form": [], "actions": [], "api_name": "StoreConfirm_button_default", "describe_api_name": "TPMActivityAgreementObj", "description": "", "label": "确认协议", "button_type": "common", "use_pages": ["detail"], "jump_url": "", "define_type": "system", "is_active": true, "is_deleted": false, "version": 1}