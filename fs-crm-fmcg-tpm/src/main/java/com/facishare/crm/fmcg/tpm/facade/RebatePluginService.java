package com.facishare.crm.fmcg.tpm.facade;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.RebateFields;
import com.facishare.crm.fmcg.tpm.business.TPMTriggerActionService;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.domain.BulkInvalidActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.EditActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.InvalidActionDomainPlugin;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Slf4j
@SuppressWarnings("Duplicates")
@Service
@ServiceModule("activity_dealer_cost_relation_rebate_validate")
public class RebatePluginService {
    @ServiceMethod("invalid_before")
    public InvalidActionDomainPlugin.Result invalidBefore(InvalidActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        if (arg.getExtraData() != null && Objects.equals(TPMTriggerActionService.REQUEST_APP_NAME, arg.getExtraData().get(TPMTriggerActionService.REQUEST_FROM))) {
            log.info("from inner request,skip validate.");
            return new InvalidActionDomainPlugin.Result();
        }
        IObjectData data = arg.getObjectData().toObjectData();
        if (!StringUtils.isEmpty(data.get(RebateFields.TPM_ACTIVITY_ID, String.class)) ||
                !StringUtils.isEmpty(data.get(RebateFields.TPM_DEALER_ACTIVITY_COST_ID, String.class))) {
            throw new ValidateException(I18N.text(I18NKeys.REBATE_PLUGIN_SERVICE_0));
        }

        return new InvalidActionDomainPlugin.Result();
    }

    @ServiceMethod("bulk_invalid_before")
    public BulkInvalidActionDomainPlugin.Result bulkInvalidBefore(BulkInvalidActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        log.info("bulk_invalid_before arg:{}", JSON.toJSONString(arg));
        if (arg.getExtraData() != null && Objects.equals(TPMTriggerActionService.REQUEST_APP_NAME, arg.getExtraData().get(TPMTriggerActionService.REQUEST_FROM))) {
            log.info("from inner request,skip validate.");
            return new BulkInvalidActionDomainPlugin.Result();
        }
        List<IObjectData> dataList = arg.getObjectDataList().stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dataList)) {
            return new BulkInvalidActionDomainPlugin.Result();
        }
        List<String> exceptionDataName = Lists.newArrayList();
        dataList.forEach(data -> {
            if (!StringUtils.isEmpty(data.get(RebateFields.TPM_ACTIVITY_ID, String.class)) ||
                    !StringUtils.isEmpty(data.get(RebateFields.TPM_DEALER_ACTIVITY_COST_ID, String.class))) {
                exceptionDataName.add(data.getName());
            }
        });
        if (!CollectionUtils.isEmpty(exceptionDataName)) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.REBATE_PLUGIN_SERVICE_1), exceptionDataName));
        }
        return new BulkInvalidActionDomainPlugin.Result();
    }

    @ServiceMethod("edit_before")
    public EditActionDomainPlugin.Result before(EditActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        log.info("edit_before arg:{}", JSON.toJSONString(arg));
        IObjectData masterData = arg.getObjectData().toObjectData();
        String activityId = masterData.get(RebateFields.TPM_ACTIVITY_ID, String.class);
        String dealerCostId = masterData.get(RebateFields.TPM_DEALER_ACTIVITY_COST_ID, String.class);
        if (!StringUtils.isEmpty(activityId) || !StringUtils.isEmpty(dealerCostId)) {
            throw new ValidateException(I18N.text(I18NKeys.REBATE_PLUGIN_SERVICE_2));
        }

        return new EditActionDomainPlugin.Result();
    }

}
