package com.facishare.crm.fmcg.tpm.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.crm.fmcg.tpm.service.abstraction.PluginInstanceService;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fmcg.framework.http.TPMProxy;
import com.fmcg.framework.http.contract.tpm.DomainPluginInstance;
import com.fxiaoke.common.SqlEscaper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/1 上午10:27
 */
@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class PluginInstanceServiceImpl implements PluginInstanceService {

    @Resource
    private TPMProxy tpmProxy;

    private final SpecialTableMapper specialTableMapper = SpringUtil.getContext().getBean(SpecialTableMapper.class);

    @Override
    public Integer deletePluginUnit(String tenantId, String apiName, String pluginName) {
        //删除插件的sql
        String sql = "delete from domain_plugin_instance where tenant_id = '%s' and ref_object_api_name = '%s' and plugin_api_name ='%s'";
        int code = (specialTableMapper.setTenantId(tenantId)).deleteBySql(String.format(sql, tenantId, apiName, pluginName));
        //删除 影响行数 - code
        log.info("delete storeWriteOff effect line number is {}", code);
        return code;
    }

    @Override
    public Boolean findPluginUnit(String tenantId, String apiName, String pluginName) {
        // 查询 插件的sql
        String sql = "select id from domain_plugin_instance where tenant_id = '%s' and ref_object_api_name = '%s' and plugin_api_name ='%s'";
        List<Map> bySql = (specialTableMapper.setTenantId(tenantId)).findBySql(String.format(sql, SqlEscaper.pg_escape(tenantId), SqlEscaper.pg_escape(apiName), SqlEscaper.pg_escape(pluginName)));
        return CollectionUtils.isNotEmpty(bySql);
    }


    @Override
    public void addPluginUnit(Integer tenantId, Integer user, String apiName, String pluginName) {
        DomainPluginInstance.Arg arg = new DomainPluginInstance.Arg();
        arg.setPluginApiName(pluginName);
        arg.setObjectApiName(apiName);

        DomainPluginInstance.Result result = tpmProxy.pluginInstance(tenantId, user, arg);
        if (result == null) {
            log.error("add plugin instance remote call fail");
            return;
        }

        if (result.getCode() != 0) {
            log.info("add plugin instance fail, message is {}", result.getMessage());
        }

    }

    @Override
    public Integer deleteUseLessByPluginName(String tenantId, String pluginName) {
        //删除插件的sql
        String sql = "delete from domain_plugin_instance where plugin_api_name ='%s'";
        int code = (specialTableMapper.setTenantId(tenantId)).deleteBySql(String.format(sql, pluginName));
        //删除 影响行数 - code
        log.info("delete storeWriteOff effect line number is {}", code);
        return code;
    }
}
