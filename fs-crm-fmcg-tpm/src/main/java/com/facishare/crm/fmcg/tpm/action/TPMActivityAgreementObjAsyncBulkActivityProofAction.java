package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityAgreementFields;
import com.facishare.crm.fmcg.tpm.api.proof.PreAdd;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAsyncBulkAction;
import com.facishare.paas.appframework.core.predef.action.BaseObjectApprovalAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Author: linmj
 * Date: 2024/2/5 14:26
 */
public class TPMActivityAgreementObjAsyncBulkActivityProofAction extends AbstractStandardAsyncBulkAction<TPMActivityAgreementObjAsyncBulkActivityProofAction.Arg, TPMActivityAgreementObjActivityProofAction.Arg> {


    private IObjectData agreement;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.ACTIVITY_PROOF.getActionCode());
    }


    @Override
    protected String getButtonApiName() {
        return ObjectAction.ACTIVITY_PROOF.getButtonApiName();
    }

    @Override
    protected String getActionCode() {
        return null;
    }


    @Override
    protected void init() {
        super.init();
        agreement = this.dataList.get(0);
    }


    @Override
    protected String getDataIdByParam(TPMActivityAgreementObjActivityProofAction.Arg arg) {
        return arg.getDataId();
    }

    @Override
    protected List<TPMActivityAgreementObjActivityProofAction.Arg> getButtonParams() {
        return arg.getDataIds().stream()
                .map(data -> {
                    TPMActivityAgreementObjActivityProofAction.Arg arg = new TPMActivityAgreementObjActivityProofAction.Arg();
                    arg.setDataId(data);
                    return arg;
                })
                .collect(Collectors.toList());
    }


    @Data
    public static class Arg {

        private List<String> dataIds;


        public static Arg of(List<String> dataIds) {
            Arg arg = new Arg();
            arg.setDataIds(dataIds);
            return arg;
        }
    }

}
