package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;

/**
 * Author: linmj
 * Date: 2023/10/30 15:34
 */
public class StorePromotionRecordObjInvalidAction extends StandardInvalidAction {

    @Override
    protected void before(Arg arg) {
        throw new ValidateException(I18N.text(I18NKeys.STORE_PROMOTION_RECORD_OBJ_INVALID_ACTION_0));
    }
}
