package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TPMBudgetDisassemblyNewDetailObjInsertImportDataAction extends StandardInsertImportDataAction {


    @Override
    protected void before(Arg arg) {
        throw new ValidateException("insert import not allowed!");
    }

}
