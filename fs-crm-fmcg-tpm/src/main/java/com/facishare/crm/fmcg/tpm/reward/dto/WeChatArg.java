package com.facishare.crm.fmcg.tpm.reward.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
public class WeChatArg implements Serializable {

    /**
     * 当前微信 appId
     */
    @JSONField(name = "app_id")
    @JsonProperty(value = "app_id")
    @SerializedName("app_id")
    private String appId;

    /**
     * 微信认证token，每次都不一样
     */
    private String token;

    /**
     * 通过 appId 和 token 换来的 openId
     */
    @JSONField(name = "open_id")
    @JsonProperty(value = "open_id")
    @SerializedName("open_id")
    private String openId;

    /**
     * 通过 appId 和 token 换来的 unionId
     */
    @JSONField(name = "union_id")
    @JsonProperty(value = "union_id")
    @SerializedName("union_id")
    private String unionId;

    /**
     * 当前请求的环境,sandbox/dev/default
     */
    private String environment;

    /**
     * fxiaoke_fmcg：app_id = 纷享小程序
     * mn_nnh：app_id = 牛牛惠
     * plugin：other
     * 请求的来源，plugin/fxiaoke_fmcg/mn_nnh
     */
    private String source;

    /**
     * 杰哥解释
     */
    @JSONField(name = "tenant_code")
    @JsonProperty(value = "tenant_code")
    @SerializedName("tenant_code")
    private String tenantCode;

    private Boolean skipWxValidate = false;
}
