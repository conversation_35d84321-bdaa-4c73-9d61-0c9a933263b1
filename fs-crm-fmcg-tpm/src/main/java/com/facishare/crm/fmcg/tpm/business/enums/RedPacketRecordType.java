package com.facishare.crm.fmcg.tpm.business.enums;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/11/14 17:03
 */
public enum RedPacketRecordType {
    /**
     * 全部
     */
    ALL("ALL"),
    /**
     * 当天
     */
    TODAY("TODAY"),
    /**
     * 待提现
     */
    AWAIT("AWAIT");

    private String value;

    private RedPacketRecordType(String value) {
        this.value = value;
    }

    private static final Map<String, RedPacketRecordType> VALUE_MAP = Stream.of(values()).collect(Collectors.toMap(RedPacketRecordType::value, v -> v, (a, b) -> a));

    public String value() {
        return this.value;
    }

    public RedPacketRecordType of(String value) {
        return VALUE_MAP.get(value);
    }
}
