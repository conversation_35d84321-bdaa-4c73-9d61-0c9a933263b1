package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetCarryForwardService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IFiscalTimeService;
import com.facishare.crm.fmcg.tpm.business.dto.CarryForwardDetailDataDocument;
import com.facishare.crm.fmcg.tpm.business.enums.CarryForwardType;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/7/28 12:08
 */
@SuppressWarnings("Duplicates")
public class TPMBudgetCarryForwardObjLoadDetailDataController extends PreDefineController<TPMBudgetCarryForwardObjLoadDetailDataController.Arg, TPMBudgetCarryForwardObjLoadDetailDataController.Result> {

    private final BudgetTypeDAO budgetTypeDAO = SpringUtil.getContext().getBean(BudgetTypeDAO.class);
    private final IBudgetCarryForwardService budgetCarryForwardService = SpringUtil.getContext().getBean(IBudgetCarryForwardService.class);
    private final IFiscalTimeService fiscalTimeService = SpringUtil.getContext().getBean(IFiscalTimeService.class);

    private BudgetTypePO type;
    private BudgetTypeNodeEntity node;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    /**
     * validate budget template and period
     *
     * @param arg arg
     */
    @Override
    protected void before(Arg arg) {
        super.before(arg);

        if (!Strings.isNullOrEmpty(arg.getBudgetTemplateId())) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CARRY_FORWARD_OBJ_LOAD_DETAIL_DATA_CONTROLLER_0));
        }

        if (arg.getSourcePeriod() <= 0 || arg.getTargetPeriod() <= 0) {
            throw new ValidateException("[period] value is not a correct time stamp value.");
        }

        if (Strings.isNullOrEmpty(arg.getBudgetTypeId())) {
            throw new ValidateException("[budget_type_id] can not be null or empty.");
        }
        this.type = budgetTypeDAO.get(controllerContext.getTenantId(), arg.getBudgetTypeId());
        if (Objects.isNull(this.type)) {
            throw new ValidateException("budget type not found.");
        }

        if (Strings.isNullOrEmpty(arg.getBudgetNodeId())) {
            throw new ValidateException("[budget_node_id] can not be null or empty.");
        }
        Optional<BudgetTypeNodeEntity> oNode = this.type.getNodes().stream().filter(f -> f.getNodeId().equals(arg.getBudgetNodeId())).findFirst();
        if (!oNode.isPresent()) {
            throw new ValidateException("budget node not found.");
        }
        this.node = oNode.get();

        if (fiscalTimeService.notNextPeriod(controllerContext.getTenantId(), this.node.getTimeDimension(), arg.getSourcePeriod(), arg.getTargetPeriod())) {
            throw new ValidateException("[target_period] value must bigger than [source_period] value.");
        }
    }

    @Override
    protected Result doService(Arg arg) {
        String carryForwardType = arg.getCarryForwardType();
        ConfirmInfo confirmInfo = null;
        if (StringUtils.isEmpty(carryForwardType)) {
            confirmInfo = budgetCarryForwardService.confirmInfo(this.type, this.node, CarryForwardType.of(carryForwardType), arg.getSourcePeriod(), arg.getTargetPeriod());
            if (confirmInfo.getNotHaveNextPeriodBudgetAccountSize() == 0) {
                confirmInfo = null;
                carryForwardType = CarryForwardType.CARRY_FORWARD_WITH_NEXT_PERIOD_BUDGET.code();
            }
        }
        return Result.builder().data(budgetCarryForwardService.loadDetailData(this.type, this.node, CarryForwardType.of(carryForwardType), arg.getSourcePeriod(), arg.getTargetPeriod()))
                .confirmInfo(confirmInfo).build();
    }

    @Data
    @ToString
    public static class Arg implements Serializable {

        @SerializedName("budget_type_id")
        @JSONField(name = "budget_type_id")
        @JsonProperty("budget_type_id")
        private String budgetTypeId;

        @SerializedName("budget_node_id")
        @JSONField(name = "budget_node_id")
        @JsonProperty("budget_node_id")
        private String budgetNodeId;

        @SerializedName("budget_template_id")
        @JSONField(name = "budget_template_id")
        @JsonProperty("budget_template_id")
        private String budgetTemplateId;

        @SerializedName("source_period")
        @JSONField(name = "source_period")
        @JsonProperty("source_period")
        private long sourcePeriod;

        @SerializedName("target_period")
        @JSONField(name = "target_period")
        @JsonProperty("target_period")
        private long targetPeriod;

        @SerializedName("carry_forward_type")
        @JSONField(name = "carry_forward_type")
        @JsonProperty("carry_forward_type")
        private String carryForwardType;
    }

    @Data
    @ToString
    @Builder
    public static class Result implements Serializable {

        @SerializedName("data")
        @JSONField(name = "data")
        @JsonProperty("data")
        private List<CarryForwardDetailDataDocument> data;

        @SerializedName("confirm_info")
        @JSONField(name = "confirm_info")
        @JsonProperty("confirm_info")
        private ConfirmInfo confirmInfo;
    }

    @Data
    @ToString
    @Builder
    public static class ConfirmInfo implements Serializable {

        @SerializedName("not_have_next_period_budget_account_size")
        @JSONField(name = "not_have_next_period_budget_account_size")
        @JsonProperty("not_have_next_period_budget_account_size")
        private Integer notHaveNextPeriodBudgetAccountSize;

        @SerializedName("have_next_period_budget_account_size")
        @JSONField(name = "have_next_period_budget_account_size")
        @JsonProperty("have_next_period_budget_account_size")
        private Integer haveNextPeriodBudgetAccountSize;

        @SerializedName("total_budget_account_size")
        @JSONField(name = "total_budget_account_size")
        @JsonProperty("total_budget_account_size")
        private Integer totalBudgetAccountSize;

    }
}
