package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.tpm.api.activity.TPMActivityScript;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetAccountService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetDisassemblyService;
import com.facishare.crm.fmcg.tpm.business.abstraction.ICarryForwardActionService;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofAuditSourceConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.auth.model.AuthContext;
import com.facishare.paas.auth.model.FunctionPojo;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.UdefButton;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fmcg.framework.http.FmcgServiceProxy;
import com.fmcg.framework.http.contract.fmcgservice.ExistRecord;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.common.SqlEscaper;
import com.fxiaoke.paas.auth.factory.FuncClient;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.MapUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/8/20 下午5:54
 */
//IgnoreI18nFile
@SuppressWarnings("Duplicates")
public class TPMActivityObjScriptController extends PreDefineController<TPMActivityScript.Arg, TPMActivityScript.Result> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    private static final ICarryForwardActionService carryForwardActionService = SpringUtil.getContext().getBean(ICarryForwardActionService.class);
    private static final IBudgetDisassemblyService budgetDisassemblyService = SpringUtil.getContext().getBean(IBudgetDisassemblyService.class);
    private static final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);
    private static final ActivityTypeManager activityTypeManage = SpringUtil.getContext().getBean(ActivityTypeManager.class);
    private static final IBudgetAccountService budgetAccountService = SpringUtil.getContext().getBean(IBudgetAccountService.class);
    private static final SpecialTableMapper specialTableMapper = SpringUtil.getContext().getBean(SpecialTableMapper.class);
    private static final FmcgServiceProxy fmcgServiceProxy = SpringUtil.getContext().getBean(FmcgServiceProxy.class);

    private static final FuncClient funcClient = SpringUtil.getContext().getBean(FuncClient.class);

    private static final long[] DECIMAL_BASE = new long[]{0, 10, 100, 1000, 10000, 100000, 1000000, ********, ********0};

    @Override
    protected TPMActivityScript.Result doService(TPMActivityScript.Arg arg) {
        switch (arg.getModule()) {
            case "proof_audit_update_cost_id":
                return proofAuditUpdateCostId(arg);
            case "init_budget_button":
                return initBudgetButton(arg);
            case "proof_random_audit_status_update":
                return updateProofRandomAuditStatus(arg);
            case "init_close_activity_button":
                return initCloseActivityButton(arg);
            case "init_close_activity_template_button":
                return initCloseActivityTemplateButton(arg);
            case "init_random_audit_button":
                return initRandomAuditButton(arg);
            case "disassociate_cost":
                return disassociateCost(arg);
            case "fix_adjust_no_budget_detail":
                return fixAdjustNoBudgetDetail(arg);
            case "open_activity":
                return openActivity(arg);
            case "adjust_statistics":
                return adjustStatistics(arg);
            case "invalid_adjust":
                return invalidAdjustBySpecial(arg);
            case "reset_before_after_amount":
                return resetBeforeAfterAmountForAdjust(arg);
            case "recalculate_budget":
                return reCalculateBudget(arg);
            case "invalid_budget":
                return invalidBudget(arg);
            case "related_audit_status":
                return relatedAuditStatus(arg);
            case "init_budget_account_button":
                return initBudgetAccountButton(arg);
            case "init_store_write_off_button":
                return initStoreWriteOffButton(arg);
            case "init_budget_statistic_table_refresh_off_button":
                return initBudgetStatisticTableRefreshOffButton(arg);
            case "init_budget_accrual_button":
                return initBudgetAccrualButton(arg);
            case "enable_budget":
                return enableBudget(arg);
            case "init_carry_forward_retry_button":
                return initCarryForwardRetryButton(arg);
            case "init_disassembly_retry_button":
                return initDisassemblyRetryButton(arg);
            case "init_activity_proof_button":
                return initActivityProofButton(arg);
            case "init_close_activity_agreement_button":
                return initCloseActivityAgreementButton(arg);
            case "init_cancel_budget_provision_button":
                return initCancelBudgetProvisionButton(arg);
            case "init_reuse_provision_occupy_button":
                return initReuseProvisionOccupyButton(arg);
            default:
        }
        return new TPMActivityScript.Result();
    }

    private TPMActivityScript.Result initCarryForwardRetryButton(TPMActivityScript.Arg arg) {
        for (String tenantId : arg.getTenantIds()) {
            carryForwardActionService.initRetryButton(tenantId);
        }
        return new TPMActivityScript.Result();
    }

    private TPMActivityScript.Result initActivityProofButton(TPMActivityScript.Arg arg) {
        for (String tenantId : arg.getTenantIds()) {
            initSystemButton(tenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, ObjectAction.ACTIVITY_PROOF, Lists.newArrayList(), Lists.newArrayList("detail", "list"));
        }
        return new TPMActivityScript.Result();
    }

    private TPMActivityScript.Result initCloseActivityAgreementButton(TPMActivityScript.Arg arg) {

        IFilter filter = new Filter();
        filter.setFieldName(TPMActivityAgreementFields.AGREEMENT_STATUS);
        filter.setFieldValues(Lists.newArrayList(TPMActivityAgreementFields.AGREEMENT_STATUS__CLOSE));
        filter.setOperator(Operator.N);

        Wheres wheres = new Wheres();
        wheres.setConnector("OR");
        wheres.setFilters(Lists.newArrayList(filter));

        for (String tenantId : arg.getTenantIds()) {
            initSystemButton(tenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, ObjectAction.CLOSE_ACTIVITY_AGREEMENT, Lists.newArrayList(wheres), Lists.newArrayList("detail"));
        }
        return new TPMActivityScript.Result();
    }

    private TPMActivityScript.Result initCancelBudgetProvisionButton(TPMActivityScript.Arg arg) {


        IFilter filter = new Filter();
        filter.setFieldName(TPMBudgetProvisionObjFields.PROVISION_STATUS);
        filter.setOperator(Operator.NEQ);
        filter.setFieldValues(Lists.newArrayList(TPMBudgetProvisionObjFields.ProvisionStatus.OCCUPIED));

        Wheres wheres = new Wheres();
        wheres.setConnector("OR");
        wheres.setFilters(Lists.newArrayList(filter));

        for (String tenantId : arg.getTenantIds()) {
            initSystemButton(tenantId, ApiNames.TPM_BUDGET_PROVISION_OBJ, ObjectAction.CANCEL_BUDGET_PROVISION, Lists.newArrayList(wheres), Lists.newArrayList("detail", "list"));
        }
        return new TPMActivityScript.Result();
    }

    private TPMActivityScript.Result initReuseProvisionOccupyButton(TPMActivityScript.Arg arg) {

        IFilter filter = new Filter();
        filter.setFieldName(TPMBudgetProvisionObjFields.PROVISION_STATUS);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(TPMBudgetProvisionObjFields.ProvisionStatus.OCCUPY_FAIL));

        Wheres wheres = new Wheres();
        wheres.setConnector("OR");
        wheres.setFilters(Lists.newArrayList(filter));

        for (String tenantId : arg.getTenantIds()) {
            initSystemButton(tenantId, ApiNames.TPM_BUDGET_PROVISION_OBJ, ObjectAction.REUSE_PROVISION_OCCUPY, Lists.newArrayList(wheres), Lists.newArrayList("detail", "list"));
        }
        return new TPMActivityScript.Result();
    }

    public void initSystemButton(String tenantId, String apiName, ObjectAction buttonAction, List<Wheres> wheres, List<String> usePages) {
        IUdefButton old = serviceFacade.findButtonByApiName(User.systemUser(tenantId), buttonAction.getButtonApiName(), apiName);
        if (Objects.isNull(old)) {
            IUdefButton button = new UdefButton();
            button.setTenantId(tenantId);
            button.setDescribeApiName(apiName);
            button.setApiName(buttonAction.getButtonApiName());
            button.setLabel(buttonAction.getActionLabel());
            button.setDefineType("system");
            button.setButtonType("common");
            button.setParamForm(Lists.newArrayList());
            button.setJumpUrl("");
            button.setUrl("");
            button.setLockDataShowButton(false);

            button.setWheres(Lists.newArrayList(wheres));
            button.setDescription("");

            button.setIsActive(true);
            button.setDeleted(false);
            button.setUsePages(usePages);
            serviceFacade.createCustomButton(User.systemUser(tenantId), button);

            AuthContext authContext = AuthContext.builder().tenantId(tenantId).appId("CRM").userId("-10000").build();
            String functionCode = String.format("%s||%s", apiName, buttonAction.getActionCode());
            FunctionPojo function = new FunctionPojo();
            function.setAppId("CRM");
            function.setParentCode("00000000000000000000000000000000");
            function.setTenantId(tenantId);
            function.setFuncName(buttonAction.getActionLabel());
            function.setFuncCode(functionCode);
            function.setFuncType(0);
            function.setIsEnabled(true);
            funcClient.addFunc(authContext, Lists.newArrayList(function));

            Set<String> addFunctionCodes = Sets.newHashSet();
            addFunctionCodes.add(functionCode);
            funcClient.updateRoleModifiedFuncPermission(authContext, "00000000000000000000000000000006", addFunctionCodes, Sets.newHashSet());
        }else {
            old.setUrl("");
            serviceFacade.updateCustomButton(User.systemUser(tenantId), old);

            String functionCode = String.format("%s||%s", apiName, buttonAction.getActionCode());
            Set<String> addFunctionCodes = Sets.newHashSet();
            addFunctionCodes.add(functionCode);
            AuthContext authContext = AuthContext.builder().tenantId(tenantId).appId("CRM").userId("-10000").build();
            funcClient.updateRoleModifiedFuncPermission(authContext, "00000000000000000000000000000006", addFunctionCodes, Sets.newHashSet());
        }
    }

    private TPMActivityScript.Result initDisassemblyRetryButton(TPMActivityScript.Arg arg) {
        for (String tenantId : arg.getTenantIds()) {
            budgetDisassemblyService.initFailedRetryButton(tenantId);
        }
        return new TPMActivityScript.Result();
    }

    private TPMActivityScript.Result initBudgetStatisticTableRefreshOffButton(TPMActivityScript.Arg arg) {
        String sqlTemplate = "insert into mt_udef_button(\"id\",\"tenant_id\",\"api_name\",\"label\",\"use_pages\",\"describe_api_name\",\"description\",\"wheres\",\"param_form\",\"actions\",\"button_type\",\"jump_url\",\"create_time\",\"last_modified_time\",\"created_by\",\"last_modified_by\",\"is_active\",\"is_deleted\",\"version\",\"define_type\",\"is_batch\",\"url\",\"display\",\"redirect_type\",\"lock_data_show_button\")values('%s','%s','%s','%s','[\"detail\"]','%s','',null,'[]',null,'common','',null,null,'-10000','-10000','t','f',1,'system',null,null,null,null,null);";
        for (String tenantId : arg.getTenantIds()) {
            List<Map> list = getBudgetStatisticTableButtonList(tenantId);
            Set<String> buttonApiNameSet = new HashSet<>();
            list.forEach(v -> buttonApiNameSet.add((String) v.get("api_name")));

            if (!buttonApiNameSet.contains("StatisticTableRefresh_button_default")) {
                (specialTableMapper.setTenantId(tenantId)).insertBySql(String.format(sqlTemplate, IdGenerator.get(), tenantId, "StatisticTableRefresh_button_default", "刷新预算汇总", ApiNames.TPM_BUDGET_STATISTIC_TABLE));
            }
        }
        return new TPMActivityScript.Result();
    }

    private List<Map> getBudgetStatisticTableButtonList(String tenantId) {
        String sql = "select * from mt_udef_button where tenant_id = '%s' and api_name='StatisticTableRefresh_button_default'  and describe_api_name = 'TPMBudgetStatisticTableObj'";
        List<Map> list = (specialTableMapper.setTenantId(tenantId)).findBySql(String.format(sql, SqlEscaper.pg_escape(tenantId)));
        return CollectionUtils.isEmpty(list) ? Lists.newArrayList() : list;
    }

    TPMActivityScript.Result proofAuditUpdateCostId(TPMActivityScript.Arg arg) {
        for (String tenantId : arg.getTenantIds()) {

            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setLimit(-1);
            query.setOffset(0);

            Filter proofFilter = new Filter();
            proofFilter.setFieldName(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID);
            proofFilter.setOperator(Operator.ISN);
            proofFilter.setFieldValues(Lists.newArrayList());

            Filter costFilter = new Filter();
            costFilter.setFieldName(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID);
            costFilter.setOperator(Operator.IS);
            costFilter.setFieldValues(Lists.newArrayList());

            query.setFilters(Lists.newArrayList(proofFilter, costFilter));

            CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, query, partialData -> {
                List<IObjectData> proofs = serviceFacade.findObjectDataByIds(tenantId, partialData.stream().map(v -> v.get(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID, String.class)).collect(Collectors.toList()), ApiNames.TPM_ACTIVITY_PROOF_OBJ);
                Map<String, IObjectData> auditMap = new HashMap<>();
                partialData.forEach(audit -> auditMap.put(audit.get(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID, String.class, ""), audit));
                proofs.forEach(proof -> {
                    String costId = proof.get(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID, String.class);
                    if (!Strings.isNullOrEmpty(costId)) {
                        auditMap.get(proof.getId()).set(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID, costId);
                    } else {
                        auditMap.remove(proof.getId());
                    }
                });
                if (auditMap.size() > 0) {
                    serviceFacade.batchUpdateByFields(User.systemUser(tenantId), new ArrayList<>(auditMap.values()), Lists.newArrayList(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID));
                }
            });
        }

        return new TPMActivityScript.Result();
    }


    private TPMActivityScript.Result initStoreWriteOffButton(TPMActivityScript.Arg arg) {
        String sqlTemplate = "insert into mt_udef_button(\"id\",\"tenant_id\",\"api_name\",\"label\",\"use_pages\",\"describe_api_name\",\"description\",\"wheres\",\"param_form\",\"actions\",\"button_type\",\"jump_url\",\"create_time\",\"last_modified_time\",\"created_by\",\"last_modified_by\",\"is_active\",\"is_deleted\",\"version\",\"define_type\",\"is_batch\",\"url\",\"display\",\"redirect_type\",\"lock_data_show_button\")values('%s','%s','%s','%s','[\"list_batch\", \"list\"]','%s','',null,'[]',null,'common','',null,null,'-10000','-10000','t','f',1,'system',null,null,null,null,null);";
        for (String tenantId : arg.getTenantIds()) {
            List<Map> list = getStoreButtonList(tenantId);
            Set<String> buttonApiNameSet = new HashSet<>();
            list.forEach(v -> buttonApiNameSet.add((String) v.get("api_name")));
            if (!buttonApiNameSet.contains("CostWriteOff_button_default")) {
                (specialTableMapper.setTenantId(tenantId)).insertBySql(String.format(sqlTemplate, IdGenerator.get(), tenantId, "CostWriteOff_button_default", "门店费用核销", "TPMStoreWriteOffObj"));
            }
        }
        return new TPMActivityScript.Result();

    }

    private List<Map> getStoreButtonList(String tenantId) {
        String sql = "select * from mt_udef_button where tenant_id = '%s' and api_name = 'CostWriteOff_button_default' and describe_api_name = 'TPMStoreWriteOffObj'";
        List<Map> list = (specialTableMapper.setTenantId(tenantId)).findBySql(String.format(sql, SqlEscaper.pg_escape(tenantId)));
        return CollectionUtils.isEmpty(list) ? Lists.newArrayList() : list;
    }

    TPMActivityScript.Result initBudgetButton(TPMActivityScript.Arg arg) {
        String sqlTemplate = "insert into mt_udef_button(\"id\",\"tenant_id\",\"api_name\",\"label\",\"use_pages\",\"describe_api_name\",\"description\",\"wheres\",\"param_form\",\"actions\",\"button_type\",\"jump_url\",\"create_time\",\"last_modified_time\",\"created_by\",\"last_modified_by\",\"is_active\",\"is_deleted\",\"version\",\"define_type\",\"is_batch\",\"url\",\"display\",\"redirect_type\",\"lock_data_show_button\")values('%s','%s','%s','%s','[\"list\"]','%s','',null,'[]',null,'common','',null,null,'-10000','-10000','t','f',1,'system',null,null,null,null,null);";
        for (String tenantId : arg.getTenantIds()) {
            List<Map> list = getBudgetButtonList(tenantId);
            Set<String> buttonApiNameSet = new HashSet<>();
            list.forEach(v -> buttonApiNameSet.add((String) v.get("api_name")));
            if (!buttonApiNameSet.contains(ObjectAction.BUDGET_TRANSFER.getButtonApiName())) {
                (specialTableMapper.setTenantId(tenantId)).insertBySql(String.format(sqlTemplate, IdGenerator.get(), tenantId, ObjectAction.BUDGET_TRANSFER.getButtonApiName(), "预算调拨", "TPMActivityBudgetObj"));
            }
            if (!buttonApiNameSet.contains(ObjectAction.BUDGET_TRANSFER_IN.getButtonApiName())) {
                (specialTableMapper.setTenantId(tenantId)).insertBySql(String.format(sqlTemplate, IdGenerator.get(), tenantId, ObjectAction.BUDGET_TRANSFER_IN.getButtonApiName(), "预算追加", "TPMActivityBudgetObj"));
            }
            if (!buttonApiNameSet.contains(ObjectAction.BUDGET_TRANSFER_OUT.getButtonApiName())) {
                (specialTableMapper.setTenantId(tenantId)).insertBySql(String.format(sqlTemplate, IdGenerator.get(), tenantId, ObjectAction.BUDGET_TRANSFER_OUT.getButtonApiName(), "预算扣减", "TPMActivityBudgetObj"));
            }
        }
        return new TPMActivityScript.Result();
    }

    TPMActivityScript.Result initBudgetAccountButton(TPMActivityScript.Arg arg) {
        String sqlTemplate = "insert into mt_udef_button(\"id\",\"tenant_id\",\"api_name\",\"label\",\"use_pages\",\"describe_api_name\",\"description\",\"wheres\",\"param_form\",\"actions\",\"button_type\",\"jump_url\",\"create_time\",\"last_modified_time\",\"created_by\",\"last_modified_by\",\"is_active\",\"is_deleted\",\"version\",\"define_type\",\"is_batch\",\"url\",\"display\",\"redirect_type\",\"lock_data_show_button\")values('%s','%s','%s','%s','[\"list\"]','%s','','%s','[]',null,'common','',null,null,'-10000','-10000','t','f',1,'system',null,null,null,null,null);";
        for (String tenantId : arg.getTenantIds()) {
            List<Map> list = getBudgetAccountButtonList(tenantId);
            Set<String> buttonApiNameSet = new HashSet<>();
            list.forEach(v -> buttonApiNameSet.add((String) v.get("api_name")));

            if (!buttonApiNameSet.contains(ObjectAction.BUDGET_TRANSFER_IN.getButtonApiName())) {
                (specialTableMapper.setTenantId(tenantId)).insertBySql(String.format(sqlTemplate, IdGenerator.get(), tenantId, ObjectAction.BUDGET_TRANSFER_IN.getButtonApiName(), "预算追加", ApiNames.TPM_BUDGET_ACCOUNT, "[{\"connector\":\"OR\",\"filters\":[{\"field_name\":\"budget_status\",\"field_values\":[\"enable\"],\"operator\":\"EQ\",\"value_type\":0}]}]"));
            }
            if (!buttonApiNameSet.contains(ObjectAction.BUDGET_TRANSFER_OUT.getButtonApiName())) {
                (specialTableMapper.setTenantId(tenantId)).insertBySql(String.format(sqlTemplate, IdGenerator.get(), tenantId, ObjectAction.BUDGET_TRANSFER_OUT.getButtonApiName(), "预算扣减", ApiNames.TPM_BUDGET_ACCOUNT, "[{\"connector\":\"OR\",\"filters\":[{\"field_name\":\"budget_status\",\"field_values\":[\"enable\"],\"operator\":\"EQ\",\"value_type\":0}]}]"));
            }
            if (!buttonApiNameSet.contains(ObjectAction.BUDGET_TRANSFER.getButtonApiName())) {
                (specialTableMapper.setTenantId(tenantId)).insertBySql(String.format(sqlTemplate, IdGenerator.get(), tenantId, ObjectAction.BUDGET_TRANSFER.getButtonApiName(), "预算调拨", ApiNames.TPM_BUDGET_ACCOUNT, "[{\"connector\":\"OR\",\"filters\":[{\"field_name\":\"budget_status\",\"field_values\":[\"enable\"],\"operator\":\"EQ\",\"value_type\":0}]}]"));
            }
            if (!buttonApiNameSet.contains(ObjectAction.BUDGET_TAKE_APART.getButtonApiName())) {
                (specialTableMapper.setTenantId(tenantId)).insertBySql(String.format(sqlTemplate, IdGenerator.get(), tenantId, ObjectAction.BUDGET_TAKE_APART.getButtonApiName(), "预算拆解", ApiNames.TPM_BUDGET_ACCOUNT, "[{\"connector\":\"OR\",\"filters\":[{\"field_name\":\"budget_status\",\"field_values\":[\"enable\"],\"operator\":\"EQ\",\"value_type\":0}]}]"));
            }
            if (!buttonApiNameSet.contains(ObjectAction.ENABLE_BUDGET.getButtonApiName())) {
                String tmp = "insert into mt_udef_button(\"id\",\"tenant_id\",\"api_name\",\"label\",\"use_pages\",\"describe_api_name\",\"description\",\"wheres\",\"param_form\",\"actions\",\"button_type\",\"jump_url\",\"create_time\",\"last_modified_time\",\"created_by\",\"last_modified_by\",\"is_active\",\"is_deleted\",\"version\",\"define_type\",\"is_batch\",\"url\",\"display\",\"redirect_type\",\"lock_data_show_button\")values('%s','%s','%s','%s','%s','%s','','%s','[]',null,'common','',null,null,'-10000','-10000','t','f',1,'system',null,null,null,null,null);";
                (specialTableMapper.setTenantId(tenantId)).insertBySql(String.format(tmp, IdGenerator.get(), tenantId, ObjectAction.ENABLE_BUDGET.getButtonApiName(), "启用预算", "[\"detail\",\"list_batch\"]", ApiNames.TPM_BUDGET_ACCOUNT, "[{\"connector\":\"OR\",\"filters\":[{\"field_name\":\"budget_status\",\"field_values\":[\"disable\"],\"operator\":\"EQ\",\"value_type\":0},{\"field_name\":\"life_status\",\"field_values\":[\"normal\"],\"operator\":\"EQ\",\"value_type\":0}]}]"));
            }
        }
        return new TPMActivityScript.Result();
    }


    TPMActivityScript.Result initCloseActivityButton(TPMActivityScript.Arg arg) {
        String sqlTemplate = "insert into mt_udef_button(\"id\",\"tenant_id\",\"api_name\",\"label\",\"use_pages\",\"describe_api_name\",\"description\",\"wheres\",\"param_form\",\"actions\",\"button_type\",\"jump_url\",\"create_time\",\"last_modified_time\",\"created_by\",\"last_modified_by\",\"is_active\",\"is_deleted\",\"version\",\"define_type\",\"is_batch\",\"url\",\"display\",\"redirect_type\",\"lock_data_show_button\")values('%s','%s','%s','%s','[\"list\"]','%s','',null,'[]',null,'common','',null,null,'-10000','-10000','t','f',1,'system',null,null,null,null,null);";
        for (String tenantId : arg.getTenantIds()) {
            List<Map> list = getActivityButtonList(tenantId);
            Set<String> buttonApiNameSet = new HashSet<>();
            list.forEach(v -> buttonApiNameSet.add((String) v.get("api_name")));
            if (!buttonApiNameSet.contains("CloseTPMActivity_button_default")) {
                (specialTableMapper.setTenantId(tenantId)).insertBySql(String.format(sqlTemplate, IdGenerator.get(), tenantId, "CloseTPMActivity_button_default", "结案", "TPMActivityObj"));
            }
        }
        return new TPMActivityScript.Result();
    }

    TPMActivityScript.Result initCloseActivityTemplateButton(TPMActivityScript.Arg arg) {
        String sqlTemplate = "insert into mt_udef_button(\"id\",\"tenant_id\",\"api_name\",\"label\",\"use_pages\",\"describe_api_name\",\"description\",\"wheres\",\"param_form\",\"actions\",\"button_type\",\"jump_url\",\"create_time\",\"last_modified_time\",\"created_by\",\"last_modified_by\",\"is_active\",\"is_deleted\",\"version\",\"define_type\",\"is_batch\",\"url\",\"display\",\"redirect_type\",\"lock_data_show_button\")values('%s','%s','%s','%s','[\"list\"]','%s','',null,'[]',null,'common','',null,null,'-10000','-10000','t','f',1,'system',null,null,null,null,null);";
        for (String tenantId : arg.getTenantIds()) {
            List<Map> list = getActivityTemplateButtonList(tenantId);
            Set<String> buttonApiNameSet = new HashSet<>();
            list.forEach(v -> buttonApiNameSet.add((String) v.get("api_name")));
            if (!buttonApiNameSet.contains("CloseTPMActivity_button_default")) {
                (specialTableMapper.setTenantId(tenantId)).insertBySql(String.format(sqlTemplate, IdGenerator.get(), tenantId, "CloseTPMActivity_button_default", "结案", "TPMActivityUnifiedCaseObj"));
            }
        }
        return new TPMActivityScript.Result();
    }

    TPMActivityScript.Result initRandomAuditButton(TPMActivityScript.Arg arg) {
        String sqlTemplate = "insert into mt_udef_button(\"id\",\"tenant_id\",\"api_name\",\"label\",\"use_pages\",\"describe_api_name\",\"description\",\"wheres\",\"param_form\",\"actions\",\"button_type\",\"jump_url\",\"create_time\",\"last_modified_time\",\"created_by\",\"last_modified_by\",\"is_active\",\"is_deleted\",\"version\",\"define_type\",\"is_batch\",\"url\",\"display\",\"redirect_type\",\"lock_data_show_button\")values('%s','%s','%s','%s','[\"detail\"]','%s','',null,'[]',null,'common','',null,null,'-10000','-10000','t','f',1,'system',null,null,null,null,null);";
        for (String tenantId : arg.getTenantIds()) {
            List<Map> list = getRandomAuditButtonList(tenantId);
            Set<String> buttonApiNameSet = new HashSet<>();
            list.forEach(v -> buttonApiNameSet.add((String) v.get("api_name")));
            if (!buttonApiNameSet.contains("TPMProofRandomAudit_button_default")) {
                (specialTableMapper.setTenantId(tenantId)).insertBySql(String.format(sqlTemplate, IdGenerator.get(), tenantId, "TPMProofRandomAudit_button_default", "抽检", "TPMActivityProofAuditObj"));
            }
        }
        return new TPMActivityScript.Result();
    }

    TPMActivityScript.Result initBudgetAccrualButton(TPMActivityScript.Arg arg) {
        String sqlTemplate = "insert into mt_udef_button(\"id\",\"tenant_id\",\"api_name\",\"label\",\"use_pages\",\"describe_api_name\",\"description\",\"wheres\",\"param_form\",\"actions\",\"button_type\",\"jump_url\",\"create_time\",\"last_modified_time\",\"created_by\",\"last_modified_by\",\"is_active\",\"is_deleted\",\"version\",\"define_type\",\"is_batch\",\"url\",\"display\",\"redirect_type\",\"lock_data_show_button\")values('%s','%s','%s','%s','[\"detail\",\"list\",\"list_batch\"]','%s','',null,'[]',null,'common','',null,null,'-10000','-10000','t','f',1,'system',null,null,null,null,null);";
        for (String tenantId : arg.getTenantIds()) {
            List<Map> list = getBudgetAccrualButtonList(tenantId);
            Set<String> buttonApiNameSet = new HashSet<>();
            list.forEach(v -> buttonApiNameSet.add((String) v.get("api_name")));
            if (!buttonApiNameSet.contains("BudgetAccrual_button_default")) {
                (specialTableMapper.setTenantId(tenantId)).insertBySql(String.format(sqlTemplate, IdGenerator.get(), tenantId, "BudgetAccrual_button_default", "入账", "TPMBudgetAccrualObj"));
            }
        }
        return new TPMActivityScript.Result();
    }

    TPMActivityScript.Result updateProofRandomAuditStatus(TPMActivityScript.Arg arg) {

        for (String tenantId : arg.getTenantIds()) {
            ExistRecord.Arg existRecordArg = new ExistRecord.Arg();
            existRecordArg.setKey("TPM_AUDIT_MODE");
            existRecordArg.setValue("1");
            ExistRecord.Result existResult = fmcgServiceProxy.existRecord(Integer.parseInt(tenantId), -10000, existRecordArg);
            if (!existResult.getHasSet()) {
                log.info("{} is not random audit tenant.", tenantId);
                continue;
            }
            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setLimit(-1);
            query.setOffset(0);
            Filter randomAuditFilter = new Filter();
            randomAuditFilter.setFieldName(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS);
            randomAuditFilter.setOperator(Operator.ISN);
            randomAuditFilter.setFieldValues(Lists.newArrayList());
            query.setFilters(Lists.newArrayList(randomAuditFilter));

            CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, query, (dataLists) -> {
                Map<String, IObjectData> proofId2Data = new HashMap<>();
                dataLists.forEach(data -> {
                    String randomAuditStatus = data.get(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS, String.class);
                    if (!Strings.isNullOrEmpty(randomAuditStatus)) {
                        proofId2Data.put(data.get(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID, String.class), data);
                    }
                });
                List<IObjectData> proofs = serviceFacade.findObjectDataByIds(tenantId, new ArrayList<>(proofId2Data.keySet()), ApiNames.TPM_ACTIVITY_PROOF_OBJ);
                if (!CollectionUtils.isEmpty(proofs)) {
                    proofs.forEach(proof -> proof.set(TPMActivityProofFields.RANDOM_AUDIT_STATUS, proofId2Data.get(proof.getId()).get(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS, String.class)));
                    serviceFacade.batchUpdateByFields(User.systemUser(tenantId), proofs, Lists.newArrayList(TPMActivityProofFields.RANDOM_AUDIT_STATUS));
                }
            });

        }


        return new TPMActivityScript.Result();
    }

    private List<Map> getBudgetButtonList(String tenantId) {
        String sql = "select * from mt_udef_button where tenant_id = '%s' and api_name in ('BudgetTransfer_button_default','BudgetTransferIn_button_default','BudgetTransferOut_button_default') and describe_api_name = 'TPMActivityBudgetObj'";
        List<Map> list = (specialTableMapper.setTenantId(tenantId)).findBySql(String.format(sql, SqlEscaper.pg_escape(tenantId)));
        return CollectionUtils.isEmpty(list) ? Lists.newArrayList() : list;
    }

    private List<Map> getBudgetAccountButtonList(String tenantId) {
        String sql = "select * from mt_udef_button where tenant_id = '%s' and api_name in ('BudgetTransfer_button_default','BudgetTransferIn_button_default','BudgetTransferOut_button_default','BudgetCarryOver_button_default','BudgetTakeApart_button_default','EnableBudget_button_default') and describe_api_name = 'TPMBudgetAccountObj'";
        List<Map> list = (specialTableMapper.setTenantId(tenantId)).findBySql(String.format(sql, SqlEscaper.pg_escape(tenantId)));
        return CollectionUtils.isEmpty(list) ? Lists.newArrayList() : list;
    }

    private List<Map> getActivityButtonList(String tenantId) {
        String sql = "select * from mt_udef_button where tenant_id = '%s' and api_name ='CloseTPMActivity_button_default' and describe_api_name = 'TPMActivityObj'";
        List<Map> list = (specialTableMapper.setTenantId(tenantId)).findBySql(String.format(sql, SqlEscaper.pg_escape(tenantId)));
        return CollectionUtils.isEmpty(list) ? Lists.newArrayList() : list;
    }

    private List<Map> getActivityTemplateButtonList(String tenantId) {
        String sql = "select * from mt_udef_button where tenant_id = '%s' and api_name ='CloseTPMActivity_button_default' and describe_api_name = 'TPMActivityUnifiedCaseObj'";
        List<Map> list = (specialTableMapper.setTenantId(tenantId)).findBySql(String.format(sql, SqlEscaper.pg_escape(tenantId)));
        return CollectionUtils.isEmpty(list) ? Lists.newArrayList() : list;
    }

    private List<Map> getRandomAuditButtonList(String tenantId) {
        String sql = "select * from mt_udef_button where tenant_id = '%s' and api_name ='TPMProofRandomAudit_button_default' and describe_api_name = 'TPMActivityProofAuditObj'";
        List<Map> list = (specialTableMapper.setTenantId(tenantId)).findBySql(String.format(sql, SqlEscaper.pg_escape(tenantId)));
        return CollectionUtils.isEmpty(list) ? Lists.newArrayList() : list;
    }

    private List<Map> getBudgetAccrualButtonList(String tenantId) {
        String sql = "select * from mt_udef_button where tenant_id = '%s' and api_name ='BudgetAccrual_button_default' and describe_api_name = 'TPMBudgetAccrualObj'";
        List<Map> list = (specialTableMapper.setTenantId(tenantId)).findBySql(String.format(sql, SqlEscaper.pg_escape(tenantId)));
        return CollectionUtils.isEmpty(list) ? Lists.newArrayList() : list;
    }


    TPMActivityScript.Result disassociateCost(TPMActivityScript.Arg arg) {
        log.info("disassociate cost arg:{}", arg);
        JSONArray idLists = arg.getParams().getJSONArray("ids");
        arg.getTenantIds().forEach(tenantId -> {
            idLists.forEach(id -> {
                String costId = (String) id;

                SearchTemplateQuery proofQuery = new SearchTemplateQuery();
                proofQuery.setLimit(-1);
                proofQuery.setOffset(0);

                IFilter costFilter = new Filter();
                costFilter.setFieldName(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID);
                costFilter.setOperator(Operator.EQ);
                costFilter.setFieldValues(Lists.newArrayList(costId));
                proofQuery.setFilters(Lists.newArrayList(costFilter));

                List<IObjectData> proofs = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PROOF_OBJ, proofQuery);

                List<String> proofUpdateFields = Lists.newArrayList(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID);
                for (List<IObjectData> proofObjs : Lists.partition(proofs, 200)) {
                    proofObjs.forEach(v -> v.set(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID, ""));
                    serviceFacade.batchUpdateByFields(User.systemUser(tenantId), proofObjs, proofUpdateFields);
                }


                SearchTemplateQuery proofAuditQuery = new SearchTemplateQuery();
                proofAuditQuery.setLimit(-1);
                proofAuditQuery.setOffset(0);

                IFilter costForAuditFilter = new Filter();
                costForAuditFilter.setFieldName(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID);
                costForAuditFilter.setOperator(Operator.EQ);
                costForAuditFilter.setFieldValues(Lists.newArrayList(costId));
                proofAuditQuery.setFilters(Lists.newArrayList(costForAuditFilter));

                List<IObjectData> proofAudits = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, proofAuditQuery);

                List<String> proofAuditUpdateFields = Lists.newArrayList(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID);
                for (List<IObjectData> proofAuditObjs : Lists.partition(proofAudits, 200)) {
                    proofAuditObjs.forEach(v -> v.set(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID, ""));
                    serviceFacade.batchUpdateByFields(User.systemUser(tenantId), proofAuditObjs, proofAuditUpdateFields);
                }
            });
        });

        return new TPMActivityScript.Result();
    }

    TPMActivityScript.Result fixAdjustNoBudgetDetail(TPMActivityScript.Arg arg) {
        String baseSql = "select *  from fmcg_tpm_activity_budget_adjust  adjust  left join  fmcg_tpm_activity_budget_detail  detail on detail.budget_adjust_id  = adjust.id  and  detail.is_deleted  = adjust.is_deleted  where adjust.is_deleted = 0  and detail.budget_adjust_id is null and adjust.tenant_id = '%s' order by adjust.create_time asc  limit %s offset %s";
        int limit = Integer.parseInt((String) arg.getParams().getOrDefault("limit", "100"));
        int offset = Integer.parseInt((String) arg.getParams().getOrDefault("offset", "0"));
        boolean isIncreaseMode = arg.getParams().getBooleanValue("isIncreaseMode");
        int maxNum = Integer.parseInt((String) arg.getParams().getOrDefault("maxNum", "1000"));
        Long sleepTime = arg.getParams().getLongValue("sleepTime");
        for (String tenantId : arg.getTenantIds()) {
            String querySql = String.format(baseSql, SqlEscaper.pg_escape(tenantId), SqlEscaper.pg_escape(String.valueOf(limit)), SqlEscaper.pg_escape(String.valueOf(offset)));
            List<Map> dataMapList;
            Map<String, IObjectData> budgetMap = new HashMap<>();
            int count = 0;
            while (!CollectionUtils.isEmpty(dataMapList = (specialTableMapper.setTenantId(tenantId)).findBySql(querySql))) {
                count += dataMapList.size();
                if (count > maxNum) {
                    break;
                }
                dataMapList.forEach(dataMap -> {
                    //1支出 2收入
                    String id = (String) dataMap.get("id");
                    String fromBudgetId = (String) dataMap.get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID);
                    String toBudgetId = (String) dataMap.get(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID);

                    double amount = getValue(dataMap, TPMActivityBudgetAdjustFields.AMOUNT, Double.class);
                    long createTime = getValue(dataMap, CommonFields.CREATE_TIME, Long.class);
                    if (!Strings.isNullOrEmpty(fromBudgetId)) {
                        IObjectData fromBudget = getBudget(budgetMap, tenantId, fromBudgetId);
                        String sourceId = id + "from";
                        if (!isExist(tenantId, sourceId)) {
                            double beforeAmount = getValue(dataMap, TPMActivityBudgetAdjustFields.BALANCE_BEFORE_TRANSFER_OUT, Double.class);
                            double afterAmount = getValue(dataMap, TPMActivityBudgetAdjustFields.BALANCE_AFTER_TRANSFER_OUT, Double.class);
                            IObjectData detail = addBudgetDetail(tenantId, (String) dataMap.get("owner"), "1", fromBudgetId,
                                    String.format("预算调整：「%s」调整", fromBudget.get("name")), -amount, beforeAmount, afterAmount,
                                    createTime, null, null, sourceId, id, createTime, createTime
                            );
                            updateCreateTime(tenantId, detail, createTime);
                        }
                    }

                    if (!Strings.isNullOrEmpty(toBudgetId)) {
                        IObjectData toBudget = getBudget(budgetMap, tenantId, toBudgetId);
                        String sourceId = id + "to";
                        if (!isExist(tenantId, sourceId)) {
                            double beforeAmount = getValue(dataMap, TPMActivityBudgetAdjustFields.BALANCE_BEFORE_TRANSFER_IN, Double.class);
                            double afterAmount = getValue(dataMap, TPMActivityBudgetAdjustFields.BALANCE_AFTER_TRANSFER_IN, Double.class);
                            IObjectData detail = addBudgetDetail(tenantId, (String) dataMap.get("owner"), "2", toBudgetId,
                                    String.format("预算调整：「%s」调整", toBudget.get("name")), amount, beforeAmount, afterAmount,
                                    createTime, null, null, sourceId, id, createTime, createTime
                            );
                            updateCreateTime(tenantId, detail, createTime);
                        }
                    }

                });

                if (sleepTime != 0) {
                    try {
                        Thread.sleep(sleepTime);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
                if (isIncreaseMode) {
                    offset += limit;
                    querySql = String.format(baseSql, tenantId, limit, offset);
                }
            }
            offset = Integer.parseInt((String) arg.getParams().getOrDefault("offset", "0"));
        }

        return new TPMActivityScript.Result();
    }

    private IObjectData getBudget(Map<String, IObjectData> dataMap, String tenantId, String id) {
        if (!dataMap.containsKey(id)) {
            dataMap.put(id, serviceFacade.findObjectData(User.systemUser(tenantId), id, ApiNames.TPM_ACTIVITY_BUDGET));
        }
        return dataMap.get(id);

    }

    public IObjectData addBudgetDetail(String tenantId, String owner, String type, String budgetId, String remark, double amount, double beforeBalance, double afterBalance, long operateTime, String extraData, String activityId, String sourceId, String adjustId, long createTime, long modifyTime) {
        IObjectData objectData = new ObjectData();
        objectData.setRecordType("default__c");
        objectData.set(CommonFields.LOCK_STATUS, CommonFields.LOCK_STATUS__LOCK);
        objectData.setTenantId(tenantId);
        objectData.setOwner(Lists.newArrayList(owner));
        objectData.setDescribeApiName(ApiNames.TPM_ACTIVITY_BUDGET_DETAIL_OBJ);
        objectData.set(TPMActivityBudgetDetailFields.ACTIVITY_ID, activityId);
        objectData.set(TPMActivityBudgetDetailFields.TYPE, type);
        objectData.set(TPMActivityBudgetDetailFields.BUDGET_TABLE_ID, budgetId);
        objectData.set(TPMActivityBudgetDetailFields.REMARK, remark);
        objectData.set(TPMActivityBudgetDetailFields.AMOUNT_BEFORE_OPERATION, keepNDecimal(beforeBalance, 3));
        objectData.set(TPMActivityBudgetDetailFields.AMOUNT_AFTER_OPERATION, keepNDecimal(afterBalance, 3));
        objectData.set(TPMActivityBudgetDetailFields.OPERATE_TIME, operateTime);
        objectData.set(TPMActivityBudgetDetailFields.AMOUNT, keepNDecimal(amount, 3));
        objectData.set(TPMActivityBudgetDetailFields.SOURCE_ID, sourceId);
        objectData.set(TPMActivityBudgetDetailFields.EXTRA_DATA, extraData);
        objectData.set(TPMActivityBudgetDetailFields.BUDGET_ADJUST_ID, adjustId);
        objectData.setCreateTime(createTime);
        objectData.setLastModifiedTime(modifyTime);

        return serviceFacade.saveObjectData(User.systemUser(tenantId), objectData);
    }

    private double keepNDecimal(double value, int n) {
        long base = 10;
        if (n >= DECIMAL_BASE.length) {
            for (int i = 0; i < n; i++) base *= 10;
        } else {
            base = DECIMAL_BASE[n];
        }
        return Math.round(value * base) * 1.0 / base;
    }

    private boolean isExist(String tenantId, String sourceId) {

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(1);
        Filter sourceIdFilter = new Filter();
        sourceIdFilter.setOperator(Operator.EQ);
        sourceIdFilter.setFieldName(TPMActivityBudgetDetailFields.SOURCE_ID);
        sourceIdFilter.setFieldValues(Lists.newArrayList(sourceId));
        query.setFilters(Lists.newArrayList(sourceIdFilter));

        return serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_BUDGET_DETAIL_OBJ, query).getTotalNumber() > 0;
    }

    private <T> T getValue(Map dataMap, String key, Class<T> clazz) {
        Object value = dataMap.get(key);
        if (value instanceof BigDecimal) {
            BigDecimal bigDecimal = ((BigDecimal) value);
            if (clazz.equals(Double.class)) {
                return clazz.cast(bigDecimal.doubleValue());
            } else if (clazz.equals(Integer.class)) {
                return clazz.cast(bigDecimal.intValue());
            } else if (clazz.equals(Long.class)) {
                return clazz.cast(bigDecimal.longValue());
            } else {
                return clazz.cast(bigDecimal.doubleValue());
            }
        }
        return clazz.cast(value);
    }

    private void updateCreateTime(String tenantId, IObjectData data, long time) {

        String baseSql = "update fmcg_tpm_activity_budget_detail set create_time=%s,last_modified_time=%s where id = '%s' and tenant_id = '%s'";

        (specialTableMapper.setTenantId(tenantId)).batchUpdateBySql(String.format(baseSql, time, time, data.getId(), tenantId));
    }

    TPMActivityScript.Result openActivity(TPMActivityScript.Arg arg) {
        arg.getTenantIds().forEach(tenantId -> {
            List<String> activityIds = arg.getParams().getObject("activityIds", new TypeReference<List<String>>() {
            });
            activityIds.forEach(activityId -> budgetService.openActivity(tenantId, activityId));
        });
        return new TPMActivityScript.Result();
    }


    TPMActivityScript.Result adjustStatistics(TPMActivityScript.Arg arg) {
        arg.getTenantIds().forEach(tenantId -> {

            Map<String, List<Double>> budgetMap = new HashMap<>();
            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setLimit(-1);
            query.setOffset(0);

            Filter valueFilter = new Filter();
            valueFilter.setFieldValues(Lists.newArrayList("true"));
            valueFilter.setOperator(Operator.EQ);
            valueFilter.setFieldName("is_historical_order_data__c");
            query.setFilters(Lists.newArrayList(valueFilter));


            CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_BUDGET_ADJUST_OBJ, query, partialData -> partialData.forEach(data -> {
                String fromBudgetId = data.get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID, String.class);
                if (!Strings.isNullOrEmpty(fromBudgetId)) {
                    List<Double> amountList = budgetMap.getOrDefault(fromBudgetId, Lists.newArrayList(0D, 0D));
                    amountList.set(0, amountList.get(0) - data.get(TPMActivityBudgetAdjustFields.AMOUNT, Double.class));
                    budgetMap.putIfAbsent(fromBudgetId, amountList);
                }
                String toBudgetId = data.get(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID, String.class);
                if (!Strings.isNullOrEmpty(toBudgetId)) {
                    List<Double> amountList = budgetMap.getOrDefault(toBudgetId, Lists.newArrayList(0D, 0D));
                    amountList.set(1, amountList.get(1) + data.get(TPMActivityBudgetAdjustFields.AMOUNT, Double.class));
                    budgetMap.putIfAbsent(toBudgetId, amountList);
                }
            }));

            String selfApiName = (String) arg.getParams().getOrDefault("apiName", "historical_order_statistics__c");
            budgetMap.forEach((budgetId, valueList) -> {
                IObjectData budget = serviceFacade.findObjectData(User.systemUser(tenantId), budgetId, ApiNames.TPM_ACTIVITY_BUDGET);
                //部门预算
                if ("default__c".equals(budget.getRecordType())) {

                    ObjectData selfData = new ObjectData();
                    selfData.setOwner(Lists.newArrayList("-10000"));
                    selfData.setRecordType("default__c");
                    selfData.setTenantId(tenantId);
                    selfData.setDescribeApiName(selfApiName);
                    selfData.set("department__c", budget.get(TPMActivityBudgetFields.BUDGET_DEPARTMENT));
                    selfData.set("in__c", CommonUtils.keepNDecimal(valueList.get(1), 3));
                    selfData.set("out__c", CommonUtils.keepNDecimal(valueList.get(0), 3));
                    serviceFacade.saveObjectData(User.systemUser(tenantId), selfData);

                } else if ("record_IF13q__c".equals(budget.getRecordType())) {
                    ObjectData selfData = new ObjectData();
                    selfData.setOwner(Lists.newArrayList("-10000"));
                    selfData.setRecordType("default__c");
                    selfData.setTenantId(tenantId);
                    selfData.setDescribeApiName(selfApiName);
                    selfData.set("account__c", budget.get("field_ZD7ii__c"));
                    selfData.set("in__c", CommonUtils.keepNDecimal(valueList.get(1), 3));
                    selfData.set("out__c", CommonUtils.keepNDecimal(valueList.get(0), 3));
                    serviceFacade.saveObjectData(User.systemUser(tenantId), selfData);
                }
            });
        });


        return new TPMActivityScript.Result();
    }

    TPMActivityScript.Result invalidAdjustBySpecial(TPMActivityScript.Arg arg) {

        arg.getTenantIds().forEach(tenantId -> {

            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setOffset(0);
            query.setLimit(100);

            Filter valueFilter = new Filter();
            valueFilter.setOperator(Operator.EQ);
            valueFilter.setFieldName(arg.getParams().getOrDefault("filterKey", "is_historical_order_data__c").toString());
            valueFilter.setFieldValues(Lists.newArrayList(arg.getParams().getOrDefault("filterValue", "true").toString()));

            long beginTime = Long.parseLong(arg.getParams().getOrDefault("beginTime", "0").toString());
            long endTime = Long.parseLong(arg.getParams().getOrDefault("endTime", "0").toString());
            Filter timeFilter = new Filter();
            timeFilter.setOperator(Operator.BETWEEN);
            timeFilter.setFieldName(CommonFields.CREATE_TIME);
            timeFilter.setFieldValues(Lists.newArrayList(Long.toString(beginTime), Long.toString(endTime)));
            query.setFilters(Lists.newArrayList(valueFilter, timeFilter));

            List<IObjectData> dataList;
            while ((dataList = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_BUDGET_ADJUST_OBJ, query).getData()).size() != 0) {
                log.info("left total:{}", dataList.size());
                budgetService.invalidAdjust(tenantId, dataList);
            }
        });
        return new TPMActivityScript.Result();
    }


    TPMActivityScript.Result resetBeforeAfterAmountForAdjust(TPMActivityScript.Arg arg) {

        arg.getTenantIds().forEach(tenantId -> {

            if (CollectionUtils.isEmpty(arg.getParams().getJSONArray("ids"))) {
                SearchTemplateQuery query = new SearchTemplateQuery();
                query.setOffset(0);
                query.setLimit(-1);

                List<IObjectData> dataList = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_BUDGET, query);
                dataList.forEach(v -> budgetService.resetDetailOperationAmount(tenantId, v.getId()));
                dataList.forEach(v -> budgetService.calculateBudget(tenantId, v.getId()));

            } else {
                List<String> ids = arg.getParams().getObject("ids", new TypeReference<List<String>>() {
                });
                ids.forEach(id -> budgetService.resetDetailOperationAmount(tenantId, id));
                ids.forEach(id -> budgetService.calculateBudget(tenantId, id));
            }

        });
        return new TPMActivityScript.Result();
    }

    TPMActivityScript.Result reCalculateBudget(TPMActivityScript.Arg arg) {

        arg.getTenantIds().forEach(tenantId -> {

            if (CollectionUtils.isEmpty(arg.getParams().getJSONArray("ids"))) {
                SearchTemplateQuery query = new SearchTemplateQuery();
                query.setOffset(0);
                query.setLimit(-1);

                List<IObjectData> dataList = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_BUDGET, query);

                dataList.forEach(v -> {
                    String uuid = UUID.randomUUID().toString();
                    try {
                        budgetService.tryLockBudget(tenantId, v.getId(), uuid);
                        budgetService.calculateBudget(tenantId, v.getId());
                    } finally {
                        budgetService.unLockBudget(tenantId, v.getId(), uuid);
                    }
                });

            } else {
                List<String> ids = arg.getParams().getObject("ids", new TypeReference<List<String>>() {
                });
                ids.forEach(id -> {
                    String uuid = UUID.randomUUID().toString();
                    try {
                        budgetService.tryLockBudget(tenantId, id, uuid);
                        budgetService.calculateBudget(tenantId, id);
                    } finally {
                        budgetService.unLockBudget(tenantId, id, uuid);
                    }

                });
            }
        });
        return new TPMActivityScript.Result();
    }

    TPMActivityScript.Result invalidBudget(TPMActivityScript.Arg arg) {
        arg.getTenantIds().forEach(tenantId -> {
            List<String> ids = arg.getParams().getObject("ids", new TypeReference<List<String>>() {
            });
            List<IObjectData> budgets = serviceFacade.findObjectDataByIds(tenantId, ids, ApiNames.TPM_ACTIVITY_BUDGET);
            budgetService.invalidBudget(tenantId, budgets);
        });

        return new TPMActivityScript.Result();
    }

    TPMActivityScript.Result relatedAuditStatus(TPMActivityScript.Arg arg) {
        arg.getTenantIds().forEach(tenantId -> {
            List<ActivityTypeExt> activityTypeExts = activityTypeManage.queryActivityTypeContainsAudit(tenantId);
            Map<String, ActivityTypeExt> id2activityTypeMap = activityTypeExts.stream().filter(v -> !ApiNames.TPM_ACTIVITY_PROOF_OBJ.equals(v.auditSourceConfig().getMasterApiName())).collect(Collectors.toMap(v -> v.get().getId().toString(), v -> v, (old, now) -> old));
            id2activityTypeMap.forEach((activityTypeId, activityTypeExt) -> {
                List<IObjectData> activities = getActivityByType(tenantId, activityTypeId);
                if (CollectionUtils.isEmpty(activities)) {
                    return;
                }
                ActivityProofAuditSourceConfigEntity sourceConfigEntity = activityTypeExt.auditSourceConfig();
                List<IObjectData> audits = getAuditByActivityIds(tenantId, activities.stream().map(DBRecord::getId).collect(Collectors.toList()));
                Map<String, String> statusMap = audits.stream().filter(v -> !Strings.isNullOrEmpty(v.get(sourceConfigEntity.getReferenceAuditSourceFieldApiName(), String.class)))
                        .collect(Collectors.toMap(v -> v.get(sourceConfigEntity.getReferenceAuditSourceFieldApiName(), String.class), v -> v.get(TPMActivityProofAuditFields.AUDIT_STATUS, String.class), (old, now) -> old));
                if (Strings.isNullOrEmpty(sourceConfigEntity.getAuditStatusApiName()) || MapUtils.isEmpty(statusMap)) {
                    return;
                }
                List<IObjectData> auditedObjects = getAuditedObjById(tenantId, sourceConfigEntity.getMasterApiName(), sourceConfigEntity.getAuditStatusApiName(), new ArrayList<>(statusMap.keySet()));
                for (List<IObjectData> parts : Lists.partition(auditedObjects, 50)) {
                    parts.forEach(part -> part.set(sourceConfigEntity.getAuditStatusApiName(), statusMap.get(part.getId())));
                    serviceFacade.batchUpdate(parts, User.systemUser(tenantId));
                }
            });
        });

        return new TPMActivityScript.Result();
    }

    private List<IObjectData> getActivityByType(String tenantId, String activityType) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        Filter filter = new Filter();
        filter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(activityType));
        query.setFilters(Lists.newArrayList(filter));
        return CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_OBJ, query);
    }

    private List<IObjectData> getAuditByActivityIds(String tenantId, List<String> activityIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        Filter filter = new Filter();
        filter.setFieldName(TPMActivityProofAuditFields.ACTIVITY_ID);
        filter.setOperator(Operator.HASANYOF);
        filter.setFieldValues(activityIds);
        query.setFilters(Lists.newArrayList(filter));
        return CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, query);
    }

    private List<IObjectData> getAuditedObjById(String tenantId, String objectApiName, String auditStatusApiName, List<String> ids) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        Filter filter = new Filter();
        filter.setFieldName(CommonFields.ID);
        filter.setOperator(Operator.HASANYOF);
        filter.setFieldValues(ids);

        Filter auditStatusFilter = new Filter();
        auditStatusFilter.setFieldName(auditStatusApiName);
        auditStatusFilter.setFieldValues(Lists.newArrayList());
        auditStatusFilter.setOperator(Operator.IS);

        query.setFilters(Lists.newArrayList(filter, auditStatusFilter));
        return CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), objectApiName, query);

    }


    TPMActivityScript.Result enableBudget(TPMActivityScript.Arg arg) {

        arg.getTenantIds().forEach(tenantId -> {

            if (!CollectionUtils.isEmpty(arg.getParams().getJSONArray("ids"))) {
                // boolean isForce = arg.getParams().getBooleanValue("is_force");
                List<String> budgetIds = arg.getParams().getObject("ids", new TypeReference<List<String>>() {
                });
                List<IObjectData> dataLists = serviceFacade.findObjectDataByIds(tenantId, budgetIds, ApiNames.TPM_BUDGET_ACCOUNT);
                dataLists.forEach(data -> budgetAccountService.enableBudget(User.systemUser(tenantId), data));
            }
        });
        return new TPMActivityScript.Result();
    }
}
