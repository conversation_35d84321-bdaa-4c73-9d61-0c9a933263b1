package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.crm.fmcg.tpm.business.dto.FiscalYear;
import groovy.lang.Tuple2;

public interface IFiscalTimeService {

    long correctPeriodTime(
            String tenantId,
            String dim,
            long time);

    boolean notSamePeriod(
            String tenantId,
            String dim,
            long a,
            long b);

    boolean notNextPeriod(
            String tenantId,
            String dim,
            long cur,
            long next
    );
    Long nextPeriod(
            String tenantId,
            String dim,
            long cur
    );
    Long parentPeriod(
            String tenantId,
            String parentDim,
            String childDim,
            long cur
    );
    Tuple2<Long, Long> calculateTimeSpan(
            String tenantId,
            String dim,
            long time
    );

    FiscalYear fiscalYear(
            String tenantId,
            String dim,
            long time
    );
}