package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.tpm.api.method.IdempotentArgBase;
import com.facishare.crm.fmcg.tpm.mq.model.ApprovalEventOBJ;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.metadata.api.IObjectData;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/3/17 3:36 PM
 */
public interface IBudgetService {

    boolean isOpenBudge(int tenantId);

    int getBudgetStartMonth(int tenantId);

    Map<String, Double> calculateActivity(String tenantId, String activityId);

    Map<String, Double> calculateBudget(String tenantId, String budgetId);

    void calculateBoth(String tenantId, String budgetId, String activityId);

    Map<String, Double> getActivityAmountFields(String tenantId, String activityId);

    Map<String, Double> getBudgetAmountFields(String tenantId, String budgetId);

    Double getBudgetAvailableAmount(String tenantId, IObjectData budget,Map<String,Double> amountMap);

    void tryLockBudget(ActionContext actionContext, String budgetId);

    void tryLockBudget(ActionContext actionContext, String uniqueKey, String budgetId);

    void tryLockBudget(String tenantId, String budgetId, String val);

    void unLockBudget(String tenantId, String budgetId, String val);

    void unLockBudget(ActionContext actionContext);

    void unLockBudget(ActionContext actionContext, String uniqueKey);

    JSONObject getApprovalInstanceSnapshot(String tenantId, String apiName, String objectId);

    IObjectData addBudgetDetail(String tenantId, IObjectData detail,IdempotentArgBase idempotent);

    IObjectData addBudgetDetail(String tenantId, String owner, String type, String budgetId, String remark, double amount, double beforeBalance, double afterBalance, long operateTime, String extraData, String activityId, String sourceId, String adjustId, IdempotentArgBase idempotent);

    IObjectData addBudgetDetail(String tenantId, String owner, String type, String budgetId, String remark, double amount, double beforeBalance, double afterBalance, long operateTime, String extraData, String activityId,String sourceId,IdempotentArgBase idempotent);

    void updateApprovalIdForDetail(String tenantId, String dataId, String approvalId);

    IObjectData getApprovalInstance(String tenantId, String apiName, String objectId);

    void updateBudgetAmount(String tenantId, IObjectData budget, Double transferInAmount, Double transferOutAmount);

    void updateBudgetAdjustAmount(String tenantId, IObjectData budgetAdjust, Double beforeTransferInAmount,Double afterTransferInAmount, Double beforeTransferOutAmount,Double afterTransferOutAmount);

    void dealPassRefundForBudgetAdjust(String tenantId, String approvalId, ApprovalEventOBJ message, IObjectData adjustObj);

    void dealRejectRefundForBudgetAdjust(String status, String tenantId, String approvalId, ApprovalEventOBJ message, IObjectData adjustObj);

    void dealRefund(String status, String tenantId, String approvalId, ApprovalEventOBJ message, IObjectData activity);

    void dealPassActivity(String status, String tenantId, String approvalId, ApprovalEventOBJ message, IObjectData activity);

    boolean needApproval(String tenantId,String apiName,String triggerType);

    boolean needApproval(String tenantId,String apiName,String dataId,String triggerType);

    boolean needUpdateApproval(String tenantId,String apiName,String dataId,String triggerType,Map<String,Object> updateMap);

    void rmSaveIdempotent(ActionContext actionContext);

    void buildCallbackKey(ActionContext actionContext);

    IObjectData getLastBudgetDetail(String tenantId,String budgetId);

    void dealPassCost(String tenantId,String activityId,String budgetId,ApprovalEventOBJ message);

    void openActivity(String tenantId,String activityId);

    void invalidAdjust(String tenantId, List<IObjectData> adjusts);

    void resetDetailOperationAmount(String tenantId,String budgetId);

    void invalidBudget(String tenantId, List<IObjectData> budgets);

    void updateActivityAmount(String tenantId, String activityId, BigDecimal changeToAmount);
}
