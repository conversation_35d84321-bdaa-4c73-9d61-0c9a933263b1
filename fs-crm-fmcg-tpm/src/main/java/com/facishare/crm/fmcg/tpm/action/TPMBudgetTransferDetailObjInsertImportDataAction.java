package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetTransferDetailFields;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;

/**
 * author: wuyx
 * description:
 * createTime: 2022/6/30 11:41
 */
@Slf4j
public class TPMBudgetTransferDetailObjInsertImportDataAction extends StandardInsertImportDataAction {

    @Override
    protected void before(Arg arg) {
        log.info("start insertImport budgetTransferDetail. arg ={}", JSON.toJSONString(arg));
        super.before(arg);
    }

    @Override
    protected List<IObjectData> importData(List<IObjectData> validList) {
        List<IObjectData> validateData = new ArrayList<>();
        List<ImportError> errorList = new ArrayList<>();
        for (IObjectData valid : validList) {
            ActionContext addActionContext = new ActionContext(actionContext.getRequestContext(), ApiNames.TPM_BUDGET_TRANSFER_DETAIL, "Add");
            addActionContext.setAttribute("triggerWorkflow", arg.getIsWorkFlowEnabled());
            addActionContext.setAttribute("triggerFlow", arg.getIsApprovalFlowEnabled());
            BaseObjectSaveAction.Arg saveArg = new BaseObjectSaveAction.Arg();
            saveArg.setObjectData(ObjectDataDocument.of(valid));
            try {
                BaseObjectSaveAction.Result result = serviceFacade.triggerAction(addActionContext, saveArg, BaseObjectSaveAction.Result.class);
                validateData.add(result.getObjectData().toObjectData());
            } catch (ValidateException ex) {
                ImportError error = new ImportError();

                Optional<ImportData> importData = this.dataList.stream().filter(data -> valid.getId().equals(data.getData().getId())).findFirst();
                Integer row = importData.get().getRowNo();
                error.setRowNo(row);
                error.setErrorMessage(ex.getMessage());
                errorList.add(error);
                log.info("insert single data.", ex);
            }
        }
        mergeErrorList(errorList);
        actionContext.setAttribute("triggerWorkflow", false);
        actionContext.setAttribute("triggerFlow", false);
        return validateData;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        return super.after(arg, result);
    }

    @Override
    protected void convertFields(List<ImportData> dataList) {
        super.convertFields(dataList);
        log.info("insertImport budgetTransferDetail dataList:{}", JSON.toJSONString(dataList));

        List<ImportError> errorList = new ArrayList<>();
        dataList.forEach(data -> {
            try {
                validationTransfer(data.getData());
            } catch (ValidateException ex) {
                ImportError error = new ImportError();
                error.setRowNo(data.getRowNo());
                error.setErrorMessage(ex.getMessage());
                errorList.add(error);
            }
        });
        mergeErrorList(errorList);
    }

    private void validationTransfer(IObjectData objectData) {

        String fromBudgetId = (String) objectData.get(TPMBudgetTransferDetailFields.TRANSFER_OUT_BUDGET_ACCOUNT_ID);
        String toBudgetId = (String) objectData.get(TPMBudgetTransferDetailFields.TRANSFER_IN_BUDGET_ACCOUNT_ID);
        if (Objects.isNull(fromBudgetId) && Objects.isNull(toBudgetId)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TRANSFER_DETAIL_OBJ_INSERT_IMPORT_DATA_ACTION_0));
        }

        String amountStr = (String) objectData.get(TPMBudgetTransferDetailFields.AMOUNT);
        if (Strings.isNullOrEmpty(amountStr)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TRANSFER_DETAIL_OBJ_INSERT_IMPORT_DATA_ACTION_1));
        }

        String recordType = (String) objectData.get(CommonFields.RECORD_TYPE);
        validationTransferOutIdTransferInId(objectData, recordType);
        //calculationTransfer(objectData, recordType, outAvailableAmount, inAvailableAmount, amount);
    }

    private void validationTransferOutIdTransferInId(IObjectData objectData, String recordType) {
        String transferOutBudgetAccountId = (String) objectData.get(TPMBudgetTransferDetailFields.TRANSFER_OUT_BUDGET_ACCOUNT_ID);
        String transferInBudgetAccountId = (String) objectData.get(TPMBudgetTransferDetailFields.TRANSFER_IN_BUDGET_ACCOUNT_ID);

        switch (recordType) {
            case TPMBudgetTransferDetailFields.RECORD_TYPE_DEFAULT:
                validationTransferOutId(transferOutBudgetAccountId);
                validationTransferInId(transferInBudgetAccountId);
                if (transferOutBudgetAccountId.equals(transferInBudgetAccountId)) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_TRANSFER_DETAIL_OBJ_INSERT_IMPORT_DATA_ACTION_2));
                }
                break;
            case TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_DEDUCT:
                validationTransferOutId(transferOutBudgetAccountId);
                validationEmptyTransferInId(transferInBudgetAccountId);
                break;
            case TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_ADD:
                validationEmptyTransferOutId(transferOutBudgetAccountId);
                validationTransferInId(transferInBudgetAccountId);
                break;
            default:
                break;
        }
    }

    private void validationTransferOutId(String transferOutId) {
        if (Strings.isNullOrEmpty(transferOutId)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TRANSFER_DETAIL_OBJ_INSERT_IMPORT_DATA_ACTION_3));
        }
    }

    private void validationTransferInId(String transferInId) {
        if (Strings.isNullOrEmpty(transferInId)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TRANSFER_DETAIL_OBJ_INSERT_IMPORT_DATA_ACTION_4));
        }
    }

    private void validationEmptyTransferOutId(String transferOutId) {
        if (!Strings.isNullOrEmpty(transferOutId)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TRANSFER_DETAIL_OBJ_INSERT_IMPORT_DATA_ACTION_5));
        }
    }

    private void validationEmptyTransferInId(String transferInId) {
        if (!Strings.isNullOrEmpty(transferInId)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TRANSFER_DETAIL_OBJ_INSERT_IMPORT_DATA_ACTION_6));
        }
    }

    private void calculationTransfer(IObjectData objectData, String recordType, BigDecimal outAvailableAmount, BigDecimal inAvailableAmount, BigDecimal amount) {
        switch (recordType) {
            case TPMBudgetTransferDetailFields.RECORD_TYPE_DEFAULT:
                objectData.set(TPMBudgetTransferDetailFields.AMOUNT_BEFORE_TRANSFER_IN, inAvailableAmount);
                objectData.set(TPMBudgetTransferDetailFields.AMOUNT_AFTER_TRANSFER_IN, inAvailableAmount.add(amount));
                objectData.set(TPMBudgetTransferDetailFields.AMOUNT_BEFORE_TRANSFER_OUT, outAvailableAmount);
                objectData.set(TPMBudgetTransferDetailFields.AMOUNT_AFTER_TRANSFER_OUT, inAvailableAmount.subtract(amount));
                break;
            case TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_ADD:
                objectData.set(TPMBudgetTransferDetailFields.AMOUNT_BEFORE_TRANSFER_IN, inAvailableAmount);
                objectData.set(TPMBudgetTransferDetailFields.AMOUNT_AFTER_TRANSFER_IN, inAvailableAmount.add(amount));
                break;
            case TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_DEDUCT:
                objectData.set(TPMBudgetTransferDetailFields.AMOUNT_BEFORE_TRANSFER_OUT, outAvailableAmount);
                objectData.set(TPMBudgetTransferDetailFields.AMOUNT_AFTER_TRANSFER_OUT, inAvailableAmount.subtract(amount));
                break;
            default:
                break;
        }
    }
}
