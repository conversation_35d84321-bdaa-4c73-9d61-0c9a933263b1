package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.business.abstraction.IProofService;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/14 下午4:39
 */
public class TPMActivityProofObjProofFrequencyController extends PreDefineController<TPMActivityProofObjProofFrequencyController.Arg, TPMActivityProofObjProofFrequencyController.Result> {

    private IProofService proofService = SpringUtil.getContext().getBean(IProofService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected Result doService(Arg arg) {

        IObjectData activity = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getActivityId(), ApiNames.TPM_ACTIVITY_OBJ);
        IObjectData store = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getStoreId(), ApiNames.ACCOUNT_OBJ);
        IObjectData agreement = Strings.isNullOrEmpty(arg.getAgreementId()) ? null : serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getAgreementId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        proofService.validateProofTimeAndFrequent(controllerContext.getTenantId(), store.getId(), activity, agreement, 2);
        return new Result();
    }

    @Data
    @ToString
    static class Arg implements Serializable {

        @SerializedName("activity_id")
        @JSONField(name = "activity_id")
        @JsonProperty("activity_id")
        private String activityId;

        @SerializedName("store_id")
        @JSONField(name = "store_id")
        @JsonProperty("store_id")
        private String storeId;

        @SerializedName("agreement_id")
        @JSONField(name = "agreement_id")
        @JsonProperty("agreement_id")
        private String agreementId;

    }

    @Data
    @ToString
    static class Result implements Serializable {

    }
}
