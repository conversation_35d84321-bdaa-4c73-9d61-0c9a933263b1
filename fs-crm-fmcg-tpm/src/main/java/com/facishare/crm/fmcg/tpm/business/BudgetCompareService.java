package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetCompareService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetSubjectService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IFiscalTimeService;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetDimensionEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import groovy.lang.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/8/13 15:49
 */
@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class BudgetCompareService implements IBudgetCompareService {

    @Resource(name = "tpmOrganizationService")
    private OrganizationService organizationService;
    @Resource
    private IBudgetSubjectService budgetSubjectService;
    @Resource
    private IFiscalTimeService fiscalTimeService;

    protected static final String DELIMITER = "^";

    protected static final Map<Integer, Integer> START_MONTH_OF_QUARTER = Maps.newHashMap();

    static {
        START_MONTH_OF_QUARTER.put(1, Calendar.JANUARY);
        START_MONTH_OF_QUARTER.put(2, Calendar.APRIL);
        START_MONTH_OF_QUARTER.put(3, Calendar.JULY);
        START_MONTH_OF_QUARTER.put(4, Calendar.OCTOBER);
    }

    @Resource
    private ServiceFacade serviceFacade;

    @Override
    public void childAccountValidate(String tenantId, BudgetTypeNodeEntity sourceNode, IObjectData sourceAccount, BudgetTypeNodeEntity targetNode, IObjectData targetAccount) {
        // 时间维度校验
        timeValidate(tenantId, sourceAccount, targetAccount, sourceNode, targetNode);
        // 部门维度校验
        departmentValidate(tenantId, sourceAccount, targetAccount, sourceNode, targetNode);
        // 其他设置的维度校验
        dimensionsValidate(tenantId, sourceAccount, targetAccount, sourceNode, targetNode);
    }

    private void timeValidate(String tenantId, IObjectData parent, IObjectData target, BudgetTypeNodeEntity parentNode, BudgetTypeNodeEntity targetNode) {
        if (parentNode.getTimeDimension().equals(targetNode.getTimeDimension())) {
            // 上下级节点时间维度配置相同时，上下级预算表的预算期间必须保持一致
            String periodApiName = "budget_period_" + parentNode.getTimeDimension();
            if (fiscalTimeService.notSamePeriod(tenantId, parentNode.getTimeDimension(), parent.get(periodApiName, Long.class), target.get(periodApiName, Long.class))) {
                throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_COMPARE_SERVICE_0), parentNode.getName(), targetNode.getName()));
            }
        } else {
            // 下级节点的时间维度是上级节点的子集时，下级预算表的预算期间必须是上级预算表预算期间的子集
            String parentPeriodApiName = "budget_period_" + parentNode.getTimeDimension();
            Tuple2<Long, Long> period = fiscalTimeService.calculateTimeSpan(tenantId, parentNode.getTimeDimension(), parent.get(parentPeriodApiName, Long.class));
            String targetPeriodApiName = "budget_period_" + targetNode.getTimeDimension();
            long targetTime = target.get(targetPeriodApiName, Long.class);
            if (targetTime < period.getFirst() || targetTime >= period.getSecond()) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_COMPARE_SERVICE_1));
            }
        }
    }

    private void departmentValidate(String tenantId, IObjectData parent, IObjectData target, BudgetTypeNodeEntity parentNode, BudgetTypeNodeEntity targetNode) {
        List<String> targetDepartmentList = CommonUtils.cast(target.get(TPMBudgetAccountFields.BUDGET_DEPARTMENT), String.class);
        if (CollectionUtils.isEmpty(targetDepartmentList)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_COMPARE_SERVICE_2));
        }
        String targetDepartment = targetDepartmentList.get(0);
        List<String> parentDepartmentList = CommonUtils.cast(parent.get(TPMBudgetAccountFields.BUDGET_DEPARTMENT), String.class);
        if (CollectionUtils.isEmpty(parentDepartmentList)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_COMPARE_SERVICE_3));
        }
        String parentDepartment = parentDepartmentList.get(0);
        if (parentNode.getDepartmentDimensionLevel() == targetNode.getDepartmentDimensionLevel()) {
            // 上下级节点部门层级配置相同时，上下级预算表的部门必须一致
            if (!parentDepartment.equals(targetDepartment)) {
                throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_COMPARE_SERVICE_4), parentNode.getName(), targetNode.getName()));
            }
        } else {
            // 下级节点的部门层级更深时，下级预算表的预算部门必须是上级预算表的预算部门的子部门
            List<Integer> departmentIds = organizationService.queryLowerDepartmentIds(Integer.parseInt(tenantId), Integer.parseInt(parentDepartment));
            if (departmentIds.stream().noneMatch(departmentId -> departmentId == Integer.parseInt(targetDepartment))) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_COMPARE_SERVICE_5));
            }
        }
    }

    private void dimensionsValidate(String tenantId, IObjectData parent, IObjectData target, BudgetTypeNodeEntity parentNode, BudgetTypeNodeEntity targetNode) {
        IObjectDescribe describe = serviceFacade.findObject(tenantId, ApiNames.TPM_BUDGET_ACCOUNT);
        Map<String, Integer> dimensionLevelMap = targetNode.getDimensions().stream().collect(Collectors.toMap(BudgetDimensionEntity::getApiName, BudgetDimensionEntity::getLevel));
        // 遍历所有设置的维度
        for (BudgetDimensionEntity dimension : parentNode.getDimensions()) {
            String parentValue = parent.get(dimension.getApiName(), String.class);
            String targetValue = target.get(dimension.getApiName(), String.class);
            String dimensionLabel = describe.getFieldDescribe(dimension.getApiName()).getLabel();

            // 维度级别相等时的校验
            if (dimension.getLevel() == dimensionLevelMap.get(dimension.getApiName())) {
                if (!Objects.equals(parentValue, targetValue)) {
                    throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_COMPARE_SERVICE_6), parentNode.getName(), targetNode.getName(), dimensionLabel));
                }
            } else {
                // 维度级别不等时的两个维度的校验
                if (dimension.getApiName().equals(TPMBudgetAccountFields.BUDGET_SUBJECT_ID)) {
                    if (!budgetSubjectService.isParent(tenantId, parentValue, targetValue)) {
                        throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_COMPARE_SERVICE_7), dimensionLabel));
                    }
                } else if (dimension.getApiName().equals(TPMBudgetAccountFields.PRODUCT_CATEGORY_ID)) {
                    if (!isParentCategory(tenantId, parentValue, targetValue)) {
                        throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_COMPARE_SERVICE_8), dimensionLabel));
                    }
                } else {
                    throw new ValidateException("level dimension not support.");
                }
            }
        }
    }

    private boolean isParentCategory(String tenantId, String parent, String target) {
        if (parent.equals(target)) {
            return false;
        }
        User sys = User.systemUser(tenantId);
        String cur = target;
        int loop = 0;
        while (loop < 15) {
            IObjectData data = serviceFacade.findObjectDataIgnoreAll(sys, cur, ApiNames.PRODUCT_CATEGORY_OBJ);
            cur = data.get("pid", String.class);
            if (Strings.isNullOrEmpty(cur)) {
                break;
            }
            if (cur.equals(parent)) {
                return true;
            }
            loop++;
        }
        return false;
    }

    @Override
    public boolean notSameDimension(Set<String> fields, IObjectData sourceData, IObjectData targetData) {
        return !Objects.equals(initDimensionKey(fields, sourceData), initDimensionKey(fields, targetData));
    }

    private String initDimensionKey(Set<String> dimensionFields, IObjectData data) {
        String department = CommonUtils.cast(data.get(TPMBudgetAccountFields.BUDGET_DEPARTMENT), String.class).get(0);
        String dimensionKey = dimensionFields.stream().map(dimensionField -> String.valueOf(data.get(dimensionField))).collect(Collectors.joining(DELIMITER));
        return department + DELIMITER + dimensionKey;
    }

    @Override
    public boolean notTheSpecifiedDepartmentLevel(String tenantId, int level, int departmentId) {
        if (DepartmentDto.COMPANY_DEPARTMENT_ID == (departmentId)) {
            return level != 0;
        }
        DepartmentDto department = organizationService.getDepartment(Integer.parseInt(tenantId), departmentId);
        log.info("department dto data : {}", JSON.toJSONString(department));
        return level != (department.getAncestors().size());
    }
}
