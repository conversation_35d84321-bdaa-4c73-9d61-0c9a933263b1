package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetAccountService;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 4:39 PM
 */
@SuppressWarnings("unused")
public class TPMBudgetAccountObjWebDetailController extends StandardWebDetailController {

    private final IBudgetAccountService budgetAccountService = SpringUtil.getContext().getBean(IBudgetAccountService.class);
    private final BudgetTypeDAO budgetTypeDAO = SpringUtil.getContext().getBean(BudgetTypeDAO.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Result inner = super.after(arg, result);

        String typeId = inner.getData().toObjectData().get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class);
        BudgetTypePO type = budgetTypeDAO.get(controllerContext.getTenantId(), typeId);
        if (Objects.isNull(type)) {
            throw new ValidateException("budget type not found.");
        }

        String nodeId = inner.getData().toObjectData().get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class);
        BudgetTypeNodeEntity node = type.getNodes().stream().filter(n -> n.getNodeId().equals(nodeId)).findFirst().orElse(null);
        if (Objects.isNull(node)) {
            throw new ValidateException("budget node not found.");
        }

        ILayout layout = budgetAccountService.overrideLayout(inner.getLayout().toLayout(), node);
        inner.setLayout(LayoutDocument.of(layout));
        return inner;
    }
}
