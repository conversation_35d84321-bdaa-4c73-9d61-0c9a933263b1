package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.business.ActivityService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 * @date 2021/3/9 下午7:28
 */
@SuppressWarnings("Duplicates")
public class TPMActivityObjIncrementUpdateAction extends StandardIncrementUpdateAction {

    public static Logger log = LoggerFactory.getLogger(TPMActivityObjIncrementUpdateAction.class);

    public static final ActivityService activityService = SpringUtil.getContext().getBean(ActivityService.class);
    private static Map<String, Set<String>> ALLOW_EDIT_FIELD_MAP = new HashMap<>();

    static {
        ConfigFactory.getConfig("fs-fmcg-tpm-config", iConfig -> {
            String editFieldJSON = iConfig.get("activity_allow_edit_field_map");
            if (!com.google.common.base.Strings.isNullOrEmpty(editFieldJSON)) {
                ALLOW_EDIT_FIELD_MAP = JSON.parseObject(editFieldJSON, new TypeReference<Map<String, Set<String>>>() {
                });
            }
        });
    }

    @Override
    protected void before(Arg arg) {

        boolean isSkipValidate = false;
        try {
            isSkipValidate = activityService.validateActivityEnableEdit(actionContext.getTenantId(), arg.getData().getId());
        } catch (ValidateException e) {
            log.info(e.getMessage());
        }

        Set<String> all = ALLOW_EDIT_FIELD_MAP.getOrDefault("all", new HashSet<>());
        if (!CollectionUtils.isEmpty(all) && all.contains(actionContext.getTenantId())) {
            isSkipValidate = true;
        }

        if (!isSkipValidate) {
            if (actionContext.isFromFunction() || actionContext.isFromOpenAPI()) {
                List<String> updateFields = new ArrayList<>();
                if (TPMGrayUtils.isAllowActivityFunctionUpdate(actionContext.getTenantId())) {
                    updateFields.addAll(Lists.newArrayList(TPMActivityFields.ACTIVITY_AMOUNT, TPMActivityFields.MULTI_DEPARTMENT_RANGE));
                }

                Set<String> allowEditFields = Stream.concat(
                        ALLOW_EDIT_FIELD_MAP.getOrDefault("default", Collections.emptySet()).stream(),
                        ALLOW_EDIT_FIELD_MAP.getOrDefault(actionContext.getTenantId(), Collections.emptySet()).stream()
                ).collect(Collectors.toSet());

                if (!CollectionUtils.isEmpty(allowEditFields)) {
                    updateFields.addAll(allowEditFields);
                }

                arg.getData().keySet().forEach(key -> {
                    if (!Strings.isNullOrEmpty(key) && !key.endsWith("__c") && !key.equals("_id") && !updateFields.contains(key))
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_INCREMENT_UPDATE_ACTION_0));
                });
            }
        }
        super.before(arg);
    }

}
