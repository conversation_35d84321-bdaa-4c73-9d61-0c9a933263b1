package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.tpm.api.consume.EndConsume;
import com.facishare.crm.fmcg.tpm.api.enumeration.LogType;
import com.facishare.crm.fmcg.tpm.api.log.LogData;
import com.facishare.crm.fmcg.tpm.api.method.IdempotentArgBase;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.business.OperateInfoService;
import com.facishare.crm.fmcg.tpm.business.UnifiedActivityCommonLogicBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetConsumeV2Service;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRedisLockService;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityWriteOffSourceConfigEntity;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.PackTransactionProxyImpl;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.service.abstraction.PackTransactionProxy;
import com.facishare.crm.fmcg.tpm.service.abstraction.TransactionService;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.service.PromotionPolicyService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectApprovalAction;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/9/8 上午10:59
 */
@Slf4j
@SuppressWarnings("Duplicates")
public class TPMActivityObjCloseTPMActivityAction extends BaseObjectApprovalAction<TPMActivityObjCloseTPMActivityAction.Arg, TPMActivityObjCloseTPMActivityAction.Result> implements TransactionService<TPMActivityObjCloseTPMActivityAction.Arg, TPMActivityObjCloseTPMActivityAction.Result> {

    private IObjectData objectData;
    private IObjectData dbData;

    private final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);
    private final IRedisLockService redisLockService = SpringUtil.getContext().getBean(IRedisLockService.class);
    private final OperateInfoService operateInfoService = SpringUtil.getContext().getBean(OperateInfoService.class);
    private final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);
    private final IBudgetConsumeV2Service budgetConsumeV2 = SpringUtil.getContext().getBean(IBudgetConsumeV2Service.class);
    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);
    private final UnifiedActivityCommonLogicBusiness unifiedActivityCommonLogicBusiness = SpringUtil.getContext().getBean(UnifiedActivityCommonLogicBusiness.class);
    private final PromotionPolicyService promotionPolicyService = SpringUtil.getContext().getBean(PromotionPolicyService.class);

    private static final List<String> ALLOW_CLOSE_LIST = Lists.newArrayList(
            TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS,
            TPMActivityFields.ACTIVITY_STATUS__SCHEDULE,
            TPMActivityFields.ACTIVITY_STATUS__END
    );

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList("CloseTPMActivity");
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getDataId());
    }

    @Override
    protected Result doAct(Arg arg) {
        Result validateResult = validateActivity();
        if (Objects.nonNull(validateResult)) {
            return validateResult;
        }

        if (!tryLock()) {
            throw new ValidateException("try lock activity failed.");
        }

        if (this.needTriggerApprovalFlow()) {
            Map<String, Map<String, Object>> changeData = Maps.newHashMap();
            Map<String, Object> changeDatum = Maps.newHashMap();
            changeDatum.put(TPMActivityFields.CLOSE_STATUS, TPMActivityFields.CLOSE_STATUS__CLOSED);
            changeData.put(this.objectData.getId(), changeDatum);

            Map<String, ApprovalFlowStartResult> startApprovalResult = this.startApprovalFlow(
                    Lists.newArrayList(this.objectData),
                    ApprovalFlowTriggerType.CLOSE_TPM_ACTIVITY,
                    changeData,
                    Maps.newHashMap(),
                    null
            );

            if (MapUtils.isNotEmpty(startApprovalResult)
                    && startApprovalResult.containsKey(this.objectData.getId())
                    && startApprovalResult.get(this.objectData.getId()).isSuccess()) {

                Map<String, Object> update = new HashMap<>();
                update.put(TPMActivityFields.ACTIVITY_STATUS, TPMActivityFields.ACTIVITY_STATUS__APPROVAL);
                serviceFacade.updateWithMap(actionContext.getUser(), this.objectData, update);

                return Result.success(objectData);
            }
        }

        return packTransactionProxy.packAct(this, arg);
    }

    private boolean tryLock() {
        return redisLockService.tryLock(actionContext.getRequestContext(), arg.getDataId(), UUID.randomUUID().toString(), 20000L);
    }

    @Override
    protected IObjectData getPreObjectData() {
        return objectData;
    }

    @Override
    protected IObjectData getPostObjectData() {
        return objectData;
    }

    @Override
    protected String getButtonApiName() {
        return "CloseTPMActivity_button_default";
    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.notEmpty(dataList)) {
            objectData = dataList.get(0);
            dbData = ObjectDataExt.of(objectData).copy();
        }
    }

    @Override
    public Result doActTransaction(Arg arg) {
        String activityId = arg.getDataId();
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);

        String budgetId = activity.get(TPMActivityFields.BUDGET_TABLE, String.class, "");
        if (!Strings.isNullOrEmpty(budgetId)) {
            budgetService.tryLockBudget(actionContext, budgetId);
            double activityAmount = activity.get(TPMActivityFields.ACTIVITY_AMOUNT, Double.class, 0.0);
            double actualAmount = activity.get(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, Double.class, 0.0);
            IObjectData budget = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), budgetId, ApiNames.TPM_ACTIVITY_BUDGET);
            Map<String, Double> amountMap = new HashMap<>();
            double availableAmount = budgetService.getBudgetAvailableAmount(actionContext.getTenantId(), budget, amountMap);


            LogData logData = LogData.builder().data(JSON.toJSONString(arg)).actionContext(JSON.toJSONString(actionContext)).build();
            logData.setAttribute("budget", budget);
            String logId = operateInfoService.log(actionContext.getTenantId(), LogType.CLOSE_ACTIVITY.value(), JSON.toJSONString(logData), actionContext.getUser().getUpstreamOwnerIdOrUserId(), ApiNames.TPM_ACTIVITY_OBJ, arg.getDataId(), false);


            if (activityAmount - actualAmount < 0 && !TPMGrayUtils.excessDeductionForCost(actionContext.getTenantId()))
                throw new ValidateException(I18N.text(I18NKeys.AMOUNT_OVER_THE_LIMIT_CAN_NOT_CLOSE));
            if (activityAmount - actualAmount > 0) {
                budgetService.addBudgetDetail(actionContext.getTenantId(), actionContext.getUser().getUpstreamOwnerIdOrUserId(),
                        "2",
                        budgetId,
                        String.format("活动结案：「%s」结案", activity.get("name")),//ignorei18n
                        activityAmount - actualAmount,
                        availableAmount,
                        availableAmount + activityAmount - actualAmount,
                        System.currentTimeMillis(),
                        String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                        arg.getDataId(),
                        TraceContext.get().getTraceId(),
                        IdempotentArgBase.builder().idempotentKey(actionContext.getPostId() + ":" + budgetId).build());
            }
        }
        EndConsume.Result endResult = budgetConsumeV2.endConsume(actionContext.getUser(), this.objectData.getDescribeApiName(), this.objectData.getId(), true);
        if (endResult.getNeedConfirm()) {
            throw new ValidateException(endResult.getTips());
        }
        //change status
        Map<String, Object> update = new HashMap<>();
        long closeTime = System.currentTimeMillis();
        long endDate = this.objectData.get(TPMActivityFields.END_DATE, Long.class);
        update.put(TPMActivityFields.CLOSE_STATUS, TPMActivityFields.CLOSE_STATUS__CLOSED);
        update.put(TPMActivityFields.CLOSE_TIME, closeTime);
        this.objectData.set(TPMActivityFields.CLOSE_STATUS, TPMActivityFields.CLOSE_STATUS__CLOSED);
        this.objectData.set(TPMActivityFields.CLOSE_TIME, closeTime);
        if (endDate >= closeTime) {
            this.objectData.set(TPMActivityFields.ACTIVITY_STATUS, TPMActivityFields.ACTIVITY_STATUS__CLOSED);
            update.put(TPMActivityFields.ACTIVITY_STATUS, TPMActivityFields.ACTIVITY_STATUS__CLOSED);
        }
        serviceFacade.updateWithMap(actionContext.getUser(), activity, update);
        budgetService.calculateBudget(actionContext.getTenantId(), activity.get(TPMActivityFields.BUDGET_TABLE, String.class, ""));

        unifiedActivityCommonLogicBusiness.recalculateUnifiedAmountField(actionContext.getTenantId(), activity.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class));
        serviceFacade.log(actionContext.getUser(), EventType.MODIFY, ActionType.Modify, this.objectDescribe, this.objectData, update, dbData);
        return Result.success(objectData);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        BuryService.asyncTpmLog(Integer.parseInt(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_ACTIVITY, BuryOperation.CLOSE_THE_CASE, false);
        //结案后更新促销类活动的政策状态
        handlePromotionPolicy(objectData);
        return super.after(arg, result);
    }

    private void handlePromotionPolicy(IObjectData objectData) {
        if (promotionPolicyService.judgedIsPromotionPolicyData(objectData)) {
            promotionPolicyService.disablePromotionPolicy(actionContext.getTenantId(), actionContext.getUser().getUpstreamOwnerIdOrUserId(), objectData);
        }
    }

    private Result validateActivity() {
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getDataId(), ApiNames.TPM_ACTIVITY_OBJ);

        if (Objects.isNull(activity)) {
            throw new ValidateException("activity not found.");
        }

        // 已结案活动不允许重复结案
        String closeStatus = (String) activity.get(TPMActivityFields.CLOSED_STATUS);
        if (TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closeStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_HAS_CLOSED));
        }

        String activityStatus = (String) activity.get(TPMActivityFields.ACTIVITY_STATUS);
        if (TPMGrayUtils.isAllowProcessActivityClose(actionContext.getTenantId())) {
            // 灰度了进行中结案之后[schedule][in_progress][end]状态下的活动方案才可以结案
            if (!ALLOW_CLOSE_LIST.contains(activityStatus)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_HAS_NOT_END_CAN_NOT_CLOSE));
            }
        } else {
            // 未灰度进行中结案的只有[end]状态下的活动方案才可以结案
            if (!TPMActivityFields.ACTIVITY_STATUS__END.equals(activityStatus)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_HAS_NOT_END_CAN_NOT_CLOSE));
            }
        }

        ActivityTypeExt activityType = activityTypeManager.find(actionContext.getTenantId(), activity.get(TPMActivityFields.ACTIVITY_TYPE, String.class));
        if (!Boolean.TRUE.equals(arg.getIsConfirmWriteOff())) {
            if (Objects.nonNull(activityType.auditNode()) && hasProofUnaudited(activityType)) {
                return Result.showTips(objectData, true, I18N.text(I18NKeys.SHOWTIPS_ACTIVITY_OBJ_CLOSE_ACTIVITY_ACTION_0));
            }
            if (Objects.nonNull(activityType.writeOffNode()) && (hasWriteOffSourceDataNotWriteOffed(activityType) || hasWriteOffDataNotApproved())) {
                return Result.showTips(objectData, true, I18N.text(I18NKeys.SHOWTIPS_ACTIVITY_OBJ_CLOSE_ACTIVITY_ACTION_1));
            }
            if (Objects.nonNull(activityType.writeOffNode()) && hasWriteOffDataApproved()) {
                return Result.showTips(objectData, true, I18N.text(I18NKeys.SHOWTIPS_ACTIVITY_OBJ_CLOSE_ACTIVITY_ACTION_2));
            }
        }

        return null;
    }

    // 有未检核的举证数据
    private boolean hasProofUnaudited(ActivityTypeExt activityType) {
        IFilter proofFilter = new Filter();
        proofFilter.setFieldName(activityType.auditSourceConfig().getReferenceActivityFieldApiName());
        proofFilter.setOperator(Operator.EQ);
        proofFilter.setFieldValues(Lists.newArrayList(arg.getDataId()));

        // total count of proof
        int proofCount = count(activityType.auditSourceConfig().getMasterApiName(), proofFilter);

        IFilter auditFilter = new Filter();
        auditFilter.setFieldName(TPMActivityProofAuditFields.ACTIVITY_ID);
        auditFilter.setOperator(Operator.EQ);
        auditFilter.setFieldValues(Lists.newArrayList(arg.getDataId()));

        // total count of proof audit
        int auditCount = count(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, auditFilter);

        return proofCount > auditCount;
    }

    // 有未核销的核销依据数据
    private boolean hasWriteOffSourceDataNotWriteOffed(ActivityTypeExt activityType) {
        ActivityWriteOffSourceConfigEntity config = activityType.writeOffSourceConfig();
        if (Objects.isNull(config) || Strings.isNullOrEmpty(config.getApiName())) {
            return false;
        }

        IFilter activityFilter = new Filter();
        activityFilter.setFieldName(config.getReferenceActivityFieldApiName());
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(arg.getDataId()));

        IFilter writeOffFilter = new Filter();
        writeOffFilter.setFieldName(config.getReferenceWriteOffFieldApiName());
        writeOffFilter.setOperator(Operator.IS);
        writeOffFilter.setFieldValues(Lists.newArrayList());

        return count(config.getApiName(), activityFilter, writeOffFilter) > 0;
    }

    // 有未审核通过的核销数据
    private boolean hasWriteOffDataNotApproved() {
        IFilter activityFilter = new Filter();
        activityFilter.setFieldName(TPMDealerActivityCostFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(arg.getDataId()));

        IFilter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.IN);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("ineffective"));

        return count(ApiNames.TPM_DEALER_ACTIVITY_COST, activityFilter, lifeStatusFilter) > 0;
    }

    private boolean hasWriteOffDataApproved() {
        IFilter activityFilter = new Filter();
        activityFilter.setFieldName(TPMDealerActivityCostFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(arg.getDataId()));

        IFilter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.IN);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__UNDER_REVIEW,CommonFields.LIFE_STATUS__IN_CHANGE));

        return count(ApiNames.TPM_DEALER_ACTIVITY_COST, activityFilter, lifeStatusFilter) > 0;
    }

    // count by filters
    private int count(String apiName, IFilter... filters) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(1);
        stq.setOffset(0);
        stq.setSearchSource("db");
        stq.setFindExplicitTotalNum(true);

        stq.setFilters(Lists.newArrayList(filters));
        return serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), apiName, stq).getTotalNumber();
    }

    @Override
    protected void finallyDo() {
        try {
            super.finallyDo();
        } finally {
            budgetService.unLockBudget(actionContext);
            redisLockService.unLock(actionContext.getRequestContext());
        }
    }

    @Data
    public static class Arg {

        @SerializedName("objectDataId")
        @JSONField(name = "objectDataId")
        @JsonProperty("objectDataId")
        private String dataId;

        @SerializedName("is_confirm_write_off")
        @JSONField(name = "is_confirm_write_off")
        @JsonProperty("is_confirm_write_off")
        private Boolean isConfirmWriteOff;
    }

    @Data
    public static class Result {

        private ObjectDataDocument objectData;

        private String tips;

        private Boolean isShowTips;

        public static Result showTips(IObjectData objectData, Boolean isShowTips, String tips) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            result.setIsShowTips(isShowTips);
            result.setTips(tips);
            return result;
        }

        public static Result success(IObjectData objectData) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            result.setIsShowTips(false);
            result.setTips(null);
            return result;
        }
    }
}
