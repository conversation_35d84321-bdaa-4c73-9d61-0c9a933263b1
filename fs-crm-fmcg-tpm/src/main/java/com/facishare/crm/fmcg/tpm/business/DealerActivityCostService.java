package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.apiname.ActivityMaxWriteOffCountEnum;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDealerActivityCostService;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofAuditSourceConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityWriteOffSourceConfigEntity;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Author: linmj
 * Date: 2023/5/12 16:04
 */

@Slf4j
@Service
public class DealerActivityCostService implements IDealerActivityCostService {


    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private ActivityTypeManager activityTypeManager;

    @Resource
    private StoreBusiness storeBusiness;

    @Override
    public void validateOnceWriteOffData(String tenantId, IObjectData activity, IObjectData store, long start, long end) {
        String activityType = activity.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
        String maxWriteOffCount = activity.get(TPMActivityFields.MAX_WRITE_OFF_COUNT, String.class);
        if (!ActivityMaxWriteOffCountEnum.ONCE.value().equals(maxWriteOffCount)) {
            return;
        }
        ActivityTypeExt activityTypeExt = activityTypeManager.find(tenantId, activityType);
        ActivityProofAuditSourceConfigEntity auditSourceConfig = activityTypeExt.auditSourceConfig();
        ActivityWriteOffSourceConfigEntity writeOffSourceConfig = activityTypeExt.writeOffSourceConfig();
        if (writeOffSourceConfig == null || Strings.isNullOrEmpty(writeOffSourceConfig.getApiName())) {
            return;
        }

        validateTimeRange(tenantId, activity.getId(), store, writeOffSourceConfig, start, end);
        validateUnfinishedData(tenantId, activity.getId(), store, auditSourceConfig, writeOffSourceConfig, start, end);
        validateIfContainsAllData(tenantId, activity.getId(), store, writeOffSourceConfig, start, end);
    }

    private void validateUnfinishedData(String tenantId, String activityId, IObjectData store, ActivityProofAuditSourceConfigEntity auditSourceConfig, ActivityWriteOffSourceConfigEntity writeOffSourceConfig, long start, long end) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);
        int index = 1;
        StringBuilder pattern = new StringBuilder();
        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(writeOffSourceConfig.getReferenceActivityFieldApiName());
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));
        query.getFilters().add(activityIdFilter);
        pattern.append(index++).append(" and ");

        if (start != 0 && end != -1) {
            Filter createTimeFilter = new Filter();
            createTimeFilter.setFieldName(CommonFields.CREATE_TIME);
            createTimeFilter.setOperator(Operator.BETWEEN);
            createTimeFilter.setFieldValues(Lists.newArrayList(String.valueOf(start), String.valueOf(end)));
            query.getFilters().add(createTimeFilter);
            pattern.append(index++).append("  and ");
        }

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.IN);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__INEFFECTIVE, CommonFields.LIFE_STATUS__IN_CHANGE, CommonFields.LIFE_STATUS__UNDER_REVIEW));
        query.getFilters().add(lifeStatusFilter);
        pattern.append(index++).append("  and ");

        if (auditSourceConfig != null && auditSourceConfig.getMasterApiName().equals(writeOffSourceConfig.getApiName())) {
            Filter scheduleAuditStatusFilter = new Filter();
            scheduleAuditStatusFilter.setFieldName(auditSourceConfig.getAuditStatusApiName());
            scheduleAuditStatusFilter.setOperator(Operator.EQ);
            scheduleAuditStatusFilter.setFieldValues(Lists.newArrayList("schedule"));
            query.getFilters().add(scheduleAuditStatusFilter);

            Filter emptyAuditStatusFilter = new Filter();
            emptyAuditStatusFilter.setFieldName(auditSourceConfig.getAuditStatusApiName());
            emptyAuditStatusFilter.setOperator(Operator.IS);
            emptyAuditStatusFilter.setFieldValues(Lists.newArrayList());
            query.getFilters().add(emptyAuditStatusFilter);

            pattern.append(" ( ").append(index++).append(" or ").append(index++).append(" or ").append(index++).append(" ) ");
        } else {
            pattern.append(index++);
        }

        if (setStoreFilter(tenantId, query, writeOffSourceConfig, store)) {
            pattern.append(" and ").append(index);
        }

        query.setPattern(pattern.toString());
        List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), writeOffSourceConfig.getApiName(), query);
        if (!data.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_SERVICE_0));
        }
    }

    private void validateIfContainsAllData(String tenantId, String activityId, IObjectData store, ActivityWriteOffSourceConfigEntity writeOffSourceConfig, long start, long end) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(2000);
        query.setOffset(0);
        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(writeOffSourceConfig.getReferenceActivityFieldApiName());
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));
        query.getFilters().add(activityIdFilter);

        setStoreFilter(tenantId, query, writeOffSourceConfig, store);


        int all = serviceFacade.countObjectDataFromDB(tenantId, writeOffSourceConfig.getApiName(), query);

        query.getFilters().clear();

        Filter createTimeFilter = new Filter();
        createTimeFilter.setFieldName(CommonFields.CREATE_TIME);
        createTimeFilter.setOperator(Operator.BETWEEN);
        createTimeFilter.setFieldValues(Lists.newArrayList(String.valueOf(start), String.valueOf(end)));

        setStoreFilter(tenantId, query, writeOffSourceConfig, store);

        if (start != 0) {
            query.getFilters().addAll(Lists.newArrayList(activityIdFilter, createTimeFilter));
        } else {
            query.getFilters().add(activityIdFilter);
        }

        int range = serviceFacade.countObjectDataFromDB(tenantId, writeOffSourceConfig.getApiName(), query);

        if (all != range) {
            log.info("all:{},rage:{}", all, range);
            throw new ValidateException(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_SERVICE_1));
        }
    }

    private boolean setStoreFilter(String tenantId, SearchTemplateQuery query, ActivityWriteOffSourceConfigEntity writeOffSourceConfig, IObjectData store) {
        Filter storeFilter = new Filter();
        storeFilter.setOperator(Operator.EQ);
        if (store != null) {
            if (storeBusiness.findDealerRecordType(tenantId).contains(store.getRecordType())) {
                storeFilter.setFieldName(writeOffSourceConfig.getDealerFieldApiName());
                if (!Strings.isNullOrEmpty(writeOffSourceConfig.getDealerFieldApiName())) {
                    storeFilter.setFieldValues(Lists.newArrayList(store.getId()));
                    query.getFilters().add(storeFilter);
                    return true;
                }
            } else {
                storeFilter.setFieldName(writeOffSourceConfig.getAccountFieldApiName());
                if (!Strings.isNullOrEmpty(writeOffSourceConfig.getAccountFieldApiName())) {
                    storeFilter.setFieldValues(Lists.newArrayList(store.getId()));
                    query.getFilters().add(storeFilter);
                    return true;
                }
            }
        }
        return false;
    }

    private void validateTimeRange(String tenantId, String activityId, IObjectData store, ActivityWriteOffSourceConfigEntity writeOffSourceConfig, long start, long end) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        int index = 1;
        StringBuilder pattern = new StringBuilder();
        query.setLimit(2000);
        query.setOffset(0);
        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(writeOffSourceConfig.getReferenceActivityFieldApiName());
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));
        query.getFilters().add(activityIdFilter);
        pattern.append(index++).append(" and ");

        if (setStoreFilter(tenantId, query, writeOffSourceConfig, store)) {
            pattern.append(index++).append(" and ");
        }

        Filter gtEndTimeFilter = new Filter();
        gtEndTimeFilter.setFieldName(CommonFields.CREATE_TIME);
        gtEndTimeFilter.setOperator(Operator.GT);
        gtEndTimeFilter.setFieldValues(Lists.newArrayList(String.valueOf(end)));
        query.getFilters().add(gtEndTimeFilter);

        Filter ltEndTimeFilter = new Filter();
        ltEndTimeFilter.setFieldName(CommonFields.CREATE_TIME);
        ltEndTimeFilter.setOperator(Operator.LT);
        ltEndTimeFilter.setFieldValues(Lists.newArrayList(String.valueOf(start)));
        query.getFilters().add(ltEndTimeFilter);


        pattern.append(" ( ").append(index++).append(" or ").append(index).append(" ) ");
        query.setPattern(pattern.toString());

        int num = serviceFacade.countObjectDataFromDB(tenantId, writeOffSourceConfig.getApiName(), query);

        if (num != 0) {
            log.info("num:{}", num);
            throw new ValidateException(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_SERVICE_2));
        }
    }

}
