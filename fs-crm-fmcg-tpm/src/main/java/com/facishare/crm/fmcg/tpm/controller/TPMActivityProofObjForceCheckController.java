package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.DataPrivilegeService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import de.lab4inf.math.util.Strings;
import lombok.Data;
import lombok.ToString;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.fmcg.tpm.controller.TPMActivityAgreementObjRelatedListController.YINLU_FILTER_TIME;

/**
 * <AUTHOR>
 * @date 2022/4/20 下午2:41
 */
public class TPMActivityProofObjForceCheckController extends PreDefineController<TPMActivityProofObjForceCheckController.Arg, TPMActivityProofObjForceCheckController.Result> {


    private final DataPrivilegeService dataPrivilegeService = SpringUtil.getContext().getBean(DataPrivilegeService.class);

    private OrganizationService organizationService = SpringUtil.getContext().getBean(OrganizationService.class);

    private static Map<String, List<String>> WHITE_LIST_MAP = new HashMap<>();

    private ISearchTemplate activitySearchTemplate;

    private IObjectDescribe activityDescribe;

    private User systemUser;

    static {
        ConfigFactory.getConfig("gray-rel-fmcg", iConfig -> {
            String json = iConfig.get("YINLU_FORCE_CHECK_WHITE_LIST");
            if (!Strings.isNullOrEmpty(json)) {
                JSONObject map = JSON.parseObject(json);
                Map<String, List<String>> newMap = new HashMap<>();
                map.keySet().forEach(tenantId -> {
                    newMap.put(tenantId, map.getJSONArray(tenantId).toJavaList(String.class));
                });
                WHITE_LIST_MAP = newMap;
            }
        });
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected void before(Arg arg) {
        this.systemUser = User.systemUser(controllerContext.getTenantId());
        this.activityDescribe = serviceFacade.findDescribeAndLayout(this.systemUser, ApiNames.TPM_ACTIVITY_OBJ, false, null).getObjectDescribe();
        this.activitySearchTemplate = serviceFacade.findSearchTemplateByIdAndType(this.systemUser, "", ApiNames.TPM_ACTIVITY_OBJ, "All");
        super.before(arg);
    }

    @Override
    protected Result doService(Arg arg) {
        //todo: white list
        Result result = new Result();
        result.setAllowComplete(true);
        if (isSkip()) {
            return result;
        }
        stopWatch.lap("startQueryVisitProof");
        List<String> proofedActivityIds = new ArrayList<>();
        if (!Strings.isNullOrEmpty(arg.getVisitId())) {
            SearchTemplateQuery queryVisitProofQuery = new SearchTemplateQuery();

            queryVisitProofQuery.setLimit(-1);
            queryVisitProofQuery.setOffset(0);
            queryVisitProofQuery.setSearchSource("db");

            Filter visitIdFilter = new Filter();
            visitIdFilter.setFieldName(TPMActivityProofFields.VISIT_ID);
            visitIdFilter.setOperator(Operator.EQ);
            visitIdFilter.setFieldValues(Lists.newArrayList(arg.getVisitId()));

            Filter storeFilter = new Filter();
            storeFilter.setFieldName(TPMActivityProofFields.STORE_ID);
            storeFilter.setOperator(Operator.EQ);
            storeFilter.setFieldValues(Lists.newArrayList(arg.getStoreId()));
            queryVisitProofQuery.setFilters(Lists.newArrayList(visitIdFilter, storeFilter));

            CommonUtils.queryData(serviceFacade, User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, queryVisitProofQuery, partialData -> partialData.forEach(v -> proofedActivityIds.add(v.get(TPMActivityProofFields.ACTIVITY_ID, String.class))));
        }
        stopWatch.lap("startQueryAgreement");
        SearchTemplateQuery queryAgreement = new SearchTemplateQuery();
        queryAgreement.setOffset(0);
        queryAgreement.setLimit(2000);
        queryAgreement.setSearchSource("db");

        Filter agreementStatusFilter = new Filter();
        agreementStatusFilter.setFieldName(TPMActivityAgreementFields.AGREEMENT_STATUS);
        agreementStatusFilter.setOperator(Operator.EQ);
        agreementStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS));

        Filter storeFilter = new Filter();
        storeFilter.setFieldName(TPMActivityAgreementFields.STORE_ID);
        storeFilter.setOperator(Operator.EQ);
        storeFilter.setFieldValues(Lists.newArrayList(arg.getStoreId()));

        queryAgreement.setFilters(Lists.newArrayList(agreementStatusFilter, storeFilter));

        if (TPMGrayUtils.isYinLu(controllerContext.getTenantId())) {
            //创建时间大于 5.28号的才可以被查询
            Filter timeFilter = new Filter();
            timeFilter.setFieldName(CommonFields.CREATE_TIME);
            timeFilter.setOperator(Operator.GT);
            timeFilter.setFieldValues(Lists.newArrayList(YINLU_FILTER_TIME));
            queryAgreement.getFilters().add(timeFilter);
        }

        List<IObjectData> aggList = serviceFacade.aggregateFindBySearchQuery(controllerContext.getTenantId(), queryAgreement, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, TPMActivityAgreementFields.ACTIVITY_ID, "count", "");
        Set<String> activityIds = aggList.stream().map(v -> (String) v.get(TPMActivityAgreementFields.ACTIVITY_ID)).collect(Collectors.toSet());
        stopWatch.lap("startQueryActivity");
        if (CollectionUtils.isEmpty(activityIds)) {
            return result;
        }

        List<Integer> departmentIds;

        SearchTemplateQuery queryActivity = new SearchTemplateQuery();

        queryActivity.setSearchSource("db");

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(CommonFields.ID);
        activityIdFilter.setOperator(Operator.IN);
        activityIdFilter.setFieldValues(new ArrayList<>(activityIds));

        queryActivity.setFilters(Lists.newArrayList(activityIdFilter));

        if (TPMGrayUtils.isYinLu(controllerContext.getTenantId())) {
            //创建时间大于 5.28号的才可以被查询
            Filter timeFilter = new Filter();
            timeFilter.setFieldName(CommonFields.CREATE_TIME);
            timeFilter.setOperator(Operator.GT);
            timeFilter.setFieldValues(Lists.newArrayList(YINLU_FILTER_TIME));
            queryActivity.getFilters().add(timeFilter);
        }

        if (!controllerContext.getUser().isOutUser() && !TPMGrayUtils.denyDepartmentFilterOnActivity(controllerContext.getTenantId())) {
            departmentIds = organizationService.getDepartmentIds(controllerContext.getUser().getTenantIdInt(), Integer.parseInt(controllerContext.getUser().getUpstreamOwnerIdOrUserId()));
            if (CollectionUtils.isEmpty(departmentIds)) {
                return result;
            }
            log.info("departments : {}", departmentIds);
            Filter departmentFilter = new Filter();
            departmentFilter.setFieldName(TPMActivityFields.DEPARTMENT_RANGE);
            departmentFilter.setOperator(Operator.IN);
            departmentFilter.setFieldValues(departmentIds.stream().map(String::valueOf).collect(Collectors.toList()));
            queryActivity.getFilters().add(departmentFilter);
        }

        if (!CollectionUtils.isEmpty(arg.getActivityTypes()) && !arg.getActivityTypes().contains("all")) {
            Filter activityTypeFilter = new Filter();
            activityTypeFilter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
            activityTypeFilter.setOperator(Operator.IN);
            activityTypeFilter.setFieldValues(arg.getActivityTypes());
            queryActivity.getFilters().add(activityTypeFilter);
        }
        queryActivity.setLimit(-1);
        queryActivity.setOffset(0);

        Set<String> mainActivityIds = new HashSet<>();

        SearchQueryUtil.handleDataPrivilege(dataPrivilegeService, controllerContext.getUser(), queryActivity, this.activityDescribe, this.activitySearchTemplate, serviceFacade.isAdmin(controllerContext.getUser()));

        CommonUtils.queryData(serviceFacade, User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, queryActivity, partialData -> partialData.forEach(v -> mainActivityIds.add(v.getId())));
        stopWatch.lap("endQueryActivity");
        log.info("proof activity size:{}", proofedActivityIds.size());
        log.info("real activity size:{}", mainActivityIds.size());
        mainActivityIds.removeAll(proofedActivityIds);
        if (!mainActivityIds.isEmpty()) {
            result.setAllowComplete(false);
        }
        return result;
    }

    private boolean isSkip() {
        List<String> list = WHITE_LIST_MAP.get(controllerContext.getTenantId());
        if (!CollectionUtils.isEmpty(list)) {
            if (list.contains("-1") || list.contains(controllerContext.getUser().getUserId())) {
                return true;
            }
        }
        return false;
    }

    @Data
    @ToString
    public static class Arg implements Serializable {

        @JSONField(name = "visit_id")
        @JsonProperty("visit_id")
        @SerializedName("visit_id")
        private String visitId;


        @JSONField(name = "store_id")
        @JsonProperty("store_id")
        @SerializedName("store_id")
        private String storeId;

        @JSONField(name = "activity_type_list")
        @JsonProperty("activity_type_list")
        @SerializedName("activity_type_list")
        private List<String> activityTypes;

    }

    @Data
    @ToString
    public static class Result implements Serializable {

        @JSONField(name = "allowComplete")
        @JsonProperty("allowComplete")
        @SerializedName("allowComplete")
        private Boolean allowComplete;

    }
}
