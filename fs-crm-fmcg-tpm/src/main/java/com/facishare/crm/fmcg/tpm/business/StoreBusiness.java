package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.common.apiname.AccountFields;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityAgreementFields;
import com.facishare.crm.fmcg.tpm.dao.mongo.ConfigDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ConfigEnum;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ConfigPO;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/7/28 10:49
 */
@Component
@SuppressWarnings("Duplicates")
public class StoreBusiness {

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private DescribeCacheService describeCacheService;

    @Resource
    private ConfigDAO configDAO;

    public String findDealerFieldApiName(String tenantId) {
        ConfigPO config = configDAO.queryByKey(tenantId, ConfigEnum.STORE_DEALER.key());
        String apiName = Objects.isNull(config) ? null : (String) config.getValue();
        if (Strings.isNullOrEmpty(apiName)) {
            apiName = "dealer_id";
        }
        return apiName;
    }

    public String findDealerId(String tenantId, IObjectData store) {
        List<String> dealerRecordTypes = findDealerRecordType(tenantId);
        if (dealerRecordTypes.contains(store.getRecordType())) {
            return store.getId();
        }
        ConfigPO config = configDAO.queryByKey(tenantId, ConfigEnum.STORE_DEALER.key());
        String specialField = Objects.isNull(config) ? null : (String) config.getValue();
        String dealerId;
        if (Strings.isNullOrEmpty(specialField)) {
            dealerId = (String) store.get("dealer_id__c");
            if (Strings.isNullOrEmpty(dealerId)) {
                dealerId = (String) store.get("dealer_id");
            }
        } else {
            dealerId = (String) store.get(specialField);
        }
        return dealerId;
    }

    //todo:1.增加缓存 2. condition activity refactor
    public List<String> findDealerRecordType(String tenantId) {
        ConfigPO config = configDAO.queryByKey(tenantId, ConfigEnum.DEALER_RECORD_TYPE.key());

        List<String> dealerRecordType = new ArrayList<>();

        if (config != null) {
            if (config.getValue() instanceof String) {
                dealerRecordType.add(config.getValue().toString());
            } else {
                dealerRecordType = (List<String>) config.getValue();
            }
        }

        if (CollectionUtils.isEmpty(dealerRecordType)) {
            dealerRecordType = (List<String>) ConfigEnum.DEALER_RECORD_TYPE.value();
        }

        return dealerRecordType;
    }

    public boolean isDealer(String tenantId, String accountId) {
        if (accountId == null) {
            return false;
        }
        IObjectData account = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), accountId, ApiNames.ACCOUNT_OBJ);
        if (account == null) {
            return false;
        }
        List<String> dealerRecordType = findDealerRecordType(tenantId);
        return dealerRecordType.contains(account.getRecordType());
    }

    /**
     * @param tenantId   企业id
     * @param storeIds   门店id
     * @param storeLabel 要更新的门店标签
     * @param operation  1.新增 0. 删除
     */
    public void updateStoreLabel(String tenantId, List<String> storeIds, String storeLabel, int operation) {
        storeIds.remove(null);
        if (CollectionUtils.isEmpty(storeIds)) {
            return;
        }
        if (!describeCacheService.isExistField(tenantId, ApiNames.ACCOUNT_OBJ, AccountFields.CUSTOMER_LABEL)) {
            return;
        }
        List<IObjectData> stores = serviceFacade.findObjectDataByIds(tenantId, storeIds, ApiNames.ACCOUNT_OBJ);
        List<IObjectData> updateList = new ArrayList<>();
        if (operation == 0) {
            for (IObjectData store : stores) {
                if (storeContainsAgreement(tenantId, store.getId())) {
                    continue;
                }
                List<String> labels = (List<String>) store.get(AccountFields.CUSTOMER_LABEL, List.class, new ArrayList());
                if (!CollectionUtils.isEmpty(labels) && labels.contains(storeLabel)) {
                    labels.remove(storeLabel);
                    updateList.add(store);
                }
            }
        } else {
            for (IObjectData store : stores) {
                List<String> labels = (List<String>) store.get(AccountFields.CUSTOMER_LABEL, List.class, new ArrayList());
                if (!CollectionUtils.isEmpty(labels) && !labels.contains(storeLabel) || CollectionUtils.isEmpty(labels)) {
                    labels.add(storeLabel);
                    updateList.add(store);
                    store.set(AccountFields.CUSTOMER_LABEL, labels);
                }
            }
        }
        if (!CollectionUtils.isEmpty(updateList)) {
            for (List<IObjectData> parts : Lists.partition(updateList, 50)) {
                serviceFacade.batchUpdateByFields(User.systemUser(tenantId), parts, Lists.newArrayList(AccountFields.CUSTOMER_LABEL));
            }
        }
    }

    private boolean storeContainsAgreement(String tenantId, String storeId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);

        long now = System.currentTimeMillis();

        Filter storeFilter = new Filter();
        storeFilter.setFieldName(TPMActivityAgreementFields.STORE_ID);
        storeFilter.setOperator(Operator.EQ);
        storeFilter.setFieldValues(Lists.newArrayList(storeId));

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityAgreementFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LTE);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityAgreementFields.END_DATE);
        endDateFilter.setOperator(Operator.GTE);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        query.setFilters(Lists.newArrayList(storeFilter, beginDateFilter, endDateFilter));

        return !CollectionUtils.isEmpty(CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query));
    }
}
