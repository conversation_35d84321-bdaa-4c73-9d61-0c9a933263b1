package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofAuditFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofFields;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofAuditSourceConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/8/4 下午3:35
 */
@Slf4j
public class TPMActivityProofAuditObjInvalidAction extends StandardInvalidAction {
    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);
    private ActivityTypeExt activityTypeExt;

    @Override
    protected void before(Arg arg) {

        IObjectData audit = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()),arg.getObjectDataId(), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);
        activityTypeExt = activityTypeManager.findByActivityId(actionContext.getTenantId(),(String) audit.get(TPMActivityProofAuditFields.ACTIVITY_ID));

        String costId = audit.get(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID,String.class);

        if(!Strings.isNullOrEmpty(costId)){
            throw new ValidateException(I18N.text(I18NKeys.SINGLE_AUDIT_INVALID_FAIL_DUE_TO_RELATED_BY_COST));
        }
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result result1 =  super.after(arg, result);

        ActivityProofAuditSourceConfigEntity sourceConfigEntity = activityTypeExt.auditSourceConfig();
        if(ApiNames.TPM_ACTIVITY_PROOF_OBJ.equals(sourceConfigEntity.getMasterApiName())){
            String proofId = (String)result1.getObjectData().get(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID);
            IObjectData proof = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()),proofId,ApiNames.TPM_ACTIVITY_PROOF_OBJ);
            Map<String,Object> updateMap = new HashMap<>();
            updateMap.put(TPMActivityProofFields.AUDIT_STATUS,"schedule");
            updateMap.put(TPMActivityProofFields.RANDOM_AUDIT_STATUS,null);
            serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()),proof,updateMap);
        }else {
            if(!Strings.isNullOrEmpty(sourceConfigEntity.getAuditStatusApiName())){
                String auditedId = (String) result1.getObjectData().get(sourceConfigEntity.getReferenceAuditSourceFieldApiName());
                Map<String,Object> updateMap = new HashMap<>(2);
                IObjectData auditedObject = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()),auditedId,sourceConfigEntity.getMasterApiName());
                updateMap.put(sourceConfigEntity.getAuditStatusApiName(),"schedule");
                serviceFacade.updateWithMap (User.systemUser(actionContext.getTenantId()), auditedObject,updateMap);
            }else {
                log.info("activity type {} is no audit status field",activityTypeExt.get().getId().toString());
            }
        }


        return result1;
    }
}
