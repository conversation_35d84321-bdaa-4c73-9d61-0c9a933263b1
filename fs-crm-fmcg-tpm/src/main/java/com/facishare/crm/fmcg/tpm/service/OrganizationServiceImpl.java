package com.facishare.crm.fmcg.tpm.service;

import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.department.BatchGetLowDepartmentIds;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.department.arg.*;
import com.facishare.organization.api.model.department.result.*;
import com.facishare.organization.api.model.departmentmember.MainDepartment;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.GetAllEmployeesDtoArg;
import com.facishare.organization.api.model.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.arg.GetEmployeesDtoByDepartmentIdArg;
import com.facishare.organization.api.model.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.api.model.employee.result.GetEmployeesDtoByDepartmentIdResult;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/12 7:19 PM
 */
@Component("tpmOrganizationService")
public class OrganizationServiceImpl implements OrganizationService {

    public static final Logger log = LoggerFactory.getLogger(OrganizationServiceImpl.class);

    @Resource
    private EmployeeProviderService employeeProviderService;

    @Resource
    private DepartmentProviderService departmentProviderService;

    public EmployeeDto getEmployee(int tenantId, int employeeId) {
        GetEmployeeDtoArg arg = new GetEmployeeDtoArg();
        arg.setEmployeeId(employeeId);
        arg.setEnterpriseId(tenantId);
        GetEmployeeDtoResult result = employeeProviderService.getEmployeeDto(arg);
        return result.getEmployeeDto();
    }

    @Override
    public DepartmentDto getDepartment(int tenantId, int departmentId) {
        GetDepartmentDtoArg arg = new GetDepartmentDtoArg();
        arg.setEnterpriseId(tenantId);
        arg.setDepartmentId(departmentId);
        GetDepartmentDtoResult result = departmentProviderService.getDepartmentDto(arg);
        return result.getDepartment();
    }

    @Override
    public DepartmentDto getParentDepartment(int tenantId, int subDepartmentId, int parentLevel) {
        GetUpperDepartmentDtoArg arg = new GetUpperDepartmentDtoArg();
        arg.setEnterpriseId(tenantId);
        arg.setDepartmentId(subDepartmentId);
        arg.setSelf(true);
        List<DepartmentDto> upperDepartments = departmentProviderService.getUpperDepartmentDto(arg).getDepartments();

        for (DepartmentDto upperDepartment : upperDepartments) {
            if (parentLevel == (upperDepartment.getAncestors().size())) {
                return upperDepartment;
            }
        }
        throw new ValidateException("get parent department fail");
    }

    @Override
    public List<DepartmentDto> getChildrenDepartment(int tenantId, int parentDepartmentId) {
        GetChildrenDepartmentDtoArg arg = new GetChildrenDepartmentDtoArg();
        arg.setEnterpriseId(tenantId);
        arg.setDepartmentId(parentDepartmentId);
        arg.setSelf(false);
        arg.setRunStatus(RunStatus.ACTIVE);
        GetChildrenDepartmentDtoResult childrenDepartment = departmentProviderService.getChildrenDepartment(arg);
        return childrenDepartment.getDepartmentDtos();
    }

    public List<EmployeeDto> queryAllEmployee(int tenantId) {
        GetAllEmployeesDtoArg arg = new GetAllEmployeesDtoArg();
        arg.setRunStatus(RunStatus.ALL);
        arg.setEnterpriseId(tenantId);
        return employeeProviderService.getAllEmployees(arg).getEmployeeDtoList();
    }

    public List<Integer> getDepartmentIds(int tenantId, int employeeId) {
        GetEmployeeDtoArg empArg = new GetEmployeeDtoArg();
        empArg.setEnterpriseId(tenantId);
        empArg.setEmployeeId(employeeId);

        log.info("find employee arg : {}", empArg);

        GetEmployeeDtoResult empResult = employeeProviderService.getEmployeeDto(empArg);

        log.info("find employee arg : {}", empResult);

        if (empResult.getEmployeeDto().getMainDepartmentId() == null) {
            return Lists.newArrayList();
        }

        List<Integer> departmentIds = Lists.newArrayList(empResult.getEmployeeDto().getMainDepartmentId());

        GetDepartmentDtoArg depArg = new GetDepartmentDtoArg();
        depArg.setDepartmentId(empResult.getEmployeeDto().getMainDepartmentId());
        depArg.setEnterpriseId(tenantId);

        log.info("find department arg : {}", depArg);

        GetDepartmentDtoResult depResult = departmentProviderService.getDepartmentDto(depArg);
        departmentIds.addAll(depResult.getDepartment().getAncestors());

        if (!departmentIds.contains(999999)) {
            departmentIds.add(999999);
        }

        return departmentIds;
    }

    public List<Integer> queryEmployeeIds(int tenantId, int departmentId) {
        GetEmployeesDtoByDepartmentIdArg arg = new GetEmployeesDtoByDepartmentIdArg();

        arg.setEnterpriseId(tenantId);
        arg.setDepartmentId(departmentId);
        arg.setIncludeLowDepartment(true);
        arg.setRunStatus(RunStatus.ALL);
        arg.setMainDepartment(MainDepartment.ALL);

        GetEmployeesDtoByDepartmentIdResult result = employeeProviderService.getEmployeesByDepartmentId(arg);
        return result.getEmployeeDtos().stream().map(EmployeeDto::getEmployeeId).collect(Collectors.toList());
    }

    @Override
    public List<Integer> queryLowerDepartmentIds(int tenantId, int departmentId) {
        BatchGetLowDepartmentIds.Arg arg = new BatchGetLowDepartmentIds.Arg();
        arg.setDepartmentIds(Lists.newArrayList(departmentId));
        arg.setSelf(true);
        arg.setRecursive(true);
        arg.setRunStatus(RunStatus.ACTIVE);
        arg.setEnterpriseId(tenantId);
        return departmentProviderService.batchGetLowDepartmentIds(arg).getDepartmentIds();
    }

    @Override
    public List<Integer> queryUpperDepartmentIds(int tenantId, int departmentId) {
        GetUpperDepartmentDtoArg arg = new GetUpperDepartmentDtoArg();
        arg.setEnterpriseId(tenantId);
        arg.setDepartmentId(departmentId);
        arg.setSelf(true);
        return departmentProviderService.getUpperDepartmentDto(arg).getDepartments().stream().map(DepartmentDto::getDepartmentId).collect(Collectors.toList());
    }

    @Override
    public List<DepartmentDto> queryLowerDepartments(int tenantId, int departmentId) {
        BatchGetLowDepartmentsDtoArg arg = new BatchGetLowDepartmentsDtoArg();
        arg.setDepartmentIds(Lists.newArrayList(departmentId));
        arg.setSelf(true);
        arg.setRunStatus(RunStatus.ACTIVE);
        arg.setEnterpriseId(tenantId);
        return departmentProviderService.batchGetLowDepartment(arg).getDepartmentDtos();
    }

    @Override
    public List<Integer> batchQueryLowerDepartmentIds(int tenantId, List<Integer> departmentIds) {
        BatchGetLowDepartmentIds.Arg arg = new BatchGetLowDepartmentIds.Arg();
        arg.setDepartmentIds(departmentIds);
        arg.setSelf(true);
        arg.setRecursive(true);
        arg.setRunStatus(RunStatus.ACTIVE);
        arg.setEnterpriseId(tenantId);
        return departmentProviderService.batchGetLowDepartmentIds(arg).getDepartmentIds();
    }

    @Override
    public List<Integer> batchQueryLowerDepartmentIdsIncludeAll(int tenantId, List<Integer> departmentIds) {
        BatchGetLowDepartmentIds.Arg arg = new BatchGetLowDepartmentIds.Arg();
        arg.setDepartmentIds(departmentIds);
        arg.setSelf(true);
        arg.setRecursive(true);
        arg.setRunStatus(RunStatus.ALL);
        arg.setEnterpriseId(tenantId);
        return departmentProviderService.batchGetLowDepartmentIds(arg).getDepartmentIds();
    }

    public boolean employeeInRange(int tenantId, int employeeId, List<Integer> departmentIds) {
        if (CollectionUtils.isEmpty(departmentIds)) {
            return false;
        }

        GetEmployeeDtoArg empArg = new GetEmployeeDtoArg();
        empArg.setEnterpriseId(tenantId);
        empArg.setEmployeeId(employeeId);

        log.info("find employee arg : {}", empArg);

        GetEmployeeDtoResult empResult = employeeProviderService.getEmployeeDto(empArg);

        log.info("find employee arg : {}", empResult);

        if (empResult.getEmployeeDto().getMainDepartmentId() == null) {
            return false;
        }

        if (departmentIds.contains(empResult.getEmployeeDto().getMainDepartmentId())) {
            return true;
        }

        GetDepartmentDtoArg depArg = new GetDepartmentDtoArg();
        depArg.setDepartmentId(empResult.getEmployeeDto().getMainDepartmentId());
        depArg.setEnterpriseId(tenantId);

        log.info("find department arg : {}", depArg);

        GetDepartmentDtoResult depResult = departmentProviderService.getDepartmentDto(depArg);

        log.info("find department result : {}", depResult);

        for (Integer departmentId : departmentIds) {
            if (depResult.getDepartment().getAncestors().contains(departmentId)) {
                return true;
            }
        }
        return false;
    }

    public List<Integer> queryAllDepartmentIds(int tenantId) {

        GetAllDepartmentIdsArg arg = new GetAllDepartmentIdsArg();
        arg.setRunStatus(RunStatus.ACTIVE);
        arg.setEnterpriseId(tenantId);

        GetAllDepartmentIdsResult departmentIdsResult = departmentProviderService.getAllDepartmentIds(arg);
        if (departmentIdsResult.getDepartmentIds() == null) {
            return Lists.newArrayList();
        }
        if (!departmentIdsResult.getDepartmentIds().contains(999999)) {
            departmentIdsResult.getDepartmentIds().add(999999);
        }
        return departmentIdsResult.getDepartmentIds();
    }

    @Override
    public List<String> getDepartmentIdsByNames(int tenantId, List<String> names) {
        GetDepartmentByNamesArg arg = new GetDepartmentByNamesArg();
        arg.setEnterpriseId(tenantId);
        arg.setNames(names);
        arg.setRunStatus(RunStatus.ACTIVE);
        GetDepartmentByNamesResult departmentByNames = departmentProviderService.getDepartmentByNames(arg);
        return departmentByNames.getDepartments().stream().map(DepartmentDto::getDepartmentId).map(String::valueOf).collect(Collectors.toList());
    }

    @Override
    public List<DepartmentDto> queryAllDepartment(int tenantId) {
        List<DepartmentDto> departments = Lists.newArrayList();
        GetAllDepartmentDtoArg arg = new GetAllDepartmentDtoArg();
        arg.setEnterpriseId(tenantId);
        arg.setRunStatus(RunStatus.ACTIVE);
        GetAllDepartmentDtoResult res = departmentProviderService.getAllDepartmentDto(arg);
        if (Objects.nonNull(res) && !CollectionUtils.isEmpty(res.getDepartments())) {
            return res.getDepartments();
        }
        return departments;
    }

    @Override
    public List<DepartmentDto> batchGetDepartment(int tenantId, List<Integer> departments) {
        BatchGetDepartmentDtoArg arg = new BatchGetDepartmentDtoArg();
        arg.setDepartmentIds(departments);
        arg.setRunStatus(RunStatus.ACTIVE);
        arg.setEnterpriseId(tenantId);
        return departmentProviderService.batchGetDepartmentDto(arg).getDepartments();
    }

    @Override
    public List<DepartmentDto> batchGetAllDepartment(int tenantId, List<Integer> departments) {
        BatchGetDepartmentDtoArg arg = new BatchGetDepartmentDtoArg();
        arg.setDepartmentIds(departments);
        arg.setRunStatus(RunStatus.ALL);
        arg.setEnterpriseId(tenantId);
        return departmentProviderService.batchGetDepartmentDto(arg).getDepartments();
    }
}
