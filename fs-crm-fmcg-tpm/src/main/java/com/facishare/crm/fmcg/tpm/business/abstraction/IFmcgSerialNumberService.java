package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;

/**
 * Author: linmj
 * Date: 2023/9/27 16:19
 */
public interface IFmcgSerialNumberService {


    List<IObjectData> queryAllSerialNumberStatusBySerialNumberId(String tenantId, String serialNumberId);

    IObjectData querySerialNumberByName(String tenantId, String name);

    /**
     * @param tenantId
     * @param snCodeId
     * @param enableSablesOutLogic 是否使用销售出库替代签收的逻辑逻辑
     * @return 门店签收或则门店盘点状态 取最新的  追加销售出库
     */
    IObjectData getStoreSignSerialNumberStatusObj(String tenantId, String snCodeId, boolean enableSablesOutLogic);

    IObjectData getProductObjFromSerialNumberObj(String tenantId, String productId);

    IObjectData getSerialNumberObjByRealCode(String tenantId, String realCode);

    String getActionIdByActionUniqueId(String tenantId, String actionUniqueId);

    String getActionUniqueIdByActionId(String tenantId, String actionId);

    String getLastStatusExceptionTypes(String tenantId, String snId, String actionId);

    List<String> filterSerialNumberByDealerBlackList(String tenantId, List<IObjectData> serialNumberList, List<IObjectData> dealerBlackList);

    List<String> filterSerialNumberByActivityBlackList(String tenantId, List<IObjectData> serialNumberList, String activityId);

}
