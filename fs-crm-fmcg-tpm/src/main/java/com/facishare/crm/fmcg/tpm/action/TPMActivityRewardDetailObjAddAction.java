package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class TPMActivityRewardDetailObjAddAction extends StandardAddAction {
    @Override
    protected void before(Arg arg) {
        if (!actionContext.isFromOpenAPI()) {
            throw new ValidateException("action not allowed!");
        }
        super.before(arg);
    }
}