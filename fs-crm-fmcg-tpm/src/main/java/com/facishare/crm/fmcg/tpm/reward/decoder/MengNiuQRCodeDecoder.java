package com.facishare.crm.fmcg.tpm.reward.decoder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.FMCGSerialNumberFields;
import com.facishare.crm.fmcg.common.utils.EncryptionService;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.reward.abstraction.QRCodeDecoder;
import com.facishare.crm.fmcg.tpm.reward.annotation.Name;
import com.facishare.crm.fmcg.tpm.reward.dto.SerialNumberData;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fmcg.framework.http.MengNiuProxy;
import com.fmcg.framework.http.contract.mengniu.QueryMarkRelationInfo;
import com.fs.fmcg.sdk.ai.plat.SecretUtil;
import com.github.autoconf.ConfigFactory;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * Author: linmj
 * Date: 2024/4/23 16:34
 */

@Name(name = "mengNiuQRCodeDecoder")
@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class MengNiuQRCodeDecoder implements QRCodeDecoder {

    @Resource
    private MengNiuProxy mengNiuProxy;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private EncryptionService encryptionService;

    @Override
    public SerialNumberData decode(String tenantId, String code) {
        log.info("start decode : {}", code);

        code = fetchRealCodeIfUrl(code);
        SerialNumberData data;

        data = localDecodeCoupon(tenantId, code);
        if (Objects.nonNull(data)) {
            log.info("decode coupon success : {}", JSON.toJSONString(data));
            return data;
        }

        data = localDecodeOuter(tenantId, code);
        if (Objects.nonNull(data)) {
            log.info("decode outer success : {}", JSON.toJSONString(data));
            return data;
        }

        data = localDecodeInner(tenantId, code);
        if (Objects.nonNull(data)) {
            log.info("decode inner success : {}", JSON.toJSONString(data));
            return data;
        }

        return data;
    }

    @Override
    public SerialNumberData decodeCoupon(String tenantId, String code) {
        return localDecodeCoupon(tenantId, fetchRealCodeIfUrl(code));
    }

    @Override
    public SerialNumberData decodeOuter(String tenantId, String code) {
        return localDecodeOuter(tenantId, fetchRealCodeIfUrl(code));
    }

    @Override
    public SerialNumberData decodeInner(String tenantId, String code) {
        return localDecodeInner(tenantId, fetchRealCodeIfUrl(code));
    }

    private SerialNumberData localDecodeOuter(String tenantId, String code) {
        if (Strings.isNullOrEmpty(code)) {
            return null;
        }

        String name = encryptionService.sha256(encryptionService.sha256(code)).toUpperCase();
        IObjectData sn = findObjectData(tenantId, name, false);

        if (Objects.isNull(sn)) {
            return null;
        }

        return convert(SerialNumberData.OUTER_TYPE, code, sn);
    }

    private SerialNumberData localDecodeInner(String tenantId, String code) {
        if (Strings.isNullOrEmpty(code)) {
            return null;
        }

        String outerCode = convertInnerCodeToOuterCode(tenantId, code);
        if (outerCode == null) {
            return null;
        }

        IObjectData sn = findObjectData(tenantId, outerCode, false);

        if (Objects.isNull(sn)) {
            return null;
        }

        return convert(SerialNumberData.INNER_TYPE, code, sn);
    }

    private SerialNumberData localDecodeCoupon(String tenantId, String code) {
        if (Strings.isNullOrEmpty(code)) {
            return null;
        }

        String name = encryptionService.sha256(encryptionService.sha256(code)).toUpperCase();
        IObjectData sn = findObjectData(tenantId, name, true);

        if (Objects.isNull(sn)) {
            return null;
        }

        return convert(SerialNumberData.COUPON_TYPE, code, sn);
    }

    private String convertInnerCodeToOuterCode(String tenantId, String code) {
        QueryMarkRelationInfo.Arg arg = new QueryMarkRelationInfo.Arg();

        JSONObject body = new JSONObject();
        body.put("markCode", code);
        arg.setRequestData(JSON.toJSONString(body));

        long time = System.currentTimeMillis();
        String clientId;
        String sk;

        if ("777421".equals(tenantId) || "82958".equals(tenantId)) {
            clientId = ConfigFactory.getConfig("gray-rel-fmcg").get("sales_mengniu_openapi_client_id");
            sk = ConfigFactory.getConfig("gray-rel-fmcg").get("sales_mengniu_openapi_sk");
        } else {
            clientId = "bf68d8a3-4763-40aa-a8d8-a34763e0aace";
            sk = "D7ABC695-6EB3-4902-ABC6-956EB3790228";
        }

        String sign = SecretUtil.md5(clientId + sk + time).toUpperCase();

        String outerCode = null;
        Integer markType;
        Integer markStatus;
        String contextTenantId = TraceContext.get().getEi();

        try {
            log.info("convert inner code to outer code arg : {}", arg);
            TraceContext.get().setEi(tenantId);

            QueryMarkRelationInfo.Result result = mengNiuProxy.queryMarkRelationInfo(clientId, sign, String.valueOf(time), arg);

            log.info("convert inner code to outer code result : {}", result);

            if (result.getData() == null) {
                throw new ValidateException("内外码转换失败，请扫描正确二维码。");//ignorei18n
            }

            markStatus = result.getData().getInteger("markStatus");
            markType = result.getData().getInteger("markType");

            if (1 == markStatus && 1 == markType) {
                outerCode = result.getData().getString("logisticsEncryptedMarkCode");
            }

        } catch (Exception ex) {
            log.error("convert inner code to outer code error : ", ex);
            return null;
        } finally {
            TraceContext.get().setEi(contextTenantId);
        }

        if (Strings.isNullOrEmpty(outerCode)) {
            return null;
        }

        return outerCode;
    }

    private IObjectData findObjectData(String tenantId, String name, boolean isCoupon) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.NAME);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(name));

        SearchTemplateQuery query;
        if (isCoupon) {
            IFilter couponFilter = new Filter();
            couponFilter.setFieldName("code_type__c");
            couponFilter.setOperator(Operator.EQ);
            couponFilter.setFieldValues(Lists.newArrayList("coupon"));

            query = QueryDataUtil.minimumFindOneQuery(idFilter, couponFilter);
        } else {
            query = QueryDataUtil.minimumFindOneQuery(idFilter);
        }

        List<IObjectData> data = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.FMCG_SERIAL_NUMBER_OBJ,
                query,
                Lists.newArrayList("_id", CommonFields.TENANT_ID, CommonFields.NAME, FMCGSerialNumberFields.PRODUCT_ID, FMCGSerialNumberFields.MANUFACTURE_DATE)
        );

        if (CollectionUtils.isEmpty(data)) {
            return null;
        }

        return data.get(0);
    }

    private SerialNumberData convert(String type, String code, IObjectData snObj) {
        return SerialNumberData.builder()
                .type(type)
                .realCode(code)
                .snId(snObj.getId())
                .name(snObj.getName())
                .skuId(snObj.get(FMCGSerialNumberFields.PRODUCT_ID, String.class))
                .manufactureDate(snObj.get(FMCGSerialNumberFields.MANUFACTURE_DATE, Long.class))
                .data(snObj)
                .build();
    }

    private String fetchRealCodeIfUrl(String code) {
        if (StringUtils.isBlank(code)) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_REWARD_SERVICE_6));
        }

        try {
            if (code.endsWith("/")) {
                code = code.substring(0, code.length() - 1);
            }
            code = code.substring(code.lastIndexOf("/") + 1);
        } catch (Exception ex) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_REWARD_SERVICE_5));
        }
        if (Strings.isNullOrEmpty(code)) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_REWARD_SERVICE_6));
        }
        return code;
    }
}