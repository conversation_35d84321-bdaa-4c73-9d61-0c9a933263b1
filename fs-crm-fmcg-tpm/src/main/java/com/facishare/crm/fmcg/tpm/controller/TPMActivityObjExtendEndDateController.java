package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.tpm.api.activity.ExtendEndDate;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 4:39 PM
 */
//IgnoreI18nFile
@SuppressWarnings("Duplicates")
public class TPMActivityObjExtendEndDateController extends PreDefineController<ExtendEndDate.Arg, ExtendEndDate.Result> {

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected ExtendEndDate.Result doService(ExtendEndDate.Arg arg) {
        IObjectData data = serviceFacade.findObjectData(controllerContext.getUser(), arg.getObjectDataId(), ApiNames.TPM_ACTIVITY_OBJ);

        String activityStatus = data.get(TPMActivityFields.ACTIVITY_STATUS, String.class);
        if (!Objects.equals(activityStatus, TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS) &&
                !Objects.equals(activityStatus, TPMActivityFields.ACTIVITY_STATUS__END) &&
                !Objects.equals(activityStatus, TPMActivityFields.ACTIVITY_STATUS__SCHEDULE)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EXTEND_END_DATE_CONTROLLER_0));
        }

        Long currentEndDate = data.get(TPMActivityFields.END_DATE, Long.class);
        if (Objects.isNull(currentEndDate) || currentEndDate > arg.getEndDate()) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EXTEND_END_DATE_CONTROLLER_1));
        }

        long begin = data.get(TPMActivityFields.BEGIN_DATE, Long.class);
        long now = System.currentTimeMillis();

        String status;
        if (now < begin) {
            status = TPMActivityFields.ACTIVITY_STATUS__SCHEDULE;
        } else if (now < arg.getEndDate()) {
            status = TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS;
        } else {
            status = TPMActivityFields.ACTIVITY_STATUS__END;
        }

        Map<String, Object> updater = Maps.newHashMap();
        updater.put(TPMActivityFields.END_DATE, arg.getEndDate());
        updater.put(TPMActivityFields.ACTIVITY_STATUS, status);

        IObjectData newData = serviceFacade.updateWithMap(controllerContext.getUser(), data, updater);

        IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ);
        serviceFacade.logWithCustomMessage(
                controllerContext.getUser(),
                EventType.MODIFY,
                ActionType.MODIFY,
                describe,
                newData,
                String.format("延长活动周期，新结束日期：%s。", dateFormat.format(new Date(arg.getEndDate())))
        );

        return ExtendEndDate.Result.builder().objectData(ObjectDataDocument.of(newData)).build();
    }
}
