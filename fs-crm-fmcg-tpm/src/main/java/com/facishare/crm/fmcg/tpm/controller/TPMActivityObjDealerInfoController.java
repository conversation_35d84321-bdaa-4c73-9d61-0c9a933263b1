package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityCashingProductFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/16 下午3:58
 */
public class TPMActivityObjDealerInfoController extends PreDefineController<TPMActivityObjDealerInfoController.Arg, TPMActivityObjDealerInfoController.Result> {


    private static final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);
    private static final String AGREEMENT_CASHING_TYPE_KEY = "__agreement_cashing_type";

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return new ArrayList<>();
    }

    @Override
    protected Result doService(Arg arg) {

        IObjectData store = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getStoreId(), ApiNames.ACCOUNT_OBJ);
        List<String> recordType = storeBusiness.findDealerRecordType(controllerContext.getTenantId());
        String dealerCashingType = TPMActivityCashingProductFields.CASH;
        String storeCashingType = TPMActivityCashingProductFields.CASH;
        if (TPMGrayUtils.agreementCashingTypeAllowNull(controllerContext.getTenantId())) {
            dealerCashingType = "";
            storeCashingType = "";
        }
        if (!StringUtils.isEmpty(arg.getActivityId())) {
            IObjectData activity = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getActivityId(), ApiNames.TPM_ACTIVITY_OBJ);
            if (activity != null) {
                if (TPMGrayUtils.agreementCashingTypeAllowNull(controllerContext.getTenantId())) {
                    dealerCashingType = StringUtils.isEmpty(activity.get(TPMActivityFields.DEALER_CASHING_TYPE, String.class)) ? "" : activity.get(TPMActivityFields.DEALER_CASHING_TYPE, String.class);
                    storeCashingType = StringUtils.isEmpty(activity.get(TPMActivityFields.STORE_CASHING_TYPE, String.class)) ? "" : activity.get(TPMActivityFields.STORE_CASHING_TYPE, String.class);
                } else {
                    dealerCashingType = StringUtils.isEmpty(activity.get(TPMActivityFields.DEALER_CASHING_TYPE, String.class)) ? TPMActivityCashingProductFields.CASH : activity.get(TPMActivityFields.DEALER_CASHING_TYPE, String.class);
                    storeCashingType = StringUtils.isEmpty(activity.get(TPMActivityFields.STORE_CASHING_TYPE, String.class)) ? TPMActivityCashingProductFields.CASH : activity.get(TPMActivityFields.STORE_CASHING_TYPE, String.class);
                }
            }
        }
        if (recordType.contains(store.getRecordType())) {
            store.set(AGREEMENT_CASHING_TYPE_KEY, dealerCashingType);
            return Result.builder().storeData(ObjectDataDocument.of(store)).dealerData(ObjectDataDocument.of(store)).build();
        } else {
            String dealerId = storeBusiness.findDealerId(controllerContext.getTenantId(), store);
            IObjectData dealer = null;
            if (!Strings.isNullOrEmpty(dealerId)) {
                dealer = serviceFacade.findObjectDataIncludeDeleted(User.systemUser(controllerContext.getTenantId()), dealerId, ApiNames.ACCOUNT_OBJ);
            }
            store.set(AGREEMENT_CASHING_TYPE_KEY, storeCashingType);
            return Result.builder().storeData(ObjectDataDocument.of(store)).dealerData(ObjectDataDocument.of(dealer)).build();
        }
    }

    @Data
    @ToString
    static class Arg implements Serializable {

        @JSONField(name = "store_id")
        @JsonProperty("store_id")
        @SerializedName("store_id")
        private String storeId;

        @JSONField(name = "activity_id")
        @JsonProperty("activity_id")
        @SerializedName("activity_id")
        private String activityId;
    }


    @Data
    @ToString
    @Builder
    static class Result implements Serializable {

        @JSONField(name = "dealer_data")
        @JsonProperty("dealer_data")
        @SerializedName("dealer_data")
        private ObjectDataDocument dealerData;

        @JSONField(name = "store_data")
        @JsonProperty("store_data")
        @SerializedName("store_data")
        private ObjectDataDocument storeData;
    }
}
