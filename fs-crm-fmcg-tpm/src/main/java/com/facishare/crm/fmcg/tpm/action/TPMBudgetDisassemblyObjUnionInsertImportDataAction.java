package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetDisassemblyFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetDisassemblyService;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@Slf4j
public class TPMBudgetDisassemblyObjUnionInsertImportDataAction extends StandardUnionInsertImportDataAction {

    private final IBudgetDisassemblyService budgetDisassemblyService = SpringUtil.getContext().getBean(IBudgetDisassemblyService.class);
    private final Map<String, BigDecimal> availableAmountGroupByAccountId = Maps.newHashMap();

    @Override
    protected void before(Arg arg) {
        log.info("import budget.arg:{}", arg);
        super.before(arg);
        arg.setIsApprovalFlowEnabled(false);
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return super.getFuncPrivilegeCodes();
    }

    @Override
    protected List<IObjectData> importData(List<IObjectData> validList) {
        log.info("validList:{}", JSON.toJSONString(validList));
        for (IObjectData masterData : validList) {
            masterData.set(TPMBudgetDisassemblyFields.DISASSEMBLY_AMOUNT, 0);
            masterData.set(TPMBudgetAccountFields.AVAILABLE_AMOUNT, 0);
            masterData.set(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS, TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__SCHEDULE);
            masterData.set(TPMBudgetDisassemblyFields.DISASSEMBLY_FAILED_MESSAGE, null);
            masterData.set(TPMBudgetDisassemblyFields.DISASSEMBLY_COMPLETED_TIME, null);

            String sourceAccountId = masterData.get(TPMBudgetDisassemblyFields.SOURCE_BUDGET_ACCOUNT_ID, String.class);
            BigDecimal availableAmount = availableAmountGroupByAccountId.get(sourceAccountId);
            if (availableAmount == null) {
                //todo 优化，只查AVAILABLE_AMOUNT
                IObjectData sourceAccount = budgetDisassemblyService.findData(actionContext.getTenantId(), sourceAccountId, ApiNames.TPM_BUDGET_ACCOUNT);
                if (sourceAccount == null) {
                    continue;
                }
                String availableAmountStr = sourceAccount.get(TPMBudgetAccountFields.AVAILABLE_AMOUNT, String.class);
                if (StringUtils.isEmpty(availableAmountStr)) {
                    continue;
                }
                availableAmount = new BigDecimal(availableAmountStr);
                availableAmountGroupByAccountId.put(sourceAccountId, availableAmount);
            }

            masterData.set(TPMBudgetAccountFields.AVAILABLE_AMOUNT, availableAmount);

        }
        return super.importData(validList);

    }

    @Override
    protected Result after(Arg arg, Result result) {
        return super.after(arg, result);
    }


    @Override
    protected void convertFields(List<ImportData> dataList) {
        super.convertFields(dataList);
        log.info("data:{}", JSON.toJSONString(dataList));
        List<ImportError> errorList = new ArrayList<>();

        for (ImportData data : dataList) {
            try {
                validateMasterData(data.getData());
            } catch (Exception ex) {
                log.error("validateMasterData error:{},errorRowNo:{},errorMsg:{}", ex, data.getRowNo(), ex.getMessage());
                ImportError error = new ImportError();
                error.setRowNo(data.getRowNo());
                error.setErrorMessage(ex.getMessage());
                errorList.add(error);
            }


        }
        mergeErrorList(errorList);
    }

    private void validateMasterData(IObjectData data) {
        log.info("masterData import data:{}", JSON.toJSONString(data));
        budgetDisassemblyService.validateMasterData(actionContext.getTenantId(), data);
    }

}
