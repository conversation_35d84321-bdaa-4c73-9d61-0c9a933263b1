package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.api.activity.EnableAuditList;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPM2Service;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofAuditSourceConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.AuditModeType;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.privilege.DataPrivilegeService;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/2/20 4:12 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityObjEnableAuditListController extends PreDefineController<EnableAuditList.Arg, EnableAuditList.Result> {

    private static final Map<String, String> GROUP_NAME_MAP = new HashMap<>();

    public static final ITPM2Service tpm2Service = SpringUtil.getContext().getBean(ITPM2Service.class);
    public static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);
    private final DataPrivilegeService dataPrivilegeService = SpringUtil.getContext().getBean(DataPrivilegeService.class);

    private ISearchTemplate activitySearchTemplate;

    private IObjectDescribe activityDescribe;

    private User systemUser;

    private boolean isAdminRequest = false;

    void fillGroupNameMap() {
        GROUP_NAME_MAP.put("completed", I18N.text(I18NKeys.COMPLETED_AUDIT));
        GROUP_NAME_MAP.put("schedule", I18N.text(I18NKeys.SCHEDULE_AUDIT));
        GROUP_NAME_MAP.put(AuditModeType.ALL.value(), I18N.text(I18NKeys.ALL_AUDIT));
        GROUP_NAME_MAP.put(AuditModeType.RANDOM.value(), I18N.text(I18NKeys.RANDOM_AUDIT));
    }

    @Override
    protected void before(EnableAuditList.Arg arg) {
        fillGroupNameMap();
        this.systemUser = User.systemUser(controllerContext.getTenantId());
        this.isAdminRequest = serviceFacade.isAdmin(controllerContext.getUser());
        this.activityDescribe = serviceFacade.findDescribeAndLayout(this.systemUser, ApiNames.TPM_ACTIVITY_OBJ, false, null).getObjectDescribe();
        this.activitySearchTemplate = serviceFacade.findSearchTemplateByIdAndType(this.systemUser, "", ApiNames.TPM_ACTIVITY_OBJ, "All");
        super.before(arg);
    }

    @Override
    protected EnableAuditList.Result doService(EnableAuditList.Arg arg) {
        if (!serviceFacade.funPrivilegeCheck(controllerContext.getUser(), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, "Add")) {
            throw new ValidateException(I18N.text(I18NKeys.DO_NOT_HAVE_PROOF_AUDIT_CREATE_RIGHT));
        }

        boolean visitCompleted = "4".equals(arg.getVisitStatus());

        if (Strings.isNullOrEmpty(arg.getVisitId()) || Strings.isNullOrEmpty(arg.getActionId())) {
            throw new ValidateException("visit_id or action_id can not be empty.");
        }

        return visitCompleted ? listWhenCompletedV2(arg) : listWhenScheduleV2(arg);
    }


    private EnableAuditList.Result listWhenCompletedV2(EnableAuditList.Arg arg) {


        List<IObjectData> proofAuditList = queryProofAudit(controllerContext, arg.getStoreId(), arg.getVisitId(), arg.getActionId(), -1);

        Map<String, IObjectData> proofAuditMap = new HashMap<>();
        for (IObjectData proofAudit : proofAuditList) {
            String activityId = (String) proofAudit.get(TPMActivityProofAuditFields.ACTIVITY_ID);
            String randomAuditStatus = (String) proofAudit.get(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS);
            if (Strings.isNullOrEmpty(randomAuditStatus) || TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS__CHECKED.equals(randomAuditStatus)) {
                proofAuditMap.put(activityId, proofAudit);
            }
        }

        List<String> activityIdList = Lists.newArrayList(proofAuditMap.keySet());


        EnableAuditList.Result result = new EnableAuditList.Result();
        result.setNavigateStrategy("activity_list");
        result.setData(Lists.newArrayList());
        if (activityIdList.isEmpty()) {
            return result;
        }

        EnableAuditList.ActivityGroupVO allAuditGroup = new EnableAuditList.ActivityGroupVO(AuditModeType.ALL.value(), GROUP_NAME_MAP.get(AuditModeType.ALL.value()), Lists.newArrayList());
        EnableAuditList.ActivityGroupVO randomAuditGroup = new EnableAuditList.ActivityGroupVO(AuditModeType.RANDOM.value(), GROUP_NAME_MAP.get(AuditModeType.RANDOM.value()), Lists.newArrayList());


        Map<String, IObjectData> activityMap = queryActivity(controllerContext, activityIdList).stream().collect(Collectors.toMap(DBRecord::getId, v -> v));
        Map<String, ActivityTypeExt> activityTypeExtMap = activityTypeManager.findByActivityTypeIds(controllerContext.getTenantId(), activityMap.values().stream().map(v -> v.get(TPMActivityFields.ACTIVITY_TYPE, String.class)).filter(v -> !Strings.isNullOrEmpty(v)).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(v -> v.get().getId().toString(), v -> v, (before, after) -> before));


        for (String id : activityIdList) {
            IObjectData activity = activityMap.get(id);
            if (activity != null) {
                String activityType = activity.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
                ActivityTypeExt activityTypeExt = activityTypeExtMap.get(activityType);
                EnableAuditList.ActivityVO datum = new EnableAuditList.ActivityVO();
                boolean agreementRequired = tpm2Service.isNeedAgreement(Integer.valueOf(controllerContext.getTenantId()), activity);


                if (activityTypeExt.auditNode() != null) {
                    datum.setAuditRecordType(activityTypeExt.auditNode().getObjectRecordType());
                }
                String proofApiName = activityTypeExt.auditSourceConfig().getMasterApiName();
                String referenceProofApiName = activityTypeExt.auditSourceConfig().getReferenceAuditSourceFieldApiName();


                datum.setId(id);
                datum.setName(activity.getName());
                datum.setAgreementRequired(agreementRequired);
                datum.setBeginDate((Long) activity.get(TPMActivityFields.BEGIN_DATE));
                datum.setEndDate((Long) activity.get(TPMActivityFields.END_DATE));
                datum.setIsAudited(true);
                if (proofAuditMap.containsKey(id)) {
                    IObjectData proofAudit = proofAuditMap.get(id);
                    datum.setStatus((String) proofAudit.get(TPMActivityProofAuditFields.AUDIT_STATUS));
                    datum.setDataId(proofAudit.getId());
                    datum.setDataApiName(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);
                    datum.setStatus(proofAudit.get(TPMActivityProofAuditFields.AUDIT_STATUS, String.class));
                    datum.setProofApiName(proofApiName);
                    datum.setProofId(proofAudit.get(referenceProofApiName, String.class));
                }

                if (AuditModeType.ALL.value().equals(activityTypeExt.auditModeConfig().getAuditMode())) {
                    allAuditGroup.getActivityList().add(datum);
                } else {
                    randomAuditGroup.getActivityList().add(datum);
                }
            }
        }

        if (!CollectionUtils.isEmpty(allAuditGroup.getActivityList())) {
            result.getData().add(allAuditGroup);
        }
        if (!CollectionUtils.isEmpty(randomAuditGroup.getActivityList())) {
            result.getData().add(randomAuditGroup);
        }
        return result;
    }


    private EnableAuditList.Result listWhenScheduleV2(EnableAuditList.Arg arg) {
        List<ActivityTypeExt> activityTypeExts = activityTypeManager.queryActivityTypeContainsAudit(controllerContext.getTenantId());
        Map<String, ActivityTypeExt> activityType2ExtMap = activityTypeExts.stream().collect(Collectors.toMap(v -> v.get().getId().toString(), v -> v, (before, after) -> before));
        //需要每个活动单独查询没有过滤门店 没办法减少耗时 可以通过计算举证次数来过滤
        List<IObjectData> activities = CollectionUtils.isEmpty(activityTypeExts) ? new ArrayList<>() : queryActivity(new ArrayList<>(activityType2ExtMap.keySet()), Strings.isNullOrEmpty(arg.getStoreId()));
        log.info("activities size:{}", activities.size());
        Map<String, List<IObjectData>> activityType2ActivityMap = new HashMap<>();
        Map<String, IObjectData> randomAuditActivityMap = new HashMap<>();
        Map<String, IObjectData> allAuditActivityMap = new HashMap<>();
        activities.forEach(activity -> {
            String activityType = activity.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
            List<IObjectData> all = activityType2ActivityMap.getOrDefault(activityType, Lists.newArrayList());
            all.add(activity);
            activityType2ActivityMap.putIfAbsent(activityType, all);
        });
        Map<String, Integer> activityProofCountMap = new HashMap<>();
        Map<String, Integer> activityProofAuditCountMap = new HashMap<>();

        activityTypeExts.forEach(activityTypeExt -> {
            String activityType = activityTypeExt.get().getId().toString();
            if (!activityType2ActivityMap.containsKey(activityType)) {
                return;
            }
            List<String> activityIds = activityType2ActivityMap.get(activityType).stream().map(DBRecord::getId).collect(Collectors.toList());
            ActivityProofAuditSourceConfigEntity auditSourceEntity = activityTypeExt.auditSourceConfig();
            activityProofCountMap.putAll(count(auditSourceEntity.getMasterApiName(), auditSourceEntity.getAccountFieldApiName(), auditSourceEntity.getReferenceActivityFieldApiName(), arg.getStoreId(), activityIds));
            activityProofAuditCountMap.putAll(count(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, TPMActivityProofAuditFields.STORE_ID, TPMActivityProofAuditFields.ACTIVITY_ID, arg.getStoreId(), activityIds));
            if (activityTypeExt.auditModeConfig() != null && AuditModeType.RANDOM.value().equals(activityTypeExt.auditModeConfig().getAuditMode())) {
                activityType2ActivityMap.get(activityTypeExt.get().getId().toString()).forEach(v -> randomAuditActivityMap.put(v.getId(), v));
            } else {
                activityType2ActivityMap.get(activityTypeExt.get().getId().toString()).forEach(v -> allAuditActivityMap.put(v.getId(), v));
            }
        });
        log.info("activityProofCountMap:{}", activityProofCountMap);
        log.info("activityProofAuditCountMap:{}", activityProofAuditCountMap);
        log.info("activity types:{}", activityType2ActivityMap.keySet());
        EnableAuditList.Result result = new EnableAuditList.Result();
        result.setData(Lists.newArrayList());
        EnableAuditList.ActivityGroupVO allAuditGroup = new EnableAuditList.ActivityGroupVO(AuditModeType.ALL.value(), GROUP_NAME_MAP.get(AuditModeType.ALL.value()), Lists.newArrayList());
        EnableAuditList.ActivityGroupVO randomAuditGroup = new EnableAuditList.ActivityGroupVO(AuditModeType.RANDOM.value(), GROUP_NAME_MAP.get(AuditModeType.RANDOM.value()), Lists.newArrayList());

        Map<String, IObjectData> activity2AuditMap = queryProofAudit(controllerContext, arg.getStoreId(), arg.getVisitId(), arg.getActionId(), -1).stream().collect(Collectors.toMap(v -> v.get(TPMActivityProofAuditFields.ACTIVITY_ID, String.class), v -> v, (before, old) -> before));


        activityProofCountMap.forEach((activityId, proofCount) -> {
            if (proofCount == 0)
                return;
            EnableAuditList.ActivityVO activityVO = new EnableAuditList.ActivityVO();
            activityVO.setAgreementRequired(false);

            if (randomAuditActivityMap.containsKey(activityId)) {
                IObjectData activity = randomAuditActivityMap.get(activityId);
                String activityType = activity.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
                if (!Strings.isNullOrEmpty(activityType)) {
                    activityVO.setAuditRecordType(activityType2ExtMap.get(activityType).auditNode().getObjectRecordType());
                }
                activityVO.setBeginDate(activity.get(TPMActivityFields.BEGIN_DATE, Long.class));
                activityVO.setEndDate(activity.get(TPMActivityFields.END_DATE, Long.class));
                activityVO.setId(activityId);
                activityVO.setName(activity.getName());

                IObjectData audit = activity2AuditMap.get(activityId);
                //抽检是预制对象
                if (audit == null) {
                    audit = queryRandomAudit(activityId, arg.getStoreId(), TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS__UNCHECKED);
                    if (audit == null) {
                        log.info("activity:{} has no unchecked audit .", activityId);
                        return;
                    }
                    activityVO.setDataApiName(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);
                    activityVO.setDataId(audit.getId());
                    activityVO.setProofId(audit.get(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID, String.class));
                    activityVO.setProofApiName(ApiNames.TPM_ACTIVITY_PROOF_OBJ);
                    activityVO.setStatus(audit.get(TPMActivityProofAuditFields.AUDIT_STATUS, String.class));
                    activityVO.setIsAudited(false);

                } else {
                    activityVO.setDataApiName(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);
                    activityVO.setDataId(audit.getId());
                    activityVO.setProofId(audit.get(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID, String.class));
                    activityVO.setProofApiName(ApiNames.TPM_ACTIVITY_PROOF_OBJ);
                    activityVO.setStatus(audit.get(TPMActivityProofAuditFields.AUDIT_STATUS, String.class));
                    String randomAuditStatus = audit.get(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS, String.class);
                    activityVO.setIsAudited(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS__CHECKED.equals(randomAuditStatus));
                }
                randomAuditGroup.getActivityList().add(activityVO);

            } else if (allAuditActivityMap.containsKey(activityId)) {
                IObjectData activity = allAuditActivityMap.get(activityId);
                String activityType = activity.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
                if (!Strings.isNullOrEmpty(activityType)) {
                    activityVO.setAuditRecordType(activityType2ExtMap.get(activityType).auditNode().getObjectRecordType());
                }
                ActivityProofAuditSourceConfigEntity sourceConfigEntity = activityType2ExtMap.get(activityType).auditSourceConfig();
                activityVO.setBeginDate(activity.get(TPMActivityFields.BEGIN_DATE, Long.class));
                activityVO.setEndDate(activity.get(TPMActivityFields.END_DATE, Long.class));
                activityVO.setId(activityId);
                activityVO.setName(activity.getName());
                Integer auditCount = activityProofAuditCountMap.getOrDefault(activityId, 0);
                IObjectData audit = activity2AuditMap.get(activityId);
                if (audit != null) {
                    activityVO.setDataApiName(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);
                    activityVO.setDataId(audit.getId());
                    activityVO.setProofId(audit.get(sourceConfigEntity.getReferenceAuditSourceFieldApiName(), String.class));
                    activityVO.setProofApiName(sourceConfigEntity.getMasterApiName());
                    activityVO.setStatus(audit.get(TPMActivityProofAuditFields.AUDIT_STATUS, String.class));
                    activityVO.setIsAudited(true);
                } else {
                    if (proofCount.compareTo(auditCount) == 0) {
                        log.info("activity:{} has no unAudit proof.", activityId);
                        return;
                    } else {
                        /*List<String> auditedIds = queryAudit(activityId, arg.getStoreId()).stream().map(v -> v.get(sourceConfigEntity.getReferenceAuditSourceFieldApiName(), String.class)).collect(Collectors.toList());
                        IObjectData unAuditedObj = queryUnauditedProof(sourceConfigEntity.getMasterApiName(), sourceConfigEntity.getAccountFieldApiName(), sourceConfigEntity.getReferenceActivityFieldApiName(), activityId, arg.getStoreId(), auditedIds);*/
                        IObjectData unAuditedObj = queryUnauditedProof(sourceConfigEntity.getMasterApiName(), sourceConfigEntity.getAccountFieldApiName(), sourceConfigEntity.getReferenceActivityFieldApiName(), activityId, arg.getStoreId(), sourceConfigEntity.getAuditStatusApiName());
                        if (unAuditedObj == null) {
                            log.info("activity:{} has no unAudit proof.", activityId);
                            return;
                        }
                        activityVO.setProofId(unAuditedObj.getId());
                        activityVO.setProofApiName(sourceConfigEntity.getMasterApiName());
                        activityVO.setStatus("schedule");
                        activityVO.setIsAudited(false);
                    }
                }
                allAuditGroup.getActivityList().add(activityVO);
            }
        });

        if (!CollectionUtils.isEmpty(allAuditGroup.getActivityList())) {
            result.getData().add(allAuditGroup);
        }
        if (!CollectionUtils.isEmpty(randomAuditGroup.getActivityList())) {
            result.getData().add(randomAuditGroup);
        }

        int totalCount = allAuditGroup.getActivityList().size() + randomAuditGroup.getActivityList().size();
        if (totalCount == 1) {
            EnableAuditList.ActivityVO activityVO;
            boolean flag = false;
            if (allAuditGroup.getActivityList().isEmpty()) {
                activityVO = randomAuditGroup.getActivityList().get(0);
                flag = true;
            } else {
                activityVO = allAuditGroup.getActivityList().get(0);
            }
            if (activityVO.getIsAudited() || flag) {
                result.setNavigateStrategy("activity_list");
            } else {
                result.setNavigateStrategy("proof_audit");
            }
        } else if (totalCount == 0) {
            result.setNavigateStrategy("no_activity");
        } else {
            result.setNavigateStrategy("activity_list");
        }

        makeUpGroup(result);
        return result;
    }

    private IObjectData queryUnauditedProof(String proofApiName, String storeApiName, String activityApiName, String activityId, String storeId, List<String> proofIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter storeFilter = new Filter();
        storeFilter.setFieldName(storeApiName);
        storeFilter.setOperator(Operator.EQ);
        storeFilter.setFieldValues(Lists.newArrayList(storeId));

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(activityApiName);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));

        query.setFilters(Lists.newArrayList(storeFilter, activityFilter));

        if (!CollectionUtils.isEmpty(proofIds)) {
            Filter proofFilter = new Filter();
            proofFilter.setFieldName("_id");
            proofFilter.setOperator(Operator.NIN);
            proofFilter.setFieldValues(proofIds);
            query.getFilters().add(proofFilter);
        }

        OrderBy orderBy = new OrderBy();
        orderBy.setFieldName(CommonFields.CREATE_TIME);
        orderBy.setIsAsc(false);
        query.setOrders(Lists.newArrayList(orderBy));

        List<IObjectData> result = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), proofApiName, query).getData();


        return CollectionUtils.isEmpty(result) ? null : result.get(0);
    }

    private IObjectData queryUnauditedProof(String proofApiName, String storeApiName, String activityApiName, String activityId, String storeId, String auditStatusApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");


        Filter activityFilter = new Filter();
        activityFilter.setFieldName(activityApiName);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter unAuditStatusFilter = new Filter();
        unAuditStatusFilter.setFieldName(auditStatusApiName);
        unAuditStatusFilter.setOperator(Operator.EQ);
        unAuditStatusFilter.setFieldValues(Lists.newArrayList(SelfDefineProofFields.AUDIT_STATUS_SCHEDULE));

        query.setFilters(Lists.newArrayList(activityFilter, unAuditStatusFilter));

        if (!Strings.isNullOrEmpty(storeId)) {
            Filter storeFilter = new Filter();
            storeFilter.setFieldName(storeApiName);
            storeFilter.setOperator(Operator.EQ);
            storeFilter.setFieldValues(Lists.newArrayList(storeId));
            query.getFilters().add(storeFilter);
        }

        OrderBy orderBy = new OrderBy();
        orderBy.setFieldName(CommonFields.CREATE_TIME);
        orderBy.setIsAsc(false);
        query.setOrders(Lists.newArrayList(orderBy));

        List<IObjectData> result = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), proofApiName, query).getData();


        return CollectionUtils.isEmpty(result) ? null : result.get(0);
    }

    private IObjectData queryRandomAudit(String activityId, String storeId, String auditStatus) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter randomAuditStatusFilter = new Filter();
        randomAuditStatusFilter.setFieldName(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS);
        randomAuditStatusFilter.setOperator(Operator.EQ);
        randomAuditStatusFilter.setFieldValues(Lists.newArrayList(auditStatus));

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityProofAuditFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter costFilter = new Filter();
        costFilter.setFieldName(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID);
        costFilter.setOperator(Operator.IS);
        costFilter.setFieldValues(Lists.newArrayList());

        query.setFilters(Lists.newArrayList(activityFilter, randomAuditStatusFilter, costFilter));

        if (!Strings.isNullOrEmpty(storeId)) {
            Filter storeFilter = new Filter();
            storeFilter.setFieldName(TPMActivityProofAuditFields.STORE_ID);
            storeFilter.setOperator(Operator.EQ);
            storeFilter.setFieldValues(Lists.newArrayList(storeId));
            query.getFilters().add(storeFilter);
        }

        OrderBy orderBy = new OrderBy();
        orderBy.setFieldName(CommonFields.CREATE_TIME);
        orderBy.setIsAsc(false);
        query.setOrders(Lists.newArrayList(orderBy));

        List<IObjectData> results = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, query).getData();

        return CollectionUtils.isEmpty(results) ? null : results.get(0);
    }

    private Map<String, Integer> count(String objApiName, String storeApiName, String activityApiName, String storeId, List<String> activityIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        if (!Strings.isNullOrEmpty(storeId)) {
            Filter storeFilter = new Filter();
            storeFilter.setFieldName(storeApiName);
            storeFilter.setOperator(Operator.EQ);
            storeFilter.setFieldValues(Lists.newArrayList(storeId));
            query.getFilters().add(storeFilter);
        }

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(activityApiName);
        activityFilter.setOperator(Operator.IN);
        activityFilter.setFieldValues(activityIds);
        query.getFilters().add(activityFilter);

        List<IObjectData> results = serviceFacade.aggregateFindBySearchQuery(controllerContext.getTenantId(), query, objApiName, activityApiName, "count", "");

        return results.stream().collect(Collectors.toMap(v -> v.get(activityApiName, String.class), v -> v.get("groupbycount", Integer.class), (before, after) -> before));
    }

    private List<IObjectData> queryActivity(List<String> activityTypeIds, boolean isBrand) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        String pattern = "1 and 2 and 3";

        Filter activityTypeFilter = new Filter();
        activityTypeFilter.setFieldValues(activityTypeIds);
        activityTypeFilter.setOperator(Operator.IN);
        activityTypeFilter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);

        Filter closeStatus = new Filter();
        closeStatus.setFieldName(TPMActivityFields.CLOSE_STATUS);
        closeStatus.setOperator(Operator.NEQ);
        closeStatus.setFieldValues(Lists.newArrayList(TPMActivityFields.CLOSE_STATUS__CLOSED));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(TPMActivityFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        query.setFilters(Lists.newArrayList(activityTypeFilter, closeStatus, lifeStatusFilter));

        if (isBrand) {
            Filter customerTypeFilter = new Filter();
            customerTypeFilter.setOperator(Operator.EQ);
            customerTypeFilter.setFieldValues(Lists.newArrayList(ActivityCustomerTypeEnum.BRAND.value()));
            customerTypeFilter.setFieldName(TPMActivityFields.CUSTOMER_TYPE);
            query.getFilters().add(customerTypeFilter);
            pattern += " and 4 ";
        } else {
            Filter customerTypeFilter = new Filter();
            customerTypeFilter.setOperator(Operator.NEQ);
            customerTypeFilter.setFieldValues(Lists.newArrayList(ActivityCustomerTypeEnum.BRAND.value()));
            customerTypeFilter.setFieldName(TPMActivityFields.CUSTOMER_TYPE);
            query.getFilters().add(customerTypeFilter);

            Filter customerTypeNullFilter = new Filter();
            customerTypeNullFilter.setOperator(Operator.IS);
            customerTypeNullFilter.setFieldValues(Lists.newArrayList());
            customerTypeNullFilter.setFieldName(TPMActivityFields.CUSTOMER_TYPE);
            query.getFilters().add(customerTypeNullFilter);
            pattern += " and (4 or 5) ";
        }
        query.setPattern(pattern);
        SearchQueryUtil.handleDataPrivilege(dataPrivilegeService, controllerContext.getUser(), query, this.activityDescribe, this.activitySearchTemplate, this.isAdminRequest);
        return CommonUtils.queryData(serviceFacade, controllerContext.getUser(), controllerContext.getRequestContext(), ApiNames.TPM_ACTIVITY_OBJ, query, Lists.newArrayList(CommonFields.ID, TPMActivityFields.ACTIVITY_TYPE, TPMActivityFields.BEGIN_DATE, TPMActivityFields.END_DATE, CommonFields.NAME));
    }

    private List<IObjectData> queryProofAudit(ControllerContext context, String storeId, String visitId, String actionId, int limit) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(limit);
        query.setOffset(0);
        query.setSearchSource("db");

        if (!Strings.isNullOrEmpty(storeId)) {
            Filter storeIdFilter = new Filter();
            storeIdFilter.setFieldName(TPMActivityProofAuditFields.STORE_ID);
            storeIdFilter.setOperator(Operator.EQ);
            storeIdFilter.setFieldValues(Lists.newArrayList(storeId));
            query.getFilters().add(storeIdFilter);
        }

        Filter visitIdFilter = new Filter();
        visitIdFilter.setFieldName(TPMActivityProofAuditFields.VISIT_ID);
        visitIdFilter.setOperator(Operator.EQ);
        visitIdFilter.setFieldValues(Lists.newArrayList(visitId));
        query.getFilters().add(visitIdFilter);

        Filter actionIdFilter = new Filter();
        actionIdFilter.setFieldName(TPMActivityProofAuditFields.ACTION_ID);
        actionIdFilter.setOperator(Operator.EQ);
        actionIdFilter.setFieldValues(Lists.newArrayList(actionId));
        query.getFilters().add(actionIdFilter);

        OrderBy orderBy = new OrderBy();
        orderBy.setFieldName(CommonFields.CREATE_TIME);
        orderBy.setIsAsc(false);
        query.setOrders(Lists.newArrayList(orderBy));

        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, query);
    }

    private List<IObjectData> queryActivity(ControllerContext context, List<String> ids) {
        if (ids.isEmpty()) {
            return new ArrayList<>();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(ids);

        query.setFilters(Lists.newArrayList(idFilter));
        SearchQueryUtil.handleDataPrivilege(dataPrivilegeService, controllerContext.getUser(), query, this.activityDescribe, this.activitySearchTemplate, this.isAdminRequest);
        return CommonUtils.queryData(serviceFacade, context.getUser(), ApiNames.TPM_ACTIVITY_OBJ, query);
    }

    private List<IObjectData> queryAgreement(ControllerContext context, List<String> ids) {
        if (ids.isEmpty()) {
            return new ArrayList<>();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(ids);

        query.setFilters(Lists.newArrayList(idFilter));
        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query);
    }

    private List<IObjectData> queryProof(ControllerContext context, String storeId) {

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(TPMActivityProofFields.STORE_ID);
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(storeId));

        query.setFilters(Lists.newArrayList(storeIdFilter));

        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query);
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }


    private void makeUpGroup(EnableAuditList.Result result) {
        if (result == null) {
            return;
        }
        Set<String> all = Sets.newHashSet(AuditModeType.ALL.value(), AuditModeType.RANDOM.value());
        result.getData().forEach(v -> all.remove(v.getGroupKey()));

        all.forEach(key -> {
            EnableAuditList.ActivityGroupVO groupVO = new EnableAuditList.ActivityGroupVO();
            groupVO.setGroupKey(key);
            groupVO.setGroupName(GROUP_NAME_MAP.get(key));
            groupVO.setActivityList(new ArrayList<>());
            result.getData().add(groupVO);
        });
    }
}
