package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;

/**
 * Author: linmj
 * Date: 2023/4/26 14:52
 */
public interface IEnableCacheService {

    /**
     * 处理由于客户编辑或则range对象新建导致的缓存不一致，删除缓存
     * range对象例如参与门店、参与经销商的 新建和作废
     *
     * @param tenantId
     * @param accountId
     * @param changeReason
     * @param changeFields
     */
    void resetCacheByAccount(String tenantId, String accountId, String changeReason, List<String> changeFields);

    /**
     * 缓存ALL场景下的用户
     * @param tenantId
     * @param userId
     */
    void setALLCache(String tenantId, String userId);

    /**
     * 单独缓存门店和用户
     * @param tenantId
     * @param userId
     * @param storeId
     * @param isEnable
     */
    void setStoreCache(String tenantId, String userId, String storeId, Boolean isEnable);

    /**
     * 删除老数据的 或则 新数据 对应的缓存数据。至少删除 cache = false的数据
     *
     * @param tenantId
     * @param activity
     */
    void resetCacheByActivity(String tenantId, IObjectData activity);

    /**
     *
     * @param tenantId
     * @param userId
     * @param storeId
     * @return 0：false 1：true -1：无缓存、过期
     */
    int getCache(String tenantId, String userId, String storeId);
}
