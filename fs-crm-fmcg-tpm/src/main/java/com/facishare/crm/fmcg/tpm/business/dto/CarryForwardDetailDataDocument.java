package com.facishare.crm.fmcg.tpm.business.dto;

import com.facishare.crm.fmcg.common.apiname.TPMBudgetCarryForwardDetailFields;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/8/1 17:34
 */
public class CarryForwardDetailDataDocument extends ObjectDataDocument implements Serializable {

    public String getSourceBudgetAccountId() {
        return (String) get(TPMBudgetCarryForwardDetailFields.SOURCE_BUDGET_ACCOUNT_ID);
    }

    public void setCarryForwardStatus(String value) {
        put(TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS, value);
    }

    public void setSourceBudgetAccountId(String value) {
        put(TPMBudgetCarryForwardDetailFields.SOURCE_BUDGET_ACCOUNT_ID, value);
    }

    public void setSourceBudgetAccountName(String value) {
        put(TPMBudgetCarryForwardDetailFields.SOURCE_BUDGET_ACCOUNT_ID__R, value);
    }

    public void setTargetBudgetAccountId(String value) {
        put(TPMBudgetCarryForwardDetailFields.TARGET_BUDGET_ACCOUNT_ID, value);
    }

    public void setTargetBudgetAccountName(String value) {
        put(TPMBudgetCarryForwardDetailFields.TARGET_BUDGET_ACCOUNT_ID__R, value);
    }

    public void setAmount(BigDecimal value) {
        put(TPMBudgetCarryForwardDetailFields.AMOUNT, value);
    }

    public void setAfterCarryForwardAmount(BigDecimal value) {
        put(TPMBudgetCarryForwardDetailFields.AFTER_CARRY_FORWARD_AMOUNT, value);
    }
}
