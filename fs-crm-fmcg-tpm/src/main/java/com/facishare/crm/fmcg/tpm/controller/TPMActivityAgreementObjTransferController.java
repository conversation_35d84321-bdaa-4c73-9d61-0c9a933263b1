package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.tpm.api.agreement.Transfer;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofImageDTO;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileDownloadRequest;
import com.facishare.stone.sdk.request.StoneFileImageProcessRequest;
import com.facishare.stone.sdk.request.StoneFileUploadRequest;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import groovy.util.logging.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.FileCopyUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * <AUTHOR>
 * create time 2021/9/23 15:33
 */
//IgnoreI18nFile
@Slf4j
public class TPMActivityAgreementObjTransferController extends PreDefineController<Transfer.Arg, Transfer.Result> {

    private static final StoneProxyApi stoneProxyApi = SpringUtil.getContext().getBean(StoneProxyApi.class);
    private static final EIEAConverter eieaConverter = SpringUtil.getContext().getBean(EIEAConverter.class);
    private static final OrganizationService organizationService = SpringUtil.getContext().getBean(OrganizationService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    public static final int DEFAULT_LIMIT = 100;

    /**
     * transfer agreement data for ZHG
     * 1. load all data from tenant A
     * TPMActivityEnableListController#queryProof
     * 2. convert data to tenant B object
     * 3. save tenant B object data
     * serviceFacade.saveObjectData(User.systemUser(actionContext.getTenantId()), targetObjects);
     */
    @Override
    protected Transfer.Result doService(Transfer.Arg arg) {

        String sourceTenantId = "684567";
        String targetTenantId = "722872";
        String sourceTenantAccount = eieaConverter.enterpriseIdToAccount(Integer.parseInt(sourceTenantId));
        String targetTenantAccount = eieaConverter.enterpriseIdToAccount(Integer.parseInt(targetTenantId));

        Map<String, IObjectDescribe> describeMap = new HashMap<>();
        describeMap.put(ApiNames.TPM_ACTIVITY_AGREEMENT_DETAIL_OBJ, serviceFacade.findObject(targetTenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_DETAIL_OBJ));


        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setOffset(0);
        query.setLimit(DEFAULT_LIMIT);
        query.setSearchSource("db");

        if (Strings.isNullOrEmpty(arg.getId())) {
            return new Transfer.Result();
        } else if ("all".equals(arg.getId())) {

            Filter transferFilter = new Filter();
            transferFilter.setFieldName("transfered__c");
            transferFilter.setOperator(Operator.N);
            transferFilter.setFieldValues(Lists.newArrayList(Boolean.TRUE.toString()));


            query.setFilters(Lists.newArrayList(transferFilter));
        } else {

            Filter idFilter = new Filter();
            idFilter.setFieldName("_id");
            idFilter.setOperator(Operator.EQ);
            idFilter.setFieldValues(Lists.newArrayList(arg.getId()));

            Filter transferFilter = new Filter();
            transferFilter.setFieldName("transfered__c");
            transferFilter.setOperator(Operator.N);
            transferFilter.setFieldValues(Lists.newArrayList(Boolean.TRUE.toString()));

            query.setFilters(Lists.newArrayList(idFilter, transferFilter));
        }

        OrderBy order = new OrderBy();
        order.setFieldName("create_time");
        order.setIsAsc(false);
        order.setIsNullLast(false);

        query.setOrders(Lists.newArrayList(order));

        Map<String, String> sourceTenantEmployeeIdToNameMap = organizationService.queryAllEmployee(Integer.parseInt(sourceTenantId)).stream()
                .collect(Collectors.toMap(k -> String.valueOf(k.getEmployeeId()), EmployeeDto::getName));

        Map<String, String> targetTenantNameToEmployeeIdMap = organizationService.queryAllEmployee(Integer.parseInt(targetTenantId)).stream()
                .collect(Collectors.toMap(EmployeeDto::getName, k -> String.valueOf(k.getEmployeeId())));


        while (true) {
            log.info("[AT] query arg : {}", JSON.toJSONString(query));

            List<IObjectData> sourceData = serviceFacade.findBySearchQuery(User.systemUser(sourceTenantId), "object_ahPY8__c", query).getData();

            log.info("[AT] query result : {}", JSON.toJSONString(sourceData));

            if (sourceData.isEmpty()) {
                break;
            }

            for (IObjectData sourceDatum : sourceData) {
                try {

                    String sourceStoreId = (String) sourceDatum.get("field_v0L2t__c");
                    if (Strings.isNullOrEmpty(sourceStoreId)) {
                        setTransferError(sourceTenantId, sourceDatum, "终端信息为空");
                        continue;
                    }
                    IObjectData sourceStoreData = serviceFacade.findObjectData(User.systemUser(sourceTenantId), sourceStoreId, ApiNames.ACCOUNT_OBJ);
                    String sourceStoreName = sourceStoreData.getName();

                    String sourceStoreAccountNo = (String) sourceStoreData.get("account_no");

                    SearchTemplateQuery queryTargetStore = new SearchTemplateQuery();
                    queryTargetStore.setOffset(0);
                    queryTargetStore.setLimit(1);

                    IFilter targetStoreFilter = new Filter();
                    targetStoreFilter.setFieldName("field_fk6u7__c");
                    targetStoreFilter.setOperator(Operator.EQ);
                    targetStoreFilter.setFieldValues(Lists.newArrayList(sourceStoreAccountNo));

                    queryTargetStore.setFilters(Lists.newArrayList(targetStoreFilter));

                    QueryResult<IObjectData> bySearchQuery = serviceFacade.findBySearchQuery(User.systemUser(targetTenantId), ApiNames.ACCOUNT_OBJ, queryTargetStore);

                    if (CollectionUtils.isEmpty(bySearchQuery.getData())) {
                        setTransferError(sourceTenantId, sourceDatum, String.format("门店【%s】在二期企业中不存在", sourceStoreName));
                        continue;
                    }

                    String targetStoreId = "";
                    IObjectData targetStoreData = null;
                    for (IObjectData datum : bySearchQuery.getData()) {
                        targetStoreId = datum.getId();
                        targetStoreData = datum;
                        if (!Strings.isNullOrEmpty(targetStoreId)) {
                            break;
                        }
                    }


                    if (Strings.isNullOrEmpty(targetStoreId)) {
                        setTransferError(sourceTenantId, sourceDatum, String.format("门店【%s】在二期企业中不存在", sourceStoreName));
                        continue;
                    }

                    if (Objects.isNull(targetStoreData)) {
                        setTransferError(sourceTenantId, sourceDatum, String.format("门店【%s】在二期企业中不存在", sourceStoreName));
                        continue;
                    }
                    String sourceOwner = sourceDatum.getOwner().get(0);
                    String sourceOwnerName = sourceTenantEmployeeIdToNameMap.get(sourceOwner);
                    String targetOwner;
                    boolean needSetSuccessMsg = false;
                    if (targetTenantNameToEmployeeIdMap.containsKey(sourceOwnerName)) {
                        targetOwner = targetTenantNameToEmployeeIdMap.get(sourceOwnerName);
                    } else {
                        //门店负责人为必填，不可能为空
                        targetOwner = targetStoreData.getOwner().get(0);
                        needSetSuccessMsg = true;
                    }
                    IObjectData targetMasterData;

                    try {
                        targetMasterData = convertToTargetMasterData(
                                sourceTenantAccount,
                                targetTenantId,
                                targetTenantAccount,
                                targetOwner,
                                targetStoreData,
                                sourceDatum);
                    } catch (IOException ex) {
                        setTransferError(sourceTenantId, sourceDatum, String.format("迁移图片时发生IO异常：%s", ex.getMessage()));
                        continue;
                    } catch (FRestClientException ex) {
                        setTransferError(sourceTenantId, sourceDatum, String.format("访问文件服务发生业务异常：%s", ex.getMessage()));
                        continue;
                    }

                    String price = (String) sourceDatum.get("field_6f5Ig__c");
                    if (StringUtils.isBlank(price)) {
                        serviceFacade.saveObjectData(User.systemUser(targetTenantId), targetMasterData);
                    } else {
                        Map<String, List<IObjectData>> detailsData = convertToTargetObjData(targetTenantId, price);
                        SaveMasterAndDetailData.Arg saveArg = SaveMasterAndDetailData.Arg.builder()
                                .masterObjectData(targetMasterData)
                                .detailObjectData(detailsData)
                                .objectDescribes(describeMap)
                                .build();
                        serviceFacade.saveMasterAndDetailData(User.systemUser(targetTenantId), saveArg);
                    }

                    if (needSetSuccessMsg) {
                        setTransferSuccess(sourceTenantId, sourceDatum, String.format("负责人【%s】在二期企业中不存在,已将负责人默认赋值为二期企业门店【%s】负责人", sourceOwnerName, sourceStoreName));
                        continue;
                    }
                    setTransferSuccess(sourceTenantId, sourceDatum);
                } catch (Exception ex) {
                    setTransferError(sourceTenantId, sourceDatum, String.format("发生未知异常：%s", ex.getMessage()));
                }
            }
        }
        return new Transfer.Result();
    }

    private void setTransferError(String sourceTenantId, IObjectData data, String message) {
        Map<String, Object> updater = new HashMap<>();
        updater.put("transfer_error_message__c", message);
        updater.put("transfer_success__c", false);
        updater.put("transfered__c", true);
        serviceFacade.updateWithMap(User.systemUser(sourceTenantId), data, updater);
    }

    private void setTransferSuccess(String sourceTenantId, IObjectData data, String message) {
        Map<String, Object> updater = new HashMap<>();
        updater.put("transfer_success__c", true);
        updater.put("transfered__c", true);
        updater.put("transfer_error_message__c", message);
        serviceFacade.updateWithMap(User.systemUser(sourceTenantId), data, updater);
    }

    private void setTransferSuccess(String sourceTenantId, IObjectData data) {
        Map<String, Object> updater = new HashMap<>();
        updater.put("transfer_success__c", true);
        updater.put("transfered__c", true);
        serviceFacade.updateWithMap(User.systemUser(sourceTenantId), data, updater);
    }

    private Map<String, List<IObjectData>> convertToTargetObjData(String targetTenantId, String price) {
        Map<String, List<IObjectData>> detailsData = new HashMap<>();
        List<IObjectData> agreementDetails = Lists.newArrayList();

        IObjectData agreementDetail = new ObjectData();
        agreementDetail.set("activity_item_id", "612ef8a0ad19ee00015731d3");
        agreementDetail.set("name", "农贸陈列（单店费用标准）");
        agreementDetail.set("agreement_amount_standard", "1");
        agreementDetail.set("agreement_cost_standard", price);
        agreementDetail.setDescribeApiName(ApiNames.TPM_ACTIVITY_AGREEMENT_DETAIL_OBJ);
        agreementDetail.setTenantId(targetTenantId);
        agreementDetail.setOwner(Lists.newArrayList("-10000"));
        agreementDetail.setRecordType("default__c");

        agreementDetails.add(agreementDetail);
        detailsData.put(ApiNames.TPM_ACTIVITY_AGREEMENT_DETAIL_OBJ, agreementDetails);
        return detailsData;
    }

    private IObjectData convertToTargetMasterData(
            String sourceTenantAccount,
            String targetTenantId,
            String targetTenantAccount,
            String targetOwner,
            IObjectData targetStoreData,
            IObjectData sourceObject) throws IOException, FRestClientException {

        IObjectData masterData = new ObjectData();

        masterData.setDescribeApiName(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        masterData.setTenantId(targetTenantId);
        masterData.setRecordType("default__c");

        masterData.set("code", sourceObject.get("name"));
        masterData.set("name", String.format("【%s】陈列协议 - %s", targetStoreData.getName(), sourceObject.get("name")));
        masterData.set("store_id", targetStoreData.getId());
        masterData.set("dealer_id", targetStoreData.get("dealer_id"));
        masterData.set("agreement_status", "in_progress");

        masterData.set("begin_date", sourceObject.get("field_ATOt9__c"));
        masterData.set("end_date", sourceObject.get("field_C13c9__c"));
        masterData.set("is_transfer_data__c", true);
        masterData.set("owner", Lists.newArrayList(targetOwner));

        masterData.set("field_tcGwv__c", Lists.newArrayList(sourceObject.get("field_s5w95__c", String.class)));

        masterData.set("agreement_images", transferImage(sourceObject.get("field_cxrRX__c"), sourceTenantAccount, targetTenantAccount));
        masterData.set("field_21lRw__c", transferImage(sourceObject.get("field_eY1NV__c"), sourceTenantAccount, targetTenantAccount));
        masterData.set("field_2QGfd__c", transferImage(sourceObject.get("field_Vzv9h__c"), sourceTenantAccount, targetTenantAccount));

        masterData.set("field_P6ae6__c", sourceObject.get("field_h7q4r__c"));

        return masterData;
    }

    private List<Map<String, Object>> transferImage(Object source, String sourceTenantAccount, String targetTenantAccount) throws FRestClientException, IOException {
        if (source == null) {
            return Lists.newArrayList();
        }
        List<ActivityProofImageDTO> sourceImages = JSON.parseArray(JSON.toJSONString(source), ActivityProofImageDTO.class);

        List<Map<String, Object>> targetImages = new ArrayList<>();
        for (ActivityProofImageDTO sourceImage : sourceImages) {

            InputStream imageStream = downloadImage(sourceTenantAccount, sourceImage.getPath(), sourceImage.getExt());
            StoneFileUploadResponse uploadResponse = uploadImage(targetTenantAccount, imageStream, sourceImage.getExt());

            Map<String, Object> targetImage = new HashMap<>();
            targetImage.put("path", uploadResponse.getPath());
            targetImage.put("ext", uploadResponse.getExtensionName());
            targetImage.put("size", uploadResponse.getSize());
            targetImage.put("filename", sourceImage.getAgreementsFileName());

            targetImages.add(targetImage);
        }
        return targetImages;
    }

    public InputStream downloadImage(String tenantAccount, String path, String suffix) throws FRestClientException {
        StoneFileDownloadRequest request = new StoneFileDownloadRequest();

        request.setEa(tenantAccount);
        request.setEmployeeId(1000);
        request.setFileType(suffix);
        request.setCancelRemoteThumb(false);
        request.setPath(path);
        request.setSecurityGroup("");
        request.setBusiness("FMCG-TPM");

        return stoneProxyApi.downloadStream(request);
    }

    public StoneFileUploadResponse uploadImage(String tenantAccount, InputStream imageStream, String suffix) throws IOException, FRestClientException {
        StoneFileUploadRequest request = new StoneFileUploadRequest();
        request.setImageProcessRequest(new StoneFileImageProcessRequest());
        request.setNeedThumbnail(false);
        request.setKeepFormat(true);

        byte[] fileStream = getFileByte(imageStream);
        InputStream targetStream = new ByteArrayInputStream(fileStream);

        request.setFileSize(fileStream.length);
        request.setSecurityGroup("");
        request.setPermissions(Lists.newArrayList());
        request.setGlobal(false);
        request.setExtensionName(suffix);
        request.setNamedPath("");
        request.setEa(tenantAccount);
        request.setEmployeeId(-10000);
        request.setBusiness("FMCG-TPM");

        return stoneProxyApi.uploadByStream("n", request, targetStream);
    }

    private byte[] getFileByte(InputStream is) throws IOException {
        return FileCopyUtils.copyToByteArray(is);
    }
}
