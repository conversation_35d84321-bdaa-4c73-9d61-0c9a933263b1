package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountDetailFields;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.web.service.PromotionPolicyService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/13 17:28
 */
@SuppressWarnings("Duplicates")
public class TPMActivityUnifiedCaseObjBulkInvalidAction extends StandardBulkInvalidAction  {

    private final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);
    private final PromotionPolicyService promotionPolicyService = SpringUtil.getContext().getBean(PromotionPolicyService.class);

    private List<IObjectData> allPromotionActivityUnifiedCases = null;

    private List<IObjectData> activityUnifiedCases = null;

    @Override
    protected void before(Arg arg) {

        //已存在预算收支流水不可作废
        validateExistCostFlow(arg);
        //已关联活动申请不可作废
        validateRelationActivity(arg);

        super.before(arg);

    }

    private void validateRelationActivity(Arg arg) {
        // 查询关联的活动申请
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(100);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);

        Filter idFilter = new Filter();
        idFilter.setFieldName(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(arg.getDataIds());

        Filter deletedFilter = new Filter();
        deletedFilter.setFieldName(CommonFields.IS_DELETED);
        deletedFilter.setOperator(Operator.EQ);
        deletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(idFilter, deletedFilter));

        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query).getData();

        if (!data.isEmpty()) {
            List<String> activityCaseIds = data.stream().map(obj -> (String) obj.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID)).distinct().collect(Collectors.toList());
            List<IObjectData> activityCase = serviceFacade.findObjectDataByIds(actionContext.getUser().getTenantId(), activityCaseIds, ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);

            String names = activityCase.stream().map(IObjectData::getName).collect(Collectors.joining(","));
            throw new ValidateException(String.format(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_BULK_INVALID_ACTION_0), names));
        }

    }

    private void validateExistCostFlow(Arg arg) {
        if (!budgetService.isOpenBudge(Integer.parseInt(actionContext.getTenantId()))) {
            return;
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(100);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);

        Filter apiNameFilter = new Filter();
        apiNameFilter.setFieldName(TPMBudgetAccountDetailFields.RELATED_OBJECT_API_NAME);
        apiNameFilter.setOperator(Operator.EQ);
        apiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ));

        Filter idFilter = new Filter();
        idFilter.setFieldName(TPMBudgetAccountDetailFields.RELATED_OBJECT_DATA_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(arg.getDataIds());

        Filter detailStatusFilter = new Filter();
        detailStatusFilter.setFieldName(TPMBudgetAccountDetailFields.DETAIL_STATUS);
        detailStatusFilter.setOperator(Operator.EQ);
        detailStatusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountDetailFields.DetailStatus.INCLUDE));

        query.setFilters(Lists.newArrayList(apiNameFilter, idFilter, detailStatusFilter));

        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_BUDGET_ACCOUNT_DETAIL, query).getData();

        if (!data.isEmpty()) {
            List<String> activityCaseIds = data.stream().map(obj -> (String) obj.get(TPMBudgetAccountDetailFields.RELATED_OBJECT_DATA_ID)).distinct().collect(Collectors.toList());
            List<IObjectData> activityCase = serviceFacade.findObjectDataByIds(actionContext.getUser().getTenantId(), activityCaseIds, ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);

            String names = activityCase.stream().map(IObjectData::getName).collect(Collectors.joining(","));
            throw new ValidateException(String.format(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_BULK_INVALID_ACTION_1), names));
        }
    }
}
