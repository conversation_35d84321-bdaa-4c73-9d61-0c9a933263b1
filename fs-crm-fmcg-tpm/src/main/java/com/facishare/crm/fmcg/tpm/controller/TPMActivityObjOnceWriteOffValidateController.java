package com.facishare.crm.fmcg.tpm.controller;

import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.business.abstraction.IActivityService;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * Author: linmj
 * Date: 2023/5/11 18:29
 */
public class TPMActivityObjOnceWriteOffValidateController extends PreDefineController<TPMActivityObjOnceWriteOffValidateController.Arg, TPMActivityObjOnceWriteOffValidateController.Result> {


    private IActivityService activityService = SpringUtil.getContext().getBean(IActivityService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected Result doService(Arg arg) {
        IObjectData activity = serviceFacade.findObjectData(controllerContext.getUser(), arg.getActivityId(), ApiNames.TPM_ACTIVITY_OBJ);
        activityService.ifAllowCreateDataDueToOnceWriteOff(controllerContext.getTenantId(), activity);
        return new Result();
    }

    @Data
    @ToString
    public static class Arg implements Serializable {
        private String activityId;
    }

    @Data
    @NoArgsConstructor
    @ToString
    public static class Result implements Serializable {

    }
}
