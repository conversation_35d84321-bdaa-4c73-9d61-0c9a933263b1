package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.ButtonApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityAgreementFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.business.abstraction.IActivityProofButtonService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.SneakyThrows;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * author: wuyx
 * description:
 * createTime: 2022/8/10 18:26
 */
public class TPMActivityAgreementObjListController extends StandardListController {

    private IActivityProofButtonService activityProofButtonService = SpringUtil.getContext().getBean(IActivityProofButtonService.class);

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        //支持 or查询
        SearchTemplateQuery query = (SearchTemplateQuery) super.defineQuery().toSearchTemplateQuery();
        if (TPMGrayUtils.TPMUseEs(controllerContext.getTenantId())) {
            query.setSearchSource("es");
        }
        return query;
    }

    @SneakyThrows
    @Override
    protected Result after(Arg arg, Result result) {
        Result finalResult = super.after(arg, result);
        activityProofButtonService.hideRioStoreConfirmButtonInList(controllerContext.getTenantId(), arg, result);
        return finalResult;
    }


    private void hideButton(Arg arg, Result result) {

    }
}
