package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetCarryForwardFields;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDescribeLayoutController;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@SuppressWarnings("all")
public class TPMBudgetCarryForwardObjDescribeLayoutController extends AbstractStandardDescribeLayoutController<TPMBudgetCarryForwardObjDescribeLayoutController.Arg> {

    public static final BudgetTypeDAO budgetTypeDAO = SpringUtil.getContext().getBean(BudgetTypeDAO.class);
    protected static final Set<String> ALL_TIME_DIMENSION_FIELD_API_NAMES = Sets.newHashSet();
    protected static final Set<String> READONLY_FIELDS = Sets.newHashSet();

    static {
        ALL_TIME_DIMENSION_FIELD_API_NAMES.add(TPMBudgetCarryForwardFields.SOURCE_MONTH);
        ALL_TIME_DIMENSION_FIELD_API_NAMES.add(TPMBudgetCarryForwardFields.SOURCE_QUARTER);
        ALL_TIME_DIMENSION_FIELD_API_NAMES.add(TPMBudgetCarryForwardFields.SOURCE_YEAR);
        ALL_TIME_DIMENSION_FIELD_API_NAMES.add(TPMBudgetCarryForwardFields.TARGET_MONTH);
        ALL_TIME_DIMENSION_FIELD_API_NAMES.add(TPMBudgetCarryForwardFields.TARGET_QUARTER);
        ALL_TIME_DIMENSION_FIELD_API_NAMES.add(TPMBudgetCarryForwardFields.TARGET_YEAR);

        READONLY_FIELDS.add(TPMBudgetCarryForwardFields.BUDGET_TYPE_ID);
        READONLY_FIELDS.add(TPMBudgetCarryForwardFields.BUDGET_NODE_ID);
        READONLY_FIELDS.add(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS);
        READONLY_FIELDS.add(TPMBudgetCarryForwardFields.CARRY_FORWARD_TIME);
        READONLY_FIELDS.add(TPMBudgetCarryForwardFields.TARGET_YEAR);
        READONLY_FIELDS.add(TPMBudgetCarryForwardFields.TARGET_QUARTER);
        READONLY_FIELDS.add(TPMBudgetCarryForwardFields.TARGET_MONTH);
    }

    @Override
    protected StandardDescribeLayoutController.Result after(Arg arg, StandardDescribeLayoutController.Result result) {
        StandardDescribeLayoutController.Result layoutResult = super.after(arg, result);

        String typeId = arg.getBudegetTypeId();
        String nodeId = arg.getBudegetNodeId();

        if (!Strings.isNullOrEmpty(arg.getData_id())) {
            IObjectData data = serviceFacade.findObjectDataIgnoreAll(User.systemUser(controllerContext.getTenantId()), arg.getData_id(), ApiNames.TPM_BUDGET_CARRY_FORWARD);
            typeId = (String) data.get(TPMBudgetCarryForwardFields.BUDGET_TYPE_ID);
            nodeId = (String) data.get(TPMBudgetCarryForwardFields.BUDGET_NODE_ID);
        }

        if (!Strings.isNullOrEmpty(typeId) && !Strings.isNullOrEmpty(nodeId)) {
            BudgetTypePO type = budgetTypeDAO.get(controllerContext.getTenantId(), typeId);
            if (Objects.nonNull(type) && CollectionUtils.isNotEmpty(type.getNodes())) {
                final String innerNodeId = nodeId;
                BudgetTypeNodeEntity node = type.getNodes().stream().filter(f -> f.getNodeId().equals(innerNodeId)).findFirst().orElse(null);

                ObjectDataDocument objDataDocument = layoutResult.getObjectData();
                if (Objects.nonNull(objDataDocument)) {
                    objDataDocument.put("time_dimension", node.getTimeDimension());
                    layoutResult.setObjectData(objDataDocument);
                }

                Set<String> uselessFieldApiNames = ALL_TIME_DIMENSION_FIELD_API_NAMES.stream()
                        .filter(dimension -> !dimension.endsWith(node.getTimeDimension()))
                        .collect(Collectors.toSet());
                if ("add".equals(arg.getLayout_type())) {
                    uselessFieldApiNames.add(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS);
                    uselessFieldApiNames.add(TPMBudgetCarryForwardFields.CARRY_FORWARD_TIME);
                }

                LayoutExt layoutExt = LayoutExt.of(layoutResult.getLayout());
                Optional<FormComponentExt> component = layoutExt.getFormComponent();

                if (component.isPresent()) {
                    FormComponentExt formComponent = component.get();
                    for (IFieldSection fieldSection : formComponent.getFieldSections()) {
                        fieldSection.setFields(fieldSection.getFields().stream()
                                .filter(field -> !uselessFieldApiNames.contains(field.getFieldName()))
                                .collect(Collectors.toList()));

                        for (IFormField field : fieldSection.getFields()) {
                            if (READONLY_FIELDS.contains(field.getFieldName())) {
                                field.setReadOnly(true);
                            }
                            if ("edit".equals(arg.getLayout_type())) {
                                if (ALL_TIME_DIMENSION_FIELD_API_NAMES.contains(field.getFieldName())) {
                                    field.setReadOnly(true);
                                }
                            }
                        }
                    }
                }
            }
        }
        return layoutResult;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Arg extends StandardDescribeLayoutController.Arg {

        @JSONField(name = "budget_type_id")
        @JsonProperty("budget_type_id")
        private String budegetTypeId;

        @JSONField(name = "budget_node_id")
        @JsonProperty("budget_node_id")
        private String budegetNodeId;
    }
}
