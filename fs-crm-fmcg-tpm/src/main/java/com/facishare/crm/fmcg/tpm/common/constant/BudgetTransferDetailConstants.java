package com.facishare.crm.fmcg.tpm.common.constant;

import com.facishare.crm.fmcg.common.apiname.TPMBudgetTransferDetailFields;
import com.google.common.collect.Sets;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/7/19 下午4:12
 */
public interface BudgetTransferDetailConstants {

    Set<String> NEED_OCCUPY_RECORD_TYPE_SET = Sets.newHashSet(TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_DEDUCT, TPMBudgetTransferDetailFields.RECORD_TYPE_DEFAULT);

    Set<String> NEED_ADD_RECORD_TYPE_SET = Sets.newHashSet(TPMBudgetTransferDetailFields.RECORD_TYPE_DEFAULT, TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_ADD);

    String OCCUPY_ID = "occupy_id";

    String TPM_APPROVAL_ID = "tpm_approval_trace_id";

    String TPM_BIZ_TRACE_ID = "tpm_biz_trace_id";

    String REAL_DEDUCTION_FLAG = "real_deduction_flag";

    String IS_APPROVAL = "tpm_is_approval";
}
