package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.ActivityCustomerTypeEnum;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IUnifiedActivityCommonLogicBusiness;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @author: wuyx
 * @description: TPM 客户范围
 * @createTime: 2021/12/29 11:04
 */
@SuppressWarnings("Duplicates")
public class TPMActivityObjRelatedActivityStoreListController extends StandardRelatedListController {

    public static final String CUSTOMER_TYPE = "customer_type";

    public static final String ACTIVITY_DEALER_ID = "dealer_id";

    public static final String UNIFIED_CASE_ID = "activity_unified_case_id";

    private static final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);

    private final IUnifiedActivityCommonLogicBusiness unifiedActivityCommonLogicBusiness = SpringUtil.getContext().getBean(IUnifiedActivityCommonLogicBusiness.class);

    /**
     * 重新设置上下文，将上下文改为客户对象
     *
     * @param arg 搜索客户入参
     */
    @Override
    protected void before(Arg arg) {
        this.controllerContext = new ControllerContext(
                controllerContext.getRequestContext(),
                ApiNames.ACCOUNT_OBJ,
                controllerContext.getMethodName());

        log.info("store filter arg : {}", arg);

        super.before(arg);
    }

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        List<IFilter> filters = query.getFilters();
        List<IFilter> objectDescribeNameList = filters.stream().filter(iFilter -> "object_describe_api_name".equals(iFilter.getFieldName())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(objectDescribeNameList)) {
            IFilter filter = new Filter();
            filter.setFieldName("object_describe_api_name");
            filter.setConnector("AND");
            filter.setOperator(Operator.EQ);
            filter.setFieldValues(Lists.newArrayList(arg.getObjectApiName()));
            query.setFilters(Lists.newArrayList(filter));
        }
        overrideAccountQuery(query);

        super.beforeQueryData(query);
    }

    /**
     * 依据活动方案信息，注入额外的 query 信息
     *
     * @param query 搜索 query
     * @return 搜索结果
     */
    private void overrideAccountQuery(SearchTemplateQuery query) {
        String tenantId = controllerContext.getTenantId();
        String customerType = (String) arg.getMasterData().get(CUSTOMER_TYPE);
        customerType = Strings.isNullOrEmpty(customerType) ? ActivityCustomerTypeEnum.DEALER_STORE.value() : customerType;
        String activityDealerId = (String) arg.getMasterData().get(ACTIVITY_DEALER_ID);
        String unifiedActivityId = (String) arg.getMasterData().get(UNIFIED_CASE_ID);


        AtomicInteger count = new AtomicInteger(1);
        StringBuilder pattern = new StringBuilder();

        if (!CollectionUtils.isEmpty(query.getFilters())) {
            pattern.append("( ");
            for (int i = 0; i < query.getFilters().size(); i++) {
                if (i != 0) {
                    pattern.append(" and ");
                }
                pattern.append(count.getAndIncrement());
            }
            pattern.append(") ");
        }
        if (Strings.isNullOrEmpty(activityDealerId)) {
            if (Strings.isNullOrEmpty(unifiedActivityId)) {
                setCustomerTypeFilter(query, count, pattern, customerType, null);
            } else {
                IObjectData unifiedActivity = serviceFacade.findObjectData(controllerContext.getUser(), unifiedActivityId, ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
                setCustomerTypeFilter(query, count, pattern, customerType, unifiedActivityCommonLogicBusiness.getDealerIdsOfUnifiedActivity(tenantId, unifiedActivity));
            }
        } else {
            if (TPMGrayUtils.isStoreRangeIgnoreDealerFilter(tenantId)) {
                setCustomerTypeFilter(query, count, pattern, customerType, null);
            } else {
                setCustomerTypeFilter(query, count, pattern, customerType, Lists.newArrayList(activityDealerId));
            }
        }
        query.setPattern(pattern.toString());
        log.info("final pattern:{},filters；{}", query.getPattern(), JSON.toJSONString(query.getFilters()));
    }

    private void setCustomerTypeFilter(SearchTemplateQuery query, AtomicInteger count, StringBuilder pattern, String customerType, Collection<String> dealerIds) {
        if (CollectionUtils.isNotEmpty(dealerIds) && dealerIds.size() > 500) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_RELATED_ACTIVITY_STORE_LIST_CONTROLLER_0) + dealerIds.size() + I18N.text(I18NKeys.ACTIVITY_OBJ_RELATED_ACTIVITY_STORE_LIST_CONTROLLER_1));
        }

        switch (ActivityCustomerTypeEnum.of(customerType)) {
            case STORE:

                Filter recordTypeFilter = new Filter();
                recordTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
                recordTypeFilter.setOperator(Operator.NIN);
                recordTypeFilter.setFieldValues(storeBusiness.findDealerRecordType(controllerContext.getTenantId()));
                query.getFilters().add(recordTypeFilter);
                pattern.append(" and ").append(count.getAndIncrement()).append(" ");

                if (CollectionUtils.isNotEmpty(dealerIds)) {
                    if (!dealerIds.contains("-1")) {
                        Filter dealerIdFilter = new Filter();
                        dealerIdFilter.setOperator(Operator.IN);
                        dealerIdFilter.setFieldName(storeBusiness.findDealerFieldApiName(controllerContext.getTenantId()));
                        dealerIdFilter.setFieldValues(new ArrayList<>(dealerIds));
                        query.getFilters().add(dealerIdFilter);
                        pattern.append(" and ").append(count.getAndIncrement()).append(" ");
                    }
                }
                break;
            case DEALER:
                pattern.append(" and ( ");
                Filter dealerRecordTypeFilter = new Filter();
                dealerRecordTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
                dealerRecordTypeFilter.setOperator(Operator.IN);
                dealerRecordTypeFilter.setFieldValues(storeBusiness.findDealerRecordType(controllerContext.getTenantId()));
                query.getFilters().add(dealerRecordTypeFilter);
                pattern.append(" ").append(count.getAndIncrement()).append(" ");
                if (CollectionUtils.isNotEmpty(dealerIds)) {
                    if (!dealerIds.contains("-1")) {
                        Filter dealerFilter = new Filter();
                        dealerFilter.setOperator(Operator.IN);
                        dealerFilter.setFieldName(CommonFields.ID);
                        dealerFilter.setFieldValues(new ArrayList<>(dealerIds));
                        query.getFilters().add(dealerFilter);
                        pattern.append(" and ").append(count.getAndIncrement()).append(" ");
                    }
                }
                pattern.append(" ) ");
                break;
            case DEALER_STORE:
                if (CollectionUtils.isNotEmpty(dealerIds)) {
                    if (!dealerIds.contains("-1")) {
                        pattern.append(" and ( ");
                        Filter dealerIdFilter = new Filter();
                        dealerIdFilter.setOperator(Operator.IN);
                        dealerIdFilter.setFieldName(storeBusiness.findDealerFieldApiName(controllerContext.getTenantId()));
                        dealerIdFilter.setFieldValues(new ArrayList<>(dealerIds));
                        query.getFilters().add(dealerIdFilter);
                        pattern.append(count.getAndIncrement()).append(" ");

                        Filter dealerFilter = new Filter();
                        dealerFilter.setOperator(Operator.IN);
                        dealerFilter.setFieldName(CommonFields.ID);
                        dealerFilter.setFieldValues(new ArrayList<>(dealerIds));
                        query.getFilters().add(dealerFilter);
                        pattern.append(" or ").append(count.getAndIncrement()).append(" ");

                        pattern.append(" ) ");
                    }
                }
            default:
                log.info("default. customerType:{}", customerType);
        }
    }
}
