package com.facishare.crm.fmcg.tpm.action;

import cn.hutool.core.exceptions.ValidateException;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;

/**
 * Author: linmj
 * Date: 2023/10/30 15:40
 */
public class TPMActivityRewardDetailObjBulkInvalidAction extends StandardBulkInvalidAction {

    @Override
    protected void before(Arg arg) {
        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_REWARD_DETAIL_OBJ_BULK_INVALID_ACTION_0));
    }
}
