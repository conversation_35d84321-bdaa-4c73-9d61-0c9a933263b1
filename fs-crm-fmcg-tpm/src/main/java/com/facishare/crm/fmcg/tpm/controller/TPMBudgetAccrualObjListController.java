package com.facishare.crm.fmcg.tpm.controller;

import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccrualDetailFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/2 上午11:12
 */
public class TPMBudgetAccrualObjListController extends StandardListController {


    private boolean isAllowAccrualNegativeMoney = false;

    @Override
    protected void before(Arg arg) {
        this.isAllowAccrualNegativeMoney = TPMGrayUtils.isAllowAccrualNegativeMoney(controllerContext.getTenantId());
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result finalResult = super.after(arg, result);
        realButton(arg, finalResult);
        return finalResult;
    }

    private void realButton(Arg arg, Result result) {
        if (arg.isIncludeButtonInfo()) {
            if (result.getButtonInfo().getButtonMap() != null) {
                List<String> materIds = result.getDataList().stream().map(ObjectDataDocument::getId).collect(Collectors.toList());
                Map<String, Boolean> fakeMap = isFake(materIds);
                Map<String, List<String>> buttonMap = result.getButtonInfo().getButtonMap();
                materIds.forEach(masterId -> {
                    if (fakeMap.getOrDefault(masterId, true) && buttonMap.containsKey(masterId)) {
                        buttonMap.get(masterId).remove(ObjectAction.BUDGET_ACCRUAL.getButtonApiName());
                    }
                });
            }
        }
    }

    public Map<String, Boolean> isFake(List<String> masterIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setSearchSource("db");
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setLimit(2000);

        Filter masterIdFilter = new Filter();
        masterIdFilter.setFieldName(TPMBudgetAccrualDetailFields.ACCRUAL_ID);
        masterIdFilter.setOperator(Operator.IN);
        masterIdFilter.setFieldValues(masterIds);

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMBudgetAccrualDetailFields.STATUS);
        statusFilter.setOperator(Operator.EQ);
        statusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccrualDetailFields.Status.EXCLUDE));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        Filter amountFilter = new Filter();
        amountFilter.setFieldName(TPMBudgetAccrualDetailFields.ACTUAL_AMOUNT);
        amountFilter.setOperator(Operator.GT);
        amountFilter.setFieldValues(Lists.newArrayList("0"));

        if (this.isAllowAccrualNegativeMoney) {
            query.setFilters(Lists.newArrayList(masterIdFilter, statusFilter, lifeStatusFilter));
        } else {
            query.setFilters(Lists.newArrayList(masterIdFilter, statusFilter, lifeStatusFilter, amountFilter));
        }

        List<IObjectData> aggList = serviceFacade.aggregateFindBySearchQuery(controllerContext.getTenantId(), query, ApiNames.TPM_BUDGET_ACCRUAL_DETAIL_OBJ, TPMBudgetAccrualDetailFields.ACCRUAL_ID, "count", "");
        Map<String, Boolean> result = new HashMap<>();
        for (IObjectData countResult : aggList) {
            String masterId = countResult.get(TPMBudgetAccrualDetailFields.ACCRUAL_ID, String.class);
            Integer count = countResult.get("groupbycount", Integer.class, 0);
            result.put(masterId, count == 0);
        }
        return result;
    }
}
