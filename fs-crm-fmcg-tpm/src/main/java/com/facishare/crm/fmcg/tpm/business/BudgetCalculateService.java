package com.facishare.crm.fmcg.tpm.business;

import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetCalculateService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetOccupyService;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ControlStrategyType;
import com.facishare.crm.fmcg.tpm.dao.paas.BudgetAccountDetailMapper;
import com.facishare.crm.fmcg.tpm.dao.paas.BudgetAccountMapper;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/7/8 下午2:40
 */
@Slf4j
@Service
public class BudgetCalculateService implements IBudgetCalculateService {

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private IBudgetOccupyService budgetOccupyService;
    @Resource
    private BudgetAccountMapper budgetAccountMapper;
    @Resource
    private BudgetAccountDetailMapper budgetAccountDetailMapper;

    @Override
    public void recalculateBudgetAmount(User user, String accountId) {
        log.info("recalculate budget,user:{},accountId:{}", user, accountId);
        if (Strings.isNullOrEmpty(accountId)) {
            return;
        }
        updateBudgetAmountFields(user, accountId, getRealBudgetAmountMap(user, accountId));
        recalculateStaticDepartmentAmount(user, accountId);
    }

    @Override
    public Map<String, BigDecimal> getRealBudgetAmountMap(User user, String accountId) {
        if (Strings.isNullOrEmpty(accountId)) {
            return new HashMap<>();
        }
        return budgetAccountDetailMapper.setTenantId(user.getTenantId()).statisticMoney(user.getTenantId(), accountId);
    }

    @Override
    public Map<String, BigDecimal> batchGetBudgetAvailableAmount(User user, List<String> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return new HashMap<>();
        }
        return budgetAccountDetailMapper.setTenantId(user.getTenantId()).batchGetBudgetAvailableAmount(user.getTenantId(), accountIds);
    }

    @Override
    public void updateBudgetAmountFields(User user, String budgetAccountId, Map<String, BigDecimal> amountMap) {
        if (Strings.isNullOrEmpty(budgetAccountId) || MapUtils.isEmpty(amountMap)) {
            return;
        }
        log.info("update budgetAmount,budgetId:{},amountMap:{}", budgetAccountId, amountMap);
        IObjectData account = new ObjectData();
        account.setTenantId(user.getTenantId());
        account.setDescribeApiName(ApiNames.TPM_BUDGET_ACCOUNT);
        account.setId(budgetAccountId);

        long currentTime = System.currentTimeMillis();
        Map<String, Object> updateMap = new HashMap<>(amountMap);

        serviceFacade.updateWithMap(user, account, updateMap);
        /*budgetAccountMapper.setTenantId(user.getTenantId()).updateAccountAmount(user.getTenantId(), budgetAccountId, updateMap);
        IObjectDescribe describe = serviceFacade.findObjectIncludeDeleted(user.getTenantId(), ApiNames.TPM_BUDGET_ACCOUNT);
        serviceFacade.logWithCustomMessage(user, EventType.MODIFY, ActionType.UPDATE_FIELD, describe, new ObjectData(updateMap), "更新预算金额");*/
    }

    @Override
    public boolean checkBudgetAllowDeduction(User user, ControlStrategyType controlStrategyType, List<String> relatedBudgetAccountIds, List<String> brotherBudgetAccountIds, BigDecimal needAmount) {
        BigDecimal brotherAllowAmount;
        switch (controlStrategyType) {
            case UNLIMITED:
                brotherAllowAmount = getAllowAmountInControlDimension(user, brotherBudgetAccountIds);
                return brotherAllowAmount.subtract(needAmount).doubleValue() >= 0;
            case FULL_LIMIT:
                String currentBudgetId = relatedBudgetAccountIds.get(0);
                BigDecimal occupiedAmount = budgetOccupyService.statistics(user.getTenantId(), currentBudgetId);
                Map<String, BigDecimal> amountMap = getRealBudgetAmountMap(user, currentBudgetId);
                log.info("occupy amount:{}. budget amount map:{}", occupiedAmount, amountMap);
                return amountMap.get(TPMBudgetAccountFields.AVAILABLE_AMOUNT).subtract(occupiedAmount).subtract(needAmount).doubleValue() >= 0;
            case CUSTOM_DIMENSION_LIMIT:
                BigDecimal relatedAllowAmount = getAllowAmountInControlDimension(user, relatedBudgetAccountIds);
                brotherAllowAmount = getAllowAmountInControlDimension(user, brotherBudgetAccountIds);
                BigDecimal realAmount = relatedAllowAmount.compareTo(brotherAllowAmount) <= 0 ? relatedAllowAmount : brotherAllowAmount;
                return realAmount.subtract(needAmount).doubleValue() >= 0;
            default:
                return false;
        }
    }

    private BigDecimal getAllowAmountInControlDimension(User user, List<String> budgetIds) {
        Map<String, BigDecimal> occupiedMoneyMap = budgetOccupyService.batchStatistics(user.getTenantId(), budgetIds);
        BigDecimal allOccupiedMoney = new BigDecimal("0");
        for (BigDecimal v : occupiedMoneyMap.values()) {
            allOccupiedMoney = allOccupiedMoney.add(v);
        }
        BigDecimal allAvailableMoney = new BigDecimal("0");
        Map<String, BigDecimal> availableAmountMap = batchGetBudgetAvailableAmount(user, budgetIds);
        for (BigDecimal amount : availableAmountMap.values()) {
            allAvailableMoney = allAvailableMoney.add(amount);
        }
        log.info("allOccupiedMoney :{}. budget allAvailableMoney:{}", allOccupiedMoney, allAvailableMoney);
        return allAvailableMoney.subtract(allOccupiedMoney);
    }

    @Override
    public void recalculateStaticDepartmentAmount(User user, String budgetId) {
        Timer timer = new Timer();
        //不在事务内
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
                parallelTask.submit(() -> {
                    Map<String, Object> result = budgetAccountMapper.statisticDepartmentAmount(user.getTenantId(), budgetId);
                    if (!MapUtils.isEmpty(result)) {
                        Map<String, BigDecimal> updateMap = new HashMap<>();
                        BigDecimal amount = (BigDecimal) result.getOrDefault(TPMBudgetAccountFields.STATISTIC_DEPARTMENT_AMOUNT, BigDecimal.ZERO);
                        updateMap.put(TPMBudgetAccountFields.STATISTIC_DEPARTMENT_AMOUNT, amount.setScale(3, RoundingMode.DOWN));
                        updateBudgetAmountFields(user, budgetId, updateMap);

                        String parentId = (String) result.get(TPMBudgetAccountFields.PARENT_ID);
                        if (!Strings.isNullOrEmpty(parentId)) {
                            //todo:asy deal and combine the same update
                            recalculateStaticDepartmentAmount(user, parentId);
                        }
                    }
                });
                parallelTask.run();
            }
        }, 4000L);
    }


    private IObjectData formSimpleObjectData(String id) {
        IObjectData objectData = new ObjectData();
        objectData.setId(id);
        objectData.setOwner(Lists.newArrayList("-10000"));
        objectData.setRecordType("default__c");
        return objectData;
    }

    private List<IObjectData> queryAllByFilter(User user, String apiName, List<IFilter> filters) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");
        query.setOffset(0);
        query.setLimit(-1);
        query.setFilters(filters);

        return CommonUtils.queryData(serviceFacade, user, apiName, query);
    }
}
