package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.tpm.api.special.mengniu.OuterConsumerRewardRules;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fmcg.framework.http.MengNiuOuterRewardProxy;
import com.fmcg.framework.http.contract.mengniu.ActList;
import com.fs.fmcg.sdk.ai.plat.SecretUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

@SuppressWarnings("unused")
public class TPMActivityObjOuterConsumerRewardRulesController extends PreDefineController<OuterConsumerRewardRules.Arg, OuterConsumerRewardRules.Result> {

    private final MengNiuOuterRewardProxy mengNiuOuterRewardProxy = SpringUtil.getContext().getBean(MengNiuOuterRewardProxy.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected OuterConsumerRewardRules.Result doService(OuterConsumerRewardRules.Arg arg) {
        long time = System.currentTimeMillis();
        String clientId;
        String sk;

        if ("777421".equals(controllerContext.getTenantId())) {
            clientId = ConfigFactory.getConfig("gray-rel-fmcg").get("sales_mengniu_openapi_client_id");
            sk = ConfigFactory.getConfig("gray-rel-fmcg").get("sales_mengniu_openapi_sk");
        } else {
            clientId = "bf68d8a3-4763-40aa-a8d8-a34763e0aace";
            sk = "D7ABC695-6EB3-4902-ABC6-956EB3790228";
        }

        String sign = SecretUtil.md5(clientId + sk + time).toUpperCase();

        if (arg.getPage() <= 0) {
            arg.setPage(1);
        }
        if (arg.getSize() < 5) {
            arg.setSize(5);
        }
        if (arg.getSize() > 100) {
            arg.setSize(100);
        }
        if (Objects.isNull(arg.getKeyword())) {
            arg.setKeyword("");
        }

        ActList.Arg codeCenterArg = new ActList.Arg();
        codeCenterArg.setActName(arg.getKeyword());
        codeCenterArg.setPage(arg.getPage());
        codeCenterArg.setSize(arg.getSize());
        codeCenterArg.setSearchForbidden(false);

        ActList.Result codeCenterResult = mengNiuOuterRewardProxy.actList(clientId, sign, String.valueOf(time), codeCenterArg);
        if (!Boolean.TRUE.equals(codeCenterResult.getSuccess())) {
            throw new MetaDataBusinessException(codeCenterResult.getMsg(), codeCenterResult.getCode());
        }

        return convertResult(arg, codeCenterResult);
    }

    private OuterConsumerRewardRules.Result convertResult(OuterConsumerRewardRules.Arg arg, ActList.Result codeCenterResult) {
        OuterConsumerRewardRules.Result apiResult = new OuterConsumerRewardRules.Result();
        apiResult.setTotal(codeCenterResult.getData().getTotal());
        apiResult.setPage(arg.getPage());
        apiResult.setSize(arg.getSize());
        apiResult.setTotalPage(codeCenterResult.getData().getTotal() % arg.getSize() == 0 ? codeCenterResult.getData().getTotal() / arg.getSize() : codeCenterResult.getData().getTotal() / arg.getSize() + 1);
        apiResult.setData(Lists.newArrayList());
        for (ActList.OuterActivity outerActivity : codeCenterResult.getData().getData()) {
            OuterConsumerRewardRules.OuterConsumerRewardRuleDatum datum = new OuterConsumerRewardRules.OuterConsumerRewardRuleDatum();
            datum.setId(outerActivity.getCodeCenterActivityId());
            datum.setName(outerActivity.getActivityName());
            datum.setCode("MN-CCA-" + datum.getId());
            datum.setPlatform("mn_code_center");
            apiResult.getData().add(datum);
        }
        return apiResult;
    }
}
