package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

public class TPMActivityProofAuditObjRelatedListController extends StandardRelatedListController {

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        if (TPMGrayUtils.TPMUseEs(controllerContext.getTenantId())) {
            query.setSearchSource("es");
        }
        super.beforeQueryData(query);
    }
}
