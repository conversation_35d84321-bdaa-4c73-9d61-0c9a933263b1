package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.apiname.TPMDealerActivityCostFields;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class TPMDealerActivityCostObjWebDetailController extends StandardWebDetailController {

    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);

    @Override
    protected Result after(Arg arg, Result result) {
        fillDealerActivityCost(result);
        buttonFilter(result);
        return super.after(arg, result);
    }

    private void fillDealerActivityCost(Result result) {

        ObjectDataExt objectDataExt = ObjectDataExt.of(result.getData());
        Long beginDate = objectDataExt.get(TPMDealerActivityCostFields.BEGIN_DATE, Long.class);
        Long endDate = objectDataExt.get(TPMDealerActivityCostFields.END_DATE, Long.class);

        if (beginDate <= TimeUtils.MIN_DATE) {
            result.getData().put(TPMDealerActivityCostFields.BEGIN_DATE, null);
        }
        if (endDate >= TimeUtils.MAX_DATE) {
            result.getData().put(TPMDealerActivityCostFields.END_DATE, null);
        }

    }

    private void buttonFilter(Result result) {
        try {
            List<ILayout> byTypesIncludeFlowLayout = serviceFacade.getLayoutLogicService().findByTypesIncludeFlowLayout(controllerContext.getTenantId(),
                    ApiNames.TPM_DEALER_ACTIVITY_COST, Lists.newArrayList(LayoutTypes.DETAIL));
            List<String> flowLayoutApiNames = byTypesIncludeFlowLayout.stream().filter(iLayout -> Objects.equals(iLayout.getNamespace(), "flow"))
                    .map(ILayout::getName).collect(Collectors.toList());
            if (flowLayoutApiNames.contains(arg.getLayoutApiName())) {
                return;
            }
            if (result.getLayout() == null) {
                return;
            }
            List<IComponent> components = LayoutExt.of(result.getLayout()).getComponents();
            IObjectData cost = result.getData().toObjectData();
            String activityType = cost.get(TPMDealerActivityCostFields.ACTIVITY_TYPE, String.class);
            if (Strings.isNullOrEmpty(activityType)) {
                String activityId = cost.get(TPMDealerActivityCostFields.ACTIVITY_ID, String.class);
                IObjectData activity = serviceFacade.findObjectDataIncludeDeleted(controllerContext.getUser(), activityId, ApiNames.TPM_ACTIVITY_OBJ);
                activityType = activity.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
            }
            ActivityTypeExt activityTypeExt = activityTypeManager.find(controllerContext.getTenantId(), activityType);
            String lifeStatus = cost.get(CommonFields.LIFE_STATUS, String.class);
            if (activityTypeExt == null) {
                log.info("activity type not found.type:{}", activityType);
            }
            if (!CommonFields.LIFE_STATUS__NORMAL.equals(lifeStatus) || activityTypeExt == null || (activityTypeExt.writeOffChargeUpConfig() != null
                    && Boolean.FALSE.equals(activityTypeExt.writeOffChargeUpConfig().getChargeUpAccountStatus()))) {
                for (IComponent component : components) {
                    if ("head_info".equals(component.get("api_name"))) {
                        List<IButton> buttons = component.getButtons();
                        buttons.removeIf(button -> ObjectAction.ENTER_ACCOUNT.getActionCode().equals(button.getAction()) || ObjectAction.CANCEL_ENTRY.getActionCode().equals(button.getAction()));
                        component.setButtons(buttons);
                    }
                }
                if ("mobile".equals(arg.getLayoutAgentType())) {
                    ILayout iLayout = result.getLayout().toLayout();
                    List<IButton> buttons = iLayout.getButtons();
                    buttons.removeIf(button -> ObjectAction.ENTER_ACCOUNT.getActionCode().equals(button.getAction()) || ObjectAction.CANCEL_ENTRY.getActionCode().equals(button.getAction()));
                    iLayout.setButtons(buttons);
                    result.setLayout(LayoutDocument.of(iLayout));
                }
            }
        } catch (MetadataServiceException e) {
            throw new ValidateException(e.getMessage());
        }
    }
}
