package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetBusinessSubjectFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetActivitySubjectService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 * @Date 2022/6/1 17:38
 */
public class TPMBudgetBusinessSubjectObjEditAction extends StandardEditAction {

    private IBudgetActivitySubjectService budgetActivitySubjectService = SpringUtil.getContext().getBean(IBudgetActivitySubjectService.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        if (this.updatedFieldMap.containsKey(TPMBudgetBusinessSubjectFields.PARENT_SUBJECT_ID)) {
            setRightLevel();
        }
    }

    private void setRightLevel() {
        String parentId = (String) arg.getObjectData().get(TPMBudgetBusinessSubjectFields.PARENT_SUBJECT_ID);
        int level = 1;
        if (!Strings.isNullOrEmpty(parentId)) {
            IObjectData parent = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), parentId, ApiNames.TPM_BUDGET_BUSINESS_SUBJECT);
            level = parent.get(TPMBudgetBusinessSubjectFields.SUBJECT_LEVEL, Integer.class, level) + 1;
        }
        arg.getObjectData().put(TPMBudgetBusinessSubjectFields.SUBJECT_LEVEL, level);
        this.diffMasterData();
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result finalResult = super.after(arg, result);

        IObjectData resultObjectData = result.getObjectData().toObjectData();
        if (CommonFields.LIFE_STATUS__NORMAL.equals(resultObjectData.get(CommonFields.LIFE_STATUS, String.class))) {
            budgetActivitySubjectService.resetSubjectLevel(User.systemUser(actionContext.getTenantId()), resultObjectData.getId(), resultObjectData.get(TPMBudgetBusinessSubjectFields.SUBJECT_LEVEL, Integer.class, 1) + 1, Lists.newArrayList(resultObjectData.getId()));
        }

        return finalResult;
    }
}
