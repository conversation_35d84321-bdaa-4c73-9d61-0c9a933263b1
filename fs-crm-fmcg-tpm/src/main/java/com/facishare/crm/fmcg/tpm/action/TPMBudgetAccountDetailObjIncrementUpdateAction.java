package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;

/**
 * <AUTHOR>
 * @date 2022/6/29 下午2:26
 */
public class TPMBudgetAccountDetailObjIncrementUpdateAction  extends StandardIncrementUpdateAction {

    @Override
    public boolean equals(Object o) {
        throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_DETAIL_OBJ_INCREMENT_UPDATE_ACTION_0));
    }
}
