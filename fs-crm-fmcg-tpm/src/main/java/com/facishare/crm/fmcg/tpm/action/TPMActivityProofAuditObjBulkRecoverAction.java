package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkRecoverAction;
import org.apache.commons.collections.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2021/8/4 下午3:35
 */
public class TPMActivityProofAuditObjBulkRecoverAction extends StandardBulkRecoverAction {

    @Override
    protected void before(Arg arg) {
        if(CollectionUtils.isNotEmpty(arg.getIdList())){
            throw new ValidateException(I18N.text(I18NKeys.AUDIT_RECOVER_FAIL_DUE_TO_DISABLE));
        }
        super.before(arg);
    }
}
