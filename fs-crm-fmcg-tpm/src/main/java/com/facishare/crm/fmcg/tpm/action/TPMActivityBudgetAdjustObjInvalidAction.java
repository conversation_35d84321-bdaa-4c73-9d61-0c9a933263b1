package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2021/7/21 下午3:15
 */
public class TPMActivityBudgetAdjustObjInvalidAction extends StandardInvalidAction {

    private static final Logger log = LoggerFactory.getLogger(TPMActivityBudgetAdjustObjInvalidAction.class);

    @Override
    protected void before(Arg arg) {
        log.info("start invalid adjust:{}",arg);
        IObjectData data = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()),arg.getObjectDataId(), ApiNames.TPM_ACTIVITY_BUDGET_ADJUST_OBJ);
        String status = data.get("life_status",String.class);
        log.info(" adjust data :{}",data);
        if("normal".equals(status)){
            throw new ValidateException(I18N.text(I18NKeys.SINGLE_COMPLETED_ADJUST_CAN_NOT_BE_INVALID));
        }
        super.before(arg);
    }
}
