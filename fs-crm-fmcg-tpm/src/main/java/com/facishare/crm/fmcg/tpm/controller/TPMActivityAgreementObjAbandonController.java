package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.business.BudgetConsumeV2Service;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActionModeType;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import de.lab4inf.math.util.Strings;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/1/7 14:16
 */
@SuppressWarnings("Duplicates")
@Slf4j
public class TPMActivityAgreementObjAbandonController extends PreDefineController<TPMActivityAgreementObjAbandonController.Arg, TPMActivityAgreementObjAbandonController.Result> {

    private static final BudgetConsumeV2Service budgetConsumeV2Service = SpringUtil.getContext().getBean(BudgetConsumeV2Service.class);


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    public static final String IS_ABANDONED_FIELD_KEY = "is_abandoned__c";
    public static final String ABANDONED_TIME_FIELD_KEY = "abandoned_time__c";

    @Override
    protected TPMActivityAgreementObjAbandonController.Result doService(TPMActivityAgreementObjAbandonController.Arg arg) {
        try {
            log.info("agreement abandon arg : {}", arg);

            if (Strings.isNullOrEmpty(arg.getObjectDataId())) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_AGREEMENT_OBJ_ABANDON_CONTROLLER_0));
            }

            IObjectData data = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getObjectDataId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
            if (Objects.isNull(data)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_AGREEMENT_OBJ_ABANDON_CONTROLLER_1));
            }

            data.set(TPMActivityAgreementFields.AGREEMENT_STATUS, TPMActivityAgreementFields.AGREEMENT_STATUS__INVALID);
            data.set(IS_ABANDONED_FIELD_KEY, true);
            data.set(ABANDONED_TIME_FIELD_KEY, System.currentTimeMillis());

            abandonRelatedProof(arg.getObjectDataId());
            abandonRelatedAudit(arg.getObjectDataId());
            //自定义对象已经不在业务中使用，没有新数据
//            abandonRelatedSummary(arg.getObjectDataId());
            abandonRelatedWriteOff(arg.getObjectDataId(), arg.getYears());

            IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);

            data = serviceFacade.updateObjectData(controllerContext.getUser(), data, true);
            serviceFacade.logWithCustomMessage(controllerContext.getUser(),
                    EventType.MODIFY,
                    ActionType.MODIFY,
                    describe,
                    data,
                    "终止协议");//ignorei18n
            //消费规则
            budgetConsumeV2Service.middleRelease(controllerContext.getUser(), data.getDescribeApiName(), data.getId(), ActionModeType.AGREEMENT_ABANDON.getKey());

            return Result.builder()
                    .success(true)
                    .errorMessage("")
                    .data(ObjectDataDocument.of(data)).build();
        } catch (Exception ex) {
            return TPMActivityAgreementObjAbandonController.Result.builder()
                    .success(false)
                    .errorMessage(ex.getMessage())
                    .data(null).build();
        }
    }

    private void abandonRelatedWriteOff(String objectDataId, Long years) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(200);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter agreementIdFilter = new Filter();
        agreementIdFilter.setFieldName(TPMStoreWriteOffFields.ACTIVITY_ASSOCIATION);
        agreementIdFilter.setOperator(Operator.EQ);
        agreementIdFilter.setFieldValues(Lists.newArrayList(objectDataId));

        Filter approvalStatusFilter = new Filter();
        approvalStatusFilter.setFieldName("approval_status__c");
        approvalStatusFilter.setOperator(Operator.N);
        approvalStatusFilter.setFieldValues(Lists.newArrayList("pass", "progressing"));

        Filter isReceiptSentFilter = new Filter();
        isReceiptSentFilter.setFieldName("is_receipt_sent__c");
        isReceiptSentFilter.setOperator(Operator.N);
        isReceiptSentFilter.setFieldValues(Lists.newArrayList("true"));

        query.setFilters(Lists.newArrayList(agreementIdFilter, approvalStatusFilter, isReceiptSentFilter));

        if (Objects.nonNull(years)) {
            Filter timeFilter = new Filter();
            timeFilter.setFieldName("years");
            timeFilter.setOperator(Operator.EQ);
            timeFilter.setFieldValues(Lists.newArrayList(String.valueOf(years)));
            query.getFilters().add(timeFilter);
            log.info("years is {}", years);
        }

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.ID);
        order.setIsAsc(false);
        query.setOrders(Lists.newArrayList(order));

        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_STORE_WRITE_OFF_OBJ, query).getData();

        log.info("1.abandon agreement found {} write off data : {}", data.size(), JSON.toJSONString(data));

        if (CollectionUtils.isEmpty(data)) {
            return;
        }

        serviceFacade.bulkInvalid(data, User.systemUser(controllerContext.getTenantId()));
    }

    private void abandonRelatedSummary(String objectDataId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(200);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter agreementIdFilter = new Filter();
        agreementIdFilter.setFieldName("activity_agreement_id__c");
        agreementIdFilter.setOperator(Operator.EQ);
        agreementIdFilter.setFieldValues(Lists.newArrayList(objectDataId));

        Filter approvalStatusFilter = new Filter();
        approvalStatusFilter.setFieldName("approval_status__c");
        approvalStatusFilter.setOperator(Operator.N);
        approvalStatusFilter.setFieldValues(Lists.newArrayList("pass"));

        Filter isReceiptSentFilter = new Filter();
        isReceiptSentFilter.setFieldName("is_receipt_sent__c");
        isReceiptSentFilter.setOperator(Operator.N);
        isReceiptSentFilter.setFieldValues(Lists.newArrayList("true"));

        query.setFilters(Lists.newArrayList(agreementIdFilter, approvalStatusFilter, isReceiptSentFilter));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.ID);
        order.setIsAsc(false);
        query.setOrders(Lists.newArrayList(order));

        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), "agreement_proof_summary__c", query).getData();

        log.info("1.abandon agreement found {} proof summaries : {}", data.size(), JSON.toJSONString(data));

        if (CollectionUtils.isEmpty(data)) {
            return;
        }

        serviceFacade.bulkInvalid(data, User.systemUser(controllerContext.getTenantId()));
    }

    private void abandonRelatedAudit(String objectDataId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(200);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter agreementIdFilter = new Filter();
        agreementIdFilter.setFieldName(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID);
        agreementIdFilter.setOperator(Operator.EQ);
        agreementIdFilter.setFieldValues(Lists.newArrayList(objectDataId));
        query.setFilters(Lists.newArrayList(agreementIdFilter));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.ID);
        order.setIsAsc(false);
        query.setOrders(Lists.newArrayList(order));

        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query).getData();

        List<String> fields = Lists.newArrayList(IS_ABANDONED_FIELD_KEY);
        List<List<IObjectData>> dataArray = Lists.partition(data, 50);

        for (List<IObjectData> dataDatum : dataArray) {
            for (IObjectData datum : dataDatum) {
                datum.set(IS_ABANDONED_FIELD_KEY, true);
            }
            serviceFacade.batchUpdateByFields(User.systemUser(controllerContext.getTenantId()), dataDatum, fields);
        }
    }

    private void abandonRelatedProof(String objectDataId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(200);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter agreementIdFilter = new Filter();
        agreementIdFilter.setFieldName(TPMActivityProofAuditFields.ACTIVITY_AGREEMENT_ID);
        agreementIdFilter.setOperator(Operator.EQ);
        agreementIdFilter.setFieldValues(Lists.newArrayList(objectDataId));
        query.setFilters(Lists.newArrayList(agreementIdFilter));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.ID);
        order.setIsAsc(false);
        query.setOrders(Lists.newArrayList(order));

        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, query).getData();

        List<String> fields = Lists.newArrayList(IS_ABANDONED_FIELD_KEY);
        List<List<IObjectData>> dataArray = Lists.partition(data, 50);

        for (List<IObjectData> dataDatum : dataArray) {
            for (IObjectData datum : dataDatum) {
                datum.set(IS_ABANDONED_FIELD_KEY, true);
            }
            serviceFacade.batchUpdateByFields(User.systemUser(controllerContext.getTenantId()), dataDatum, fields);
        }
    }

    @Data
    @ToString
    static class Arg implements Serializable {

        @SerializedName("object_data_id")
        @JSONField(name = "object_data_id")
        @JsonProperty("object_data_id")
        private String objectDataId;

        @SerializedName("years")
        @JSONField(name = "years")
        @JsonProperty("years")
        private Long years;

    }

    @Data
    @ToString
    @Builder
    static class Result implements Serializable {

        private ObjectDataDocument data;

        @SerializedName("success")
        @JSONField(name = "success")
        @JsonProperty("success")
        private boolean success;

        @SerializedName("error_message")
        @JSONField(name = "error_message")
        @JsonProperty("error_message")
        private String errorMessage;
    }
}
