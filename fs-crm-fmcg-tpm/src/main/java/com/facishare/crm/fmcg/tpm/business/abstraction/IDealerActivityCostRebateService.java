package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.crm.fmcg.tpm.api.plugin.DealerActivityCostEnterAccount;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;

public interface IDealerActivityCostRebateService {
    IObjectData initRebate(DealerActivityCostEnterAccount.Arg afterArg);

    boolean isRebateAccount(String tenantId, String fundAccountId);

    void syncFundAccountToRebate(User user, String fundAccountId);

    void invalidRebateObjData(RequestContext requestContext, List<String> costIds);

    boolean isOpenRebate(String tenantId);
}
