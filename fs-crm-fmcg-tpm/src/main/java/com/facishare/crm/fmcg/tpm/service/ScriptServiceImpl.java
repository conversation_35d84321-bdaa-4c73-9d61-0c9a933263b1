package com.facishare.crm.fmcg.tpm.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.adapter.PayService;
import com.facishare.crm.fmcg.common.adapter.dto.UserInfo;
import com.facishare.crm.fmcg.common.adapter.dto.pay.TransferDetail;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.BatchWXTenantTransfer;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.QueryWXTenantTransferDetail;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.WXPersonalAccount;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.WXTenantAccount;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.constant.ScanCodeActionConstants;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.business.ActivityService;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.business.TPMTriggerActionService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRangeFieldBusiness;
import com.facishare.crm.fmcg.tpm.business.enums.UseRangeEnum;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetAccrualRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetNewConsumeRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.UniqueIdBaseDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.reward.dto.SelfDefineReward;
import com.facishare.crm.fmcg.tpm.reward.handler.SelfDefineRewardHandler;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.crm.fmcg.tpm.service.abstraction.PluginInstanceService;
import com.facishare.crm.fmcg.tpm.service.abstraction.ScriptService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.contract.TriggerAction;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IBudgetTypeManager;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IPhysicalRewardService;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.metadata.dto.DescribeResult;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fs.fmcg.sdk.ai.plat.SecretUtil;
import com.fxiaoke.common.SqlEscaper;
import com.github.autoconf.ConfigFactory;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/8 下午2:35
 */
//IgnoreI18nFile
@Slf4j
@Service
public class ScriptServiceImpl implements ScriptService {

    @Resource
    private IActivityTypeManager activityTypeManager;

    @Resource
    private IBudgetTypeManager budgetTypeManager;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private IRangeFieldBusiness rangeFieldBusiness;

    @Resource(name = "objectDataPgService")
    private IObjectDataService objectDataService;

    @Resource
    private PayService payService;
    @Resource
    private PluginInstanceService pluginService;

    @Resource
    private TPMTriggerActionService triggerActionService;

    @Resource
    private SpecialTableMapper specialTableMapper;

    @Resource
    private ActivityTypeDAO activityTypeDAO;

    @Resource
    private SelfDefineRewardHandler selfDefineRewardHandler;

    @Resource
    private StoreBusiness storeBusiness;

    @Resource
    private OrganizationService tpmOrganizationService;

    @Resource
    private ActivityService activityService;

    @Resource
    private IPhysicalRewardService physicalRewardService;


    @Override
    public void setDefaultUniqueId(List<String> tenantIds) {
        Map<String, UniqueIdBaseDAO> daoMap = SpringUtil.getContext().getBeansOfType(UniqueIdBaseDAO.class);
        int maxRepeat = 50000;
        int loopSize = 100;
        if (CollectionUtils.isNotEmpty(tenantIds)) {
            for (String tenantId : tenantIds) {
                if (MapUtils.isNotEmpty(daoMap)) {
                    updateUniqueId(tenantId, daoMap.values(), maxRepeat, loopSize);
                }
            }
        } else {
            if (MapUtils.isNotEmpty(daoMap)) {
                updateUniqueId(null, daoMap.values(), maxRepeat, loopSize);
            }
        }
    }

    @Override
    public void copyMongo(String fromTenantId, String toTenantId) {
        Map<String, UniqueIdBaseDAO> daoMap = SpringUtil.getContext().getBeansOfType(UniqueIdBaseDAO.class);
        for (UniqueIdBaseDAO dao : daoMap.values()) {
            List<MongoPO> pos;
            String lastId = null;
            if (dao.containsPO(toTenantId)) {
                log.info("已经复制过了。");
                continue;
            }
            int count = 0;
            while (!(pos = dao.getAllDataByTenantId(fromTenantId, lastId, 100)).isEmpty()) {
                count += pos.size();
                if (count > 10000) {
                    log.info("拷贝大量数据。db:{}", dao.getClass());
                    break;
                }
                lastId = pos.get(pos.size() - 1).getOriginalId().toString();
                log.info("lastId:{},size:{}", lastId, pos.size());
                pos.forEach(po -> {
                    po.setId(null);
                    po.setTenantId(toTenantId);
                    if (dao instanceof BudgetNewConsumeRuleDAO) {
                        ((BudgetNewConsumeRulePO) po).setRuleStatus(StatusType.DISABLE.value());
                    } else if (dao instanceof BudgetAccrualRuleDAO) {
                        ((BudgetAccrualRulePO) po).setStatus(StatusType.DISABLE.value());
                    } else if (dao instanceof ActivityTypeDAO) {
                        List<ActivityNodeEntity> activityNodes = ((ActivityTypePO) po).getActivityNodes();
                        createStoreWriteOffPlugin(toTenantId, activityNodes);
                    }
                });
                dao.coverAll(toTenantId, pos);
            }
        }
        activityTypeManager.publishSyncActivityTypeFieldTask(String.valueOf(toTenantId));
        budgetTypeManager.publishSyncBudgetTypeFieldTask(String.valueOf(toTenantId));
    }

    private void createStoreWriteOffPlugin(String toTenantId, List<ActivityNodeEntity> activityNodes) {
        if (activityNodes == null) {
            return;
        }

        for (ActivityNodeEntity activityNode : activityNodes) {
            try {
                if (NodeType.STORE_WRITE_OFF.value().equals(activityNode.getType())) {
                    String apiName = activityNode.getActivityStoreWriteOffConfig().getStoreWriteOffSourceConfig().getApiName();
                    //查询对象是否已绑定插件, 否，则add
                    if (!pluginService.findPluginUnit(toTenantId, apiName, "tpm_store_write_off")) {
                        pluginService.addPluginUnit(Integer.valueOf(toTenantId), -10000, apiName, "tpm_store_write_off");
                    }
                }
            } catch (Exception exception) {
                log.info("createStoreWriteOffPlugin fail, exception :", exception);
            }
        }
    }

    @Override
    public void cleanTPMMongo(List<String> tenantIds, boolean isForceDelete) {
        Map<String, UniqueIdBaseDAO> daoMap = SpringUtil.getContext().getBeansOfType(UniqueIdBaseDAO.class);
        if (CollectionUtils.isNotEmpty(tenantIds)) {
            for (String tenantId : tenantIds) {
                if (MapUtils.isNotEmpty(daoMap)) {
                    for (UniqueIdBaseDAO dao : daoMap.values()) {
                        dao.deleteAll(tenantId, isForceDelete);
                    }
                }
            }
        }
    }

    @Override
    public void cleanTPMMongo2(String tenantId, String db, int pageSize, int maxSize) {
        Map<String, UniqueIdBaseDAO> daoMap = SpringUtil.getContext().getBeansOfType(UniqueIdBaseDAO.class);
        log.info("daoMap:{}", daoMap);
        UniqueIdBaseDAO dao = daoMap.get(db);
        if (dao != null) {
            dao.deleteALl(tenantId, maxSize, pageSize);
        }
    }

    @Override
    public Map<String, String> updateStoreRange(List<String> tenantIds, boolean forceUpdate) {
        Map<String, String> result = new HashMap<>();
        tenantIds.forEach(tenantId -> {
            Map<String, String> inner = new HashMap<>();
            try {
                inner.putAll(updateStoreRangeByApiName(tenantId, ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ, forceUpdate));
            } catch (Exception e) {
                inner.put(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ, e.getMessage());
                log.info("updateStoreRange TPM_ACTIVITY_UNIFIED_CASE_OBJ ", e);
            }
            try {
                inner.putAll(updateStoreRangeByApiName(tenantId, ApiNames.TPM_ACTIVITY_OBJ, forceUpdate));
            } catch (Exception e) {
                inner.put(ApiNames.TPM_ACTIVITY_OBJ, e.getMessage());
                log.info("updateStoreRange TPM_ACTIVITY_OBJ ", e);
            }
            result.put(tenantId, JSON.toJSONString(inner));
        });
        return result;
    }

    @Override
    public Map<String, String> addActivityStoreIfActivityHasDealer(List<String> tenantIds) {
        Map<String, String> result = new HashMap<>();
        tenantIds.forEach(tenantId -> result.put(tenantId, addActivityStoreInDealerActivity(tenantId)));
        return result;
    }

    @Override
    public String changeObjectFieldSupportRepeat(String tenantId, String describeApiName, String fieldName) {

        String supportConfig = ConfigFactory.getConfig("gray-rel-fmcg").get("support_repeat_field_config", "{\"TPMBudgetBusinessSubjectObj\":[\"name\"]}");
        JSONObject config = JSON.parseObject(supportConfig);
        if (config.get(describeApiName) == null) {
            throw new ValidateException(I18N.text(I18NKeys.SCRIPT_SERVICE_IMPL_0) + describeApiName);
        } else if (!config.getJSONArray(describeApiName).contains(fieldName)) {
            throw new ValidateException(I18N.text(I18NKeys.SCRIPT_SERVICE_IMPL_1) + fieldName);
        }
        IObjectDescribe describe = serviceFacade.findObject(tenantId, describeApiName);
        if (describe == null) {
            throw new ValidateException(I18N.text(I18NKeys.SCRIPT_SERVICE_IMPL_2));
        }
        IFieldDescribe fieldDescribe = describe.getFieldDescribe(fieldName);
        if (fieldDescribe == null) {
            throw new ValidateException(I18N.text(I18NKeys.SCRIPT_SERVICE_IMPL_3));
        }
        if (!fieldDescribe.isUnique()) {
            return "success";
        }

        String countSql = "select count(1) from mt_unique where tenant_id = '%s' and describe_api_name = '%s' and field_name = '%s'";
        String deleteSql = "delete from mt_unique where tenant_id = '%s' and describe_api_name = '%s' and field_name = '%s'";
        countSql = String.format(countSql, SqlEscaper.escape(tenantId), SqlEscaper.escape(describeApiName), SqlEscaper.escape(fieldName));
        deleteSql = String.format(deleteSql, SqlEscaper.escape(tenantId), SqlEscaper.escape(describeApiName), SqlEscaper.escape(fieldName));
        try {
            List<Map> countList = objectDataService.findBySql(tenantId, countSql);
            if (CollectionUtils.isNotEmpty(countList)) {
                if (countList.get(0) != null && new BigDecimal(countList.get(0).getOrDefault("count", 0).toString()).intValue() > 100000) {
                    throw new ValidateException(I18N.text(I18NKeys.SCRIPT_SERVICE_IMPL_4));
                }
            }
            fieldDescribe.setUnique(false);
            IObjectDescribe newDescribe = serviceFacade.updateFieldDescribe(describe, Lists.newArrayList(fieldDescribe));
            if (newDescribe.getFieldDescribe(fieldName).isUnique()) {
                throw new ValidateException(I18N.text(I18NKeys.SCRIPT_SERVICE_IMPL_5));
            }
            log.info("delete count:{}", objectDataService.deleteBySql(tenantId, deleteSql));
        } catch (MetadataServiceException e) {
            log.info("delete err.", e);
            throw new ValidateException("delete unique data err.");
        }
        return "success";
    }

    @Override
    public String updateActivityCostObjActivityTypeField(List<String> tenantIds) {
        for (String tenantId : tenantIds) {
            TraceContext.get().setTraceId("updateActivityCostObjActivityTypeField:" + UUID.randomUUID());
            log.info("start updateActivityCostObjActivityTypeField tenantID：{}", tenantId);
            User user = User.systemUser(tenantId);

            SearchTemplateQuery activityQuery = new SearchTemplateQuery();
            activityQuery.setLimit(-1);
            activityQuery.setOffset(0);

            List<String> queryFields = Lists.newArrayList(CommonFields.OBJECT_DESCRIBE_API_NAME, TPMActivityFields.ACTIVITY_TYPE, CommonFields.TENANT_ID);
            CommonUtils.executeInAllDataWithFields(serviceFacade, user, ApiNames.TPM_ACTIVITY_OBJ, activityQuery, queryFields, dataList -> {
                log.info("execute data size :{}", dataList.size());
                setDefaultForCost(tenantId, user, dataList);
            });
        }
        return "success";
    }

    @Override
    public String wxTransfer(String tenantId, JSONObject transferData) {
        log.info("tenantId:{},transferData:{}", tenantId, transferData);

        UserInfo userInfo = UserInfo.builder().tenantId(tenantId).userId("-10000").build();
        BatchWXTenantTransfer.Arg batchWXTenantTransferArg = new BatchWXTenantTransfer.Arg();
        String batchId = transferData.getString("batchTransferId");
        batchWXTenantTransferArg.setBatchTransferId(batchId);
        batchWXTenantTransferArg.setBatchName(batchId);
        batchWXTenantTransferArg.setBatchRemarks(batchId);
        JSONObject accountInfo = transferData.getJSONObject("receiveAccountInfo");
        batchWXTenantTransferArg.setReceiverAccounts(Lists.newArrayList());
        BigDecimal amount = accountInfo.getBigDecimal("amount");
        BigDecimal twoHundred = new BigDecimal("200");
        String businessId = accountInfo.getString("businessId");
        int count = 0;
        while (amount.compareTo(BigDecimal.ZERO) > 0) {
            WXPersonalAccount wxPersonalAccount = new WXPersonalAccount();
            wxPersonalAccount.setRemarks(accountInfo.getString("remarks"));
            wxPersonalAccount.setOpenId(accountInfo.getString("openId"));
            wxPersonalAccount.setBusinessId(businessId + (count++));
            wxPersonalAccount.setAppId(accountInfo.getString("appId"));
            wxPersonalAccount.setUserName(accountInfo.getString("userName"));
            if (amount.compareTo(twoHundred) > 0) {
                wxPersonalAccount.setAmount(twoHundred);
                amount = amount.subtract(twoHundred);
            } else {
                wxPersonalAccount.setAmount(amount);
                amount = BigDecimal.ZERO;
            }
            batchWXTenantTransferArg.getReceiverAccounts().add(wxPersonalAccount);
        }
        JSONObject payAccount = transferData.getJSONObject("payAccount");
        WXTenantAccount wxTenantAccount = new WXTenantAccount();
        batchWXTenantTransferArg.setPayeeWXAccount(wxTenantAccount);
        wxTenantAccount.setAccount(payAccount.getString("account"));
        wxTenantAccount.setTenantAccount(payAccount.getString("tenantAccount"));

        log.info("transfer arg:{}", JSONObject.toJSONString(batchWXTenantTransferArg));
        BatchWXTenantTransfer.Result queryResult = payService.batchWXTenantTransfer(userInfo, batchWXTenantTransferArg);
        log.info("transfer result:{}", JSONObject.toJSONString(queryResult));
        return "success";
    }

    @Override
    public List<TransferDetail> queryTransferDetails(String tenantId, String batchId) {

        UserInfo userInfo = UserInfo.builder().tenantId(tenantId).userId("-10000").build();
        QueryWXTenantTransferDetail.Arg arg = new QueryWXTenantTransferDetail.Arg();
        arg.setBatchTransferId(batchId);
        QueryWXTenantTransferDetail.Result queryResult = payService.queryWXTenantTransferDetails(userInfo, arg);
        log.info("transfer result:{}", JSONObject.toJSONString(queryResult));
        return queryResult.getTransferDetails();
    }

    @Override
    public List<Map> queryByTenant(String tenantId, String sql) {
        log.info("sql :{}", sql);
        sql = SecretUtil.symmetricalDecode(sql);
        log.info("sql :{}", sql);
        return specialTableMapper.setTenantId(tenantId).findBySql(sql);
    }

    @Override
    public void fillFreshStandard(String tenantId, Long createTime) {
        List<ActivityTypePO> activityTypes = activityTypeDAO.queryByStartWithTypeTemplateId(tenantId, "reward.");
        List<String> bigDateTypes = new ArrayList<>();
        List<String> othersTypes = new ArrayList<>();
        activityTypes.forEach(type -> {
            if (ScanCodeActionConstants.BIG_DATE_ACTIVITY_TYPE_TEMPLATE_ID.equals(type.getTemplateId())) {
                bigDateTypes.add(type.getId().toString());
            } else {
                othersTypes.add(type.getId().toString());
            }
        });
        if (CollectionUtils.isNotEmpty(bigDateTypes)) {
            SearchTemplateQuery bigDateQuery = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(SearchQueryUtil.filter(TPMActivityFields.ACTIVITY_TYPE, Operator.IN, bigDateTypes), SearchQueryUtil.filter(TPMActivityFields.PRODUCT_RANGE_FRESH_STANDARD, Operator.IS, Lists.newArrayList())));
            CommonUtils.executeInAllDataWithFields(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_OBJ, bigDateQuery, Lists.newArrayList(CommonFields.ID, CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.OWNER, CommonFields.TENANT_ID, TPMActivityFields.PRODUCT_RANGE_FRESH_STANDARD, "product_range_fresh_standard__c"), dataList -> {
                dataList.forEach(data -> {
                    if (Strings.isNullOrEmpty(data.get("product_range_fresh_standard__c", String.class))) {
                        data.set(TPMActivityFields.PRODUCT_RANGE_FRESH_STANDARD, TPMActivityFields.ProductRangeFreshStandard.BY_REMAINING_DAYS);
                    } else {
                        data.set(TPMActivityFields.PRODUCT_RANGE_FRESH_STANDARD, data.get("product_range_fresh_standard__c", String.class));
                    }
                });
                serviceFacade.batchUpdateByFields(User.systemUser(tenantId), dataList, Lists.newArrayList(TPMActivityFields.PRODUCT_RANGE_FRESH_STANDARD));
            });
        }
        if (CollectionUtils.isNotEmpty(othersTypes)) {
            SearchTemplateQuery otherQuery = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(SearchQueryUtil.filter(TPMActivityFields.ACTIVITY_TYPE, Operator.IN, othersTypes)));
            if (createTime != null) {
                otherQuery.getFilters().add(SearchQueryUtil.filter(CommonFields.CREATE_TIME, Operator.LTE, Lists.newArrayList(String.valueOf(createTime))));
            }

            CommonUtils.executeInAllDataWithFields(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_OBJ, otherQuery, Lists.newArrayList(CommonFields.ID, CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.OWNER, CommonFields.TENANT_ID, TPMActivityFields.PRODUCT_RANGE_FRESH_STANDARD), dataList -> {
                dataList.forEach(data -> data.set(TPMActivityFields.PRODUCT_RANGE_FRESH_STANDARD, TPMActivityFields.ProductRangeFreshStandard.NO_LIMIT));
                serviceFacade.batchUpdateByFields(User.systemUser(tenantId), dataList, Lists.newArrayList(TPMActivityFields.PRODUCT_RANGE_FRESH_STANDARD));
            });
        }
        SearchTemplateQuery productMatchTypeQuery = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(SearchQueryUtil.filter(TPMActivityProductRangeFields.MATCH_METHOD, Operator.IS, Lists.newArrayList()), SearchQueryUtil.filter(TPMActivityProductRangeFields.TO_BE_EXPIRED_DAYS, Operator.ISN, Lists.newArrayList())));
        CommonUtils.executeInAllDataWithFields(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PRODUCT_RANGE_OBJ, productMatchTypeQuery, Lists.newArrayList(CommonFields.ID, CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.OWNER, CommonFields.TENANT_ID, TPMActivityProductRangeFields.MATCH_METHOD), dataList -> {
            dataList.forEach(data -> data.set(TPMActivityProductRangeFields.MATCH_METHOD, TPMActivityProductRangeFields.MATCH_METHOD__BIG_DATE));
            serviceFacade.batchUpdateByFields(User.systemUser(tenantId), dataList, Lists.newArrayList(TPMActivityProductRangeFields.MATCH_METHOD));
        });

    }

    @Override
    public Map<String, Boolean> productRangeTest(String tenantId, List<String> activityIds, String productId, String snId) {
        List<IObjectData> activities = serviceFacade.findObjectDataByIds(tenantId, activityIds, ApiNames.TPM_ACTIVITY_OBJ);
        IObjectData sn = serviceFacade.findObjectData(User.systemUser(tenantId), snId, ApiNames.FMCG_SERIAL_NUMBER_OBJ);
        return rangeFieldBusiness.judgeProductInActivitiesProductRange(tenantId, productId, activities, sn);
    }

    @Override
    public void changeActivityRewardPersonId(String tenantId) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(SearchQueryUtil.filter(TPMActivityRewardDetailFields.REWARD_PART, Operator.EQ, Lists.newArrayList("消费者")),
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.REWARD_TYPE, Operator.EQ, Lists.newArrayList(TPMActivityRewardDetailFields.RewardType.RED_PACKET))));
        CommonUtils.executeInAllDataWithFields(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_REWARD_DETAIL_OBJ, query, Lists.newArrayList(TPMActivityRewardDetailFields.REWARD_PERSON_ID, CommonFields.ID, CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.TENANT_ID, TPMActivityRewardDetailFields.RELATED_OBJECT_API_NAME, TPMActivityRewardDetailFields.RELATED_OBJECT_DATA_ID), dataList -> {
            List<String> redDetailIds = dataList.stream().map(data -> data.get(TPMActivityRewardDetailFields.RELATED_OBJECT_DATA_ID, String.class)).collect(Collectors.toList());
            Map<String, IObjectData> redDetailMap = serviceFacade.findObjectDataByIds(tenantId, redDetailIds, ApiNames.RED_PACKET_RECORD_DETAIL_OBJ).stream().collect(Collectors.toMap(DBRecord::getId, v -> v));
            List<String> redIds = redDetailMap.values().stream().map(v -> v.get(RedPacketRecordDetailObjFields.RED_PACKET_RECORD_ID, String.class)).collect(Collectors.toList());
            Map<String, IObjectData> redPacketMap = serviceFacade.findObjectDataByIds(tenantId, redIds, ApiNames.RED_PACKET_RECORD_OBJ).stream().collect(Collectors.toMap(DBRecord::getId, v -> v));
            List<IObjectData> updateList = new ArrayList<>();
            dataList.forEach(data -> {
                String rewardPerson = data.get(TPMActivityRewardDetailFields.REWARD_PERSON_ID, String.class);
                if (Strings.isNullOrEmpty(rewardPerson) || (!Strings.isNullOrEmpty(rewardPerson) && (rewardPerson.contains(".") || rewardPerson.contains("消费者")))) {
                    IObjectData redDetail = redDetailMap.get(data.get(TPMActivityRewardDetailFields.RELATED_OBJECT_DATA_ID, String.class));
                    if (redDetail != null) {
                        IObjectData redPacket = redPacketMap.get(redDetail.get(RedPacketRecordDetailObjFields.RED_PACKET_RECORD_ID, String.class));
                        String unionId = redPacket.get(RedPacketRecordObjFields.TRANSFEREE_WX_UNION_ID, String.class);
                        if (!Strings.isNullOrEmpty(unionId)) {
                            data.set(TPMActivityRewardDetailFields.REWARD_PERSON_ID, unionId);
                            updateList.add(data);
                        }
                    }
                }
            });
            serviceFacade.batchUpdateByFields(User.systemUser(tenantId), updateList, Lists.newArrayList(TPMActivityRewardDetailFields.REWARD_PERSON_ID));
        });
    }

    @Override
    public List batchTriggerSelfDefineReward(String tenantId, List<String> snStatusIds) {
        log.info("start batch trigger self define reward, tenantId is {}, snStatusIds is {}", tenantId, snStatusIds);
        List<SelfDefineReward.Result> result = new ArrayList();
        snStatusIds.forEach(id -> {
            SelfDefineReward.Arg arg = new SelfDefineReward.Arg();
            arg.setBusinessId("compensation:" + id);
            arg.setTenantId(tenantId);
            arg.setSerialNumberStatusId(id);
            result.add(selfDefineRewardHandler.handle(arg));
        });

        return result;
    }

    @Override
    public List<String> filterActivityByAccount(String tenantId, String userId, String storeId, List<String> activityTypes, List<String> departmentIds) {

        IObjectData storeData = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), storeId, ApiNames.ACCOUNT_OBJ);
        if (storeData == null) {
            throw new ValidateException("account not found.");
        }

        List<String> finalDepartmentIds = new ArrayList<>(departmentIds);
        int tenantIdInt = Integer.parseInt(tenantId);

        departmentIds.stream().distinct().forEach(id -> {
            DepartmentDto departmentDto = tpmOrganizationService.getDepartment(tenantIdInt, Integer.parseInt(id));
            if (departmentDto == null) {
                throw new ValidateException("部门Id:" + id + "对应部门不存在");
            }
            finalDepartmentIds.addAll(departmentDto.getAncestors().stream().map(String::valueOf).collect(Collectors.toList()));
        });

        List<IObjectData> activities = findActivityByStore(tenantId, finalDepartmentIds, storeData, null, activityTypes);

        return activities.stream().map(DBRecord::getId).collect(Collectors.toList());
    }

    @Override
    public String callBackByPhysicalReward(String tenantId, List<String> physicalRewardIds) {
        return physicalRewardService.callBackByPhysicalReward(tenantId, physicalRewardIds);
    }

    private List<IObjectData> findActivityByStore(String tenantId, List<String> departmentIds, IObjectData store, List<String> includeActivityIds, List<String> activityTypeIds) {
        if (CollectionUtils.isEmpty(activityTypeIds)) {
            return new ArrayList<>();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setFilters(Lists.newArrayList());

        int index = 1;
        StringBuilder pattern = new StringBuilder();

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(TPMActivityFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));
        query.getFilters().add(lifeStatusFilter);
        pattern.append("  ").append(index++);

        Filter activityStatusFilter = new Filter();
        activityStatusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        activityStatusFilter.setOperator(Operator.IN);
        activityStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS, TPMActivityFields.ACTIVITY_STATUS__SCHEDULE));
        query.getFilters().add(activityStatusFilter);
        pattern.append(" and ").append(index++).append(" ");

        if (!CollectionUtils.isEmpty(includeActivityIds)) {
            Filter idFilter = new Filter();
            idFilter.setFieldName(CommonFields.ID);
            idFilter.setOperator(Operator.IN);
            idFilter.setFieldValues(includeActivityIds);
            query.getFilters().add(idFilter);
            pattern.append(" and ").append(index++).append(" ");
        }

        if (!CollectionUtils.isEmpty(departmentIds)) {
            Filter departmentFilter = new Filter();
            departmentFilter.setFieldName(TPMActivityFields.MULTI_DEPARTMENT_RANGE);
            departmentFilter.setOperator(Operator.HASANYOF);
            departmentFilter.setFieldValues(departmentIds);
            query.getFilters().add(departmentFilter);

            pattern.append(" and ").append(index++).append(" ");
        }

        Filter storeFilter = new Filter();
        storeFilter.setFieldName(TPMActivityFields.DEALER_ID);
        storeFilter.setOperator(Operator.IS);
        storeFilter.setFieldValues(Lists.newArrayList());
        query.getFilters().add(storeFilter);

        String dealerId = storeBusiness.findDealerId(tenantId, store);

        if (!Strings.isNullOrEmpty(dealerId)) {
            Filter dealerIdFilter = new Filter();
            dealerIdFilter.setFieldName(TPMActivityFields.DEALER_ID);
            dealerIdFilter.setOperator(Operator.EQ);
            dealerIdFilter.setFieldValues(Lists.newArrayList(dealerId));
            query.getFilters().add(dealerIdFilter);
            pattern.append(" and  ( ").append(index++).append(" or ").append(index++).append(" ) ");
        } else {
            pattern.append(" and ").append(index++);
        }

        if (!CollectionUtils.isEmpty(activityTypeIds) && !activityTypeIds.contains("all")) {
            Filter activityTypeFilter = new Filter();
            activityTypeFilter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
            activityTypeFilter.setOperator(Operator.IN);
            activityTypeFilter.setFieldValues(activityTypeIds);
            query.getFilters().add(activityTypeFilter);
            pattern.append(" and  ").append(index);
        }

        query.setPattern(pattern.toString());
        List<IObjectData> activities = CommonUtils.queryAllDataInFields(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_OBJ, query, Lists.newArrayList(CommonFields.ID, CommonFields.NAME, CommonFields.CREATE_TIME, TPMActivityFields.DEALER_ID, TPMActivityFields.ACTIVITY_TYPE, TPMActivityFields.STORE_RANGE, TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, CommonFields.OBJECT_DESCRIBE_API_NAME, "reward_activity_tenant__c", TPMActivityFields.PRODUCT_RANGE, TPMActivityFields.PRODUCT_RANGE_FRESH_STANDARD, TPMActivityFields.ACTIVATION_START_TIME, TPMActivityFields.ACTIVATION_END_TIME, TPMActivityFields.ACTIVATION_END_TIME, TPMActivityFields.IS_ALLOW_OUTER_CODE_SCAN));

        Map<String, Boolean> existsMap = rangeFieldBusiness.judgeStoreInActivitiesStoreRange(tenantId, store.getId(), dealerId, activities, false, true);

        return activities.stream().filter(v -> existsMap.getOrDefault(v.getId(), false)).collect(Collectors.toList());
    }

    @Override
    public Map<String, String> createI18nReferenceLabel(String tenantId, List<String> filterApiName) {
        Map<String, String> i18nKey = new HashMap<>();
        String format = "%s.field.%s.reference_label";
        for (String apiName : filterApiName) {
            if (apiName.endsWith("__c") || !filterApiName.contains(apiName)) {
                continue;
            }
            if (apiName.equals("TPMBudgetCarryForwardDetailObj")) {
                System.out.println(apiName);
            }

            IObjectDescribe describe = serviceFacade.findObject(tenantId, apiName);

            List<IFieldDescribe> fieldDescribes = describe.getFieldDescribes().stream().filter(v -> v.getType().equals("object_reference")).collect(Collectors.toList());

            for (IFieldDescribe field : fieldDescribes) {
                if (field.getApiName().endsWith("__c")) {
                    continue;
                }
                i18nKey.put(String.format(format, apiName, field.getApiName()), field.get("target_related_list_label", String.class));
            }
        }
        return i18nKey;
    }

    @Override
    public String updateObject(String tenantId, String apiName, String objectId) {
        TriggerAction.Arg arg = TriggerAction.Arg.builder().actionName("Edit").apiName(apiName).objectData(buildObjectData(tenantId, apiName, objectId)).user(User.systemUser(tenantId)).triggerFlow(true).triggerWorkflow(true).build();

        BaseObjectSaveAction.Result result = triggerActionService.triggerAction(arg);
        return "success";
    }

    private IObjectData buildObjectData(String tenantId, String apiName, String objectId) {

        IObjectData objectData = new ObjectData();
        // 对象id
        objectData.setId(objectId);
        objectData.setTenantId(tenantId);
        objectData.setDescribeApiName(apiName);
        objectData.set(TPMActivityAgreementFields.DESCRIPTION, "---");
        return objectData;
    }

    private String addActivityStoreInDealerActivity(String tenantId) {

        List<IObjectData> activities = queryDealerFixActivity(tenantId);
        activities.forEach(activity -> {
            String dealerId = activity.get(TPMActivityFields.DEALER_ID, String.class);
            if (!existsActivityStore(tenantId, dealerId, activity.getId())) {
                IObjectData data = new ObjectData();
                data.setDescribeApiName(ApiNames.TPM_ACTIVITY_STORE_OBJ);
                data.setTenantId(tenantId);
                data.setOwner(Lists.newArrayList("-10000"));
                data.set(TPMActivityStoreFields.STORE_ID, dealerId);
                data.set(TPMActivityStoreFields.ACTIVITY_ID, activity.getId());
                serviceFacade.saveObjectData(User.systemUser(tenantId), data);
            }
        });
        return "success";
    }

    private boolean existsActivityStore(String tenantId, String storeId, String activityId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(1);

        Filter storeIdIdFilter = new Filter();
        storeIdIdFilter.setFieldName(TPMActivityStoreFields.STORE_ID);
        storeIdIdFilter.setOperator(Operator.EQ);
        storeIdIdFilter.setFieldValues(Lists.newArrayList(storeId));

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(TPMActivityStoreFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        query.setFilters(Lists.newArrayList(storeIdIdFilter, activityIdFilter));

        return CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_STORE_OBJ, query, Lists.newArrayList("_id")).isEmpty();


    }

    private List<IObjectData> queryDealerFixActivity(String tenantId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(-1);

        Filter dealerIdFilter = new Filter();
        dealerIdFilter.setFieldName(TPMActivityFields.DEALER_ID);
        dealerIdFilter.setOperator(Operator.ISN);
        dealerIdFilter.setFieldValues(Lists.newArrayList());

        Filter storeRangeFilter = new Filter();
        storeRangeFilter.setFieldName(TPMActivityFields.STORE_RANGE);
        storeRangeFilter.setOperator(Operator.CONTAINS);
        storeRangeFilter.setFieldValues(Lists.newArrayList("FIXED"));

        Filter activityStatusFilter = new Filter();
        activityStatusFilter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
        activityStatusFilter.setOperator(Operator.IN);
        activityStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS, TPMActivityFields.ACTIVITY_STATUS__SCHEDULE, TPMActivityFields.ACTIVITY_STATUS__APPROVAL));

        query.setFilters(Lists.newArrayList(dealerIdFilter, storeRangeFilter, activityStatusFilter));

        return CommonUtils.queryAllDataInFields(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_OBJ, query, Lists.newArrayList(TPMActivityFields.DEALER_ID));
    }

    @Override
    public String updateActivityAndWriteOffDefaultValue(List<String> tenantIds) {
        for (String tenantId : tenantIds) {
            log.info("start updateActivityAndWriteOffDefaultValue tenantID：{}", tenantId);
            User user = User.systemUser(tenantId);
            DescribeResult describeResult = serviceFacade.findDescribeAndLayout(user, ApiNames.TPM_ACTIVITY_OBJ, false, null);
            if (describeResult.getObjectDescribe().getFieldDescribe(TPMActivityFields.CUSTOMER_TYPE) == null) {
                log.info("tenantId:{},没有刷对象字段。", tenantId);
                continue;
            }
            String now = String.valueOf(System.currentTimeMillis());
            SearchTemplateQuery activityQuery = new SearchTemplateQuery();
            activityQuery.setLimit(-1);
            activityQuery.setOffset(0);

            Filter customerFilter = new Filter();
            customerFilter.setFieldName(TPMActivityFields.CUSTOMER_TYPE);
            customerFilter.setOperator(Operator.IS);
            customerFilter.setFieldValues(Lists.newArrayList());


            Filter endTimeFilter = new Filter();
            endTimeFilter.setFieldName(TPMActivityFields.END_DATE);
            endTimeFilter.setOperator(Operator.GTE);
            endTimeFilter.setFieldValues(Lists.newArrayList(now));


            activityQuery.setFilters(Lists.newArrayList(customerFilter, endTimeFilter));
            List<String> queryFields = Lists.newArrayList(TPMActivityFields.CUSTOMER_TYPE, TPMActivityFields.DEALER_CASHING_TYPE, TPMActivityFields.MAX_WRITE_OFF_COUNT, CommonFields.OBJECT_DESCRIBE_API_NAME, TPMActivityFields.ACTIVITY_TYPE, CommonFields.TENANT_ID);
            CommonUtils.executeInAllDataWithFields(serviceFacade, user, ApiNames.TPM_ACTIVITY_OBJ, activityQuery, queryFields, dataList -> {
                log.info("execute data size :{}", dataList.size());
                dataList.forEach(data -> {
                    data.set(TPMActivityFields.CUSTOMER_TYPE, ActivityCustomerTypeEnum.DEALER_STORE.value());
                    data.set(TPMActivityFields.MAX_WRITE_OFF_COUNT, ActivityMaxWriteOffCountEnum.MULTI.value());
                    data.set(TPMActivityFields.IS_AUTO_CLOSE, false);
                });
                serviceFacade.batchUpdateByFields(user, dataList, Lists.newArrayList(TPMActivityFields.CUSTOMER_TYPE, TPMActivityFields.MAX_WRITE_OFF_COUNT));
                setDefaultForCost(tenantId, user, dataList);
            });
        }
        return "success";
    }

    private void setDefaultForCost(String tenantId, User user, List<IObjectData> activities) {
        Map<String, IObjectData> activityMap = activities.stream().collect(Collectors.toMap(DBRecord::getId, v -> v, (a, b) -> a));

        List<String> queryFields = Lists.newArrayList(TPMDealerActivityCostFields.ACTIVITY_ID, TPMDealerActivityCostFields.ACTIVITY_TYPE, CommonFields.OBJECT_DESCRIBE_API_NAME, TPMDealerActivityCostFields.DEALER_CASHING_TYPE, CommonFields.TENANT_ID);
        SearchTemplateQuery costQuery = new SearchTemplateQuery();
        costQuery.setLimit(-1);
        costQuery.setOffset(0);
        Filter activityTypeFilter = new Filter();
        activityTypeFilter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
        activityTypeFilter.setOperator(Operator.IS);
        activityTypeFilter.setFieldValues(Lists.newArrayList());
        costQuery.setFilters(Lists.newArrayList(activityTypeFilter));

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(TPMDealerActivityCostFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.IN);
        activityIdFilter.setFieldValues(activities.stream().map(DBRecord::getId).collect(Collectors.toList()));
        costQuery.getFilters().add(activityIdFilter);

        List<IObjectData> costs = CommonUtils.queryAllDataInFields(serviceFacade, user, ApiNames.TPM_DEALER_ACTIVITY_COST, costQuery, queryFields);
        List<IObjectData> updateDataList = new ArrayList<>();
        costs.forEach(cost -> {
            String activityId = cost.get(TPMDealerActivityCostFields.ACTIVITY_ID, String.class);
            if (Strings.isNullOrEmpty(activityId)) {
                return;
            }
            IObjectData activity = activityMap.get(activityId);

            cost.set(TPMDealerActivityCostFields.ACTIVITY_TYPE, activity.get(TPMActivityFields.ACTIVITY_TYPE, String.class));
            if (!Strings.isNullOrEmpty(activity.get(TPMActivityFields.DEALER_CASHING_TYPE, String.class))) {
                cost.set(TPMDealerActivityCostFields.DEALER_CASHING_TYPE, activity.get(TPMActivityFields.DEALER_CASHING_TYPE, String.class));
            }
            if (!Strings.isNullOrEmpty(cost.get(TPMDealerActivityCostFields.ACTIVITY_TYPE, String.class)) || !Strings.isNullOrEmpty(cost.get(TPMDealerActivityCostFields.DEALER_CASHING_TYPE, String.class))) {
                updateDataList.add(cost);
            }
        });
        log.info("update costList size :{}", updateDataList.size());
        Lists.partition(updateDataList, 200).forEach(parts -> serviceFacade.batchUpdateByFields(user, parts, queryFields));
    }


    private Map<String, String> updateStoreRangeByApiName(String tenantId, String apiName, boolean forceUpdate) {
        Map<String, String> errorMap = new HashMap<>();
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(0);
        query.setOffset(0);
        Filter filter = new Filter();
        filter.setFieldName(TPMActivityFields.STORE_RANGE);
        filter.setOperator(Operator.CONTAINS);
        filter.setFieldValues(Lists.newArrayList("CONDITION"));
        query.setFilters(Lists.newArrayList(filter));
        List<IObjectData> allData = CommonUtils.queryAllDataInFields(serviceFacade, User.systemUser(tenantId), apiName, query, Lists.newArrayList(CommonFields.TENANT_ID, CommonFields.OBJECT_DESCRIBE_API_NAME, TPMActivityFields.STORE_RANGE));
        Lists.partition(allData, 100).forEach(dataList -> {
            List<IObjectData> updateList = new ArrayList<>();
            dataList.forEach(data -> {
                try {
                    String storeRange = data.get(TPMActivityFields.STORE_RANGE, String.class);
                    if (Strings.isNullOrEmpty(storeRange)) {
                        log.info("activity store range err.activity:{}", data);
                        return;
                    }
                    JSONObject rangeObj = JSON.parseObject(storeRange);
                    String type = rangeObj.getString("type");
                    if (type != null && UseRangeEnum.CONDITION.value().equals(type.toUpperCase())) {
                        if (forceUpdate || !rangeObj.containsKey("code")) {
                            if (apiName.equals(ApiNames.TPM_ACTIVITY_OBJ)) {
                                rangeObj.put("code", rangeFieldBusiness.formActivityStoreRangeRuleCode(tenantId, data.get(TPMActivityFields.CUSTOMER_TYPE, String.class, ActivityCustomerTypeEnum.DEALER_STORE.value()), rangeObj.getString("value"), data.get(TPMActivityFields.DEALER_ID, String.class)));
                            } else {
                                rangeObj.put("code", rangeFieldBusiness.formDealerRangeRuleCode(tenantId, rangeObj.getString("value")));
                            }
                            data.set(TPMActivityFields.STORE_RANGE, rangeObj.toJSONString());
                            updateList.add(data);
                        }
                    }
                } catch (Exception e) {
                    errorMap.put(data.getDescribeApiName() + "." + data.getId(), e.getMessage());
                }
            });
            if (CollectionUtils.isNotEmpty(updateList)) {
                serviceFacade.batchUpdateByFields(User.systemUser(tenantId), updateList, Lists.newArrayList(TPMActivityFields.STORE_RANGE));
            }
        });
        log.info("errMap:{}", errorMap);
        return errorMap;
    }


    private void updateUniqueId(String tenantId, Collection<UniqueIdBaseDAO> daoList, int maxRepeat, int loopSize) {
        for (UniqueIdBaseDAO dao : daoList) {
            int count = 0;
            List<MongoPO> pos;
            while (count < maxRepeat && !(pos = dao.getNullUniqueIdPOs(tenantId, loopSize)).isEmpty()) {
                count += pos.size();
                pos.forEach(po -> {
                    if (Strings.isNullOrEmpty(po.getUniqueId())) {
                        po.setUniqueId(po.getId().toString());
                    }
                });
                dao.coverAll(tenantId, pos);
            }
            log.info("update count:{}", count);
        }
    }
}
