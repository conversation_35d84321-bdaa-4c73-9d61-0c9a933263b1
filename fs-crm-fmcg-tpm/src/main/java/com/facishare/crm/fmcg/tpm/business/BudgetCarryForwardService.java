package com.facishare.crm.fmcg.tpm.business;

import com.beust.jcommander.internal.Maps;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetCarryForwardDetailFields;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetCarryForwardService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IFiscalTimeService;
import com.facishare.crm.fmcg.tpm.business.dto.CarryForwardDetailDataDocument;
import com.facishare.crm.fmcg.tpm.business.enums.CarryForwardType;
import com.facishare.crm.fmcg.tpm.controller.TPMBudgetCarryForwardObjLoadDetailDataController;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetDimensionEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/7/26 11:20
 */
@Service
@SuppressWarnings("Duplicates")
public class BudgetCarryForwardService implements IBudgetCarryForwardService {

    protected static final String DELIMITER = "^";
    protected static final Map<Integer, Integer> START_MONTH_OF_QUARTER = Maps.newHashMap();

    static {
        START_MONTH_OF_QUARTER.put(1, Calendar.JANUARY);
        START_MONTH_OF_QUARTER.put(2, Calendar.APRIL);
        START_MONTH_OF_QUARTER.put(3, Calendar.JULY);
        START_MONTH_OF_QUARTER.put(4, Calendar.OCTOBER);
    }

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private IFiscalTimeService fiscalTimeService;

    /**
     * load carry forward detail data
     *
     * @param sourcePeriod source period time stamp
     * @param targetPeriod target period time stamp
     * @return details
     */
    @Override
    public List<CarryForwardDetailDataDocument> loadDetailData(BudgetTypePO type, BudgetTypeNodeEntity node, CarryForwardType carryForwardType, long sourcePeriod, long targetPeriod) {
        if (Objects.isNull(carryForwardType)) {
            return Lists.newArrayList();
        }
        List<IObjectData> sourceData = querySourceBudgetAccount(type, node, sourcePeriod);
        List<IObjectData> targetData = queryTargetBudgetAccount(type, node, targetPeriod);
        return merge(node, carryForwardType, sourceData, targetData);
    }

    @Override
    @SuppressWarnings("all")
    public TPMBudgetCarryForwardObjLoadDetailDataController.ConfirmInfo confirmInfo(BudgetTypePO type, BudgetTypeNodeEntity node, CarryForwardType carryForwardType, long sourcePeriod, long targetPeriod) {
        Map<String, IObjectData> sourceMap = groupByDimensionKey(node, querySourceBudgetAccount(type, node, sourcePeriod));
        Map<String, IObjectData> targetMap = groupByDimensionKey(node, queryTargetBudgetAccount(type, node, targetPeriod));
        int notHaveNextPeriodBudgetAccountSize = 0;
        int haveNextPeriodBudgetAccountSize = 0;
        if (Objects.isNull(carryForwardType)) {
            for (Map.Entry<String, IObjectData> sourceEntry : sourceMap.entrySet()) {
                if (targetMap.containsKey(sourceEntry.getKey())) {
                    haveNextPeriodBudgetAccountSize += 1;
                    continue;
                }
                notHaveNextPeriodBudgetAccountSize += 1;
            }

        }
        return TPMBudgetCarryForwardObjLoadDetailDataController.ConfirmInfo.builder().totalBudgetAccountSize(sourceMap.keySet().size()).haveNextPeriodBudgetAccountSize(haveNextPeriodBudgetAccountSize).notHaveNextPeriodBudgetAccountSize(notHaveNextPeriodBudgetAccountSize).build();
    }

    private CarryForwardDetailDataDocument buildDocumentWithNextPeriod(Map.Entry<String, IObjectData> sourceEntry, Map<String, IObjectData> targetMap) {
        CarryForwardDetailDataDocument datum = new CarryForwardDetailDataDocument();

        datum.setSourceBudgetAccountId(sourceEntry.getValue().getId());
        datum.setSourceBudgetAccountName(sourceEntry.getValue().getName());
        datum.setTargetBudgetAccountId(targetMap.get(sourceEntry.getKey()).getId());
        datum.setTargetBudgetAccountName(targetMap.get(sourceEntry.getKey()).getName());
        datum.setCarryForwardStatus(TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS__SCHEDULE);

        BigDecimal amount = new BigDecimal(sourceEntry.getValue().get(TPMBudgetAccountFields.AVAILABLE_AMOUNT, String.class));
        BigDecimal afterCarryForwardAmount = new BigDecimal(targetMap.get(sourceEntry.getKey()).get(TPMBudgetAccountFields.AVAILABLE_AMOUNT, String.class)).add(amount);

        datum.setAmount(amount);
        datum.setAfterCarryForwardAmount(afterCarryForwardAmount);
        return datum;
    }

    private CarryForwardDetailDataDocument buildDocumentWithoutNextPeriod(Map.Entry<String, IObjectData> sourceEntry, Map<String, IObjectData> targetMap) {
        CarryForwardDetailDataDocument datum = new CarryForwardDetailDataDocument();

        datum.setSourceBudgetAccountId(sourceEntry.getValue().getId());
        datum.setSourceBudgetAccountName(sourceEntry.getValue().getName());
        datum.setCarryForwardStatus(TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS__SCHEDULE);

        BigDecimal amount = new BigDecimal(sourceEntry.getValue().get(TPMBudgetAccountFields.AVAILABLE_AMOUNT, String.class));
        BigDecimal afterCarryForwardAmount = new BigDecimal("0").add(amount);
        if (targetMap.containsKey(sourceEntry.getKey())) {
            datum.setTargetBudgetAccountId(targetMap.get(sourceEntry.getKey()).getId());
            datum.setTargetBudgetAccountName(targetMap.get(sourceEntry.getKey()).getName());
            afterCarryForwardAmount = new BigDecimal(targetMap.get(sourceEntry.getKey()).get(TPMBudgetAccountFields.AVAILABLE_AMOUNT, String.class)).add(amount);
        }
        datum.setAmount(amount);
        datum.setAfterCarryForwardAmount(afterCarryForwardAmount);
        return datum;
    }

    /**
     * merge source data list and target data list to carry forward detail list
     *
     * @param node       budget template
     * @param sourceData source data list
     * @param targetData target data list
     * @return carry forward detail
     */
    private List<CarryForwardDetailDataDocument> merge(BudgetTypeNodeEntity node, CarryForwardType carryForwardType, List<IObjectData> sourceData, List<IObjectData> targetData) {
        Map<String, IObjectData> sourceMap = groupByDimensionKey(node, sourceData);
        Map<String, IObjectData> targetMap = groupByDimensionKey(node, targetData);
        List<CarryForwardDetailDataDocument> data = Lists.newArrayList();
        for (Map.Entry<String, IObjectData> sourceEntry : sourceMap.entrySet()) {
            String dimensionKey = sourceEntry.getKey();
            switch (carryForwardType) {
                case CARRY_FORWARD_WITH_NEXT_PERIOD_BUDGET:
                    if (targetMap.containsKey(dimensionKey)) {
                        data.add(buildDocumentWithNextPeriod(sourceEntry, targetMap));
                    }
                    break;
                case CARRY_FORWARD_WITHOUT_NEXT_PERIOD_BUDGET_OR_CREATE:
                    data.add(buildDocumentWithoutNextPeriod(sourceEntry, targetMap));
                    break;
                default:
                    throw new ValidateException("carry forward type is not supported");
            }
        }
        return data;
    }

    /**
     * group budget account data by dimension key
     *
     * @param data budget account data list
     * @return budget account map
     */
    private Map<String, IObjectData> groupByDimensionKey(BudgetTypeNodeEntity node, List<IObjectData> data) {
        Set<String> dimensionFields = node.getDimensions().stream().map(BudgetDimensionEntity::getApiName).collect(Collectors.toSet());
        Map<String, IObjectData> dataMap = Maps.newHashMap();
        for (IObjectData sourceDatum : data) {
            dataMap.put(initDimensionKey(dimensionFields, sourceDatum), sourceDatum);
        }
        return dataMap;
    }

    /**
     * build up dimension identity key
     *
     * @param dimensionFields dimension field api name list
     * @param data            object data
     * @return dimension identity key
     */
    @Override
    public String initDimensionKey(Set<String> dimensionFields, IObjectData data) {
        String department = CommonUtils.cast(data.get(TPMBudgetAccountFields.BUDGET_DEPARTMENT), String.class).get(0);
        String dimensionKey = dimensionFields.stream().map(dimensionField -> String.valueOf(data.get(dimensionField))).collect(Collectors.joining(DELIMITER));
        return department + DELIMITER + dimensionKey;
    }

    /**
     * query source budget account data list
     *
     * @param time time stamp
     * @return budget account data list
     */
    private List<IObjectData> querySourceBudgetAccount(BudgetTypePO type, BudgetTypeNodeEntity node, long time) {
        String periodFieldApiName = String.format("budget_period_%s", node.getTimeDimension());
        long period = fiscalTimeService.correctPeriodTime(type.getTenantId(), node.getTimeDimension(), time);

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        Filter typeIdFilter = new Filter();
        typeIdFilter.setFieldName(TPMBudgetAccountFields.BUDGET_TYPE_ID);
        typeIdFilter.setOperator(Operator.EQ);
        typeIdFilter.setFieldValues(Lists.newArrayList(type.getId().toString()));

        Filter nodeIdFilter = new Filter();
        nodeIdFilter.setFieldName(TPMBudgetAccountFields.BUDGET_NODE_ID);
        nodeIdFilter.setOperator(Operator.EQ);
        nodeIdFilter.setFieldValues(Lists.newArrayList(node.getNodeId()));

        Filter periodFilter = new Filter();
        periodFilter.setFieldName(periodFieldApiName);
        periodFilter.setOperator(Operator.EQL);
        periodFilter.setFieldValues(Lists.newArrayList(String.valueOf(period)));

        Filter amountFilter = new Filter();
        amountFilter.setFieldName(TPMBudgetAccountFields.AVAILABLE_AMOUNT);
        amountFilter.setOperator(Operator.GT);
        amountFilter.setFieldValues(Lists.newArrayList("0"));

        query.setFilters(Lists.newArrayList(lifeStatusFilter, typeIdFilter, nodeIdFilter, periodFilter, amountFilter));

        List<String> fields = node.getDimensions().stream().map(BudgetDimensionEntity::getApiName).collect(Collectors.toList());
        fields.add(CommonFields.ID);
        fields.add(CommonFields.NAME);
        fields.add(TPMBudgetAccountFields.AVAILABLE_AMOUNT);
        fields.add(TPMBudgetAccountFields.BUDGET_DEPARTMENT);

        return QueryDataUtil.find(
                serviceFacade,
                type.getTenantId(),
                ApiNames.TPM_BUDGET_ACCOUNT,
                query,
                fields);
    }

    /**
     * query target budget account data list
     *
     * @param time time stamp
     * @return budget account data list
     */
    private List<IObjectData> queryTargetBudgetAccount(BudgetTypePO type, BudgetTypeNodeEntity node, long time) {
        String periodFieldApiName = String.format("budget_period_%s", node.getTimeDimension());
        long period = fiscalTimeService.correctPeriodTime(type.getTenantId(), node.getTimeDimension(), time);

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        Filter budgetStatusFilter = new Filter();
        budgetStatusFilter.setFieldName(TPMBudgetAccountFields.BUDGET_STATUS);
        budgetStatusFilter.setOperator(Operator.EQ);
        budgetStatusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountFields.BUDGET_STATUS__ENABLE));

        Filter typeIdFilter = new Filter();
        typeIdFilter.setFieldName(TPMBudgetAccountFields.BUDGET_TYPE_ID);
        typeIdFilter.setOperator(Operator.EQ);
        typeIdFilter.setFieldValues(Lists.newArrayList(type.getId().toString()));

        Filter nodeIdFilter = new Filter();
        nodeIdFilter.setFieldName(TPMBudgetAccountFields.BUDGET_NODE_ID);
        nodeIdFilter.setOperator(Operator.EQ);
        nodeIdFilter.setFieldValues(Lists.newArrayList(node.getNodeId()));

        Filter periodFilter = new Filter();
        periodFilter.setFieldName(periodFieldApiName);
        periodFilter.setOperator(Operator.EQL);
        periodFilter.setFieldValues(Lists.newArrayList(String.valueOf(period)));

        query.setFilters(Lists.newArrayList(lifeStatusFilter, budgetStatusFilter, typeIdFilter, nodeIdFilter, periodFilter));

        List<String> fields = node.getDimensions().stream().map(BudgetDimensionEntity::getApiName).collect(Collectors.toList());
        fields.add(CommonFields.ID);
        fields.add(CommonFields.NAME);
        fields.add(TPMBudgetAccountFields.AVAILABLE_AMOUNT);
        fields.add(TPMBudgetAccountFields.BUDGET_DEPARTMENT);

        return QueryDataUtil.find(
                serviceFacade,
                type.getTenantId(),
                ApiNames.TPM_BUDGET_ACCOUNT,
                query,
                fields);
    }
}
