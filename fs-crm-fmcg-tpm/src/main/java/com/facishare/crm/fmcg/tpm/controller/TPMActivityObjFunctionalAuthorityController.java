package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.tpm.api.proof.FunctionalAuthority;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.privilege.dto.PrivilegeResult;
import com.google.common.collect.Lists;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/9/7 下午2:58
 */
public class TPMActivityObjFunctionalAuthorityController extends PreDefineController<FunctionalAuthority.Arg, FunctionalAuthority.Result> {

    private static Map<String, Map<String, String>> SPECIAL_FUNCTION_CODE_MAP = new HashMap<>();

    static {
        Map<String, String> proofAuditMap = new HashMap<>();
        proofAuditMap.put("Audit", "Add");
        proofAuditMap.put("RandomAudit", ObjectAction.TPM_PROOF_RANDOM_AUDIT.getActionCode());
        SPECIAL_FUNCTION_CODE_MAP.put(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, proofAuditMap);
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected FunctionalAuthority.Result doService(FunctionalAuthority.Arg arg) {
        FunctionalAuthority.Result result = new FunctionalAuthority.Result();
        log.info("arg:{}", arg);
        Map<String, List<String>> queryMap = new HashMap<>();
        arg.getFunctionCodeQueryMap().forEach((k, v) -> queryMap.put(k, v.stream().map(code -> getRealCode(k, code)).collect(Collectors.toList())));
        Map<String, Map<String, Boolean>> resultMap = queryFunctionCodeMap(queryMap);

        arg.getFunctionCodeQueryMap().forEach((apiName, codeList) -> {
            Map<String, Boolean> innerMap = resultMap.get(apiName);
            Map<String, Boolean> newMap = new HashMap<>();
            codeList.forEach(code -> {
                String realCode = getRealCode(apiName, code);
                newMap.put(code, innerMap.getOrDefault(realCode, false));
            });
            resultMap.put(apiName, newMap);
        });
        result.setFunctionCodeResultMap(resultMap);
        log.info("result:{}", result);
        return result;
    }

    private String getRealCode(String apiName, String code) {
        if (SPECIAL_FUNCTION_CODE_MAP.containsKey(apiName)) {
            return SPECIAL_FUNCTION_CODE_MAP.get(apiName).getOrDefault(code, code);
        }
        return code;
    }

    private Map<String, Map<String, Boolean>> queryFunctionCodeMap(Map<String, List<String>> functionCodeListMap) {
        PrivilegeResult<Map<String, Map<String, Boolean>>> codeRst = serviceFacade.batchObjectFuncPrivilegeCheck(controllerContext.getRequestContext(), functionCodeListMap);
        return codeRst.getCode() == 0 ? codeRst.getResult() : new HashMap<>();
    }
}
