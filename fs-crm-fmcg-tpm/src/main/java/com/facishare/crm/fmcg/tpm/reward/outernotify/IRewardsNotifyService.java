package com.facishare.crm.fmcg.tpm.reward.outernotify;

import com.facishare.crm.fmcg.tpm.reward.dto.OuterRewardExecuteResult;
import com.facishare.crm.fmcg.tpm.reward.dto.RewardNotify;
import com.facishare.crm.fmcg.tpm.reward.dto.RewardNotifyRetry;

public interface IRewardsNotifyService {

    RewardNotify.Result rewardsNotify(String environment, String auth, RewardNotify.Arg arg);

    RewardNotifyRetry.Result retry(RewardNotifyRetry.Arg arg);

    OuterRewardExecuteResult reward(String environment, String outerConsumerRewardRuleId, String snId);
}
