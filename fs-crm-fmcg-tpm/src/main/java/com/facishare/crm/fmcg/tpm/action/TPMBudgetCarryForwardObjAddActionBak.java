package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.tpm.business.BudgetOperatorFactory;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetCompareService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetOperator;
import com.facishare.crm.fmcg.tpm.business.abstraction.IFiscalTimeService;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetDimensionEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.service.abstraction.ITransactionProxy;
import com.facishare.crm.fmcg.tpm.utils.TraceUtil;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IBudgetTypeManager;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import groovy.lang.Tuple2;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/7/25 18:32
 */
@SuppressWarnings("Duplicates")
@Slf4j
public class TPMBudgetCarryForwardObjAddActionBak extends StandardAddAction {

    private final RedissonClient redissonCmd = SpringUtil.getContext().getBean("redissonCmd", RedissonClient.class);
    private final IBudgetCompareService budgetCompareService = SpringUtil.getContext().getBean(IBudgetCompareService.class);
    private final IFiscalTimeService fiscalTimeService = SpringUtil.getContext().getBean(IFiscalTimeService.class);
    private final IBudgetTypeManager budgetTypeManager = SpringUtil.getContext().getBean(IBudgetTypeManager.class);
    private final ITransactionProxy transProxy = SpringUtil.getContext().getBean(ITransactionProxy.class);

    // all source budget operators
    private final Map<String, CarryForwardOperators> operators = Maps.newHashMap();

    // budget template data find by [budget_template_id] of master data
    private BudgetTypePO type;
    private BudgetTypeNodeEntity node;

    // carry forward source period
    private Long sourcePeriod;

    // carry forward target period
    private Long targetPeriod;

    // carry forward lock
    private RLock lock;
    private String approvalTraceId;
    private String businessTraceId;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        this.stopWatch.lap("framework.before");

        this.prepareBudgetTrace();
        this.stopWatch.lap("prepareBudgetTrace");

        this.loadRelatedData();
        this.stopWatch.lap("loadRelatedData");

        this.validateMasterData();
        this.stopWatch.lap("validateMasterData");

        this.validateDetailData();
        this.stopWatch.lap("validateDetailData");

        this.tryLockCarryForward();
        this.stopWatch.lap("tryLockCarryForward");

        this.tryLockAllDetails();
        this.stopWatch.lap("tryLockAllDetails");
    }

    private void prepareBudgetTrace() {
        this.approvalTraceId = TraceUtil.initApprovalTraceId();
        this.businessTraceId = TraceUtil.initBusinessTraceId();

        this.customCallbackData.put(TraceUtil.FMCG_TPM_BUDGET_APPROVAL_CALLBACK_TRACE_ID_KEY, this.approvalTraceId);
        this.customCallbackData.put(TraceUtil.FMCG_TPM_BUDGET_BUSINESS_CALLBACK_TRACE_ID_KEY, this.businessTraceId);
    }

    /**
     * load related data
     */
    private void loadRelatedData() {
        String typeId = (String) arg.getObjectData().get(TPMBudgetCarryForwardFields.BUDGET_TYPE_ID);
        if (Strings.isNullOrEmpty(typeId)) {
            throw new ValidateException("[budget_type_id] can not be null or empty.");
        }
        this.type = budgetTypeManager.get(actionContext.getTenantId(), typeId);
        if (Objects.isNull(this.type)) {
            throw new ValidateException("budget type not found.");
        }

        String nodeId = (String) arg.getObjectData().get(TPMBudgetCarryForwardFields.BUDGET_NODE_ID);
        if (Strings.isNullOrEmpty(nodeId)) {
            throw new ValidateException("[budget_node_id] can not be null or empty.");
        }
        this.node = this.type.getNodes().stream().filter(f -> f.getNodeId().equals(nodeId)).findFirst().orElse(null);
        if (Objects.isNull(this.node)) {
            throw new ValidateException("budget node not found.");
        }
    }

    /**
     * validate master data of TPMBudgetCarryForwardObj
     */
    private void validateMasterData() {

        if (!this.node.isEnableCarryForward()) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CARRY_FORWARD_OBJ_ADD_ACTION_BAK_0));
        }

        String sourcePeriodFieldApiName = String.format("source_%s", this.node.getTimeDimension());
        String targetPeriodFieldApiName = String.format("target_%s", this.node.getTimeDimension());

        this.sourcePeriod = (Long) arg.getObjectData().get(sourcePeriodFieldApiName);
        if (Objects.isNull(this.sourcePeriod)) {
            throw new ValidateException(String.format("[%s] can not be null.", sourcePeriodFieldApiName));
        }

        this.targetPeriod = (Long) arg.getObjectData().get(targetPeriodFieldApiName);
        if (Objects.isNull(this.targetPeriod)) {
            throw new ValidateException(String.format("[%s] can not be null.", targetPeriodFieldApiName));
        }

        if (fiscalTimeService.notNextPeriod(actionContext.getTenantId(), this.node.getTimeDimension(), this.sourcePeriod, this.targetPeriod)) {
            throw new ValidateException(String.format("[%s] must be the next time dimension of [%s].", targetPeriodFieldApiName, sourcePeriodFieldApiName));
        }

        this.arg.getObjectData().put(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS, TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__FROZEN);
    }

    /**
     * validate detail data of TPMBudgetCarryForwardDetailObj
     */
    private void validateDetailData() {
        List<ObjectDataDocument> details = arg.getDetails().get(ApiNames.TPM_BUDGET_CARRY_FORWARD_DETAIL);

        if (CollectionUtils.isEmpty(details)) {
            throw new ValidateException("budget carry forward detail list can not be null or empty.");
        }

        if (details.size() > 200) {
            throw new ValidateException("budget carry forward detail list size must less than 200.");
        }

        List<String> sourceIds = details.stream().map(detail -> (String) detail.get(TPMBudgetCarryForwardDetailFields.SOURCE_BUDGET_ACCOUNT_ID)).collect(Collectors.toList());
        List<String> targetIds = details.stream().map(detail -> (String) detail.get(TPMBudgetCarryForwardDetailFields.TARGET_BUDGET_ACCOUNT_ID)).collect(Collectors.toList());

        Map<String, IObjectData> sourceMap = validateAndQueryBudgetAccountMap(sourceIds, this.sourcePeriod);
        Map<String, IObjectData> targetMap = validateAndQueryBudgetAccountMap(targetIds, this.targetPeriod);

        validateDimensionData(details, sourceMap, targetMap);
    }

    private void tryLockCarryForward() {
        String key = String.format("FMCG:TPM:BUDGET_CARRY_FORWARD_LOCK:%s.%s", actionContext.getTenantId(), arg.getObjectData().get(TPMBudgetCarryForwardFields.BUDGET_NODE_ID));
        this.lock = redissonCmd.getLock(key);

        try {
            if (!this.lock.tryLock(5L, 60L, TimeUnit.SECONDS)) {
                throw new ValidateException("system error, Please try again later.");
            }
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new ValidateException("try lock carry forward request cause 'InterruptedException' please try again later.");
        }
    }

    private void tryLockAllDetails() {
        List<ObjectDataDocument> details = arg.getDetails().get(ApiNames.TPM_BUDGET_CARRY_FORWARD_DETAIL);
        for (ObjectDataDocument detail : details) {
            String sourceId = (String) detail.get(TPMBudgetCarryForwardDetailFields.SOURCE_BUDGET_ACCOUNT_ID);
            String targetId = (String) detail.get(TPMBudgetCarryForwardDetailFields.TARGET_BUDGET_ACCOUNT_ID);

            IBudgetOperator sourceOperator = BudgetOperatorFactory.initOperator(BizType.CARRY_OVER_OUT, actionContext.getUser(), sourceId, this.businessTraceId, this.approvalTraceId);
            IBudgetOperator targetOperator = BudgetOperatorFactory.initOperator(BizType.CARRY_OVER_IN, actionContext.getUser(), targetId, this.businessTraceId, this.approvalTraceId);
            CarryForwardOperators operator = CarryForwardOperators.builder().source(sourceOperator).target(targetOperator).build();

            if (operator.tryLock()) {
                operators.put(sourceId, operator);
            } else {
                throw new MetaDataBusinessException("Try lock budget account fail.");
            }
        }
    }

    /**
     * validate budget account data and return budget account map
     *
     * @param ids    budget account id list
     * @param period budget period
     * @return budget account map
     */
    private Map<String, IObjectData> validateAndQueryBudgetAccountMap(List<String> ids, Long period) {
        String periodFieldApiName = String.format("budget_period_%s", this.node.getTimeDimension());

        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(200);
        stq.setOffset(0);
        stq.setNeedReturnCountNum(false);
        stq.setNeedReturnQuote(false);
        stq.setSearchSource("db");

        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(Lists.newArrayList(ids));

        stq.setFilters(Lists.newArrayList(idFilter));

        List<String> fields = this.node.getDimensions().stream().map(BudgetDimensionEntity::getApiName).collect(Collectors.toList());
        fields.add(CommonFields.ID);
        fields.add(CommonFields.NAME);
        fields.add(CommonFields.LIFE_STATUS);
        fields.add(periodFieldApiName);
        fields.add(TPMBudgetAccountFields.BUDGET_TYPE_ID);
        fields.add(TPMBudgetAccountFields.BUDGET_NODE_ID);
        fields.add(TPMBudgetAccountFields.AVAILABLE_AMOUNT);
        fields.add(TPMBudgetAccountFields.BUDGET_DEPARTMENT);

        IActionContext context = ActionContextExt.of(User.systemUser(actionContext.getTenantId()), actionContext.getRequestContext()).getContext();
        List<IObjectData> data = serviceFacade.findBySearchTemplateQueryWithFields(
                context,
                ApiNames.TPM_BUDGET_ACCOUNT,
                stq,
                fields
        ).getData();

        IObjectData wrongTypeIdDatum = data.stream()
                .filter(datum -> !this.type.getId().toString().equals(datum.get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class)))
                .findFirst().orElse(null);
        if (Objects.nonNull(wrongTypeIdDatum)) {
            throw new ValidateException(String.format("[%s] detail validate error, wrong budget type id.", wrongTypeIdDatum.get(CommonFields.NAME)));
        }

        IObjectData wrongNodeIdDatum = data.stream()
                .filter(datum -> !this.node.getNodeId().equals(datum.get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class)))
                .findFirst().orElse(null);
        if (Objects.nonNull(wrongNodeIdDatum)) {
            throw new ValidateException(String.format("[%s] detail validate error, wrong budget node id.", wrongNodeIdDatum.get(CommonFields.NAME)));
        }

        IObjectData wrongPeriodDatum = data.stream()
                .filter(datum -> fiscalTimeService.notSamePeriod(actionContext.getTenantId(), this.node.getTimeDimension(), period, datum.get(periodFieldApiName, Long.class)))
                .findFirst().orElse(null);
        if (Objects.nonNull(wrongPeriodDatum)) {
            throw new ValidateException(String.format("[%s] detail validate error, wrong budget period.", wrongPeriodDatum.get(CommonFields.NAME)));
        }

        return data.stream().collect(Collectors.toMap(DBRecord::getId, v -> v));
    }

    /**
     * validate dimension
     *
     * @param details   carry forward detail list
     * @param sourceMap source budget account data map
     * @param targetMap target budget account data map
     */
    private void validateDimensionData(List<ObjectDataDocument> details, Map<String, IObjectData> sourceMap, Map<String, IObjectData> targetMap) {
        Set<String> fields = this.node.getDimensions().stream().map(BudgetDimensionEntity::getApiName).collect(Collectors.toSet());

        for (ObjectDataDocument detail : details) {
            String sourceId = (String) detail.get(TPMBudgetCarryForwardDetailFields.SOURCE_BUDGET_ACCOUNT_ID);
            String targetId = (String) detail.get(TPMBudgetCarryForwardDetailFields.TARGET_BUDGET_ACCOUNT_ID);

            IObjectData sourceData = sourceMap.get(sourceId);
            if (Objects.isNull(sourceData)) {
                throw new ValidateException(String.format("[%s] source data not exists.", sourceId));
            }

            IObjectData targetData = targetMap.get(targetId);
            if (Objects.isNull(targetData)) {
                throw new ValidateException(String.format("[%s] target data not exists.", targetId));
            }
            if (!CommonFields.LIFE_STATUS__NORMAL.equals(targetData.get(CommonFields.LIFE_STATUS, String.class))) {
                throw new ValidateException(String.format("[%s] target data life status error.", targetId));
            }
            if (budgetCompareService.notSameDimension(fields, sourceData, targetData)) {
                throw new ValidateException(String.format("[%s - %s] source data dimensions not equal to target data dimensions.", sourceId, targetId));
            }
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        return transProxy.call(() -> {
            Result innerResult = super.doAct(arg);
            this.stopWatch.lap("framework.doAct");

            IObjectData data = innerResult.getObjectData().toObjectData();
            setWhatValueForOperators(data);
            this.stopWatch.lap("setWhatValueForOperators");

            freezeRemainingAmount();
            this.stopWatch.lap("freezeRemainingAmount");

            return innerResult;
        });
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result inner = super.after(arg, result);

        IObjectData data = inner.getObjectData().toObjectData();
        if (!isApprovalFlowStartSuccess(data.getId())) {
            try {
                List<IObjectData> details = inner.getDetails().get(ApiNames.TPM_BUDGET_CARRY_FORWARD_DETAIL).stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList());
                this.unfreezeAndDoCarryForward(data, details);
                this.stopWatch.lap("unfreezeAndDoCarryForward");

                asyncAddBudgetLog(arg);
            } catch (Exception ex) {
                log.error("unfreeze and do carry forward failed : ", ex);
                this.setCarryForwardFailed(data);
                this.stopWatch.lap("setCarryForwardFailed");
            }
        }

        return inner;
    }

    private void asyncAddBudgetLog(Arg arg) {
        BuryService.asyncBudgetLog(actionContext.getTenantId(), actionContext.getUser().getUserIdInt(), BuryModule.Budget.BUDGET_CARRY_FORWARD, BuryOperation.CREATE);

        BigDecimal amount = arg.getObjectData().toObjectData().get(TPMBudgetCarryForwardFields.AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        BuryService.asyncBudgetAmountLog(Integer.valueOf(actionContext.getTenantId()), actionContext.getUser().getUserIdInt(), BuryModule.BUDGET_AMOUNT.CARRY_FORWARD_AMOUNT, BuryOperation.CREATE, amount.doubleValue());
    }

    private void setCarryForwardFailed(IObjectData data) {
        Map<String, Object> updater = Maps.newHashMap();
        updater.put(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS, TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__FAILED);
        this.serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), data, updater);
    }

    @Override
    protected void finallyDo() {

        this.unlockCarryForward();
        this.stopWatch.lap("unlockCarryForward");

        this.unlockAllDetails();
        this.stopWatch.lap("unlockAllDetails");

        super.finallyDo();
        this.stopWatch.lap("framework.finallyDo");
    }

    /**
     * carry forward unlock
     */
    private void unlockCarryForward() {
        if (!Objects.isNull(this.lock) && this.lock.isLocked() && this.lock.isHeldByCurrentThread()) {
            this.lock.unlock();
        }
    }

    /***
     * unlock all source operator
     */
    private void unlockAllDetails() {
        for (CarryForwardOperators operator : this.operators.values()) {
            operator.unlock();
        }
    }

    private void setWhatValueForOperators(IObjectData data) {
        for (CarryForwardOperators operator : this.operators.values()) {
            operator.setWhat(data);
        }
    }

    /**
     * occupy budget account
     */
    protected void freezeRemainingAmount() {
        for (CarryForwardOperators operator : this.operators.values()) {
            operator.source.freezeRemainingAmount();
            operator.source.recalculate();
        }
    }

    protected void unfreezeAndDoCarryForward(IObjectData data, List<IObjectData> details) {
        Map<String, Tuple2<BigDecimal, BigDecimal>> amountMap = Maps.newHashMap();

        for (CarryForwardOperators operator : operators.values()) {
            BigDecimal amount = operator.source.unfreeze(BizType.RELEASE);

            operator.source.expenditure(amount);
            operator.source.recalculate();

            BigDecimal targetAmount = operator.target.realAmount().get(TPMBudgetAccountFields.AVAILABLE_AMOUNT);
            operator.target.income(amount);
            operator.target.recalculate();

            amountMap.put(operator.getSource().getAccount().getId(), new Tuple2<>(amount, amount.add(targetAmount)));
        }

        Map<String, Object> updater = Maps.newHashMap();
        updater.put(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS, TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__SUCCESS);

        this.serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), data, updater);

        for (IObjectData detail : details) {
            String key = detail.get(TPMBudgetCarryForwardDetailFields.SOURCE_BUDGET_ACCOUNT_ID, String.class);
            Tuple2<BigDecimal, BigDecimal> amountValue = amountMap.get(key);

            detail.set(TPMBudgetCarryForwardDetailFields.AMOUNT, amountValue.getFirst());
            detail.set(TPMBudgetCarryForwardDetailFields.AFTER_CARRY_FORWARD_AMOUNT, amountValue.getSecond());
        }

        this.serviceFacade.batchUpdateByFields(
                User.systemUser(actionContext.getTenantId()),
                details,
                Lists.newArrayList(
                        TPMBudgetCarryForwardDetailFields.AMOUNT,
                        TPMBudgetCarryForwardDetailFields.AFTER_CARRY_FORWARD_AMOUNT
                ));
    }

    @Data
    @Builder
    public static class CarryForwardOperators {

        private IBudgetOperator source;

        private IBudgetOperator target;

        private boolean tryLock() {
            return this.source.tryLock() && this.target.tryLock();
        }

        private void unlock() {
            this.source.unlock();
            this.target.unlock();
        }

        private void setWhat(IObjectData what) {
            this.source.setWhat(what);
            this.target.setWhat(what);
        }
    }
}
