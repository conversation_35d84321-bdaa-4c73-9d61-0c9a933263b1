package com.facishare.crm.fmcg.tpm.business.abstraction;

/**
 * <AUTHOR>
 * @date 2021/7/19 上午11:51
 */
public interface IOperateInfoService {

    String log(String tenantId,String logType,String logData,String logDataType,String operatorId,String bizId,String targetApiName,String relatedDataId,int isApprovalData,long operateTime);

    String log(String tenantId,String logType,String logData,String operateId,String targetApiName,String relatedDataId,boolean isApprovalData);

    void updateLog(String id,String tenantId ,String approvalInstanceId);

    int deleteLog(String tenantId,String id);
}
