package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityAgreementFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofFields;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.api.proof.PreAdd;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.BaseObjectApprovalAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Author: linmj
 * Date: 2024/2/5 14:26
 */
public class TPMActivityAgreementObjActivityProofAction extends BaseObjectApprovalAction<TPMActivityAgreementObjActivityProofAction.Arg, TPMActivityAgreementObjActivityProofAction.Result> {


    private IObjectData agreement;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.ACTIVITY_PROOF.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getDataId());
    }

    @Override
    protected IObjectData getPreObjectData() {
        return agreement;
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.ACTIVITY_PROOF.getButtonApiName();
    }

    @Override
    protected IObjectData getPostObjectData() {
        return agreement;
    }

    @Override
    protected void init() {
        super.init();
        agreement = this.dataList.get(0);
    }

    @Override
    protected Map<String, Object> getArgs() {
        Map<String, Object> map = super.getArgs();
        map.put("visit_id", arg.getVisitId());
        map.put("action_id", arg.getActionId());
        return map;
    }

    @Override
    protected Result doAct(Arg arg) {
        String agreementStatus = agreement.get(TPMActivityAgreementFields.AGREEMENT_STATUS, String.class);
        if (!TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS.equals(agreementStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_AGREEMENT_OBJ_ACTIVITY_PROOF_ACTION_0));
        }
        String storeId = agreement.get(TPMActivityAgreementFields.STORE_ID, String.class);
        String activityId = agreement.get(TPMActivityAgreementFields.ACTIVITY_ID, String.class);
        if (!Strings.isNullOrEmpty(arg.getActionId())) {
            validateRepeat(activityId, arg.getVisitId(), arg.getActionId());
        }
        PreAdd.Arg preAddArg = new PreAdd.Arg();
        preAddArg.setVisitId(arg.getVisitId());
        preAddArg.setActionId(arg.getActionId());
        preAddArg.setActivityAgreementId(arg.dataId);
        preAddArg.setStoreId(storeId);
        preAddArg.setActivityId(activityId);

        ControllerContext preAddContext = new ControllerContext(actionContext.getRequestContext(), ApiNames.TPM_ACTIVITY_PROOF_OBJ, "PreAdd");
        PreAdd.Result preAddResult = serviceFacade.triggerController(preAddContext, preAddArg, PreAdd.Result.class);
        return Result.of(agreement, preAddResult);
    }

    private void validateRepeat(String activityId, String checkinId, String actionId) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1,
                Lists.newArrayList(SearchQueryUtil.filter(TPMActivityProofFields.ACTIVITY_ID, Operator.EQ, Lists.newArrayList(activityId)),
                        SearchQueryUtil.filter(TPMActivityProofFields.VISIT_ID, Operator.EQ, Lists.newArrayList(checkinId)),
                        SearchQueryUtil.filter(TPMActivityProofFields.ACTION_ID, Operator.EQ, Lists.newArrayList(actionId))));

        List<IObjectData> data = CommonUtils.queryData(serviceFacade, actionContext.getUser(), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query, Lists.newArrayList(CommonFields.ID));
        if (!data.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_AGREEMENT_OBJ_ACTIVITY_PROOF_ACTION_1));
        }
    }

    @Data
    public static class Arg {

        @SerializedName("objectDataId")
        @JSONField(name = "objectDataId")
        @JsonProperty("objectDataId")
        private String dataId;

        @SerializedName("visit_id")
        @JSONField(name = "visit_id")
        @JsonProperty("visit_id")
        private String visitId;

        @SerializedName("action_id")
        @JSONField(name = "action_id")
        @JsonProperty("action_id")
        private String actionId;


        public static Arg of(String dataId) {
            Arg arg = new Arg();
            arg.setDataId(dataId);
            return arg;
        }
    }


    @Data
    public static class Result {
        private ObjectDataDocument objectData;
        private PreAdd.Result preAddResult;

        public static Result of(IObjectData objectData, PreAdd.Result preAddResult) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            result.setPreAddResult(preAddResult);
            return result;
        }
    }

    @Override
    protected void finallyDo() {
        super.finallyDo();
        BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_AGREEMENT_PRESET_PROOF_NUMBER, BuryOperation.CLICK, true);
    }
}
