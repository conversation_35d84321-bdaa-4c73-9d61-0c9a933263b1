package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.utils.I18NEnums;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetAccountDetailService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetAccountService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetStatisticTableService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IFiscalTimeService;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.crm.fmcg.tpm.service.abstraction.ITransactionProxy;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
@SuppressWarnings("Duplicates")
public class TPMBudgetAccountObjIncrementUpdateAction extends StandardIncrementUpdateAction {

    private final BudgetTypeDAO budgetTypeDAO = SpringUtil.getContext().getBean(BudgetTypeDAO.class);

    private final IFiscalTimeService fiscalTimeService = SpringUtil.getContext().getBean(IFiscalTimeService.class);
    private final IBudgetAccountService budgetAccountService = SpringUtil.getContext().getBean(IBudgetAccountService.class);
    private final ITransactionProxy transProxy = SpringUtil.getContext().getBean(ITransactionProxy.class);
    private final IBudgetAccountDetailService budgetAccountDetailService = SpringUtil.getContext().getBean(IBudgetAccountDetailService.class);
    private final IBudgetStatisticTableService budgetStatisticTableService = SpringUtil.getContext().getBean(IBudgetStatisticTableService.class);

    private BudgetTypePO type;
    private BudgetTypeNodeEntity node;
    private String periodFieldApiName;
    private boolean isEnableBudget = false;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        stopWatch.lap("framework.before");

        this.setDefaultValue();
        stopWatch.lap("setDefaultValue");

        this.loadRelatedData();
        stopWatch.lap("loadRelatedData");

        this.editableValidate();
        stopWatch.lap("editableValidate");

        this.accountDataValidate();
        stopWatch.lap("accountDataValidate");


    }

    private void editableValidate() {
        List<String> budgetDimensionFields = getBudgetDimensionField();
        if (TPMBudgetAccountFields.BUDGET_STATUS__ENABLE.equals(this.dbObjectData.get(TPMBudgetAccountFields.BUDGET_STATUS, String.class))) {
            Set<String> masterSet = new HashSet<>(arg.getData().keySet());
            masterSet.remove(CommonFields.NAME);
            masterSet.remove(CommonFields.ID);
            masterSet.remove(CommonFields.TENANT_ID);
            masterSet.remove(CommonFields.OBJECT_DESCRIBE_API_NAME);
            masterSet.remove(TPMBudgetAccountFields.BUDGET_STATUS);
            for (String field : masterSet) {
                if (!field.endsWith("__c")) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_OBJ_INCREMENT_UPDATE_ACTION_0));
                }
                if (budgetDimensionFields.contains(field)) {
                    throw new ValidateException(I18N.text(I18NEnums.FORBID_EDIT_BUDGET_DIMENSION_FIELD.getCode()));
                }
            }

        } else if (budgetAccountDetailService.existBudgetDetail(actionContext.getTenantId(), arg.getData().getId())) {
            //已经有明细的费用预算表 不允许更改除了状态外的其他信息。
            for (String field : arg.getData().keySet()) {
                if (!field.equals(TPMBudgetAccountFields.BUDGET_STATUS) && !field.endsWith("__c")) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_OBJ_INCREMENT_UPDATE_ACTION_1));
                }
                if (budgetDimensionFields.contains(field)) {
                    throw new ValidateException(I18N.text(I18NEnums.FORBID_EDIT_BUDGET_DIMENSION_FIELD.getCode()));
                }
            }
        }
    }

    private List<String> getBudgetDimensionField() {
        List<String> fields = Lists.newArrayList();
        if (this.node != null) {
            this.node.getDimensions().forEach(budgetDimensionEntity -> fields.add(budgetDimensionEntity.getApiName()));
            this.node.getControlDimensions().forEach(budgetDimensionEntity -> fields.add(budgetDimensionEntity.getApiName()));
        }
        return fields;
    }

    public void loadRelatedData() {
        IObjectData master = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), this.objectData.getId(), this.objectData.getDescribeApiName());
        ObjectDataExt.toMap(this.objectData).forEach(master::set);

        String typeId = master.get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class);
        if (Strings.isNullOrEmpty(typeId)) {
            throw new ValidateException("[budget_type_id] can not be null or empty.");
        }
        this.type = budgetTypeDAO.get(actionContext.getTenantId(), typeId);
        if (Objects.isNull(this.type)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_OBJ_INCREMENT_UPDATE_ACTION_2));
        }

        String nodeId = master.get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class);
        if (Strings.isNullOrEmpty(nodeId)) {
            throw new ValidateException("[budget_node_id] can not be null or empty.");
        }
        Optional<BudgetTypeNodeEntity> oNode = this.type.getNodes().stream().filter(f -> f.getNodeId().equals(nodeId)).findFirst();
        if (!oNode.isPresent()) {
            throw new ValidateException("budget node not found.");
        }
        this.node = oNode.get();
        this.periodFieldApiName = String.format("budget_period_%s", this.node.getTimeDimension());
    }

    private void accountDataValidate() {
        Set<String> dimensionFields = Sets.newHashSet(TPMBudgetAccountFields.EFFECTIVE_PERIOD, TPMBudgetAccountFields.BUDGET_DEPARTMENT, TPMBudgetAccountFields.BUDGET_PERIOD_YEAR, TPMBudgetAccountFields.BUDGET_PERIOD_MONTH, TPMBudgetAccountFields.BUDGET_PERIOD_QUARTER);
        if (!CollectionUtils.isEmpty(this.node.getDimensions())) {
            this.node.getDimensions().forEach(dimension -> dimensionFields.add(dimension.getApiName()));
        }
        if (dimensionFields.removeAll(arg.getData().keySet())) {
            budgetAccountService.validate(actionContext.getTenantId(), this.type, this.node, this.dbObjectData);
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        return transProxy.call(() -> {
            Result inner = super.doAct(arg);
            this.stopWatch.lap("framework.doAct");

            if (arg.getData().containsKey(TPMBudgetAccountFields.BUDGET_STATUS)
                    && TPMBudgetAccountFields.BUDGET_STATUS__ENABLE.equals(this.objectData.get(TPMBudgetAccountFields.BUDGET_STATUS, String.class))) {
                this.isEnableBudget = true;
            }
            return inner;
        });
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result inner = super.after(arg, result);
        this.enableBudget();
        IObjectData budget = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), Lists.newArrayList(this.objectData.getId()), ApiNames.TPM_BUDGET_ACCOUNT).get(0);
        asyncSaveBudgetStatisticTable(budget);
        return inner;
    }

    private void enableBudget() {
        //action 执行
        if (this.isEnableBudget) {
            budgetAccountService.enableBudget(User.systemUser(actionContext.getTenantId()), this.objectData);
        }
    }

    private void setDefaultValue() {
        IObjectData data = this.objectData;
        if (data.containsField(TPMBudgetAccountFields.BUDGET_NODE_ID)) {
            data.set(TPMBudgetAccountFields.CONTROL_PERIOD, this.node.getControlTimeDimension());
            data.set(TPMBudgetAccountFields.EFFECTIVE_PERIOD, this.node.getTimeDimension());
        }

        if (data.containsField(this.periodFieldApiName)) {
            Long time = data.get(this.periodFieldApiName, Long.class);
            if (Objects.isNull(time)) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_OBJ_INCREMENT_UPDATE_ACTION_3));
            }
            data.set(this.periodFieldApiName, fiscalTimeService.correctPeriodTime(actionContext.getTenantId(), this.node.getTimeDimension(), time));
        }
    }

    private void asyncSaveBudgetStatisticTable(IObjectData budgetAccountData) {
        String lifeStatus = budgetAccountData.get(CommonFields.LIFE_STATUS, String.class);
        if (CommonFields.LIFE_STATUS__NORMAL.equals(lifeStatus)) {
            budgetStatisticTableService.asyncDoStatistic(actionContext.getTenantId(), budgetAccountData);
        }
    }
}
