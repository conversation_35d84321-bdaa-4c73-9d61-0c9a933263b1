package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofDetailFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofDisplayImgFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.BaseService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public abstract class AbstractDisPlayReportBaseService extends BaseService {

    @Resource
    private ActivityTypeManager activityTypeManager;

    public boolean openAi(String tenantId) {
        return activityTypeManager.getEnableAi(tenantId);
    }

    public void setProofDisplayImgDefaultValue(String tenantId, BaseObjectSaveAction.Arg arg, boolean openAi, String retakeId) {
        if (openAi) {
            arg.getObjectData().put(TPMActivityProofFields.OPEN_AI, true);
            if (!isRecognized(tenantId, arg.getObjectData().toObjectData())) {
                arg.getObjectData().put(TPMActivityProofFields.AI_IDENTIFY_STATUS, TPMActivityProofFields.AI_IDENTIFY_STATUS_IDENTIFYING);
                List<ObjectDataDocument> activityProofDisplayImgs = arg.getDetails().getOrDefault(ApiNames.TPM_ACTIVITY_PROOF_DISPLAY_IMG_OBJ, new ArrayList<>());
                for (ObjectDataDocument activityProofDisplayImg : activityProofDisplayImgs) {
                    if (retakeId != null && !activityProofDisplayImg.getId().equals(retakeId)){
                        continue;
                    }
                    activityProofDisplayImg.put(TPMActivityProofDisplayImgFields.AI_IDENTIFY_STATUS, TPMActivityProofDisplayImgFields.AI_IDENTIFY_STATUS_IDENTIFYING);
                }
            }
        } else {
            List<ObjectDataDocument> activityProofDetails = arg.getDetails().getOrDefault(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, new ArrayList<>());
            List<ObjectDataDocument> activityProofDisplayImgs = arg.getDetails().getOrDefault(ApiNames.TPM_ACTIVITY_PROOF_DISPLAY_IMG_OBJ, new ArrayList<>());
            if (CollectionUtils.isNotEmpty(activityProofDisplayImgs) && CollectionUtils.isNotEmpty(activityProofDetails)) {
                Map<String, List<ObjectDataDocument>> displayFormIdDetailMap = activityProofDetails.stream()
                        .filter(activityProofDetail -> StringUtils.isNotBlank((String) activityProofDetail.get(TPMActivityProofDetailFields.DISPLAY_FORM_ID)))
                        .collect(Collectors.groupingBy(activityProofDetail -> (String) activityProofDetail.get(TPMActivityProofDetailFields.DISPLAY_FORM_ID)));

                Map<String, List<ObjectDataDocument>> itemIdDetailMap = activityProofDetails.stream()
                        .filter(activityProofDetail -> StringUtils.isBlank((String) activityProofDetail.get(TPMActivityProofDetailFields.DISPLAY_FORM_ID)))
                        .collect(Collectors.groupingBy(activityProofDetail -> (String) activityProofDetail.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID)));

                for (ObjectDataDocument activityProofDisplayImg : activityProofDisplayImgs) {
                    String displayFormId = (String) activityProofDisplayImg.get(TPMActivityProofDetailFields.DISPLAY_FORM_ID);
                    String activityItemId = (String) activityProofDisplayImg.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID);

                    List<ObjectDataDocument> activityInnerProofDetails;
                    if (StringUtils.isNotBlank(displayFormId)) {
                        activityInnerProofDetails = displayFormIdDetailMap.getOrDefault(displayFormId, Lists.newArrayList());
                    } else {
                        activityInnerProofDetails = itemIdDetailMap.getOrDefault(activityItemId, Lists.newArrayList());
                    }


                    List<String> systemJudgmentStatusList = activityInnerProofDetails.stream()
                            .map(activityProofDetail -> (String) activityProofDetail.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS))
                            .collect(Collectors.toList());

                    String systemJudgmentStatus = getProofImgSystemJudgmentStatus(systemJudgmentStatusList);

                    if (StringUtils.isNotBlank(systemJudgmentStatus)) {
                        activityProofDisplayImg.put(TPMActivityProofDisplayImgFields.SYSTEM_JUDGMENT_STATUS, systemJudgmentStatus);
                    }
                    String auditStatus = (StringUtils.isBlank(systemJudgmentStatus) || TPMActivityProofDisplayImgFields.NOT_DISPLAY_SYSTEM_JUDGMENT_STATUS.equals(systemJudgmentStatus)) ? TPMActivityProofDisplayImgFields.FAIL_STATUS : systemJudgmentStatus;
                    activityProofDisplayImg.put(TPMActivityProofDisplayImgFields.AUDIT_STATUS, auditStatus);
                }
                arg.getDetails().put(ApiNames.TPM_ACTIVITY_PROOF_DISPLAY_IMG_OBJ, activityProofDisplayImgs);
                String proofMasterSystemJudgmentStatus = getProofMasterSystemJudgmentStatus(tenantId, activityProofDisplayImgs.stream()
                        .map(activityProofDisplayImg -> (String) activityProofDisplayImg.get(TPMActivityProofDisplayImgFields.SYSTEM_JUDGMENT_STATUS))
                        .collect(Collectors.toList()));

                if (StringUtils.isNotBlank(proofMasterSystemJudgmentStatus)) {
                    arg.getObjectData().put(TPMActivityProofFields.SYSTEM_JUDGMENT_STATUS, proofMasterSystemJudgmentStatus);
                }
            }
        }
    }

    @NotNull
    public String getProofImgSystemJudgmentStatus(List<String> systemJudgmentStatusList) {
        /**
         * 均未陈列 = 未陈列
         * 均达标 = 达标
         * 均不达标 = 不达标
         * 部分达标 = 部分达标
         * 有待审核使用待审核
         * 存在为空或 "--" 为 --
         */

        String systemJudgmentStatus = "";

        // 如果列表为空，则返回空字符串
        if (CollectionUtils.isEmpty(systemJudgmentStatusList)) {
            systemJudgmentStatus = "";
        } else if (systemJudgmentStatusList.stream().anyMatch(status -> StringUtils.isBlank(status) || "--".equals(status))) {
            // 存在为空或 "--" 时，状态为 "--"
            systemJudgmentStatus = "";
        } else if (systemJudgmentStatusList.stream().anyMatch(TPMActivityProofDetailFields.PENDING_APPROVAL_SYSTEM_JUDGMENT_STATUS::equals)) {
            // 有待审核时，状态为待审核
            systemJudgmentStatus = TPMActivityProofDetailFields.PENDING_APPROVAL_SYSTEM_JUDGMENT_STATUS;
        } else {
            // 统计各种状态的数量

            long passCount = systemJudgmentStatusList.stream()
                    .filter(TPMActivityProofDetailFields.PASS_STATUS::equals)
                    .count();

            long failCount = systemJudgmentStatusList.stream()
                    .filter(TPMActivityProofDetailFields.FAIL_STATUS::equals)
                    .count();

            int totalCount = systemJudgmentStatusList.size();

            if (passCount == totalCount) {
                // 均达标
                systemJudgmentStatus = TPMActivityProofDisplayImgFields.PASS_STATUS;
            } else if (failCount == totalCount) {
                // 均不达标
                systemJudgmentStatus = TPMActivityProofDisplayImgFields.FAIL_STATUS;
            } else {
                // 部分达标（混合状态）
                systemJudgmentStatus = TPMActivityProofDisplayImgFields.PARTIAL_PASS_STATUS;
            }
        }
        return systemJudgmentStatus;
    }

    @NotNull
    public String getProofMasterSystemJudgmentStatus(String tenantId, List<String> systemJudgmentStatusList) {
        /**
         * 均未陈列 = 未陈列
         * 均达标 = 达标
         * 均不达标 = 不达标
         * 部分达标 = 部分达标
         * 有待审核使用待审核
         * 存在为空或 "--" 为 --
         */

        String systemJudgmentStatus = "";

        // 如果列表为空，则返回空字符串
        if (CollectionUtils.isEmpty(systemJudgmentStatusList)) {
            systemJudgmentStatus = "";
        } else if (systemJudgmentStatusList.stream().anyMatch(status -> StringUtils.isBlank(status) || "--".equals(status))) {
            // 存在为空或 "--" 时，状态为 "--"
            systemJudgmentStatus = "";
        } else if (systemJudgmentStatusList.stream().anyMatch(TPMActivityProofDisplayImgFields.PENDING_APPROVAL_SYSTEM_JUDGMENT_STATUS::equals)) {
            // 有待审核时，状态为待审核
            systemJudgmentStatus = TPMActivityProofDisplayImgFields.PENDING_APPROVAL_SYSTEM_JUDGMENT_STATUS;
        } else {
            // 统计各种状态的数量

            long passCount = systemJudgmentStatusList.stream()
                    .filter(TPMActivityProofDisplayImgFields.PASS_STATUS::equals)
                    .count();

            long failCount = systemJudgmentStatusList.stream()
                    .filter(TPMActivityProofDisplayImgFields.FAIL_STATUS::equals)
                    .count();

            int totalCount = systemJudgmentStatusList.size();

            // 根据状态分布确定最终状态
            if (passCount == totalCount) {
                // 均达标
                systemJudgmentStatus = TPMActivityProofFields.PASS_STATUS;
            } else if (failCount == totalCount) {
                // 均不达标
                systemJudgmentStatus = TPMActivityProofFields.FAIL_STATUS;
            } else {
                // 部分达标（混合状态）
                if (TPMGrayUtils.aiDisplayRioMode(tenantId)) {
                    systemJudgmentStatus = TPMActivityProofFields.PENDING_APPROVAL_SYSTEM_JUDGMENT_STATUS;
                } else {
                    systemJudgmentStatus = TPMActivityProofFields.PARTIAL_PASS_STATUS;
                }
            }
        }
        return systemJudgmentStatus;
    }


    public String getProofDetailSystemJudgmentResult(ObjectDataDocument objectData) {
        Double aiNumber = objectData.toObjectData().get(TPMActivityProofDetailFields.AI_NUMBER, Double.class);
        Double amountAmountStandard = objectData.toObjectData().get(TPMActivityProofDetailFields.PROOF_DETAIL_AMOUNT_STANDARD, Double.class);
        String systemJudgmentStatus = "";
        if (Objects.isNull(aiNumber) || Objects.isNull(amountAmountStandard)) {
            systemJudgmentStatus = TPMActivityProofDetailFields.FAIL_STATUS;
        } else if (aiNumber.compareTo(amountAmountStandard) >= 0) {
            systemJudgmentStatus = TPMActivityProofDetailFields.PASS_STATUS;
        } else if (aiNumber.compareTo(0.0) == 0) {
            systemJudgmentStatus = TPMActivityProofDetailFields.FAIL_STATUS;
        } else if (aiNumber.compareTo(amountAmountStandard) < 0) {
            systemJudgmentStatus = TPMActivityProofDetailFields.FAIL_STATUS;
        }
        return systemJudgmentStatus;
    }


    protected boolean isRecognized(String tenantId, String proofId) {
        IObjectData proofObj = serviceFacade.findObjectDataByIds(tenantId, Lists.newArrayList(proofId), ApiNames.TPM_ACTIVITY_PROOF_OBJ).get(0);
        if (Objects.isNull(proofObj)) {
            throw new ValidateException("TPMActivityProofObj is not exist");
        }
        return isRecognized(tenantId, proofObj);
    }

    protected boolean isRecognized(String tenantId, IObjectData proofObj) {
        if (Objects.isNull(proofObj)) {
            throw new ValidateException("TPMActivityProofObj is not exist");
        }

        if (TPMGrayUtils.isRioTenant(tenantId)) {
            return Boolean.TRUE.equals(proofObj.get(TPMActivityProofFields.IS_RECOGNIZED__C, Boolean.class));
        }

        return false;
    }

    protected boolean isDisplayType(ActivityTypeExt activityType) {
        if (Objects.isNull(activityType)) {
            return false;
        }
        String templateId = activityType.get().getTemplateId();
        return StringUtils.isNotBlank(templateId) && templateId.contains("display");
    }


    @NotNull
    protected static IObjectData buildDefaultIObjectData(String userId, String apiName, IObjectData toObjectData) {
        IObjectData objectData = new ObjectData();
        objectData.setDescribeApiName(apiName);
        objectData.setDataOwnDepartment(toObjectData.getDataOwnDepartment());
        objectData.setDataOwnOrganization(toObjectData.getDataOwnOrganization());
        objectData.setOutOwner(toObjectData.getOutOwner());
        objectData.setOwner(toObjectData.getOwner());
        objectData.setTenantId(toObjectData.getTenantId());
        objectData.setOutTenantId(toObjectData.getOutTenantId());
        objectData.setCreatedBy(userId);
        objectData.setCreateTime(System.currentTimeMillis());
        objectData.setLastModifiedBy(userId);
        objectData.setLastModifiedTime(System.currentTimeMillis());
        objectData.setRecordType("default__c");
        return objectData;
    }
}
