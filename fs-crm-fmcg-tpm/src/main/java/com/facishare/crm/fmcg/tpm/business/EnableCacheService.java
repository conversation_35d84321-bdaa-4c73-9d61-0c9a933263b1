package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityStoreFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityUnifiedCaseFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IEnableCacheService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRangeFieldBusiness;
import com.facishare.crm.fmcg.tpm.business.enums.UseRangeEnum;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * Author: linmj
 * Date: 2023/4/26 14:56
 */

@Slf4j
@Service
public class EnableCacheService implements IEnableCacheService {


    private static final String CACHE_PREFIX = "tpm_enable_%s#";

    private static final String ALL_CACHE_TEMPLATE = CACHE_PREFIX + "_all";
    private static final String STORE_CACHE_TEMPLATE = CACHE_PREFIX + "_store_%s";
    private static final long CACHE_TIME_BASE = 1800L;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private StoreBusiness storeBusiness;

    @Resource
    private TPM2Service tpm2Service;

    @Resource
    private IRangeFieldBusiness rangeFieldBusiness;

    @Resource(name = "redisCmd")
    private MergeJedisCmd redisCmd;

    @Autowired
    private OrganizationService organizationService;

    @Override
    public void resetCacheByAccount(String tenantId, String accountId, String changeReason, List<String> changeFields) {
        if (!tpm2Service.isTPM2Tenant(Integer.valueOf(tenantId))) {
            return;
        }
        IObjectData store = serviceFacade.findObjectData(User.systemUser(tenantId), accountId, ApiNames.ACCOUNT_OBJ);

        //如果是新建的、他不可能会出现在缓存里。
        if ("Edit".equals(changeReason)) {
            //todo:if contain cache then delete
            deleteStoreCache(tenantId, accountId);
            //todo: if account is dealer
            if (storeBusiness.findDealerRecordType(tenantId).contains(store.getRecordType())) {
                deleteAllL0CacheByDealer(tenantId, store);
            }
        }
    }

    @Override
    public void setALLCache(String tenantId, String userId) {
        String key = String.format(ALL_CACHE_TEMPLATE, tenantId);
        setZSetValue(key, userId, 2);
    }

    @Override
    public void setStoreCache(String tenantId, String userId, String storeId, Boolean isEnable) {
        String key = String.format(STORE_CACHE_TEMPLATE, tenantId, storeId);
        setZSetValue(key, userId, isEnable ? 1 : 0);
    }


    private void setZSetValue(String key, String member, int additionScore) {
        long now = System.currentTimeMillis() / 1000;
        Double redisValue = redisCmd.zscore(key, member);
        boolean setCache = redisValue == null;
        if (!setCache) {
            long timeOutBase = redisValue.longValue() / 10;
            long outTime = additionScore == 2 ? formAllOutTime(timeOutBase) : formOutTime(timeOutBase);
            long falseOutTime = formFalseOutTime(timeOutBase);
            setCache = redisValue.longValue() % 10 == 0 && now > falseOutTime || now > outTime;
        }

        if (setCache) {
            String keySet = key.substring(0, key.indexOf("#") + 1);
            redisCmd.pipeline(pipelineCmd -> {
                redisCmd.zadd(key, now * 10 + additionScore, member);
                redisCmd.sadd(keySet, key);
                redisCmd.expire(keySet, 12 * 3600L);
                if (redisCmd.ttl(key) == -1) {
                    redisCmd.expire(key, 8 * 3600L);
                }
            });
        }

    }


    @Override
    public void resetCacheByActivity(String tenantId, IObjectData activity) {

        if (TPMGrayUtils.skipEnableCheck(tenantId)) {
            return;
        }

        List<String> departmentIds = new ArrayList<>();
        if (!Strings.isNullOrEmpty(activity.get(TPMActivityFields.DEPARTMENT_RANGE, String.class))) {
            departmentIds.addAll(activity.get(TPMActivityFields.DEPARTMENT_RANGE, List.class));
        }
        if (!Strings.isNullOrEmpty(activity.get(TPMActivityFields.MULTI_DEPARTMENT_RANGE, String.class))) {
            activity.get(TPMActivityFields.MULTI_DEPARTMENT_RANGE, List.class).forEach(id -> departmentIds.add(String.valueOf(id)));
        }
        List<String> userIds = new ArrayList<>();
        for (String departmentId : departmentIds) {
            organizationService.queryEmployeeIds(Integer.parseInt(tenantId), Integer.parseInt(departmentId)).forEach(v -> userIds.add(String.valueOf(v)));
        }

        deleteAllTypeCache(tenantId, userIds);
        JSONObject storeRangeObj = JSON.parseObject(activity.get(TPMActivityFields.STORE_RANGE, String.class));
        if (storeRangeObj == null) {
            return;
        }
        //删除本来不在这里面的门店缓存
        if (UseRangeEnum.CONDITION.value().equals(storeRangeObj.getString("type"))) {
            //如果数量太多就删除了
            String conditionValue = storeRangeObj.getString("value");
            if (countConditionStore(tenantId, ApiNames.ACCOUNT_OBJ, conditionValue) > 2000) {
                deleteAllCacheByTenantId(tenantId);
                log.info("数据量过多，全部删除缓存tenantId:{},activityId:{},condition:{}", tenantId, activity.getId(), conditionValue);
            } else {
                List<String> ids = getConditionDataIds(tenantId, ApiNames.ACCOUNT_OBJ, conditionValue);
                log.info("stores that will be deleted are :{}", ids);
                deleteStoresCache(tenantId, ids);
            }
        } else if (UseRangeEnum.ALL.value().equals(storeRangeObj.getString("type"))) {
            deleteAllCacheByTenantId(tenantId);
        } else {
            List<String> fixedStores = Lists.newArrayList();
            if (!Strings.isNullOrEmpty(activity.get(TPMActivityFields.DEALER_ID, String.class))) {
                fixedStores.add(activity.get(TPMActivityFields.DEALER_ID, String.class));
            }
            fixedStores.addAll(getActivityFixedStores(tenantId, activity.getId()));
            if (CollectionUtils.isNotEmpty(fixedStores)) {
                deleteStoresCache(tenantId, fixedStores);
            }
        }
    }

    @Override
    public int getCache(String tenantId, String userId, String storeId) {
        long now = System.currentTimeMillis() / 1000;
        String allKey = String.format(ALL_CACHE_TEMPLATE, tenantId);
        Double allScore = redisCmd.zscore(allKey, userId);
        if (allScore != null && formAllOutTime(allScore.longValue() / 10) >= now) {
            return 1;
        }

        String storeKey = String.format(STORE_CACHE_TEMPLATE, tenantId, storeId);
        Double storeScore = redisCmd.zscore(storeKey, userId);
        if (storeScore != null) {
            long timeBase = storeScore.longValue() / 10;
            long outTime = formOutTime(timeBase);
            long falseOutTime = formFalseOutTime(timeBase);
            int value = (int) (storeScore.longValue() % 10);
            if (value == 1 && now <= outTime || value == 0 && now <= falseOutTime) {
                return value;
            }
        }
        return -1;
    }

    private long formOutTime(long timeBase) {
        return timeBase + CACHE_TIME_BASE * 3;
    }

    private long formAllOutTime(long timeBase) {
        return timeBase + CACHE_TIME_BASE * 10;
    }

    private long formFalseOutTime(long timeBase) {
        return timeBase + CACHE_TIME_BASE / 2;
    }

    private void deleteAllTypeCache(String tenantId, List<String> userIds) {
        String key = String.format(ALL_CACHE_TEMPLATE, tenantId);
        if (CollectionUtils.isNotEmpty(userIds)) {
            redisCmd.zrem(key, userIds.toArray(new String[0]));
        } else {
            deleteRedisKeys(String.format(CACHE_PREFIX, tenantId), new String[]{key});
        }
    }

    private void deleteAllCacheByTenantId(String tenantId) {
        String key = String.format(CACHE_PREFIX, tenantId);
        Set<String> keySets = redisCmd.smembers(key);
        if (keySets != null && !keySets.isEmpty()) {
            List<String> deleteList = new ArrayList<>(keySets);
            log.info("del key:" + deleteList);
            Lists.partition(deleteList, 200).forEach(keys -> {
                //脚本处理
                String[] delK = keys.toArray(new String[0]);
                deleteRedisKeys(key, delK);
            });
        }
    }

    private void deleteRedisKeys(String keyStoreSet, String[] keys) {
        redisCmd.pipeline((pipelineCmd -> {
            pipelineCmd.del(keys);
            pipelineCmd.srem(keyStoreSet, keys);
        }));
    }

    private int countConditionStore(String tenantId, String apiName, String queryPattern) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);
        query.setFilters(JSON.parseArray(queryPattern, IFilter.class));
        return serviceFacade.countObjectDataFromDB(tenantId, apiName, query);
    }

    private List<String> getConditionDataIds(String tenantId, String apiName, String queryPattern) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);
        query.setFilters(JSON.parseArray(queryPattern, IFilter.class));
        return CommonUtils.queryAllDataInFields(serviceFacade, User.systemUser(tenantId), apiName, query, Lists.newArrayList()).stream().map(DBRecord::getId).collect(Collectors.toList());
    }


    private void deleteStoreCache(String tenantId, String accountId) {
        deleteStoresCache(tenantId, Lists.newArrayList(accountId));
    }

    private void deleteStoresCache(String tenantId, List<String> accountIds) {
        List<String> keys = new ArrayList<>();
        String keyStoreSet = String.format(CACHE_PREFIX, tenantId);
        accountIds.forEach(id -> keys.add(String.format(STORE_CACHE_TEMPLATE, tenantId, id)));
        Lists.partition(keys, 200).forEach(partKeys -> deleteRedisKeys(keyStoreSet, partKeys.toArray(new String[0])));
    }

    private void deleteAllL0CacheByDealer(String tenantId, IObjectData dealer) {
        if (isNeedRefreshDealer(tenantId, dealer.getId())) {
            log.info("delete dealer cache");
            List<IObjectData> storesInDealer = getStoresInDealer(tenantId, dealer.getId());
            deleteStoresCache(tenantId, storesInDealer.stream().map(DBRecord::getId).collect(Collectors.toList()));
        }
    }

    private List<IObjectData> getConditionUnifiedActivity(String tenantId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(-1);

        Filter filter = new Filter();
        filter.setFieldName(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__IN_PROGRESS));
        query.setFilters(Lists.newArrayList(filter));

        Filter rangeFilter = new Filter();
        rangeFilter.setFieldName(TPMActivityUnifiedCaseFields.STORE_RANGE);
        rangeFilter.setOperator(Operator.CONTAINS);
        rangeFilter.setFieldValues(Lists.newArrayList("CONDITION"));
        query.setFilters(Lists.newArrayList(rangeFilter));

        return CommonUtils.queryAllDataInFields(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ, query, Lists.newArrayList(TPMActivityUnifiedCaseFields.STORE_RANGE));
    }

    private boolean isNeedRefreshDealer(String tenantId, String dealerId) {
        List<IObjectData> unifiedObjs = getConditionUnifiedActivity(tenantId);
        return rangeFieldBusiness.judgeDealerInActivitiesDealerRange(tenantId, dealerId, unifiedObjs).containsValue(true);
    }

    private List<IObjectData> getStoresInDealer(String tenantId, String dealerId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(-1);

        Filter filter = new Filter();
        filter.setFieldName(storeBusiness.findDealerFieldApiName(tenantId));
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(dealerId));
        query.setFilters(Lists.newArrayList(filter));

        return CommonUtils.queryAllDataInFields(serviceFacade, User.systemUser(tenantId), ApiNames.ACCOUNT_OBJ, query, Lists.newArrayList());
    }

    private List<String> getActivityFixedStores(String tenantId, String activityId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(-1);

        Filter filter = new Filter();
        filter.setFieldName(TPMActivityStoreFields.ACTIVITY_ID);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(activityId));
        query.setFilters(Lists.newArrayList(filter));

        return CommonUtils.queryAllDataInFields(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_STORE_OBJ, query, Lists.newArrayList(TPMActivityStoreFields.STORE_ID))
                .stream().map(v -> v.get(TPMActivityStoreFields.STORE_ID, String.class)).collect(Collectors.toList());
    }
}
