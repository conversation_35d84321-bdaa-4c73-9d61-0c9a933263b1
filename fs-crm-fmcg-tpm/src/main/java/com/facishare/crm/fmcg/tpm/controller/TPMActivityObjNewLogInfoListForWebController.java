package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.utils.convert.UseRangeFieldDataRender;
import com.facishare.paas.appframework.core.predef.controller.StandardNewLogInfoListForWebController;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogRecord;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.log.dto.ModifyRecord;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class TPMActivityObjNewLogInfoListForWebController extends StandardNewLogInfoListForWebController {
    private static final UseRangeFieldDataRender useRangeFieldDataRender = SpringUtil.getContext().getBean(UseRangeFieldDataRender.class);

    @Override
    protected LogRecord modifyRecordToLogRecord(ModifyRecord record) {
        LogRecord logRecord = super.modifyRecordToLogRecord(record);
        List<LogInfo.DiffObjectData> objectDataList = logRecord.getObjectData();
        if (CollectionUtils.isEmpty(objectDataList)) return logRecord;
        IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ);
        Map<String, IFieldDescribe> useRangeFieldMap = describe.getFieldDescribes().stream().filter(k -> IFieldType.UseRange.equals(k.getType())).collect(Collectors.toMap(IFieldDescribe::getApiName, o -> o));
        useRangeFieldDataRender.userRangeLogInfoHandle(objectDataList, useRangeFieldMap);
        logRecord.setObjectData(objectDataList);
        return logRecord;
    }
}
