package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;

import java.util.Map;

/**
 * Author: linmj
 * Date: 2023/4/18 10:54
 */
public interface IRangeFieldBusiness {

    String formDealerRangeRuleCode(String tenantId, String ruleJsonString);

    /**
     * @param tenantId
     * @param storeId
     * @param dealerId
     * @param enableActivities
     * @param fastJumpOut             是否快速化跳出
     * @param validateUnifiedActivity 是否校验统案范围
     * @return
     */
    Map<String, Boolean> judgeStoreInActivitiesStoreRange(String tenantId, String storeId, String dealerId, List<IObjectData> enableActivities, Boolean fastJumpOut, Boolean validateUnifiedActivity);

    Map<String, Boolean> judgeDealerInActivitiesDealerRange(String tenantId, String dealerId, List<IObjectData> unifiedActivities);

    String newUnifiedDealerRangeConditionCode(String tenantId, String storeRangeString);

    String formActivityStoreRangeRuleCode(String tenantId, String customerType, String ruleJsonString, String dealerId);

    String newActivityStoreRangeCondition(String tenantId, String customerType, String storeRangeString, String dealerId);

    Map<String, Boolean> judgeProductInActivitiesProductRange(String tenantId, String productId, List<IObjectData> activities, IObjectData serialNumber);

}
