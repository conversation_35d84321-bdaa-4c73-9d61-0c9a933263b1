package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardImportObjectController;
import com.facishare.paas.appframework.metadata.importobject.ImportType;

/**
 * author: wuyx
 * description:
 * createTime: 2022/7/13 11:30
 */
public class TPMBudgetTransferDetailObjImportObjectController extends StandardImportObjectController {

    @Override
    protected Result after(Arg arg, Result result) {
        log.info("init TPMBudgetTransferDetailObjImportObjectController after");
        Result after = super.after(arg, result);
        after.getImportObject().setSupportType(ImportType.UNSUPPORT_UPDATE_IMPORT);
        after.getImportObject().setIsApprovalFlow(true);
        return after;
    }
}
