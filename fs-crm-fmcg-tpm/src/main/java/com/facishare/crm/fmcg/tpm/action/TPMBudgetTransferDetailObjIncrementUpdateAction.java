package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;
import com.facishare.paas.metadata.api.IObjectData;
import de.lab4inf.math.util.Strings;

/**
 * author: wuyx
 * description:
 * createTime: 2022/7/14 10:46
 */
public class TPMBudgetTransferDetailObjIncrementUpdateAction extends StandardIncrementUpdateAction {

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        validationApprovalFlowStatus();
    }

    private void validationApprovalFlowStatus() {

        if (actionContext.isFromFunction() || actionContext.isFromOpenAPI()) {
            arg.getData().keySet().forEach(key -> {
                if (!Strings.isNullOrEmpty(key) && !key.endsWith("__c") && !key.equals("_id"))
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_TRANSFER_DETAIL_OBJ_INCREMENT_UPDATE_ACTION_0));
            });
        }
        IObjectData objectData = this.objectData;
        String lifeStatus = objectData.get(CommonFields.LIFE_STATUS, String.class, CommonFields.LIFE_STATUS__NORMAL);
        if (!CommonFields.LIFE_STATUS__INEFFECTIVE.equals(lifeStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TRANSFER_DETAIL_OBJ_INCREMENT_UPDATE_ACTION_1));
        }
    }
}
