package com.facishare.crm.fmcg.tpm.service;

import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.tpm.service.abstraction.ITPMI18nService;
import com.facishare.crm.fmcg.tpm.utils.DefaultI18nEntity;
import com.facishare.crm.fmcg.tpm.utils.I18nClientUtils;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.ButtonService;
import com.facishare.paas.appframework.core.predef.service.dto.button.FindButtonList;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fxiaoke.functions.utils.Maps;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.io.FileUtil.newFile;

/**
 * author: wuyx
 * description:
 * createTime: 2022/9/13 11:37
 */
@Slf4j
@SuppressWarnings("Duplicates")
@Component("tpmI18nService")
public class TPMI18nService implements ITPMI18nService {

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private ButtonService buttonService;

    private static List<String> EXCLUDE_FIELD = Lists.newArrayList("owner", "life_status", "record_type",
            "create_time", "created_by", "out_owner", "last_modified_time", "last_modified_by");

    private static List<String> EXCLUDE_BUTTON = Lists.newArrayList("Add_button_default", "Edit_button_default", "ChangeOwner_button_default",
            "Clone_button_default", "Lock_button_default", "Unlock_button_default", "Abolish_button_default", "AddTeamMember_button_default", "EditTeamMember_button_default",
            "DeleteTeamMember_button_default", "Print_button_default", "ChangePartnerOwner_button_default");

    private static List<String> EXCLUDE_LAY_OUT = Lists.newArrayList("layout_default");

    @Override
    public void saveOfApiName(long tenantId, String... apiNames) {
        if (apiNames.length <= 0) {
            return;
        }
        for (String apiName : apiNames) {

            //describe
            IObjectDescribe describe = serviceFacade.findObject(String.valueOf(tenantId), apiName);
            if (Objects.isNull(describe)) {
                continue;
            }
            List<IFieldDescribe> fieldDescribes = describe.getFieldDescribes();
            List<IFieldDescribe> preFields = fieldDescribes.stream().filter(filed ->
                    filed.isActive() && !filed.getApiName().endsWith("__c") && !EXCLUDE_FIELD.contains(filed.getApiName()))
                    .collect(Collectors.toList());

            List<Localization> localizations = Lists.newArrayList();
            for (IFieldDescribe field : preFields) {
                Localization localization = new Localization();
                if ("name".equals(field.getApiName())) {
                    localization.setKey(I18nClientUtils.getI18nNameKey(apiName));
                    localization.setTags(Lists.newArrayList("server", "android", "ios", "web", "desktop"));
                    localization.setZhCN(field.getLabel());
                    localizations.add(localization);
                } else if ("true_or_false".equals(field.getType()) || "select_one".equals(field.getType()) || "select_many".equals(field.getType())) {
                    localization.setKey(I18nClientUtils.getI18nObjFieldKey(apiName, field.getApiName()));
                    localization.setTags(Lists.newArrayList("server", "android", "ios", "web", "desktop"));
                    localization.setZhCN(field.getLabel());
                    localizations.add(localization);

                    List options = field.get("options", List.class);
                    for (Object option : options) {
                        JSONObject optionJs = (JSONObject) JSON.toJSON(option);
                        Localization optionLocalization = new Localization();
                        optionLocalization.setKey(I18nClientUtils.getI18nObjFieldOptionKey(apiName, field.getApiName(), optionJs.getString("value")));
                        optionLocalization.setTags(Lists.newArrayList("server", "android", "ios", "web", "desktop"));
                        optionLocalization.setZhCN(optionJs.getString("label"));
                        localizations.add(optionLocalization);
                    }
                } else {
                    localization.setKey(I18nClientUtils.getI18nObjFieldKey(apiName, field.getApiName()));
                    localization.setTags(Lists.newArrayList("server", "android", "ios", "web", "desktop"));
                    localization.setZhCN(field.getLabel());
                    localizations.add(localization);
                }
            }

            //layout
            List<ILayout> detailLayouts = serviceFacade.getLayoutLogicService().getDetailLayouts(String.valueOf(tenantId), describe);
            detailLayouts = detailLayouts.stream().filter(layout -> !EXCLUDE_LAY_OUT.contains(layout.get("apiName", String.class))).collect(Collectors.toList());
            for (ILayout layout : detailLayouts) {
                Localization localization = new Localization();
                localization.setKey(I18nClientUtils.getI18nLayOutKey(apiName, layout.get("apiName", String.class)));
                localization.setTags(Lists.newArrayList("server", "android", "ios", "web", "desktop"));
                localization.setZhCN(layout.get("display_name", String.class));
                localizations.add(localization);
            }

            //button
            List<ButtonDocument> buttonList = getButtonList(tenantId, apiName);
            if (CollectionUtils.isNotEmpty(buttonList)) {
                List<ButtonDocument> buttons = buttonList.stream().filter(button -> !EXCLUDE_BUTTON.contains(String.valueOf(button.get("api_name")))).collect(Collectors.toList());
                for (ButtonDocument button : buttons) {
                    Localization localization = new Localization();
                    localization.setKey(I18nClientUtils.getI18nButtonKey(apiName, String.valueOf(button.get("api_name"))));
                    localization.setTags(Lists.newArrayList("server", "android", "ios", "web", "desktop"));
                    localization.setZhCN(String.valueOf(button.get("label")));
                    localizations.add(localization);
                }
            }

            log.info("localizations:{}", JSON.toJSONString(localizations));

            System.out.println(JSON.toJSONString(localizations));
           /* if (CollectionUtils.isNotEmpty(localizations)) {
                I18nClientUtils.save(localizations);
            }*/
        }
    }

    @Override
    public void exImportReferenceByApiNames(long tenantId, String... apiNames) {
        if (apiNames.length <= 0) {
            return;
        }

        List<DefaultI18nEntity> i18nEntities = Lists.newArrayList();
        for (String apiName : apiNames) {
            IObjectDescribe describe = serviceFacade.findObject(String.valueOf(tenantId), apiName);
            if (Objects.isNull(describe)) {
                continue;
            }
            List<IFieldDescribe> fieldDescribes = describe.getFieldDescribes();
            List<IFieldDescribe> preFields = fieldDescribes.stream().filter(filed ->
                    filed.isActive() && !filed.getApiName().endsWith("__c") && !EXCLUDE_FIELD.contains(filed.getApiName()))
                    .collect(Collectors.toList());


            for (IFieldDescribe field : preFields) {
                if ("object_reference".equals(field.getType())) {
                    String key = I18nClientUtils.getReferenceKey(apiName, field.getApiName());
                    String chinese = field.get("target_related_list_label", String.class);
                    String tags = "pre_object";
                    i18nEntities.add(new DefaultI18nEntity(key, chinese, tags));
                    System.out.println("|" + key + "|" + chinese + "|" + tags + "|");
                }
            }

            if (CollectionUtils.isNotEmpty(i18nEntities)) {

            }

        }
    }

    @Override
    public void exImportLayoutByApiNames(long tenantId, String... apiNames) {
        List<DefaultI18nEntity> i18nEntities = Lists.newArrayList();

        Map<String, String> map = Maps.newLinkedHashMap();
        for (String apiName : apiNames) {
            IObjectDescribe describe = serviceFacade.findObject(String.valueOf(tenantId), apiName);
            List<ILayout> detailLayouts = serviceFacade.getLayoutLogicService().getDetailLayouts(String.valueOf(tenantId), describe);
            //detailLayouts = detailLayouts.stream().filter(layout -> !EXCLUDE_LAY_OUT.contains(layout.get("apiName", String.class))).collect(Collectors.toList());
            for (ILayout layout : detailLayouts) {
                try {
                    IComponent component = layout.getComponents().stream().filter(iComponent -> "form_component".equals(iComponent.getName())).collect(Collectors.toList()).get(0);
                    // todo
                    LayoutExt of = LayoutExt.of((ILayout) component);
                    String enmuName = "";
                    String value = "";
                    String key = I18nClientUtils.getI18nLayOutMenuKey(apiName, layout.getLayoutType(), layout.getName(), enmuName);
                    map.put(key, value);
                    List list = component.get("component", List.class);
                } catch (MetadataServiceException e) {
                    e.printStackTrace();
                }
            }

            map.forEach((s, s2) -> {
                System.out.println(s);
            });
            map.forEach((s, s2) -> {
                System.out.println(s2);
            });
        }
    }

    private List<ButtonDocument> getButtonList(long tenantId, String apiName) {
        RequestContext requestContext = RequestContext.builder().tenantId(String.valueOf(tenantId)).user(new User(String.valueOf(tenantId), "1000")).build();
        ServiceContext context = new ServiceContext(requestContext, null, null);

        FindButtonList.Arg arg = new FindButtonList.Arg();
        arg.setDescribeApiName(apiName);
        arg.setExcludeUIButton(false);
        FindButtonList.Result buttonList = buttonService.findButtonList(arg, context);
        return buttonList.getButtonList();
    }


    public void createExcel() {
        try {

            HSSFWorkbook workbook = new HSSFWorkbook();


        } catch (Exception e) {

        }
    }
}
