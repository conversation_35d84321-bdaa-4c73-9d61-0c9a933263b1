package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportDataAction;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class WithdrawRecordObjUnionInsertImportDataAction extends StandardUnionInsertImportDataAction {


    @Override
    protected void before(Arg arg) {
        throw new ValidateException("action not allowed!");
    }
}
