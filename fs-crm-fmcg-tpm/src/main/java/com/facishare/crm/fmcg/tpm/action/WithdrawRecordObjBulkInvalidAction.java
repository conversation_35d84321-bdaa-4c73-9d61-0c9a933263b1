package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;

/**
 * Author: linmj
 * Date: 2023/10/30 15:33
 */
public class WithdrawRecordObjBulkInvalidAction extends StandardBulkInvalidAction {

    @Override
    protected void before(Arg arg) {
       throw new ValidateException("invalid action not allowed!");
    }
}
