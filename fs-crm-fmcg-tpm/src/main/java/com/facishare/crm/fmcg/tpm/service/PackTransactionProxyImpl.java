package com.facishare.crm.fmcg.tpm.service;

import com.facishare.crm.fmcg.tpm.service.abstraction.PackTransactionProxy;
import com.facishare.crm.fmcg.tpm.service.abstraction.TransactionService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.UndeclaredThrowableException;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@Transactional
public class PackTransactionProxyImpl implements PackTransactionProxy {

    @Override
    public <A, R> R packAct(TransactionService<A, R> service, A arg) {
        try {
            return service.doActTransaction(arg);
        } catch (UndeclaredThrowableException ex) {
            log.error("do action transaction cause unknown exception : ", ex);

            throw new ValidateException("do action transaction cause unknown exception.");
        }
    }
}
