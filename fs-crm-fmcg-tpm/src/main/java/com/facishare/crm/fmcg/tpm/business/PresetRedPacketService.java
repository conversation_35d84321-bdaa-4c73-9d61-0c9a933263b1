package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.business.abstraction.IPresetRedPacketService;
import com.facishare.crm.fmcg.tpm.service.abstraction.IRoleService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

//IgnoreI18nFile
@Slf4j
@Component
public class PresetRedPacketService implements IPresetRedPacketService {


    @Resource
    private ServiceFacade serviceFacade;


    @Resource(name = "tpmRoleService")
    protected IRoleService roleService;

    @Resource
    private EIEAConverter eieaConverter;

    protected static final Map<String, String> EVENT_TYPE_VALUE_MAP = Maps.newHashMap();
    protected static final Map<String, String> ROLE_VALUE_MAP = Maps.newHashMap();
    protected static final Map<String, String> ROLE_VALUE_MAP_V2 = Maps.newHashMap();
    protected static final Map<String, String> ROLE_VALUE_MAP_REVERSE = Maps.newHashMap();
    protected static final Map<String, String> ACCOUNT_TYPE_VALUE_MAP = Maps.newHashMap();
    protected static final Map<String, String> ACCOUNT_TYPE_VALUE_MAP_V2 = Maps.newHashMap();

    protected static final Map<String, String> EVENT_TYPE_VALUE_MAP_V2 = Maps.newHashMap();

    public static final String ACTIVITY_REWARD = "activityIncentives";

    static {

        EVENT_TYPE_VALUE_MAP.put("SIGN_IN_GOODS", "1");
        EVENT_TYPE_VALUE_MAP.put("CONSUMER_SCAN_CODE", "2");
        EVENT_TYPE_VALUE_MAP.put("USER_OBJECT_ACTION_REWARDS", "3");

        EVENT_TYPE_VALUE_MAP_V2.put("1", "STORE_SIGN");
        EVENT_TYPE_VALUE_MAP_V2.put("2", "CONSUMER_SCAN_CODE");
        EVENT_TYPE_VALUE_MAP_V2.put("3", "USER_OBJECT_ACTION_REWARDS");

        ROLE_VALUE_MAP.put("CONSUMER", "1");
        ROLE_VALUE_MAP.put("STORE_OWNER", "2");
        ROLE_VALUE_MAP.put("SALESMEN", "3");
        ROLE_VALUE_MAP.put("DEALER", "4");
        ROLE_VALUE_MAP.put("M_BOSS", "5");

        ROLE_VALUE_MAP_V2.put("1", "消费者");
        ROLE_VALUE_MAP_V2.put("2", "店老板");
        ROLE_VALUE_MAP_V2.put("3", "业代");
        ROLE_VALUE_MAP_V2.put("4", "经销商");
        ROLE_VALUE_MAP_V2.put("5", "经销商老板");

        ROLE_VALUE_MAP_REVERSE.put("1", "CONSUMER");
        ROLE_VALUE_MAP_REVERSE.put("2", "STORE_OWNER");
        ROLE_VALUE_MAP_REVERSE.put("3", "SALESMEN");
        ROLE_VALUE_MAP_REVERSE.put("4", "DEALER");
        ROLE_VALUE_MAP_REVERSE.put("5", "M_BOSS");

        ACCOUNT_TYPE_VALUE_MAP.put("WeChat", "1");
        ACCOUNT_TYPE_VALUE_MAP.put("TenantCloud", "2");
        ACCOUNT_TYPE_VALUE_MAP.put("AliPay", "3");
        ACCOUNT_TYPE_VALUE_MAP.put("Bank", "4");
        ACCOUNT_TYPE_VALUE_MAP.put("TenantWeChat", "5");

        ACCOUNT_TYPE_VALUE_MAP_V2.put("WeChat", "wechat");
        ACCOUNT_TYPE_VALUE_MAP_V2.put("TenantCloud", "cloud");
        ACCOUNT_TYPE_VALUE_MAP_V2.put("TenantWeChat", "wechat");
    }


    @Override
    public void presetRedPacket(String tenantId, String redPacketIds, String eventType) {
        if (Strings.isNullOrEmpty(tenantId)) {
            log.info("presetRedPacket tenantId is null");
            return;
        }
        User user = User.systemUser(tenantId);
        SearchTemplateQuery searchTemplateQuery = QueryDataUtil.minimumQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(100);

        List<IFilter> filterList = Lists.newArrayList();
        if (!Strings.isNullOrEmpty(redPacketIds)) {
            IFilter idFilter = new Filter();
            idFilter.setFieldName(CommonFields.ID);
            idFilter.setOperator(Operator.IN);
            idFilter.setFieldValues(Arrays.stream(redPacketIds.split(",")).collect(Collectors.toList()));
            filterList.add(idFilter);
        }
        if (!"all".equals(eventType)){
            IFilter eventTypeFilter = new Filter();
            eventTypeFilter.setFieldName(RedPacketRecordFields.EVENT_TYPE);
            eventTypeFilter.setOperator(Operator.EQ);
            eventTypeFilter.setFieldValues(Lists.newArrayList(eventType));
            filterList.add(eventTypeFilter);
        }

        IFilter payStatusFilter = new Filter();
        payStatusFilter.setFieldName(RedPacketRecordFields.PAYMENT_STATUS);
        payStatusFilter.setOperator(Operator.IN);
        payStatusFilter.setFieldValues(Lists.newArrayList("2"));
        filterList.add(payStatusFilter);

        IFilter presetFilter = new Filter();
        presetFilter.setFieldName("preset__c");
        presetFilter.setOperator(Operator.N);
        presetFilter.setFieldValues(Lists.newArrayList("1"));
        filterList.add(presetFilter);
        searchTemplateQuery.setFilters(filterList);


        // todo 转账异常的，可以重发的，考虑不同步数据，因为可能会重发。
        List<IObjectData> objectDataList;
        do {
            objectDataList = QueryDataUtil.find(serviceFacade, tenantId, RedPacketRecordFields.API_NAME, searchTemplateQuery);
            if (CollectionUtils.isEmpty(objectDataList)) {
                break;
            }

            List<IObjectData> presetObjectDataList = buildPresetObjectData(objectDataList);
            if (CollectionUtils.isNotEmpty(presetObjectDataList)) {
                List<IObjectData> objectData = serviceFacade.bulkSaveObjectData(presetObjectDataList, user);
                if (CollectionUtils.isNotEmpty(objectData)) {
                    //更新表示已同步的字段 preset__c
                    Map<String, Object> updateMap = new HashMap<>(4);
                    updateMap.put("preset__c", "1");
                    serviceFacade.batchUpdateWithMap(user, objectDataList, updateMap);

                    List<String> ids = objectData.stream().map(IObjectData::getId).collect(Collectors.toList());
                    presetRedPacketDetailV2(tenantId, ids);
                }
            }
            searchTemplateQuery.setOffset(searchTemplateQuery.getOffset() + objectDataList.size());
        } while (objectDataList.size() >= 100);
    }

    private void presetRedPacketDetailV2(String tenantId, List<String> redPacketIds) {
        User user = User.systemUser(tenantId);
        SearchTemplateQuery searchTemplateQuery = QueryDataUtil.minimumQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(100);

        if (CollectionUtils.isNotEmpty(redPacketIds)) {
            IFilter idFilter = new Filter();
            idFilter.setFieldName(RedPacketRecordDetailFields.RED_PACKET_RECORD_ID);
            idFilter.setOperator(Operator.IN);
            idFilter.setFieldValues(redPacketIds);
            searchTemplateQuery.setFilters(Lists.newArrayList(idFilter));
        }

        List<IObjectData> objectDataList;
        do {
            objectDataList = QueryDataUtil.find(serviceFacade, tenantId, RedPacketRecordDetailFields.API_NAME, searchTemplateQuery);
            if (CollectionUtils.isEmpty(objectDataList)) {
                break;
            }

            List<IObjectData> presetObjectDataList = buildPresetObjectDetailData(objectDataList);
            if (CollectionUtils.isNotEmpty(presetObjectDataList)) {
                serviceFacade.bulkSaveObjectData(presetObjectDataList, user);
            }
            searchTemplateQuery.setOffset(searchTemplateQuery.getOffset() + objectDataList.size());
        } while (objectDataList.size() >= 100);
    }

    private List<IObjectData> buildPresetObjectDetailData(List<IObjectData> objectDataList) {
        List<IObjectData> dataList = new ArrayList<>();
        for (IObjectData objectData : objectDataList) {
            IObjectData datum = new ObjectData();
            datum.setTenantId(objectData.getTenantId());
            datum.setDescribeApiName(ApiNames.RED_PACKET_RECORD_DETAIL_OBJ);
            datum.setOwner(objectData.getOwner());
            datum.setRecordType(CommonFields.RECORD_TYPE__DEFAULT);

            datum.set(RedPacketRecordDetailObjFields.RED_PACKET_RECORD_ID, objectData.get(RedPacketRecordDetailFields.RED_PACKET_RECORD_ID, String.class));

            datum.set(RedPacketRecordDetailObjFields.SALES_ORDER_DETAIL_NAME, objectData.get(RedPacketRecordDetailFields.SALES_ORDER_DETAIL_NAME, String.class));
            datum.set(RedPacketRecordDetailObjFields.SALES_ORDER_DETAIL_ID, objectData.get(RedPacketRecordDetailFields.SALES_ORDER_DETAIL_ID, String.class));

            datum.set(RedPacketRecordDetailObjFields.PRODUCT_ID, objectData.get(RedPacketRecordDetailFields.PRODUCT_ID, String.class));

            datum.set(RedPacketRecordDetailObjFields.SERIAL_NUMBER_ID, objectData.get(RedPacketRecordDetailFields.SERIAL_NUMBER_ID, String.class));
            datum.set(RedPacketRecordDetailObjFields.MANUFACTURE_DATE, objectData.get(RedPacketRecordDetailFields.MANUFACTURE_DATE, String.class));
            datum.set(RedPacketRecordDetailObjFields.BATCH_CODE, objectData.get(RedPacketRecordDetailFields.BATCH_CODE, String.class));

            datum.set(RedPacketRecordDetailObjFields.RED_PACKET_AMOUNT, objectData.get(RedPacketRecordDetailFields.AMOUNT, String.class));
            datum.set(RedPacketRecordDetailObjFields.AMOUNT_CONFIG_ID, objectData.get(RedPacketRecordDetailFields.AMOUNT_CONFIG_ID, String.class));

            dataList.add(datum);
        }
        return dataList;
    }

    private List<IObjectData> buildPresetObjectData(List<IObjectData> objectDataList) {
        List<IObjectData> dataList = new ArrayList<>();
        for (IObjectData objectData : objectDataList) {
            IObjectData data = new ObjectData();
            data.setTenantId(objectData.getTenantId());
            data.setDescribeApiName(ApiNames.RED_PACKET_RECORD_OBJ);
            data.setRecordType(CommonFields.RECORD_TYPE__DEFAULT);
            data.setOwner(objectData.getOwner());

            data.set(RedPacketRecordObjFields.EVENT_TYPE, ACTIVITY_REWARD);
            data.set(RedPacketRecordObjFields.TRIGGER_EVENT, EVENT_TYPE_VALUE_MAP_V2.get(objectData.get(RedPacketRecordFields.EVENT_TYPE,String.class)));
            data.set(RedPacketRecordObjFields.ACTIVITY_ID, objectData.get(RedPacketRecordFields.ACTIVITY_ID));
            data.set(RedPacketRecordObjFields.REWARD_PART, ROLE_VALUE_MAP_V2.get(objectData.get(RedPacketRecordFields.ROLE, String.class)));
            data.set(RedPacketRecordObjFields.REWARD_AMOUNT, objectData.get(RedPacketRecordFields.AMOUNT));

            data.set(RedPacketRecordObjFields.RELATED_OBJECT_API_NAME, objectData.get(RedPacketRecordFields.EVENT_OBJECT_API_NAME));
            data.set(RedPacketRecordObjFields.RELATED_OBJECT_NAME, objectData.get(RedPacketRecordFields.EVENT_OBJECT_NAME));
            data.set(RedPacketRecordObjFields.RELATED_OBJECT_TENANT_ID, objectData.get(RedPacketRecordFields.EVENT_OBJECT_TENANT_ID));

            data.set(RedPacketRecordObjFields.RELATED_OBJECT_TENANT_NAME, objectData.get(RedPacketRecordFields.EVENT_OBJECT_TENANT_NAME));
            data.set(RedPacketRecordObjFields.RELATED_OBJECT_DATA_ID, objectData.get(RedPacketRecordFields.EVENT_OBJECT_DATA_ID));
            data.set(RedPacketRecordObjFields.ACCOUNT_NAME, objectData.get(RedPacketRecordFields.RELATED_STORE_NAME));
            data.set(RedPacketRecordObjFields.ACCOUNT_ID, objectData.get(RedPacketRecordFields.RELATED_STORE_ID));
            data.set(RedPacketRecordObjFields.TRANSFEROR_ACCOUNT_TYPE, ACCOUNT_TYPE_VALUE_MAP_V2.get(objectData.get(RedPacketRecordFields.FROM_ACCOUNT_TYPE, String.class)));
            data.set(RedPacketRecordObjFields.TRANSFEROR_NAME, objectData.get(RedPacketRecordFields.FROM_REAL_NAME));
            data.set(RedPacketRecordObjFields.TRANSFEROR_ID, objectData.get(RedPacketRecordFields.FROM_ID_CARD_NUMBER));
            data.set(RedPacketRecordObjFields.TRANSFEROR_WECHAT_OPEN_ID, objectData.get(RedPacketRecordFields.FROM_WECHAT_OPEN_ID));
            data.set(RedPacketRecordObjFields.TRANSFEROR_PHONE, objectData.get(RedPacketRecordFields.FROM_PHONE_NUMBER));

            String cloudAccountDealerId = objectData.get(RedPacketRecordFields.FROM_CLOUD_ACCOUNT_DEALER_ID, String.class);
            String cloudAccountBrokerId = objectData.get(RedPacketRecordFields.FROM_CLOUD_ACCOUNT_BROKER_ID, String.class);
            data.set(RedPacketRecordObjFields.FROM_CLOUD_ACCOUNT_DEALER_ID, cloudAccountDealerId);
            data.set(RedPacketRecordObjFields.FROM_CLOUD_ACCOUNT_BROKER_ID, cloudAccountBrokerId);
            if (Strings.isNullOrEmpty(cloudAccountDealerId) && Strings.isNullOrEmpty(cloudAccountBrokerId)) {
                data.set(RedPacketRecordObjFields.TRANSFEROR_ACCOUNT, "default");
            } else {
                JSONObject cloudAccountMap = new JSONObject();
                cloudAccountMap.put("dealerId", cloudAccountDealerId);
                cloudAccountMap.put("brokerId", cloudAccountBrokerId);
                data.set(RedPacketRecordObjFields.TRANSFEROR_ACCOUNT, cloudAccountMap.toString());
            }
            String tenantAccount = objectData.get(RedPacketRecordFields.FROM_CLOUD_ACCOUNT_NAME, String.class);
            data.set(RedPacketRecordObjFields.TRANSFEROR_TENANT_NAME, tenantAccount);
            if (!Strings.isNullOrEmpty(tenantAccount)) {
                data.set(RedPacketRecordObjFields.TRANSFEROR_TENANT_ID, eieaConverter.enterpriseAccountToId(tenantAccount));
            }

            data.set(RedPacketRecordObjFields.TRANSFEREE_ACCOUNT_TYPE, ACCOUNT_TYPE_VALUE_MAP_V2.get(objectData.get(RedPacketRecordFields.TO_ACCOUNT_TYPE)));
            data.set(RedPacketRecordObjFields.TRANSFEREE_TENANT_ID, objectData.get(RedPacketRecordFields.EVENT_OBJECT_TENANT_ID, String.class));
            data.set(RedPacketRecordObjFields.TRANSFEREE_TENANT_NAME, objectData.get(RedPacketRecordFields.EVENT_OBJECT_TENANT_NAME, String.class));

            data.set(RedPacketRecordObjFields.TRANSFEREE_NAME, objectData.get(RedPacketRecordFields.TO_REAL_NAME, String.class));
            data.set(RedPacketRecordObjFields.TRANSFEREE_ID, objectData.get(RedPacketRecordFields.TO_ID_CARD_NUMBER, String.class));
            data.set(RedPacketRecordObjFields.TRANSFEREE_WECHAT_OPEN_ID, objectData.get(RedPacketRecordFields.TO_WECHAT_OPEN_ID, String.class));
            data.set(RedPacketRecordObjFields.TRANSFEREE_PHONE, objectData.get(RedPacketRecordFields.TO_PHONE_NUMBER, String.class));
            data.set(RedPacketRecordObjFields.TRANSFEREE_WECHAT_APP_ID, objectData.get(RedPacketRecordFields.TO_WX_APP_ID, String.class));
            // 奖励人名称
            data.set(RedPacketRecordObjFields.REWARDED_PERSON, objectData.get(RedPacketRecordFields.TO_REAL_NAME, String.class));
            data.set(RedPacketRecordObjFields.TRANSFEREE_WX_UNION_ID, objectData.get(RedPacketRecordFields.TO_WX_UNION_ID, String.class));


            data.set(RedPacketRecordObjFields.RECORD_IDENTITY, objectData.get(RedPacketRecordFields.RECORD_IDENTITY));
            data.set(RedPacketRecordObjFields.RED_PACKET_LEVEL, objectData.get(RedPacketRecordFields.RED_PACKET_LEVEL));
            data.set(RedPacketRecordObjFields.BUSINESS_ID, objectData.get(RedPacketRecordFields.PAYMENT_BUSINESS_ID));
            data.set(RedPacketRecordObjFields.TRADING_ID, objectData.get(RedPacketRecordFields.PAYMENT_ORDER_ID));
            data.set(RedPacketRecordObjFields.PAYMENT_STATUS, objectData.get(RedPacketRecordFields.PAYMENT_STATUS));
            data.set(RedPacketRecordObjFields.REMARKS, objectData.get(RedPacketRecordFields.REMARKS));
            data.set(RedPacketRecordObjFields.IS_OVER_LIMIT, objectData.get(RedPacketRecordFields.IS_OVER_LIMIT));


            data.set(RedPacketRecordObjFields.DISTRIBUTE_WAY, RedPacketRecordObjFields.DistributeWay.AUTO);
            data.set(RedPacketRecordObjFields.WITHDRAWAL_STATUS, RedPacketRecordObjFields.WithdrawalStatus.NO_NEED);
            // 预知对象新字段 需要有
            data.set(RedPacketRecordObjFields.RED_PACKET_STATUS, RedPacketRecordObjFields.RedPacketStatus.EFFECTUATE);
            data.set(RedPacketRecordObjFields.REWARD_TIME, objectData.get(RedPacketRecordFields.EVENT_TIME));
            data.set(RedPacketRecordObjFields.PAYMENT_ERROR_MESSAGE, objectData.get(RedPacketRecordFields.PAYMENT_ERROR_MESSAGE));
//            data.set(RedPacketRecordObjFields.EXPIRATION_TIME, reward.getExpirationTime());
//            data.set(RedPacketRecordObjFields.REWARDED_PERSON_ID, reward.getRewardPersonId());
            // 角色信息
//            fillRewardRoleInfo(reward.getTenantId(), reward.getRewardPersonId(), reward.getRewardPersonType(), data);
            dataList.add(data);
        }
        return dataList;
    }
}
