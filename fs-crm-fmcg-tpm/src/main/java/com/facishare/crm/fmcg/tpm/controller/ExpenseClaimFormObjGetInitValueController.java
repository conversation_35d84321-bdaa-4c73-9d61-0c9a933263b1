package com.facishare.crm.fmcg.tpm.controller;

import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.common.apiname.ExpenseClaimFormFields;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: linmj
 * Date: 2023/5/8 14:41
 */
public class ExpenseClaimFormObjGetInitValueController extends PreDefineController<ExpenseClaimFormObjGetInitValueController.Arg, ExpenseClaimFormObjGetInitValueController.Result> {


    private static final OrganizationService organizationService = SpringUtil.getContext().getBean(OrganizationService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected Result doService(Arg arg) {
        Map<String, Object> initData = new HashMap<>();
        String userId = controllerContext.getUser().getUpstreamOwnerIdOrUserId();
        initData.put(ExpenseClaimFormFields.APPLICANT, Lists.newArrayList(userId));
        EmployeeDto employeeDto = organizationService.getEmployee(Integer.parseInt(controllerContext.getTenantId()), Integer.parseInt(userId));
        if (CollectionUtils.isNotEmpty(employeeDto.getMainDepartmentIds())) {
            initData.put(ExpenseClaimFormFields.APPLIED_DEPARTMENT, Lists.newArrayList(String.valueOf(employeeDto.getMainDepartmentId())));
        }
        initData.put(ExpenseClaimFormFields.PAYEE, Lists.newArrayList(userId));

        initData.put(ExpenseClaimFormFields.APPLICATION_DATE, System.currentTimeMillis());
        return new Result(initData);
    }

    @Data
    @ToString
    public static class Arg implements Serializable {

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString
    public static class Result implements Serializable {
        private Map<String, Object> initData;
    }
}
