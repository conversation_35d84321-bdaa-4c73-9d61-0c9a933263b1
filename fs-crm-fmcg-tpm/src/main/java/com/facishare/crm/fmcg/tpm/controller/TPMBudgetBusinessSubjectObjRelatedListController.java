package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetSubjectService;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetDimensionEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

@SuppressWarnings("Duplicates,unused")
public class TPMBudgetBusinessSubjectObjRelatedListController extends StandardRelatedListController {

    public final IBudgetSubjectService budgetSubjectService = SpringUtil.getContext().getBean(IBudgetSubjectService.class);
    public final BudgetTypeDAO budgetTypeDAO = SpringUtil.getContext().getBean(BudgetTypeDAO.class);

    private boolean isDisassemblyNewDetailObj = false;
    private BudgetTypeNodeEntity targetNode;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        loadDataForDisassemblyNewDetailObj();
    }

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        super.beforeQueryData(query);
        overrideQueryForDisassemblyNewDetail(query);
    }

    private void overrideQueryForDisassemblyNewDetail(SearchTemplateQuery query) {
        if (this.isDisassemblyNewDetailObj) {
            if (CollectionUtils.isEmpty(query.getFilters())) {
                query.setFilters(Lists.newArrayList());
            }

            //只查转入模板层级的科目
            int targetLevel = -1;
            for (BudgetDimensionEntity dimension : this.targetNode.getDimensions()) {
                if (Objects.equals(TPMBudgetAccountFields.BUDGET_SUBJECT_ID, dimension.getApiName())) {
                    targetLevel = dimension.getLevel();
                    break;
                }
            }

            String parentAccountId = arg.getObjectData().toObjectData().get(TPMBudgetDisassemblyFields.SOURCE_BUDGET_ACCOUNT_ID, String.class);
            IObjectData parentAccount = serviceFacade.findObjectDataIgnoreAll(User.systemUser(controllerContext.getTenantId()), parentAccountId, ApiNames.TPM_BUDGET_ACCOUNT);
            String parentSubjectId = parentAccount.get(TPMBudgetAccountFields.BUDGET_SUBJECT_ID, String.class);

            if (StringUtils.isEmpty(parentSubjectId)) {
                List<IFilter> filters = Lists.newArrayList();
                //查转入级别的
                addFilters(filters, TPMBudgetBusinessSubjectFields.SUBJECT_LEVEL, Operator.EQ, Lists.newArrayList(String.valueOf(targetLevel)));

                query.getFilters().addAll(filters);

                return;
            }
            List<String> ids = budgetSubjectService.queryLowerIds(controllerContext.getTenantId(), parentSubjectId, targetLevel);
            if (CollectionUtils.isEmpty(ids)) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_BUSINESS_SUBJECT_OBJ_RELATED_LIST_CONTROLLER_0));
            }
            IFilter idFilter = new Filter();
            idFilter.setFieldName(CommonFields.ID);
            idFilter.setOperator(Operator.IN);
            idFilter.setFieldValues(ids);

            query.getFilters().add(idFilter);
        }
    }

    private void addFilters(List<IFilter> filters, String apiName, Operator operator, List<String> values) {
        IFilter filter = new Filter();
        filter.setFieldName(apiName);
        filter.setOperator(operator);
        filter.setFieldValues(values);

        filters.add(filter);

    }

    private void loadDataForDisassemblyNewDetailObj() {
        if (!"target_related_list_TPMBudgetDisassemblyNewDetailObj_TPMBudgetBusinessSubjectObj__c".equals(arg.getRelatedListName())) {
            return;
        }

        String objectDescribeApiName = (String) arg.getObjectData().get("object_describe_api_name");
        if (!ApiNames.TPM_BUDGET_DISASSEMBLY_NEW_DETAIL_OBJ.equals(objectDescribeApiName)) {
            return;
        }

        this.isDisassemblyNewDetailObj = true;

        IObjectData masterData = arg.getObjectData().toObjectData();

        // 加载预算类型信息
        String typeId = masterData.get(TPMBudgetDisassemblyFields.BUDGET_TYPE_ID, String.class);
        if (Strings.isNullOrEmpty(typeId)) {
            throw new ValidateException("[budget_type_id] can not be null or empty.");
        }
        BudgetTypePO budgetType = budgetTypeDAO.get(controllerContext.getTenantId(), typeId);
        if (Objects.isNull(budgetType)) {
            throw new ValidateException("budget type not found.");
        }

        //加载拆解转出预算节点信息
        String sourceNodeId = masterData.get(TPMBudgetDisassemblyFields.SOURCE_BUDGET_NODE_ID, String.class);
        BudgetTypeNodeEntity parentNode = budgetType.getNodes().stream().filter(f -> f.getNodeId().equals(sourceNodeId)).findFirst().orElse(null);
        if (Objects.isNull(parentNode)) {
            throw new ValidateException("source node not found.");
        }

        //加载拆解转入预算节点信息
        String targetNodeId = masterData.get(TPMBudgetDisassemblyFields.TARGET_BUDGET_NODE_ID, String.class);
        this.targetNode = budgetType.getNodes().stream().filter(f -> f.getNodeId().equals(targetNodeId)).findFirst().orElse(null);
        if (Objects.isNull(this.targetNode)) {
            throw new ValidateException("target node not found.");
        }

        if (!Objects.equals(targetNode.getParentNodeId(), parentNode.getNodeId())) {
            throw new ValidateException(String.format("Only child nodes of [%s] are allowed to be disassembled.", parentNode.getName()));
        }
    }
}
