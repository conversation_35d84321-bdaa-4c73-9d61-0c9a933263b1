package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;

import java.util.List;

public class TPMActivityRewardDetailObjWebDetailController extends StandardWebDetailController {


    @Override
    protected Result after(Arg arg, Result result) {
        buttonFilter(result);
        return super.after(arg, result);
    }


    private void buttonFilter(Result result) {
        try {

            List<IComponent> components = LayoutExt.of(result.getLayout()).getComponents();

            for (IComponent component : components) {
                if ("head_info".equals(component.get("api_name"))) {
                    List<IButton> buttons = component.getButtons();
                    buttons.removeIf(button -> ObjectAction.UPDATE.getActionCode().equals(button.getAction()));
                    component.setButtons(buttons);
                }
            }
            if ("mobile".equals(arg.getLayoutAgentType())) {
                ILayout iLayout = result.getLayout().toLayout();
                List<IButton> buttons = iLayout.getButtons();
                buttons.removeIf(button -> ObjectAction.UPDATE.getActionCode().equals(button.getAction()));
                iLayout.setButtons(buttons);
                result.setLayout(LayoutDocument.of(iLayout));
            }

        } catch (MetadataServiceException e) {
            throw new ValidateException(e.getMessage());
        }
    }
}
