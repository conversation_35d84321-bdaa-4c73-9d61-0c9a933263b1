package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.ButtonApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityAgreementFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.business.abstraction.IActivityProofButtonService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import lombok.SneakyThrows;

import java.util.List;

/**
 * Author: linmj
 * Date: 2024/1/18 15:24
 */
public class TPMActivityAgreementObjWebDetailController extends StandardWebDetailController {

    private IActivityProofButtonService activityProofButtonService = SpringUtil.getContext().getBean(IActivityProofButtonService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Result finalResult = super.after(arg, result);
        buttonFilter(finalResult);
        return finalResult;
    }


    @SneakyThrows
    private void buttonFilter(Result result) {
        IObjectData agreement = result.getData().toObjectData();
        String activityId = agreement.get(TPMActivityAgreementFields.ACTIVITY_ID, String.class);
        activityProofButtonService.filterRioStoreConfirmButton(controllerContext.getUser(), activityId, arg.getLayoutAgentType(), result);
    }
}
