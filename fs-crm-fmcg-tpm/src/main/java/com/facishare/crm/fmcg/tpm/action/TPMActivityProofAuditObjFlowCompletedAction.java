package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofAuditFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetBusinessSubjectFields;
import com.facishare.crm.fmcg.tpm.business.BudgetConsumeV2Service;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActionModeType;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageQueue;

/**
 * <AUTHOR>
 * @date 2022/7/18 上午11:29
 */
@Slf4j
public class TPMActivityProofAuditObjFlowCompletedAction extends StandardFlowCompletedAction {

    private static final BudgetConsumeV2Service budgetConsumeV2Service = SpringUtil.getContext().getBean(BudgetConsumeV2Service.class);


    @Override
    protected void before(Arg arg) {
        super.before(arg);
        boolean isRandomAudit = Boolean.TRUE.equals(callbackData.get(TPMActivityProofAuditFields.CALLBACK_FLAG_IS_RANDOM_AUDIT));
        log.info("is random audit : {}", isRandomAudit);
        //消费规则
        if ("pass".equalsIgnoreCase(arg.getStatus()) && isRandomAudit) {
            budgetConsumeV2Service.middleRelease(actionContext.getUser(), arg.getDescribeApiName(), arg.getDataId(), ActionModeType.RANDOM_AUDIT.getKey());
        }

    }
}
