package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMProofTimePeriodDetailFields;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.api.proof.CorrectProofTimePeriodDetailStatus;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPMDisplayReportService;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.functions.utils.Lists;
import com.github.rholder.retry.*;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;


@SuppressWarnings("Duplicates")
public class TPMProofTimePeriodDetailObjCorrectStatusController extends PreDefineController<CorrectProofTimePeriodDetailStatus.Arg, CorrectProofTimePeriodDetailStatus.Result> {

    private final RedissonClient redissonCmd = SpringUtil.getContext().getBean("redissonCmd", RedissonClient.class);

    private final ITPMDisplayReportService tpmDisplayReportService = SpringUtil.getContext().getBean(ITPMDisplayReportService.class);

    private static final String REDIS_UNIQUE_TEMPLATE = "FMCG:TPM:ProofTimePeriodDetailObj:CorrectStatus:%s";


    protected static final long LOCK_WAIT = 20;
    protected static final long LOCK_LEASE = 60;

    Retryer<Boolean> retryer = RetryerBuilder.<Boolean>newBuilder()
            .retryIfResult(Boolean.FALSE::equals)
            .withWaitStrategy(WaitStrategies.fixedWait(5, TimeUnit.SECONDS))
            .withStopStrategy(StopStrategies.stopAfterAttempt(3))
            .build();

    @Override
    protected CorrectProofTimePeriodDetailStatus.Result doService(CorrectProofTimePeriodDetailStatus.Arg arg) {
        try {
            serviceFacade.findObject(controllerContext.getTenantId(), ApiNames.TPM_PROOF_TIME_PERIOD_DETAIL_OBJ);
        } catch (ObjectDefNotFoundError ex) {
            log.info("period time obj not found:tenantId{}", controllerContext.getTenantId());
            return new CorrectProofTimePeriodDetailStatus.Result();
        }

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() -> {
            if (tryLock()) {
                try {
                    retryer.call(this::running);
                } catch (ExecutionException | RetryException ex) {
                    log.error("retry job error : ", ex);
                    throw new ValidateException("period time correct status is error");
                } catch (Exception ex) {
                    log.error("period time correct status is error:tenantId{}", controllerContext.getTenantId(), ex);
                    throw new ValidateException("period time correct status is error");
                } finally {
                    unlock();
                }
            } else {
                throw new ValidateException("task is already running");
            }

        });
        parallelTask.run();

        return new CorrectProofTimePeriodDetailStatus.Result();
    }


    private boolean running() {
        try {
            innerRunning();
            log.info("running success tenantId:{}", controllerContext.getTenantId());
            return true;
        } catch (Exception ex) {
            log.error("running error : ", ex);
            return false;
        }
    }

    private void innerRunning() {
        try {
            log.info("开始更新举证时段对象状态 - 租户ID: {}", controllerContext.getTenantId());

            // 查询所有举证时段对象
            List<IObjectData> periodDetailObjList = queryAllPeriodDetailObj();
            if (periodDetailObjList.isEmpty()) {
                log.info("没有找到需要更新的举证时段对象");
                return;
            }

            // 批量处理每个时段对象
            for (IObjectData periodDetailObj : periodDetailObjList) {
                tpmDisplayReportService.processOnePeriodDetail(controllerContext.getTenantId(), periodDetailObj);
            }

            log.info("举证时段对象状态更新完成，共处理{}个对象", periodDetailObjList.size());
        } catch (Exception e) {
            log.error("更新举证时段对象状态失败", e);
            throw e;
        }
    }


    /**
     * 查询所有需要处理的举证时段对象
     */
    private List<IObjectData> queryAllPeriodDetailObj() {
        try {
            // 查询条件可以根据实际需求调整
            SearchTemplateQuery query = new SearchTemplateQuery();

            query.setLimit(-1);
            query.setOffset(0);
            query.setSearchSource("db");
            query.setNeedReturnCountNum(false);

            return QueryDataUtil.find(serviceFacade, controllerContext.getTenantId(), ApiNames.TPM_PROOF_TIME_PERIOD_DETAIL_OBJ,
                    query, Lists.newArrayList(CommonFields.ID, CommonFields.NAME, CommonFields.OBJECT_DESCRIBE_API_NAME,
                            CommonFields.RECORD_TYPE, CommonFields.TENANT_ID, TPMProofTimePeriodDetailFields.BEGIN_DATE,
                            TPMProofTimePeriodDetailFields.END_DATE, TPMProofTimePeriodDetailFields.PROOF_STATUS));
        } catch (Exception e) {
            log.error("查询举证时段对象失败", e);
            throw new RuntimeException("query proof period detail obj error", e);
        }
    }

    private void unlock() {
        String key = String.format(REDIS_UNIQUE_TEMPLATE, controllerContext.getTenantId());
        RLock lock = redissonCmd.getLock(key);

        log.info("unlock period time : {}", key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }


    private boolean tryLock() {
        String key = String.format(REDIS_UNIQUE_TEMPLATE, controllerContext.getTenantId());
        RLock lock = redissonCmd.getLock(key);

        log.info("try lock period time  : {}", key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            return true;
        }
        try {
            return lock.tryLock(LOCK_WAIT, LOCK_LEASE, TimeUnit.SECONDS);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new MetaDataBusinessException(String.format("try lock period time status cause thread interrupted exception : %s", key));
        }
    }


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }
}
