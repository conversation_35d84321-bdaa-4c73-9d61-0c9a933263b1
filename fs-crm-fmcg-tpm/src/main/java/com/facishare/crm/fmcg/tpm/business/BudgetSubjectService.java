package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetBusinessSubjectFields;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetSubjectService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/9/29 17:36
 */
@Service
@SuppressWarnings("Duplicates")
public class BudgetSubjectService implements IBudgetSubjectService {

    @Resource
    private ServiceFacade serviceFacade;

    @Override
    public boolean isParent(String tenantId, String parent, String subjectId) {
        if (parent.equals(subjectId)) {
            return false;
        }
        User sys = User.systemUser(tenantId);
        String cur = subjectId;
        int loop = 0;
        while (loop < 15) {
            IObjectData data = serviceFacade.findObjectDataIgnoreAll(sys, cur, ApiNames.TPM_BUDGET_BUSINESS_SUBJECT);
            cur = data.get(TPMBudgetBusinessSubjectFields.PARENT_SUBJECT_ID, String.class);
            if (Strings.isNullOrEmpty(cur)) {
                break;
            }
            if (cur.equals(parent)) {
                return true;
            }
            loop++;
        }
        return false;
    }

    @Override
    public String queryParentId(String tenantId, String subjectId) {
        if (StringUtils.isEmpty(subjectId)) {
            throw new ValidateException("subject id is null");
        }
        User sys = User.systemUser(tenantId);
        IObjectData data = serviceFacade.findObjectDataIgnoreAll(sys, subjectId, ApiNames.TPM_BUDGET_BUSINESS_SUBJECT);
        String parentId = data.get(TPMBudgetBusinessSubjectFields.PARENT_SUBJECT_ID, String.class);
        if (Strings.isNullOrEmpty(parentId)) {
            throw new ValidateException("parent subject id is null");
        }
        return parentId;
    }

    @Override
    public boolean notTheSpecifiedLevel(String tenantId, String subjectId, int level) {
        IObjectData subject = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), subjectId, ApiNames.TPM_BUDGET_BUSINESS_SUBJECT);
        return !subject.get(TPMBudgetBusinessSubjectFields.SUBJECT_LEVEL, Integer.class).equals(level);
    }

    @Override
    public List<String> queryLowerIds(String tenantId, String subjectId, int level) {
        // 查询父级科目层级
        IObjectData parentSubject = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), subjectId, ApiNames.TPM_BUDGET_BUSINESS_SUBJECT);
        int subjectLevel = parentSubject.get(TPMBudgetBusinessSubjectFields.SUBJECT_LEVEL, Integer.class);

        // 异常情况下不反回任何科目
        if (level < subjectLevel) {
            return Lists.newArrayList("");
        }

        // 默认返回当前科目
        List<String> cur = Lists.newArrayList(subjectId);

        // 需要向下搜索的次数，0次直接返回当前科目
        int loop = level - subjectLevel;

        // 循环搜索下级科目
        for (int i = 0; i < loop; i++) {
            SearchTemplateQuery stq = new SearchTemplateQuery();

            stq.setLimit(1000);
            stq.setOffset(0);
            stq.setNeedReturnCountNum(false);
            stq.setNeedRightJoin(false);
            stq.setNeedReturnQuote(false);

            int levelFilterValue = subjectLevel + i + 1;
            Filter levelFilter = new Filter();
            levelFilter.setFieldName(TPMBudgetBusinessSubjectFields.SUBJECT_LEVEL);
            levelFilter.setOperator(Operator.EQ);
            levelFilter.setFieldValues(Lists.newArrayList(String.valueOf(levelFilterValue)));

            Filter parentFiler = new Filter();
            parentFiler.setFieldName(TPMBudgetBusinessSubjectFields.PARENT_SUBJECT_ID);
            parentFiler.setOperator(Operator.IN);
            parentFiler.setFieldValues(cur);

            stq.setFilters(Lists.newArrayList(levelFilter, parentFiler));

            OrderBy order = new OrderBy();
            order.setFieldName(CommonFields.ID);
            order.setIsAsc(false);
            stq.setOrders(Lists.newArrayList(order));

            List<IObjectData> data = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_BUDGET_BUSINESS_SUBJECT, stq, Lists.newArrayList(CommonFields.ID));
            cur = data.stream().map(DBRecord::getId).collect(Collectors.toList());

            // 如果搜索不到下级科目了，则直接跳出
            if (CollectionUtils.isEmpty(cur)) {
                break;
            }
        }
        return cur;
    }
}
