package com.facishare.crm.fmcg.tpm.session.impl;

import com.facishare.crm.fmcg.tpm.session.SendMessageService;
import com.facishare.restful.client.exception.FRestClientException;
import com.fxiaoke.api.MessageServiceV2;
import com.fxiaoke.model.MessageResponse;
import com.fxiaoke.model.message.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * author: wuyx
 * description:
 * createTime: 2022/2/11 11:50
 */
@Slf4j
@Component
public class SendMessageServiceImpl implements SendMessageService {

    @Resource
    public MessageServiceV2 messageServiceV2;


    @Override
    public void sendTextMessage(SendTextMessageArg arg) {
        try {
            MessageResponse result = messageServiceV2.sendTextMessage(arg);
            if (result.getCode() == MessageResponse.SUCCESS_CODE) {
                log.info("message send success");
            } else {
                log.error("message send failed");
            }
        } catch (FRestClientException ex) {
            log.error("FRestClientException -", ex);
        }
    }

    @Override
    public void sendTextLinkMessage(SendTextLinkMessageArg arg) {
        try {
            MessageResponse result = messageServiceV2.sendTextLinkMessage(arg);
            if (result.getCode() == MessageResponse.SUCCESS_CODE) {
                log.info("message send success");
            } else {
                log.error("message send failed");
            }
        } catch (FRestClientException ex) {
            log.error("FRestClientException -", ex);
        }
    }

    @Override
    public void sendTextCardMessage(SendTextCardMessageArg arg) {
        try {
            MessageResponse result = messageServiceV2.sendTextCardMessage(arg);
            if (result.getCode() == MessageResponse.SUCCESS_CODE) {
                log.info("message send success");
            } else {
                log.error("message send failed");
            }
        } catch (FRestClientException ex) {
            log.error("FRestClientException -", ex);
        }
    }

    @Override
    public void sendImageCardMessage(SendImageCardMessageArg arg) {
        try {
            MessageResponse result = messageServiceV2.sendImageCardMessage(arg);
            if (result.getCode() == MessageResponse.SUCCESS_CODE) {
                log.info("message send success");
            } else {
                log.error("message send failed");
            }
        } catch (FRestClientException ex) {
            log.error("FRestClientException -", ex);
        }
    }

    @Override
    public void cleanNotReadCount(CleanNotReadCountArg arg) {
        try {
            MessageResponse result = messageServiceV2.cleanNotReadCount(arg);
            if (result.getCode() == MessageResponse.SUCCESS_CODE) {
                log.info("message send success");
            } else {
                log.error("message send failed");
            }
        } catch (FRestClientException ex) {
            log.error("FRestClientException -", ex);
        }
    }
}
