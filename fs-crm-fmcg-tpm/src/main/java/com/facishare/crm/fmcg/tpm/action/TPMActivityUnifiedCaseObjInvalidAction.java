package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountDetailFields;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/13 17:28
 */
@SuppressWarnings("Duplicates")
public class TPMActivityUnifiedCaseObjInvalidAction extends StandardInvalidAction {
    private static final Logger LOGGER = LoggerFactory.getLogger(TPMActivityUnifiedCaseObjInvalidAction.class);
    private final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);

    @Override
    protected void before(Arg arg) {
        //已存在预算收支流水不可作废
        validateExistCostFlow(arg);
        //已关联活动申请不可作废
        validateRelationActivity(arg);

        super.before(arg);
    }


    private void validateRelationActivity(Arg arg) {
        // 查询关联的活动申请

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);

        Filter idFilter = new Filter();
        idFilter.setFieldName(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(arg.getObjectDataId()));

        Filter deletedFilter = new Filter();
        deletedFilter.setFieldName(CommonFields.IS_DELETED);
        deletedFilter.setOperator(Operator.EQ);
        deletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(idFilter, deletedFilter));

        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query).getData();

        if (!data.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_INVALID_ACTION_0));
        }
    }

    private void validateExistCostFlow(Arg arg) {
        if (!budgetService.isOpenBudge(Integer.parseInt(actionContext.getTenantId()))) {
            return;
        }

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);

        Filter apiNameFilter = new Filter();
        apiNameFilter.setFieldName(TPMBudgetAccountDetailFields.RELATED_OBJECT_API_NAME);
        apiNameFilter.setOperator(Operator.EQ);
        apiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ));

        Filter idFilter = new Filter();
        idFilter.setFieldName(TPMBudgetAccountDetailFields.RELATED_OBJECT_DATA_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(arg.getObjectDataId()));

        Filter detailStatusFilter = new Filter();
        detailStatusFilter.setFieldName(TPMBudgetAccountDetailFields.DETAIL_STATUS);
        detailStatusFilter.setOperator(Operator.EQ);
        detailStatusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountDetailFields.DetailStatus.INCLUDE));

        query.setFilters(Lists.newArrayList(apiNameFilter, idFilter, detailStatusFilter));

        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_BUDGET_ACCOUNT_DETAIL, query).getData();

        if (!data.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_INVALID_ACTION_1));
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {

        try {
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUpstreamOwnerIdOrUserId()), BuryModule.TPM.TPM_ACTIVITY_UNIFIED_CASE, BuryOperation.DELETE, true);
        } catch (Exception e) {
            LOGGER.warn("活动方案埋点异常 tenantId：{}", actionContext.getTenantId(), e);//ignorei18n
        }
        return super.after(arg, result);
    }

}
