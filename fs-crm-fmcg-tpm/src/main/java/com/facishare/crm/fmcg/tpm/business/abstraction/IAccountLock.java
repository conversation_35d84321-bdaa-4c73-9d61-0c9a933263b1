package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.metadata.api.IObjectData;

/**
 * budget account consume lock
 * <p>
 * create by @yangqf
 * create time 2022/10/11 14:42
 */
public interface IAccountLock {

    /**
     * try lock account
     *
     * @param tenantId tenant id
     * @param data     account object data
     * @param node     account node information
     * @return lock result
     */
    boolean tryLock(
            String tenantId,
            IObjectData data,
            BudgetTypeNodeEntity node
    );

    /**
     * try lock account
     * @param tenantId tenant id
     * @param data     account object data
     * @param node     account node information
     * @param wait     wait time
     *
     * @return lock result
     */
    boolean tryLock(
            String tenantId,
            IObjectData data,
            BudgetTypeNodeEntity node,
            long wait
    );

    /**
     * try lock account by id
     * [ !!! query account object data and account node information from pg & mongo !!!]
     * {@link com.facishare.crm.fmcg.tpm.business.AccountLock#tryLock(String, String)}
     * @param tenantId tenant id
     * @param id       account id
     *
     * @return lock result
     */
    boolean tryLock(
            String tenantId,
            String id
    );

    /**
     * try lock account by id
     * [ !!! query account object data and account node information from pg & mongo !!!]
     * {@link com.facishare.crm.fmcg.tpm.business.AccountLock#tryLock(String, String)}
     * @param tenantId tenant id
     * @param id       account id
     * @param wait     wait time
     *
     * @return lock result
     */
    boolean tryLock(
            String tenantId,
            String id,
            long wait
    );

    /**
     * unlock account
     * @param tenantId tenant id
     * @param data     account object data
     * @param node     account node information
     */
    void unlock(
            String tenantId,
            IObjectData data,
            BudgetTypeNodeEntity node
    );

    /**
     * unlock account by id
     * [ !!! query account object data and account node information from pg & mongo !!!]
     * {@link com.facishare.crm.fmcg.tpm.business.AccountLock#unlock(String, String)}
     *
     * @param tenantId tenant id
     * @param id       account id
     */
    void unlock(
            String tenantId,
            String id
    );

    boolean tryLock(
            RequestContext requestContext,
            String tenantId,
            IObjectData data,
            BudgetTypeNodeEntity node
    );

    void unlock(
            RequestContext requestContext
    );
}
