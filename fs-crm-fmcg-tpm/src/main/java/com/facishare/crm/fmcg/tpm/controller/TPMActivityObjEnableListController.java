package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.api.activity.EnableList;
import com.facishare.crm.fmcg.tpm.business.DescribeCacheService;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDescribeCacheService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRangeFieldBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPM2Service;
import com.facishare.crm.fmcg.tpm.business.abstraction.IUnifiedActivityCommonLogicBusiness;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityDisplayImgDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityDisplayImgPO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.crm.fmcg.tpm.business.TPMDisplayReportService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.DataPrivilegeService;
import com.facishare.paas.appframework.privilege.dto.PrivilegeResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.SqlEscaper;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.facishare.crm.fmcg.tpm.controller.TPMActivityAgreementObjRelatedListController.YINLU_FILTER_TIME;

@SuppressWarnings("Duplicates")
public class TPMActivityObjEnableListController extends PreDefineController<EnableList.Arg, EnableList.Result> {

    public static final String VISIT_COMPLETED_STATUS = "4";
    private static final OrganizationService organizationService = SpringUtil.getContext().getBean(OrganizationService.class);
    private static final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);

    private Map<String, String> GROUP_NAME_MAP = new HashMap<>();

    private static final String PARTICIPATED_GROUP_KEY = "participated";
    private static final String NO_PARTICIPATED_GROUP_KEY = "no_participated";
    public static final ITPM2Service tpm2Service = SpringUtil.getContext().getBean(ITPM2Service.class);
    public static Map<String, Map<String, Integer>> PROOF_GROUP_LABEL_ORDER_MAP = new HashMap<>();
    private boolean isTpm2Tenant = false;
    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);

    private static final IDescribeCacheService I_DESCRIBE_CACHE_SERVICE = SpringUtil.getContext().getBean(IDescribeCacheService.class);

    private final DataPrivilegeService dataPrivilegeService = SpringUtil.getContext().getBean(DataPrivilegeService.class);

    private final IUnifiedActivityCommonLogicBusiness unifiedActivityCommonLogicBusiness = SpringUtil.getContext().getBean(IUnifiedActivityCommonLogicBusiness.class);

    private ISearchTemplate activitySearchTemplate;

    private IObjectDescribe activityDescribe;

    private User systemUser;

    private boolean isAdminRequest = false;

    private IRangeFieldBusiness rangeFieldBusiness = SpringUtil.getContext().getBean(IRangeFieldBusiness.class);

    private static final String MN_PROOF_ENABLE_LIST = "proof_enable_list";

    private static final Boolean mnLogic;

    private static DescribeCacheService describeCacheService = SpringUtil.getContext().getBean(DescribeCacheService.class);

    private static final ActivityDisplayImgDAO activityDisplayImgDAO = SpringUtil.getContext().getBean(ActivityDisplayImgDAO.class);

    static {
        String mnEnv = System.getProperty("mn.biz.key");
        mnLogic = !Strings.isNullOrEmpty(mnEnv) && mnEnv.contains(MN_PROOF_ENABLE_LIST);
        ConfigFactory.getConfig("fs-fmcg-tpm-config", config -> {
            String orderConfig = config.get("PROOF_GROUP_LABEL_ORDER_MAP");
            if (!Strings.isNullOrEmpty(orderConfig)) {
                PROOF_GROUP_LABEL_ORDER_MAP = JSON.parseObject(orderConfig, new TypeReference<Map<String, Map<String, Integer>>>() {
                });
            }
        });
    }

    private final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);

    @Override
    protected void before(EnableList.Arg arg) {
        GROUP_NAME_MAP.put(PARTICIPATED_GROUP_KEY, I18N.text(I18NKeys.ALREADY_PROOF));
        GROUP_NAME_MAP.put(NO_PARTICIPATED_GROUP_KEY, I18N.text(I18NKeys.IS_NOT_PROOF));
        super.before(arg);
    }

    @Override
    protected EnableList.Result doService(EnableList.Arg arg) {

        log.info("enable list arg : {}", JSON.toJSONString(arg));
        if (TPMGrayUtils.isAsyncEnableList(controllerContext.getTenantId())){
            try {
                // 使用 CompletableFuture 来安全地处理异步执行和返回值
                CompletableFuture<EnableList.Result> future = CompletableFuture.supplyAsync(() -> {
                    return doEnableListService(arg);
                });

                // 设置超时时间为14秒
                return future.get(14, TimeUnit.SECONDS);

            } catch (TimeoutException e) {
                log.error("enable list service timeout", e);
                throw new ValidateException("系统繁忙，请稍后重试。");
            } catch (InterruptedException e) {
                log.error("enable list service interrupted", e);
                Thread.currentThread().interrupt();
                throw new ValidateException("系统繁忙，请稍后重试。");
            } catch (ExecutionException e) {
                log.error("enable list service error", e);
                throw new RuntimeException(e);
            }
        } else {
            return doEnableListService(arg);
        }
    }

    private EnableList.Result doEnableListService(EnableList.Arg arg) {
        this.isTpm2Tenant = tpm2Service.isTPM2Tenant(Integer.valueOf(controllerContext.getTenantId()));
        this.isAdminRequest = serviceFacade.isAdmin(controllerContext.getUser());

        boolean visitCompleted = VISIT_COMPLETED_STATUS.equals(arg.getVisitStatus());

        this.systemUser = User.systemUser(controllerContext.getTenantId());
        this.activityDescribe = serviceFacade.findDescribeAndLayout(this.systemUser, ApiNames.TPM_ACTIVITY_OBJ, false, null).getObjectDescribe();
        this.activitySearchTemplate = serviceFacade.findSearchTemplateByIdAndType(this.systemUser, "", ApiNames.TPM_ACTIVITY_OBJ, "All");


        if (Strings.isNullOrEmpty(arg.getStoreId())) {
            //品牌商活动
            if (visitCompleted) {
                return listWhenCompleted(controllerContext, arg, null);
            } else {
                return listWhenSchedule(controllerContext, arg, null);
            }

        } else {
            IObjectData storeData = serviceFacade.findObjectDataIgnoreAll(User.systemUser(controllerContext.getTenantId()), arg.getStoreId(), ApiNames.ACCOUNT_OBJ);
            if (storeData == null) {
                throw new ValidateException("store not found.");
            }
            if (visitCompleted) {
                return listWhenCompleted(controllerContext, arg, storeData);
            } else {
                if (TPMGrayUtils.newActivityEnableCheck(controllerContext.getTenantId())) {
                    return listWhenScheduleV1(controllerContext, arg, storeData);
                } else {
                    return listWhenSchedule(controllerContext, arg, storeData);
                }
            }
        }
    }

    private EnableList.Result listWhenCompleted(ControllerContext context, EnableList.Arg arg, IObjectData store) {
        // queryProof 入参加上 arg.getActivityId 如果 arg.getActivityId 不为空，则加上查询 arg.getActivityId 的条件
        List<IObjectData> proofList = queryProof(context, arg.getStoreId(), arg.getVisitId(), arg.getActionId(), arg.getActivityIds());
        List<String> agreementIdList = new ArrayList<>();
        boolean isRio = TPMGrayUtils.isRioTenant(context.getTenantId());

        //获取 proofList 中的 举证id
        List<String> proofIds = proofList.stream().map(IObjectData::getId).collect(Collectors.toList());

        List<ActivityDisplayImgPO> activityDisplayImgList = activityDisplayImgDAO.findByProofIds(context.getTenantId(), proofIds);

        // 将 activityDisplayImgList 转换为 map
        Map<String, String> activityDisplayImgMap = new HashMap<>();
        if (activityDisplayImgList != null && !activityDisplayImgList.isEmpty()) {
            activityDisplayImgMap = activityDisplayImgList.stream()
                .collect(Collectors.toMap(
                    ActivityDisplayImgPO::getProofId, 
                    ActivityDisplayImgPO::getErrorMessage,
                    (existing, replacement) -> existing 
                ));
        }


        Map<String, IObjectData> proofMap = new HashMap<>();
        Map<String, List<String>> proofErrorMessageMap = new HashMap<>();
        Map<String, String> proofAiStatusMap = new HashMap<>();
        for (IObjectData proof : proofList) {
            String activityId = (String) proof.get(TPMActivityProofFields.ACTIVITY_ID);
            proofMap.put(activityId, proof);
            String agreementId = proof.get(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID, String.class);
            if (isRio && !Strings.isNullOrEmpty(agreementId)) {
                agreementIdList.add(agreementId);
            }
            fillProofAIMap(proof, proofAiStatusMap, activityId, activityDisplayImgMap, proofErrorMessageMap);
        }

        EnableList.Result result = new EnableList.Result();
        result.setIsRio(isRio);
        Map<String, List<String>> functionCodeListMap = new HashMap<>();
        functionCodeListMap.put(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, Lists.newArrayList("Add", "Edit", "View", "List"));
        if (store != null) {
            result.setStoreId(store.getId());
            result.setStoreName(store.getName());
            IObjectData dealer = getDealer(store);
            if (dealer != null) {
                result.setDealerId(dealer.getId());
                result.setDealerName(dealer.getName());
            }
        }
        result.setFunctionCodeMap(queryFunctionCodeMap(functionCodeListMap));
        result.setNavigateStrategy("activity_list");
        result.setData(Lists.newArrayList());
        result.setSystemTime(System.currentTimeMillis());

        if (proofMap.keySet().isEmpty()) {
            return result;
        }

        List<IObjectData> activityList = queryActivity(context, new ArrayList<>(proofMap.keySet()));

        EnableList.ActivityGroupVO group = new EnableList.ActivityGroupVO();
        group.setGroupKey(PARTICIPATED_GROUP_KEY);
        group.setGroupName(GROUP_NAME_MAP.getOrDefault(PARTICIPATED_GROUP_KEY, "--"));

        Map<String, Long> proofCountMap = countProof(context.getTenantId(), arg.getStoreId()).stream().collect(Collectors.toMap(k -> k.get(TPMActivityProofFields.ACTIVITY_ID).toString(), v -> Long.parseLong(v.get("count").toString())));
        Map<String, Long> agreementCountMap = countAgreement(context.getTenantId(), arg.getStoreId()).stream().collect(Collectors.toMap(k -> k.get(TPMActivityAgreementFields.ACTIVITY_ID) == null ? "" : k.get(TPMActivityAgreementFields.ACTIVITY_ID).toString(), v -> Long.parseLong(v.get("count").toString())));
        Map<String, ActivityTypeExt> activityTypeExtMap = activityTypeManager.findByActivityTypeIds(controllerContext.getTenantId(), activityList.stream().map(v -> v.get(TPMActivityFields.ACTIVITY_TYPE, String.class)).filter(v -> !Strings.isNullOrEmpty(v)).collect(Collectors.toList())).stream().collect(Collectors.toMap(v -> v.get().getId().toString(), v -> v, (before, after) -> before));
        Map<String, IObjectData> agreementMap = serviceFacade.findObjectDataByIds(context.getTenantId(), agreementIdList, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ).stream().collect(Collectors.toMap(v -> v.get(TPMActivityAgreementFields.ACTIVITY_ID, String.class), v -> v, (a, b) -> a));

        //TODO这里如果有AI识别错误的举证，需要有错误信息
        for (IObjectData activity : activityList) {
            EnableList.ActivityVO datum = new EnableList.ActivityVO();

            datum.setId(activity.getId());
            datum.setName(activity.getName());
            datum.setBeginDate((long) activity.get(TPMActivityFields.BEGIN_DATE));
            datum.setEndDate((long) activity.get(TPMActivityFields.END_DATE));
            datum.setProofCount(proofCountMap.getOrDefault(activity.getId(), 0L));
            datum.setAgreementCount(agreementCountMap.getOrDefault(activity.getId(), 0L));
            datum.setAgreementRequired(false);
            IObjectData agreement = agreementMap.get(activity.getId());
            if (agreement != null) {
                String rioStoreConfirmStatus = agreement.get(TPMActivityAgreementFields.STORE_CONFIRM_STATUS_FOR_RIO, String.class, "");
                String isNeedRioStoreConfirm = activity.get(TPMActivityFields.IS_NEED_RIO_STORE_CONFIRM, String.class, "");
                datum.setIsNeedRioStoreConfirm(result.getIsRio() && "1".equals(rioStoreConfirmStatus) && "1".equals(isNeedRioStoreConfirm));
            }
            datum.setAiStatus(getAiStatus(activity.getId(), proofAiStatusMap, proofErrorMessageMap));

            String proofRecordType = activity.get(TPMActivityFields.PROOF_RECORD_TYPE, String.class);
            if (Strings.isNullOrEmpty(proofRecordType)) {
                proofRecordType = activity.get(TPMActivityFields.PROOF_RECORD_TYPE__C, String.class);
                if (Strings.isNullOrEmpty(proofRecordType)) {
                    proofRecordType = "default__c";
                }
            }
            if (isTpm2Tenant) {
                String activityType = activity.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
                if (!Strings.isNullOrEmpty(activityType) && activityTypeExtMap.containsKey(activityType)) {
                    ActivityTypeExt activityTypeExt = activityTypeExtMap.get(activityType);
                    if (activityTypeExt.proofNode() != null) {
                        proofRecordType = activityTypeExt.proofNode().getObjectRecordType();
                    }
                    datum.setActivityTypeName(activityTypeExt.get().getName());
                    String templateId = activityTypeExt.get().getTemplateId();
                    log.info("activityTypeExt get templateId is {}", templateId);
                    boolean flag = !Strings.isNullOrEmpty(templateId) && templateId.contains("display") && activityTypeExt.agreementNode() != null;
                    datum.setIsDisplayActivity(flag);
                    datum.setIsShowReportLink(datum.getAiStatus() != null && "0".equals(datum.getAiStatus().getStatus()) && flag);
                }
            }

            datum.setProofRecordType(proofRecordType);

            datum.setActivityAgreementId("");
            datum.setStatus("completed");

            if (proofMap.containsKey(activity.getId())) {
                datum.setDataApiName(ApiNames.TPM_ACTIVITY_PROOF_OBJ);
                datum.setDataId(proofMap.get(activity.getId()).getId());
            }

            group.getActivityList().add(datum);
        }
        result.getData().add(group);

        makeUpGroup(result);
        return result;
    }

    private static void fillProofAIMap(IObjectData proof,
                                       Map<String, String> proofAiStatusMap,
                                       String activityId, Map<String, String> activityDisplayImgMap,
                                       Map<String, List<String>> proofErrorMessageMap) {
        String aiIdentifyStatus = proof.get(TPMActivityProofFields.AI_IDENTIFY_STATUS, String.class);
        Boolean openAi = proof.get(TPMActivityProofFields.OPEN_AI, Boolean.class);
        if (Boolean.FALSE.equals(openAi)) {
            proofAiStatusMap.put(activityId, "3");
        } else if (TPMActivityProofFields.AI_IDENTIFY_STATUS_IDENTIFYING.equals(aiIdentifyStatus)) {
            proofAiStatusMap.put(activityId, "2");
        } else if (TPMActivityProofFields.AI_IDENTIFY_STATUS_IDENTIFY_FAILED.equals(aiIdentifyStatus)) {
            proofAiStatusMap.put(activityId, "1");
            String proofId = proof.getId();
            if (activityDisplayImgMap.containsKey(proofId)) {
                String errorMessage = activityDisplayImgMap.get(proofId);
                if (!Strings.isNullOrEmpty(errorMessage)) {
                    List<String> errorMessages = proofErrorMessageMap.computeIfAbsent(activityId, k -> new ArrayList<>());
                    if (!errorMessages.contains(errorMessage)) {
                        errorMessages.add(errorMessage);
                    }
                }
            }
        } else if (TPMActivityProofFields.AI_IDENTIFY_STATUS_IDENTIFIED.equals(aiIdentifyStatus)) {
            proofAiStatusMap.put(activityId, "0");
        }
    }

    private EnableList.AiStatusVO getAiStatus(String activityId, Map<String, String> proofAiStatusMap, Map<String, List<String>> proofErrorMessageMap) {
        if (CollectionUtils.isEmpty(proofAiStatusMap) || !proofAiStatusMap.containsKey(activityId)) {
            return new EnableList.AiStatusVO();
        }

        EnableList.AiStatusVO aiStatusVO = new EnableList.AiStatusVO();
        aiStatusVO.setStatus(proofAiStatusMap.get(activityId));
        aiStatusVO.setErrorMessageList(proofErrorMessageMap.getOrDefault(activityId, Collections.emptyList()));
        return aiStatusVO;
    }

    private EnableList.Result listWhenScheduleV1(ControllerContext context, EnableList.Arg arg, IObjectData store) {
        EnableList.Result result = new EnableList.Result();

        Map<String, List<String>> functionCodeListMap = new HashMap<>();
        functionCodeListMap.put(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, Lists.newArrayList("Add", "Edit", "View", "List"));
        result.setFunctionCodeMap(queryFunctionCodeMap(functionCodeListMap));
        result.setStoreId(store.getId());
        result.setStoreName(store.getName());
        result.setSystemTime(System.currentTimeMillis());
        IObjectData dealer = getDealer(store);
        if (dealer != null) {
            result.setDealerId(dealer.getId());
            result.setDealerName(dealer.getName());
        }

        String dealerId = storeBusiness.findDealerId(context.getTenantId(), store);
        List<Integer> departmentIds = new ArrayList<>();
        if (!controllerContext.getUser().isOutUser()) {
            departmentIds = organizationService.getDepartmentIds(controllerContext.getUser().getTenantIdInt(), Integer.parseInt(controllerContext.getUser().getUpstreamOwnerIdOrUserId()));
            if (CollectionUtils.isEmpty(departmentIds)) {
                return result;
            }
            log.info("departments : {}", departmentIds);
        }
        long now = System.currentTimeMillis();

        List<String> activityTypeList = getActivityType(arg.getActivityTypeList());

        List<IObjectData> activities = TPMGrayUtils.isYinLuEnableList(context.getTenantId()) ? queryActivity(departmentIds, store.getId(), activityTypeList) : queryActivityV1(context, now, departmentIds, store.getId(), dealerId, activityTypeList);

        log.info("activities ids: {}", activities.stream().map(IObjectData::getId).collect(Collectors.toList()));

        Map<String, Long> proofCountMap = countProof(context.getTenantId(), arg.getStoreId()).stream().collect(Collectors.toMap(k -> k.get(TPMActivityProofFields.ACTIVITY_ID).toString(), v -> Long.parseLong(v.get("count").toString())));
        List<IObjectData> proofList = queryProof(context, arg.getStoreId(), arg.getVisitId(), arg.getActionId(), arg.getActivityIds());
        Map<String, Long> agreementCountMap = countAgreement(context.getTenantId(), arg.getStoreId()).stream().collect(Collectors.toMap(k -> k.get(TPMActivityAgreementFields.ACTIVITY_ID) == null ? "" : k.get(TPMActivityAgreementFields.ACTIVITY_ID).toString(), v -> Long.parseLong(v.get("count").toString())));
        Map<String, ActivityTypeExt> activityTypeExtMap = activityTypeManager.findByActivityTypeIds(controllerContext.getTenantId(), activities.stream().map(v -> v.get(TPMActivityFields.ACTIVITY_TYPE, String.class)).filter(v -> !Strings.isNullOrEmpty(v)).collect(Collectors.toList())).stream().collect(Collectors.toMap(v -> v.get().getId().toString(), v -> v, (before, after) -> before));

        //获取 proofList 中的 举证id
        List<String> proofIds = proofList.stream().map(IObjectData::getId).collect(Collectors.toList());

        List<ActivityDisplayImgPO> activityDisplayImgList = activityDisplayImgDAO.findByProofIds(context.getTenantId(), proofIds);

        // 将 activityDisplayImgList 转换为 map
        Map<String, String> activityDisplayImgMap = new HashMap<>();
        if (activityDisplayImgList != null && !activityDisplayImgList.isEmpty()) {
            activityDisplayImgMap = activityDisplayImgList.stream()
                    .collect(Collectors.toMap(
                            ActivityDisplayImgPO::getProofId,
                            ActivityDisplayImgPO::getErrorMessage,
                            (existing, replacement) -> existing
                    ));
        }

        Map<String, List<String>> proofErrorMessageMap = new HashMap<>();
        Map<String, String> proofAiStatusMap = new HashMap<>();
        Map<String, IObjectData> proofMap = new HashMap<>();
        for (IObjectData proof : proofList) {
            String activityId = (String) proof.get(TPMActivityProofFields.ACTIVITY_ID);
            proofMap.put(activityId, proof);

            fillProofAIMap(proof, proofAiStatusMap, activityId, activityDisplayImgMap, proofErrorMessageMap);
        }

        List<IObjectData> completedActivities = queryActivityV1(context, new ArrayList<>(proofMap.keySet()));
        activities.addAll(completedActivities);

        Map<String, List<EnableList.ActivityVO>> activityMap = new HashMap<>();
        int total = 0;
        int inProgress = 0;
        int completed = 0;
        Set<String> duplicateActivityIdCache = new HashSet<>();
        for (IObjectData activity : activities) {
            if (duplicateActivityIdCache.contains(activity.getId())) {
                continue;
            }
            if (!proofMap.containsKey(activity.getId())) {
                String storeRangeJson = (String) activity.get(TPMActivityFields.STORE_RANGE);
                if (!Strings.isNullOrEmpty(storeRangeJson)) {
                    JSONObject storeRange = JSON.parseObject(storeRangeJson);
                    String type = storeRange.getString("type");
                    switch (type) {
                        case "CONDITION":
                            if (CollectionUtils.isEmpty(queryConditionStore(context, storeRange, arg.getStoreId()))) {
                                continue;
                            }
                            break;
                        default:
                        case "FIXED":
                        case "ALL":
                            break;
                    }
                }
            }

            EnableList.ActivityVO datum = toActivityVO(context, now, activity, proofCountMap, proofMap, agreementCountMap, activityTypeExtMap, proofAiStatusMap, proofErrorMessageMap);
            if (TPMGrayUtils.isYinLu(controllerContext.getTenantId()) && "no_agreement".equals(datum.getStatus())) {
                continue;
            }

            duplicateActivityIdCache.add(datum.getId());

            total++;
            if ("in_progress".equals(datum.getStatus())) {
                inProgress++;
            }
            if ("completed".equals(datum.getStatus())) {
                completed++;
            }

            if (datum.getProofCount() > 0) {
                if (!activityMap.containsKey(PARTICIPATED_GROUP_KEY)) {
                    activityMap.put(PARTICIPATED_GROUP_KEY, Lists.newArrayList(datum));
                } else {
                    activityMap.get(PARTICIPATED_GROUP_KEY).add(datum);
                }
            } else {
                if (!activityMap.containsKey(NO_PARTICIPATED_GROUP_KEY)) {
                    activityMap.put(NO_PARTICIPATED_GROUP_KEY, Lists.newArrayList(datum));
                } else {
                    activityMap.get(NO_PARTICIPATED_GROUP_KEY).add(datum);
                }
            }
        }

        for (Map.Entry<String, List<EnableList.ActivityVO>> entry : activityMap.entrySet()) {
            EnableList.ActivityGroupVO group = new EnableList.ActivityGroupVO();
            group.setGroupKey(entry.getKey());
            group.setGroupName(GROUP_NAME_MAP.getOrDefault(entry.getKey(), "--"));
            group.setActivityList(entry.getValue());
            result.getData().add(group);
        }


        if (total <= 0) {
            result.setNavigateStrategy("no_activity");
        } else if (!Boolean.TRUE.equals(result.getIsRio()) && total == 1 && inProgress == 1) {
            result.setNavigateStrategy("proof_audit");
        } else if (total == 1 && completed == 1) {
            result.setNavigateStrategy("activity_list");
        } else {
            result.setNavigateStrategy("activity_list");
        }

        makeUpGroup(result);
        return result;
    }

    private EnableList.Result listWhenSchedule(ControllerContext context, EnableList.Arg arg, IObjectData store) {
        EnableList.Result result = new EnableList.Result();

        Map<String, List<String>> functionCodeListMap = new HashMap<>();
        functionCodeListMap.put(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, Lists.newArrayList("Add", "Edit", "View", "List"));
        result.setFunctionCodeMap(queryFunctionCodeMap(functionCodeListMap));
        result.setIsRio(TPMGrayUtils.isRioTenant(context.getTenantId()));
        String dealerId = null;
        if (store != null) {
            result.setStoreId(store.getId());
            result.setStoreName(store.getName());
            IObjectData dealer = getDealer(store);
            if (dealer != null) {
                result.setDealerId(dealer.getId());
                result.setDealerName(dealer.getName());
            }
            dealerId = storeBusiness.findDealerId(context.getTenantId(), store);
        }
        result.setSystemTime(System.currentTimeMillis());

        List<Integer> departmentIds = new ArrayList<>();
        if (!controllerContext.getUser().isOutUser()) {
            departmentIds = organizationService.getDepartmentIds(controllerContext.getUser().getTenantIdInt(), Integer.parseInt(controllerContext.getUser().getUpstreamOwnerIdOrUserId()));
            if (CollectionUtils.isEmpty(departmentIds)) {
                return result;
            }
            log.info("departments : {}", departmentIds);
        }
        long now = System.currentTimeMillis();

        List<String> activityTypeList = getActivityType(arg.getActivityTypeList());

        List<IObjectData> activities = store == null ? queryBrandActivity(now, departmentIds, activityTypeList) :
                TPMGrayUtils.isYinLuEnableList(context.getTenantId()) ? queryActivity(departmentIds, store.getId(), activityTypeList) : queryActivity(context, now, departmentIds, store.getId(), dealerId, activityTypeList);

        log.info("activities ids: {}", activities.stream().map(IObjectData::getId).collect(Collectors.toList()));

        // 根据活动和当前时间查询举证时段数据
        activities = filterActivitiesByProofTimePeriod(context, now, activities);

        Map<String, Long> proofCountMap = countProof(context.getTenantId(), arg.getStoreId()).stream().collect(Collectors.toMap(k -> k.get(TPMActivityProofFields.ACTIVITY_ID).toString(), v -> Long.parseLong(v.get("count").toString())));
        List<IObjectData> proofList = queryProof(context, arg.getStoreId(), arg.getVisitId(), arg.getActionId(), arg.getActivityIds());
        Map<String, Long> agreementCountMap = countAgreement(context.getTenantId(), arg.getStoreId()).stream().collect(Collectors.toMap(k -> k.get(TPMActivityAgreementFields.ACTIVITY_ID) == null ? "" : k.get(TPMActivityAgreementFields.ACTIVITY_ID).toString(), v -> Long.parseLong(v.get("count").toString())));

        //获取 proofList 中的 举证id
        List<String> proofIds = proofList.stream().map(IObjectData::getId).collect(Collectors.toList());

        List<ActivityDisplayImgPO> activityDisplayImgList = activityDisplayImgDAO.findByProofIds(context.getTenantId(), proofIds);

        // 将 activityDisplayImgList 转换为 map
        Map<String, String> activityDisplayImgMap = new HashMap<>();
        if (activityDisplayImgList != null && !activityDisplayImgList.isEmpty()) {
            activityDisplayImgMap = activityDisplayImgList.stream()
                    .collect(Collectors.toMap(
                            ActivityDisplayImgPO::getProofId,
                            ActivityDisplayImgPO::getErrorMessage,
                            (existing, replacement) -> existing
                    ));
        }
        Map<String, List<String>> proofErrorMessageMap = new HashMap<>();
        Map<String, String> proofAiStatusMap = new HashMap<>();
        Map<String, IObjectData> proofMap = new HashMap<>();
        for (IObjectData proof : proofList) {
            String activityId = (String) proof.get(TPMActivityProofFields.ACTIVITY_ID);
            proofMap.put(activityId, proof);

            fillProofAIMap(proof, proofAiStatusMap, activityId, activityDisplayImgMap, proofErrorMessageMap);

        }

        List<IObjectData> completedActivities = queryActivity(context, new ArrayList<>(proofMap.keySet()));
        if (!CollectionUtils.isEmpty(arg.getActivityIds())){
            activities = completedActivities;
        }else{
            activities.addAll(completedActivities);
        }
        Map<String, ActivityTypeExt> activityTypeExtMap = activityTypeManager.findByActivityTypeIds(controllerContext.getTenantId(), activities.stream().map(v -> v.get(TPMActivityFields.ACTIVITY_TYPE, String.class)).filter(v -> !Strings.isNullOrEmpty(v)).collect(Collectors.toList())).stream().collect(Collectors.toMap(v -> v.get().getId().toString(), v -> v, (before, after) -> before));


        Map<String, List<EnableList.ActivityVO>> activityMap = new HashMap<>();
        AtomicInteger total = new AtomicInteger(0);
        AtomicInteger inProgress = new AtomicInteger(0);
        AtomicInteger completed = new AtomicInteger(0);

        sortOutActivities(activities, proofMap, activityMap, dealerId, store == null ? null : store.getId(), proofCountMap, agreementCountMap, activityTypeExtMap, total, inProgress, completed, proofAiStatusMap, proofErrorMessageMap);

        for (Map.Entry<String, List<EnableList.ActivityVO>> entry : activityMap.entrySet()) {
            EnableList.ActivityGroupVO group = new EnableList.ActivityGroupVO();
            group.setGroupKey(entry.getKey());
            group.setGroupName(GROUP_NAME_MAP.getOrDefault(entry.getKey(), "--"));
            group.setActivityList(entry.getValue());
            result.getData().add(group);
        }

        result.getData().sort(Comparator.comparing(EnableList.ActivityGroupVO::getGroupKey).reversed());

        if (total.get() <= 0) {
            result.setNavigateStrategy("no_activity");
        } else if (!Boolean.TRUE.equals(result.getIsRio()) && total.get() == 1 && inProgress.get() == 1) {
            result.setNavigateStrategy("proof_audit");
        } else if (total.get() == 1 && completed.get() == 1) {
            result.setNavigateStrategy("activity_list");
        } else {
            result.setNavigateStrategy("activity_list");
        }
        makeUpGroup(result);
        return result;
    }

    private void sortOutActivities(List<IObjectData> activities, Map<String, IObjectData> proofMap, Map<String, List<EnableList.ActivityVO>> activityMap, String dealerId, String storeId, Map<String, Long> proofCountMap, Map<String, Long> agreementCountMap, Map<String, ActivityTypeExt> activityTypeExtMap, AtomicInteger total, AtomicInteger inProgress, AtomicInteger completed, Map<String, String> proofAiStatusMap, Map<String, List<String>> proofErrorMessageMap) {
        Set<String> duplicateActivityIdCache = new HashSet<>();
        long now = System.currentTimeMillis();

        Map<String, Boolean> validateMap = rangeFieldBusiness.judgeStoreInActivitiesStoreRange(controllerContext.getTenantId(), storeId, dealerId, activities, false, true);

        for (IObjectData activity : activities) {
            if (duplicateActivityIdCache.contains(activity.getId())) {
                continue;
            }
            if (!proofMap.containsKey(activity.getId())) {
                if (!validateMap.getOrDefault(activity.getId(), false)) {
                    continue;
                }
            }

            EnableList.ActivityVO datum = toActivityVO(controllerContext, now, activity, proofCountMap, proofMap, agreementCountMap, activityTypeExtMap, proofAiStatusMap, proofErrorMessageMap);
            if (TPMGrayUtils.isYinLu(controllerContext.getTenantId()) && "no_agreement".equals(datum.getStatus()) || mnLogic && !("in_progress".equals(datum.getStatus()) || "completed".equals(datum.getStatus()))) {
                continue;
            }

            duplicateActivityIdCache.add(datum.getId());

            total.incrementAndGet();
            if ("in_progress".equals(datum.getStatus())) {
                inProgress.incrementAndGet();
            }
            if ("completed".equals(datum.getStatus())) {
                completed.incrementAndGet();
            }

            if (datum.getProofCount() > 0) {
                if (!activityMap.containsKey(PARTICIPATED_GROUP_KEY)) {
                    activityMap.put(PARTICIPATED_GROUP_KEY, Lists.newArrayList(datum));
                } else {
                    activityMap.get(PARTICIPATED_GROUP_KEY).add(datum);
                }
            } else {
                if (!activityMap.containsKey(NO_PARTICIPATED_GROUP_KEY)) {
                    activityMap.put(NO_PARTICIPATED_GROUP_KEY, Lists.newArrayList(datum));
                } else {
                    activityMap.get(NO_PARTICIPATED_GROUP_KEY).add(datum);
                }
            }
        }
    }

    private List<IObjectData> queryProof(ControllerContext context, String storeId, String visitId, String actionId, List<String> activityIds) {
        if (Strings.isNullOrEmpty(visitId) || Strings.isNullOrEmpty(actionId)) {
            return Lists.newArrayList();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        if (!Strings.isNullOrEmpty(storeId)) {
            Filter storeIdFilter = new Filter();
            storeIdFilter.setFieldName(TPMActivityProofFields.STORE_ID);
            storeIdFilter.setOperator(Operator.EQ);
            storeIdFilter.setFieldValues(Lists.newArrayList(storeId));
            query.getFilters().add(storeIdFilter);
        }

        if (!CollectionUtils.isEmpty(activityIds)) {
            Filter activityIdsFilter = new Filter();
            activityIdsFilter.setFieldName(TPMActivityProofFields.ACTIVITY_ID);
            activityIdsFilter.setOperator(Operator.IN);
            activityIdsFilter.setFieldValues(activityIds);
            query.getFilters().add(activityIdsFilter);
        }

        Filter visitIdFilter = new Filter();
        visitIdFilter.setFieldName(TPMActivityProofFields.VISIT_ID);
        visitIdFilter.setOperator(Operator.EQ);
        visitIdFilter.setFieldValues(Lists.newArrayList(visitId));

        Filter actionIdFilter = new Filter();
        actionIdFilter.setFieldName(TPMActivityProofFields.ACTION_ID);
        actionIdFilter.setOperator(Operator.EQ);
        actionIdFilter.setFieldValues(Lists.newArrayList(actionId));

        query.setFilters(Lists.newArrayList(actionIdFilter, visitIdFilter));

        OrderBy orderBy = new OrderBy();
        orderBy.setFieldName("create_time");
        orderBy.setIsAsc(true);
        orderBy.setIsNullLast(false);

        query.setOrders(Lists.newArrayList(orderBy));

        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query);
    }

    private Map<String, Map<String, Boolean>> queryFunctionCodeMap(Map<String, List<String>> functionCodeListMap) {
        PrivilegeResult<Map<String, Map<String, Boolean>>> codeRst = serviceFacade.batchObjectFuncPrivilegeCheck(controllerContext.getRequestContext(), functionCodeListMap);
        return codeRst.getCode() == 0 ? codeRst.getResult() : new HashMap<>();
    }

    private List<IObjectData> queryActivityV1(ControllerContext context, List<String> activityIds) {
        if (activityIds.isEmpty()) {
            return new ArrayList<>();
        }
        String sql = String.format("select id as _id, * from fmcg_tpm_activity where tenant_id = '%s' and id in (%s)", context.getTenantId(), activityIds.stream().map(id -> String.format("'%s'", id)).collect(Collectors.joining(",")));
        if (TPMGrayUtils.isYinLu(controllerContext.getTenantId())) {
            sql = String.format("select id as _id, * from fmcg_tpm_activity where tenant_id = '%s' and create_time >= '%s' and id in (%s)", context.getTenantId(), YINLU_FILTER_TIME, activityIds.stream().map(id -> String.format("'%s'", id)).collect(Collectors.joining(",")));
        }
        try {
            return objectDataService.findBySql(sql, context.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ).getData();
        } catch (MetadataServiceException e) {
            return Lists.newArrayList();
        }
    }

    private List<IObjectData> queryBrandActivity(long now, List<Integer> departmentIds, List<String> activityTypeList) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setSearchSource("db");

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setFilters(Lists.newArrayList());
        query.setSearchSource("db");

        int number = 4;

        StringBuilder pattern = new StringBuilder(" 1 and 2 and 3 and 4 ");

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LT);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));
        query.getFilters().add(beginDateFilter);

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityFields.END_DATE);
        endDateFilter.setOperator(Operator.GT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));
        query.getFilters().add(endDateFilter);

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("normal"));
        query.getFilters().add(lifeStatusFilter);

        Filter closeStatusFilter = new Filter();
        closeStatusFilter.setFieldName(TPMActivityFields.CLOSE_STATUS);
        closeStatusFilter.setOperator(Operator.EQ);
        closeStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.CLOSE_STATUS__UNCLOSED));
        query.getFilters().add(closeStatusFilter);

        if (!CollectionUtils.isEmpty(activityTypeList) && !activityTypeList.contains("all")) {
            if (activityTypeList.size() == 1) {
                Filter activityType = new Filter();
                activityType.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
                activityType.setOperator(Operator.EQ);
                activityType.setFieldValues(activityTypeList);
                query.getFilters().add(activityType);
            } else {
                Filter activityType = new Filter();
                activityType.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
                activityType.setOperator(Operator.IN);
                activityType.setFieldValues(activityTypeList);
                query.getFilters().add(activityType);
            }
            pattern.append(" and ").append(++number).append(" ");
        }
        pattern.append(" and ").append(++number).append(" ");

        Filter customerTypeFilter = new Filter();
        customerTypeFilter.setFieldName(TPMActivityFields.CUSTOMER_TYPE);
        customerTypeFilter.setOperator(Operator.EQ);
        customerTypeFilter.setFieldValues(Lists.newArrayList(ActivityCustomerTypeEnum.BRAND.value()));
        query.getFilters().add(customerTypeFilter);


        if (!controllerContext.getUser().isOutUser() && !TPMGrayUtils.denyDepartmentFilterOnActivity(controllerContext.getTenantId())) {

            Filter departmentRangeFilter = new Filter();
            departmentRangeFilter.setFieldName(TPMActivityFields.DEPARTMENT_RANGE);
            departmentRangeFilter.setOperator(Operator.IN);
            departmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));

            Filter multiDepartmentRangeFilter = new Filter();
            multiDepartmentRangeFilter.setFieldName(TPMActivityFields.MULTI_DEPARTMENT_RANGE);
            multiDepartmentRangeFilter.setOperator(Operator.HASANYOF);
            multiDepartmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));

            if (I_DESCRIBE_CACHE_SERVICE.isExistField(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, TPMActivityFields.DEPARTMENT_RANGE)) {
                pattern.append(" and (").append(++number).append(" or ").append(++number).append(" ) ");
                query.getFilters().addAll(Lists.newArrayList(departmentRangeFilter, multiDepartmentRangeFilter));
            } else {
                pattern.append(" and ").append(++number).append(" ");
                query.getFilters().add(multiDepartmentRangeFilter);
            }
        }
        if (TPMGrayUtils.isYinLu(controllerContext.getTenantId())) {
            //创建时间大于 5.28号的才可以被查询
            Filter timeFilter = new Filter();
            timeFilter.setFieldName(CommonFields.CREATE_TIME);
            timeFilter.setOperator(Operator.GT);
            timeFilter.setFieldValues(Lists.newArrayList(YINLU_FILTER_TIME));
            query.getFilters().add(timeFilter);

            pattern.append(" and ").append(++number).append(" ");
        }
        query.setPattern(pattern.toString());
        SearchQueryUtil.handleDataPrivilege(dataPrivilegeService, controllerContext.getUser(), query, this.activityDescribe, this.activitySearchTemplate, this.isAdminRequest);
        return CommonUtils.queryData(serviceFacade, controllerContext.getUser(), ApiNames.TPM_ACTIVITY_OBJ, query);
    }

    private List<IObjectData> queryActivity(ControllerContext context, List<String> activityIds) {
        if (activityIds.isEmpty()) {
            return new ArrayList<>();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(activityIds);

        query.setFilters(Lists.newArrayList(idFilter));

        if (TPMGrayUtils.isYinLu(controllerContext.getTenantId())) {
            //创建时间大于 5.28号的才可以被查询
            Filter timeFilter = new Filter();
            timeFilter.setFieldName(CommonFields.CREATE_TIME);
            timeFilter.setOperator(Operator.GT);
            timeFilter.setFieldValues(Lists.newArrayList(YINLU_FILTER_TIME));
            query.getFilters().add(timeFilter);
        }

        SearchQueryUtil.handleDataPrivilege(dataPrivilegeService, controllerContext.getUser(), query, this.activityDescribe, this.activitySearchTemplate, this.isAdminRequest);
        return CommonUtils.queryData(serviceFacade, context.getUser(), ApiNames.TPM_ACTIVITY_OBJ, query);
    }

    private List<Map> countProof(String tenantId, String storeId) {
        String sql;
        if (!Strings.isNullOrEmpty(storeId)) {
            sql = String.format("select activity_id, count(1) as count from fmcg_tpm_activity_proof where tenant_id = '%s' and store_id = '%s' and life_status = 'normal' group by activity_id", tenantId, SqlEscaper.pg_escape(storeId));
        } else {
            sql = String.format("select activity_id, count(1) as count from fmcg_tpm_activity_proof where tenant_id = '%s'  and life_status = 'normal' group by activity_id", tenantId);
        }
        try {
            return objectDataService.findBySql(tenantId, sql);
        } catch (MetadataServiceException e) {
            return Lists.newArrayList();
        }
    }

    private List<Map> countAgreement(String tenantId, String storeId) {
        if (Strings.isNullOrEmpty(storeId)) {
            return Lists.newArrayList();
        }
        String sql = String.format("select activity_id, count(1) as count from fmcg_tpm_activity_agreement where tenant_id = '%s' and store_id = '%s' and is_deleted = 0 group by activity_id", tenantId, SqlEscaper.pg_escape(storeId));
        try {
            return objectDataService.findBySql(tenantId, sql);
        } catch (MetadataServiceException e) {
            return Lists.newArrayList();
        }
    }

    private List<IObjectData> queryActivityV1(ControllerContext context, long now, List<Integer> departmentIds, String storeId, String dealerId, List<String> activityTypeList) {
        String sql = "select\n" + "    id as _id,*\n" + "from\n" + "    fmcg_tpm_activity table_01\n" + "where\n" + "    tenant_id = '[tenant_id]'\n" + "    and life_status = 'normal'\n" + "    and begin_date < [now]\n" + "    and end_date > [now]\n" + "    and ( [skip_department_filter] = true or department_range in ([department_ids]))\n" + "    and (\n" + "        '[activity_type]' = 'all'\n" + "        or activity_type = '[activity_type]'\n" + "    )\n" + "    and (\n" + "        (dealer_id = '[store_id]')\n" + "        or (\n" + "            dealer_id is null\n" + "            and (\n" + "                store_range like '%\"ALL\"%'\n" + "                or (\n" + "                    store_range like '%\"FIXED\"%'\n" + "                    and exists (\n" + "                        select\n" + "                            id\n" + "                        from\n" + "                            fmcg_tpm_activity_store table_02\n" + "                        where\n" + "                            table_02.tenant_id = table_01.tenant_id\n" + "                            and table_02.life_status = 'normal'\n" + "                            and table_02.is_deleted = 0\n" + "                            and table_02.activity_id = table_01.id\n" + "                            and table_02.store_id = '[store_id]'\n" + "                    )\n" + "                )\n" + "                or store_range like '%\"CONDITION\"%'\n" + "            )\n" + "        )\n" + "        or (\n" + "            dealer_id = '[dealer_id]'\n" + "            and (\n" + "                store_range like '%\"ALL\"%'\n" + "                or (\n" + "                    store_range like '%\"FIXED\"%'\n" + "                    and exists (\n" + "                        select\n" + "                            id\n" + "                        from\n" + "                            fmcg_tpm_activity_store table_03\n" + "                        where\n" + "                            table_03.tenant_id = table_01.tenant_id\n" + "                            and table_03.life_status = 'normal'\n" + "                            and table_03.is_deleted = 0\n" + "                            and table_03.activity_id = table_01.id\n" + "                            and table_03.store_id = '[store_id]'\n" + "                    )\n" + "                )\n" + "                or store_range like '%\"CONDITION\"%'\n" + "            )\n" + "        )\n" + "    )";

        sql = sql.replace("[now]", String.valueOf(now));
        sql = sql.replace("[tenant_id]", context.getTenantId());
        String departmentIdsStr = departmentIds.stream().map(id -> String.format("'%s'", id)).collect(Collectors.joining(","));
        boolean skipDepartmentFilter = controllerContext.getUser().isOutUser() || TPMGrayUtils.denyDepartmentFilterOnActivity(context.getTenantId());
        sql = sql.replace("[skip_department_filter]", String.valueOf(skipDepartmentFilter));
        sql = sql.replace("[department_ids]", departmentIdsStr);
        String activityTypeStr = "all";
        if (CollectionUtils.isEmpty(activityTypeList) || activityTypeList.contains("all")) {
            activityTypeStr = "all";
        } else {
            activityTypeStr = activityTypeList.get(0);
        }
        sql = sql.replace("[activity_type]", activityTypeStr);
        sql = sql.replace("[store_id]", Strings.isNullOrEmpty(storeId) ? "" : storeId);
        sql = sql.replace("[dealer_id]", Strings.isNullOrEmpty(dealerId) ? "EMPTY" : dealerId);

        log.info("find activity sql : {}", sql);

        try {
            return objectDataService.findBySql(sql, context.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ).getData();
        } catch (MetadataServiceException e) {
            return Lists.newArrayList();
        }
    }

    private List<IObjectData> queryActivity(ControllerContext context, long now, List<Integer> departmentIds, String storeId, String dealerId, List<String> activityTypeList) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setSearchSource("db");

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        int number = 6;

        StringBuilder pattern = new StringBuilder(" 1 and 2 and 3 and 4 and (5 or 6) ");

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LT);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));
        query.getFilters().add(beginDateFilter);

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityFields.END_DATE);
        endDateFilter.setOperator(Operator.GT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));
        query.getFilters().add(endDateFilter);

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("normal"));
        query.getFilters().add(lifeStatusFilter);

        Filter closeStatusFilter = new Filter();
        closeStatusFilter.setFieldName(TPMActivityFields.CLOSE_STATUS);
        closeStatusFilter.setOperator(Operator.EQ);
        closeStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.CLOSE_STATUS__UNCLOSED));
        query.getFilters().add(closeStatusFilter);


        Filter customerTypeFilter = new Filter();
        customerTypeFilter.setFieldName(TPMActivityFields.CUSTOMER_TYPE);
        customerTypeFilter.setOperator(Operator.NEQ);
        customerTypeFilter.setFieldValues(Lists.newArrayList(ActivityCustomerTypeEnum.BRAND.value()));
        query.getFilters().add(customerTypeFilter);

        Filter customerTypeNullFilter = new Filter();
        customerTypeNullFilter.setFieldName(TPMActivityFields.CUSTOMER_TYPE);
        customerTypeNullFilter.setOperator(Operator.IS);
        customerTypeNullFilter.setFieldValues(Lists.newArrayList());
        query.getFilters().add(customerTypeNullFilter);


        if (!CollectionUtils.isEmpty(activityTypeList) && !activityTypeList.contains("all")) {
            if (activityTypeList.size() == 1) {
                Filter activityType = new Filter();
                activityType.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
                activityType.setOperator(Operator.EQ);
                activityType.setFieldValues(activityTypeList);
                query.getFilters().add(activityType);
            } else {
                Filter activityType = new Filter();
                activityType.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
                activityType.setOperator(Operator.IN);
                activityType.setFieldValues(activityTypeList);
                query.getFilters().add(activityType);
            }
            pattern.append(" and ").append(++number).append(" ");
        }

        pattern.append(" and (").append(++number).append(" or ").append(++number).append(" ) ");
        if (!Strings.isNullOrEmpty(dealerId)) {

            Filter dealerIdEqualFilter = new Filter();
            dealerIdEqualFilter.setFieldName(TPMActivityFields.DEALER_ID);
            dealerIdEqualFilter.setOperator(Operator.EQ);
            dealerIdEqualFilter.setFieldValues(Lists.newArrayList(dealerId));

            Filter dealerIdEmptyFilter = new Filter();
            dealerIdEmptyFilter.setFieldName(TPMActivityFields.DEALER_ID);
            dealerIdEmptyFilter.setOperator(Operator.IS);
            dealerIdEmptyFilter.setFieldValues(Lists.newArrayList());

            query.getFilters().addAll(Lists.newArrayList(dealerIdEmptyFilter, dealerIdEqualFilter));

        } else {

            Filter dealerIdEqualStoreIdFilter = new Filter();
            dealerIdEqualStoreIdFilter.setFieldName(TPMActivityFields.DEALER_ID);
            dealerIdEqualStoreIdFilter.setOperator(Operator.EQ);
            dealerIdEqualStoreIdFilter.setFieldValues(Lists.newArrayList(storeId));

            Filter dealerIdEmptyFilter = new Filter();
            dealerIdEmptyFilter.setFieldName(TPMActivityFields.DEALER_ID);
            dealerIdEmptyFilter.setOperator(Operator.IS);
            dealerIdEmptyFilter.setFieldValues(Lists.newArrayList());

            query.getFilters().addAll(Lists.newArrayList(dealerIdEmptyFilter, dealerIdEqualStoreIdFilter));
        }

        if (!controllerContext.getUser().isOutUser() && !TPMGrayUtils.denyDepartmentFilterOnActivity(context.getTenantId())) {

            Filter departmentRangeFilter = new Filter();
            departmentRangeFilter.setFieldName(TPMActivityFields.DEPARTMENT_RANGE);
            departmentRangeFilter.setOperator(Operator.IN);
            departmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));

            Filter multiDepartmentRangeFilter = new Filter();
            multiDepartmentRangeFilter.setFieldName(TPMActivityFields.MULTI_DEPARTMENT_RANGE);
            multiDepartmentRangeFilter.setOperator(Operator.HASANYOF);
            multiDepartmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));

            if (I_DESCRIBE_CACHE_SERVICE.isExistField(context.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, TPMActivityFields.DEPARTMENT_RANGE)) {
                pattern.append(" and (").append(++number).append(" or ").append(++number).append(" ) ");
                query.getFilters().addAll(Lists.newArrayList(departmentRangeFilter, multiDepartmentRangeFilter));
            } else {
                pattern.append(" and ").append(++number).append(" ");
                query.getFilters().add(multiDepartmentRangeFilter);
            }
        }
        query.setPattern(pattern.toString());
        SearchQueryUtil.handleDataPrivilege(dataPrivilegeService, controllerContext.getUser(), query, this.activityDescribe, this.activitySearchTemplate, this.isAdminRequest);
        List<String> returnFields = Lists.newArrayList(CommonFields.ID, CommonFields.TENANT_ID, CommonFields.NAME, CommonFields.CREATE_TIME, CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.LIFE_STATUS,
                TPMActivityFields.PROOF_RECORD_TYPE__C, TPMActivityFields.PROOF_RECORD_TYPE, TPMActivityFields.END_DATE, TPMActivityFields.BEGIN_DATE,
                TPMActivityFields.ACTIVITY_TYPE, TPMActivityFields.IS_NEED_RIO_STORE_CONFIRM, TPMActivityFields.STORE_RANGE, TPMActivityFields.CUSTOMER_TYPE,
                TPMActivityFields.DEALER_ID, TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, TPMActivityFields.PROOF_PERIOD, TPMActivityFields.MULTI_DEPARTMENT_RANGE,
                TPMActivityFields.DEPARTMENT_RANGE);
        if (TPMGrayUtils.isEnableQueryWithFieldByCyt(controllerContext.getTenantId())){
            log.info("queryWithFields tenantId is {}, fields size is {}", controllerContext.getTenantId(), returnFields.size());
            return CommonUtils.queryData(serviceFacade, context.getUser(), context.getRequestContext(), ApiNames.TPM_ACTIVITY_OBJ, query, returnFields);
        } else {
            return CommonUtils.queryData(serviceFacade, context.getUser(), ApiNames.TPM_ACTIVITY_OBJ, query);
        }
    }


    private List<IObjectData> queryConditionStore(ControllerContext context, JSONObject storeRange, String storeId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");

        List<Wheres> conditionWheres = new ArrayList<>();
        String valueJson = storeRange.getString("value");
        JSONArray wheres = JSON.parseArray(valueJson);
        for (int whereIndex = 0; whereIndex < wheres.size(); whereIndex++) {
            JSONObject where = wheres.getJSONObject(0);
            Wheres conditionWhere = new Wheres();
            conditionWhere.setConnector(where.getString("connector"));
            conditionWhere.setFilters(Lists.newArrayList());
            JSONArray filters = where.getJSONArray("filters");
            for (int filterIndex = 0; filterIndex < filters.size(); filterIndex++) {
                JSONObject filter = filters.getJSONObject(filterIndex);
                Filter conditionFilter = new Filter();
                conditionFilter.setFieldName(filter.getString("field_name"));
                conditionFilter.setIndexName(filter.getString("index_name"));
                conditionFilter.setFieldValueType(filter.getString("field_value_type"));
                conditionFilter.setOperator(Operator.valueOf(filter.getString("operator")));
                conditionFilter.setFieldValues(filter.getJSONArray("field_values").toJavaList(String.class));
                conditionFilter.setConnector(filter.getString("connector"));
                conditionFilter.setValueType(filter.getInteger("value_type"));
                conditionFilter.setRefDescribeApiName(filter.getString("ref_describe_api_name"));
                conditionFilter.setRefFieldApiName(filter.getString("ref_field_api_name"));
                conditionFilter.setIsCascade(filter.getBoolean("is_cascade"));
                conditionFilter.setIsMasterField(filter.getBoolean("is_master_field"));
                conditionFilter.setFilterGroup(filter.getString("filterGroup"));
                conditionWhere.getFilters().add(conditionFilter);
            }

            Filter idFilter = new Filter();
            idFilter.setFieldName("_id");
            idFilter.setOperator(Operator.EQ);
            idFilter.setFieldValues(Lists.newArrayList(storeId));
            idFilter.setConnector("AND");

            conditionWhere.getFilters().add(idFilter);
            conditionWheres.add(conditionWhere);
        }
        query.setWheres(conditionWheres);
        return serviceFacade.findBySearchQuery(User.systemUser(context.getTenantId()), ApiNames.ACCOUNT_OBJ, query).getData();
    }

    private EnableList.ActivityVO toActivityVO(ControllerContext context, long now, IObjectData activity, Map<String, Long> proofCountMap, Map<String, IObjectData> proofMap, Map<String, Long> agreementCountMap, Map<String, ActivityTypeExt> activityTypeExtMap, Map<String, String> proofAiStatusMap, Map<String, List<String>> proofErrorMessageMap) {
        boolean agreementRequired = tpm2Service.isNeedAgreement(Integer.valueOf(controllerContext.getTenantId()), activity);
        String activityType = activity.get(TPMActivityFields.ACTIVITY_TYPE, String.class);

        EnableList.ActivityVO datum = new EnableList.ActivityVO();

        datum.setId(activity.getId());
        datum.setName(activity.getName());
        datum.setBeginDate((long) activity.get(TPMActivityFields.BEGIN_DATE));
        datum.setEndDate((long) activity.get(TPMActivityFields.END_DATE));
        datum.setAgreementRequired(agreementRequired);
        String proofRecordType = activity.get(TPMActivityFields.PROOF_RECORD_TYPE, String.class);
        if (Strings.isNullOrEmpty(proofRecordType)) {
            proofRecordType = activity.get(TPMActivityFields.PROOF_RECORD_TYPE__C, String.class);
            if (Strings.isNullOrEmpty(proofRecordType)) {
                proofRecordType = "default__c";
            }
        }
        datum.setAiStatus(getAiStatus(activity.getId(), proofAiStatusMap, proofErrorMessageMap));
        if (agreementRequired) {
            ActivityNodeEntity agreementNode = activityTypeExtMap.get(activityType).agreementNode();
            if (agreementNode != null) {
                datum.setAgreementRecordType(agreementNode.getObjectRecordType());
            }
        }
        if (isTpm2Tenant) {

            if (!Strings.isNullOrEmpty(activityType) && activityTypeExtMap.containsKey(activityType)) {
                ActivityTypeExt activityTypeExt = activityTypeExtMap.get(activityType);
                if (activityTypeExt.proofNode() != null) {
                    proofRecordType = activityTypeExt.proofNode().getObjectRecordType();
                }
                datum.setActivityTypeName(activityTypeExt.get().getName());
                String templateId = activityTypeExt.get().getTemplateId();
                log.info("activityTypeExt get templateId is {}", templateId);
                // 协议类的陈列铺货类型
                boolean flag = !Strings.isNullOrEmpty(templateId) && templateId.contains("display") && activityTypeExt.agreementNode() != null;
                datum.setIsDisplayActivity(flag);
                datum.setIsShowReportLink(datum.getAiStatus() != null && "0".equals(datum.getAiStatus().getStatus()) && flag);
            }
        }

        datum.setProofRecordType(proofRecordType);
        datum.setAgreementCount(agreementCountMap.getOrDefault(activity.getId(), 0L));

        List<IObjectData> agreements = datum.getAgreementCount() != 0 ? queryAgreement(context, now, arg.getStoreId(), activity.getId()) : new ArrayList<>();

        Map<String, List<IObjectData>> agreementMap;
        if (TPMGrayUtils.agreementNotRelatedToActivity(controllerContext.getTenantId())) {
            agreementMap = new HashMap<>();
        } else {
            agreementMap = agreements.stream().collect(Collectors.groupingBy(agreement -> (String) agreement.get(TPMActivityAgreementFields.ACTIVITY_ID)));
        }

        if (!TPMGrayUtils.agreementNotRelatedToActivity(controllerContext.getTenantId()) && agreementRequired && agreementMap.containsKey(activity.getId())) {
            IObjectData agreement = agreementMap.get(activity.getId()).get(0);
            datum.setActivityAgreementId(agreement.getId());
            datum.setAgreementLifeStatus(agreement.get(CommonFields.LIFE_STATUS, String.class));
            datum.setAgreementBeginDate((long) agreement.get(TPMActivityAgreementFields.BEGIN_DATE));
            datum.setAgreementEndDate((long) agreement.get(TPMActivityAgreementFields.END_DATE));
            datum.setAgreementStatus(agreement.get(TPMActivityAgreementFields.AGREEMENT_STATUS, String.class));
            datum.setAgreementRecordType(agreement.getRecordType());
            datum.setAgreementSigningMode(agreement.get(TPMActivityAgreementFields.SIGNING_MODE, String.class));
            datum.setAgreementStoreConfirmStatus(agreement.get(TPMActivityAgreementFields.STORE_CONFIRM_STATUS, String.class));
            boolean isRio = TPMGrayUtils.isRioTenant(controllerContext.getTenantId());
            if (isRio) {
                String rioStoreConfirmStatus = agreement.get(TPMActivityAgreementFields.STORE_CONFIRM_STATUS_FOR_RIO, String.class, "");
                String isNeedRioStoreConfirm = activity.get(TPMActivityFields.IS_NEED_RIO_STORE_CONFIRM, String.class, "");
                datum.setIsNeedRioStoreConfirm("1".equals(rioStoreConfirmStatus) && "1".equals(isNeedRioStoreConfirm));
            }
            datum.setActivityAgreementName(agreement.getName());
        }

        datum.setProofCount(proofCountMap.getOrDefault(activity.getId(), 0L));
        if (proofMap.containsKey(activity.getId())) {
            datum.setStatus("completed");
            datum.setDataApiName(ApiNames.TPM_ACTIVITY_PROOF_OBJ);
            datum.setDataId(proofMap.get(activity.getId()).getId());
        } else {
            if (!TPMGrayUtils.agreementNotRelatedToActivity(controllerContext.getTenantId()) && datum.isAgreementRequired() && Strings.isNullOrEmpty(datum.getActivityAgreementId())) {
                datum.setStatus("no_agreement");
            } else if (TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE.equals(datum.getAgreementStatus())) {
                datum.setStatus("schedule_agreement");
            } else if (TPMActivityAgreementFields.AGREEMENT_STATUS__END.equals(datum.getAgreementStatus())) {
                datum.setStatus("agreement_expired");
            } else if (TPMActivityAgreementFields.AGREEMENT_STATUS__INVALID.equals(datum.getAgreementStatus())) {
                datum.setStatus("agreement_invalid");
            } else if (TPMActivityAgreementFields.SIGNING_MODE__AGENT_SIGNING.equals(datum.getAgreementSigningMode()) && TPMActivityAgreementFields.STORE_CONFIRM_STATUS__UNCONFIRMED.equals(datum.getAgreementStoreConfirmStatus())) {
                datum.setStatus("agreement_not_confirmed");
            } else if (Boolean.TRUE.equals(datum.getIsNeedRioStoreConfirm())) {
                datum.setStatus("agreement_not_confirmed_rio");
            } else if (TPMActivityAgreementFields.AGREEMENT_STATUS__CLOSE.equals(datum.getAgreementStatus())) {
                datum.setStatus("agreement_close");
            } else {
                datum.setStatus("in_progress");
            }
        }
        return datum;
    }

    private List<IObjectData> queryAgreement(ControllerContext context, long now, String storeId, String activityId) {

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityAgreementFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LT);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityAgreementFields.END_DATE);
        endDateFilter.setOperator(Operator.GT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(TPMActivityAgreementFields.STORE_ID);
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(storeId));

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(TPMActivityAgreementFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        boolean isContainsProtocolStatus = describeCacheService.isExistField(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, TPMActivityAgreementFields.RIO_PROTOCOL_STATUS);

        Filter invalidFilter = new Filter();
        invalidFilter.setFieldName(TPMActivityAgreementFields.RIO_PROTOCOL_STATUS);
        invalidFilter.setOperator(Operator.NEQ);
        invalidFilter.setFieldValues(Lists.newArrayList("void"));

        Filter invalidEmptyFilter = new Filter();
        invalidEmptyFilter.setFieldName(TPMActivityAgreementFields.RIO_PROTOCOL_STATUS);
        invalidEmptyFilter.setOperator(Operator.IS);
        invalidEmptyFilter.setFieldValues(Lists.newArrayList());

        query.setFilters(Lists.newArrayList(beginDateFilter, endDateFilter, storeIdFilter, activityIdFilter));

        if (isContainsProtocolStatus) {
            query.getFilters().add(invalidFilter);
            query.getFilters().add(invalidEmptyFilter);
            query.setPattern("1 and 2 and 3 and 4 and (5 or 6)");
        }


        List<IObjectData> agreements = CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query);
        //查未来
        if (CollectionUtils.isEmpty(agreements)) {
            query.setLimit(1);
            OrderBy timeOrder = new OrderBy();
            timeOrder.setIsAsc(true);
            timeOrder.setFieldName(TPMActivityAgreementFields.BEGIN_DATE);
            query.getOrders().clear();
            query.setOrders(Lists.newArrayList(timeOrder));
            query.getFilters().clear();

            Filter beginDate1Filter = new Filter();
            beginDate1Filter.setFieldName(TPMActivityAgreementFields.BEGIN_DATE);
            beginDate1Filter.setOperator(Operator.GT);
            beginDate1Filter.setFieldValues(Lists.newArrayList(String.valueOf(now)));
            query.setFilters(Lists.newArrayList(storeIdFilter, activityIdFilter, beginDate1Filter));
            if (isContainsProtocolStatus) {
                query.getFilters().add(invalidFilter);
                query.getFilters().add(invalidEmptyFilter);
                query.setPattern("1 and 2 and 3 and (4 or 5)");
            }

            agreements = CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query);
            //查询过去
            if (CollectionUtils.isEmpty(agreements)) {
                query.setLimit(1);
                OrderBy time1Order = new OrderBy();
                time1Order.setIsAsc(false);
                time1Order.setFieldName(TPMActivityAgreementFields.END_DATE);
                query.getOrders().clear();
                query.setOrders(Lists.newArrayList(time1Order));
                query.getFilters().clear();

                Filter endDate1Filter = new Filter();
                endDate1Filter.setFieldName(TPMActivityAgreementFields.END_DATE);
                endDate1Filter.setOperator(Operator.LT);
                endDate1Filter.setFieldValues(Lists.newArrayList(String.valueOf(now)));
                query.setFilters(Lists.newArrayList(storeIdFilter, activityIdFilter, endDate1Filter));
                if (isContainsProtocolStatus) {
                    query.getFilters().add(invalidFilter);
                    query.getFilters().add(invalidEmptyFilter);
                    query.setPattern("1 and 2 and 3 and (4 or 5)");
                }
                agreements = CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query);
            }
        }

        return agreements;
    }

    private List<String> getActivityType(List<String> baseActivityTypeList) {
        if (isTpm2Tenant) {
            List<String> ids = activityTypeManager.queryProofActivityTypeIds(controllerContext.getTenantId());
            if (CollectionUtils.isEmpty(baseActivityTypeList) || baseActivityTypeList.contains("all")) {
                return ids;
            } else {
                Set<String> baseSet = new HashSet<>(baseActivityTypeList);
                return ids.stream().filter(baseSet::contains).collect(Collectors.toList());
            }
        }

        return baseActivityTypeList;
    }

    private List<IObjectData> queryActivity(List<Integer> departmentIds, String storeId, List<String> activityTypeList) {
        SearchTemplateQuery queryAgreement = new SearchTemplateQuery();

        queryAgreement.setOffset(0);
        queryAgreement.setLimit(2000);

        queryAgreement.setSearchSource("db");

        Filter agreementStatusFilter = new Filter();
        agreementStatusFilter.setFieldName(TPMActivityAgreementFields.AGREEMENT_STATUS);
        agreementStatusFilter.setOperator(Operator.EQ);
        agreementStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS));

        Filter storeFilter = new Filter();
        storeFilter.setFieldName(TPMActivityAgreementFields.STORE_ID);
        storeFilter.setOperator(Operator.EQ);
        storeFilter.setFieldValues(Lists.newArrayList(storeId));

        queryAgreement.setFilters(Lists.newArrayList(agreementStatusFilter, storeFilter));
        if (TPMGrayUtils.isYinLu(controllerContext.getTenantId())) {
            //创建时间大于 5.28号的才可以被查询
            Filter timeFilter = new Filter();
            timeFilter.setFieldName(CommonFields.CREATE_TIME);
            timeFilter.setOperator(Operator.GT);
            timeFilter.setFieldValues(Lists.newArrayList(YINLU_FILTER_TIME));
            queryAgreement.getFilters().add(timeFilter);
        }

        List<IObjectData> aggList = serviceFacade.aggregateFindBySearchQuery(controllerContext.getTenantId(), queryAgreement, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, TPMActivityAgreementFields.ACTIVITY_ID, "count", "");
        Set<String> activityIds = aggList.stream().map(v -> (String) v.get(TPMActivityAgreementFields.ACTIVITY_ID)).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(activityIds)) {
            return new ArrayList<>();
        }

        SearchTemplateQuery queryActivity = new SearchTemplateQuery();

        queryActivity.setSearchSource("db");

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(CommonFields.ID);
        activityIdFilter.setOperator(Operator.IN);
        activityIdFilter.setFieldValues(new ArrayList<>(activityIds));

        queryActivity.setFilters(Lists.newArrayList(activityIdFilter));

        if (!controllerContext.getUser().isOutUser() && !TPMGrayUtils.denyDepartmentFilterOnActivity(controllerContext.getTenantId())) {
            Filter departmentFilter = new Filter();
            departmentFilter.setFieldName(TPMActivityFields.DEPARTMENT_RANGE);
            departmentFilter.setOperator(Operator.IN);
            departmentFilter.setFieldValues(departmentIds.stream().map(String::valueOf).collect(Collectors.toList()));
            queryActivity.getFilters().add(departmentFilter);
        }

        if (!CollectionUtils.isEmpty(activityTypeList) && !activityTypeList.contains("all")) {
            Filter activityTypeFilter = new Filter();
            activityTypeFilter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
            activityTypeFilter.setOperator(Operator.IN);
            activityTypeFilter.setFieldValues(activityTypeList);
            queryActivity.getFilters().add(activityTypeFilter);
        }
        if (TPMGrayUtils.isYinLu(controllerContext.getTenantId())) {
            //创建时间大于 5.28号的才可以被查询
            Filter timeFilter = new Filter();
            timeFilter.setFieldName(CommonFields.CREATE_TIME);
            timeFilter.setOperator(Operator.GT);
            timeFilter.setFieldValues(Lists.newArrayList(YINLU_FILTER_TIME));
            queryActivity.getFilters().add(timeFilter);
        }
        queryActivity.setLimit(-1);
        queryActivity.setOffset(0);
        queryActivity.setSearchSource("db");

        Set<String> mainActivityIds = new HashSet<>();
        SearchQueryUtil.handleDataPrivilege(dataPrivilegeService, controllerContext.getUser(), queryActivity, this.activityDescribe, this.activitySearchTemplate, this.isAdminRequest);
        CommonUtils.queryData(serviceFacade, controllerContext.getUser(), ApiNames.TPM_ACTIVITY_OBJ, queryActivity, partialData -> partialData.forEach(v -> mainActivityIds.add(v.getId())));
        return serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), new ArrayList<>(mainActivityIds), ApiNames.TPM_ACTIVITY_OBJ);
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }


    private IObjectData getDealer(IObjectData store) {
        List<String> recordType = storeBusiness.findDealerRecordType(controllerContext.getTenantId());
        if (recordType.contains(store.getRecordType())) {
            return store;
        }
        String dealerId = storeBusiness.findDealerId(controllerContext.getTenantId(), store);
        if (!Strings.isNullOrEmpty(dealerId)) {
            return serviceFacade.findObjectDataIncludeDeleted(User.systemUser(controllerContext.getTenantId()), dealerId, ApiNames.ACCOUNT_OBJ);
        }
        return null;
    }

    private void makeUpGroup(EnableList.Result result) {
        if (result == null || CollectionUtils.isEmpty(result.getData())) {
            return;
        }
        Set<String> all = new HashSet<>(GROUP_NAME_MAP.keySet());
        result.getData().forEach(v -> all.remove(v.getGroupKey()));

        all.forEach(key -> {
            EnableList.ActivityGroupVO groupVO = new EnableList.ActivityGroupVO();
            groupVO.setGroupKey(key);
            groupVO.setGroupName(GROUP_NAME_MAP.get(key));
            groupVO.setActivityList(new ArrayList<>());
            result.getData().add(groupVO);
        });
        if (result.getNavigateStrategy().equals("proof_audit")) {
            result.getData().sort((a, b) -> b.getActivityList().size() - a.getActivityList().size());
        } else {
            result.getData().sort(Comparator.comparing(EnableList.ActivityGroupVO::getGroupKey).reversed());
        }
        reOrderGroup(result.getData());
    }

    private void reOrderGroup(List<EnableList.ActivityGroupVO> data) {
        if (PROOF_GROUP_LABEL_ORDER_MAP.containsKey(controllerContext.getTenantId())) {
            Map<String, Integer> orderMap = PROOF_GROUP_LABEL_ORDER_MAP.get(controllerContext.getTenantId());
            Map<Integer, String> reverseOrderMap = orderMap.keySet().stream().collect(Collectors.toMap(orderMap::get, k -> k));
            AtomicInteger index = new AtomicInteger(-1);
            //存在就使用指定顺序，不存在就用排位顺序
            data.forEach(group -> {
                if (orderMap.containsKey(group.getGroupKey())) {
                    group.setDefaultTabIndex(orderMap.get(group.getGroupKey()));
                } else {
                    while (!reverseOrderMap.containsKey(index.incrementAndGet()));
                    group.setDefaultTabIndex(index.get());
                }
            });
            data.sort(Comparator.comparingInt(EnableList.ActivityGroupVO::getDefaultTabIndex));
        }
    }

    /**
     * 根据举证时段范围过滤活动列表
     *
     * @param context    控制器上下文
     * @param now        当前时间戳
     * @param activities 活动列表
     * @return 过滤后的活动列表
     */
    private List<IObjectData> filterActivitiesByProofTimePeriod(ControllerContext context, long now, List<IObjectData> activities) {
        if (CollectionUtils.isEmpty(activities)) {
            return activities;
        }

        // 筛选出配置了举证时段的活动
        List<IObjectData> activitiesWithProofPeriod = activities.stream()
                .filter(this::hasProofTimePeriod)
                .collect(Collectors.toList());

        // 如果没有配置举证时段的活动，直接返回原列表
        if (CollectionUtils.isEmpty(activitiesWithProofPeriod)) {
            return activities;
        }

        // 获取所有需要检查的活动ID
        List<String> activityIdsToCheck = activitiesWithProofPeriod.stream()
                .map(IObjectData::getId)
                .collect(Collectors.toList());

        // 查询当前时间内有效的举证时段数据
        List<IObjectData> validProofTimePeriods = queryProofTimePeriods(context, now, activityIdsToCheck);
        
        // 提取有效的活动ID集合
        Set<String> validActivityIds;
        if (!CollectionUtils.isEmpty(validProofTimePeriods)) {
            validActivityIds = validProofTimePeriods.stream()
                    .map(period -> period.get(TPMProofTimePeriodDetailFields.ACTIVITY_ID, String.class))
                    .collect(Collectors.toSet());
        } else {
            validActivityIds = Collections.emptySet();
        }

        // 过滤活动列表，移除有举证时段但当前时间不在举证时段范围内的活动
        return activities.stream()
                .filter(activity -> {
                    // 如果活动没有配置举证时段，保留
                    if (!hasProofTimePeriod(activity)) {
                        return true;
                    }
                    // 如果配置了举证时段，只保留当前时间在举证时段范围内的活动
                    return validActivityIds.contains(activity.getId());
                })
                .collect(Collectors.toList());
    }

    /**
     * 检查活动是否配置了举证时段
     *
     * @param activity 活动数据
     * @return 是否配置了举证时段
     */
    private boolean hasProofTimePeriod(IObjectData activity) {
        String proofPeriodValue = activity.get(TPMActivityFields.PROOF_PERIOD, String.class);
        if (Strings.isNullOrEmpty(proofPeriodValue) || proofPeriodValue.contains("ALL")) {
            return false;
        }
        
        List<TPMDisplayReportService.ProofPeriod> proofPeriods = JSONArray.parseArray(proofPeriodValue, TPMDisplayReportService.ProofPeriod.class);
        return !proofPeriods.isEmpty();
    }

    /**
     * 查询举证时段数据
     *
     * @param context     控制器上下文
     * @param now         当前时间戳
     * @param activityIds 活动ID列表
     * @return 举证时段数据列表
     */
    private List<IObjectData> queryProofTimePeriods(ControllerContext context, long now, List<String> activityIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setSearchSource("db");
        query.setLimit(-1);
        query.setOffset(0);

        // 活动ID过滤条件
        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(TPMProofTimePeriodDetailFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.IN);
        activityIdFilter.setFieldValues(activityIds);

        // 来源类型过滤条件
        Filter sourceTypeFilter = new Filter();
        sourceTypeFilter.setFieldName(TPMProofTimePeriodDetailFields.SOURCE_TYPE);
        sourceTypeFilter.setOperator(Operator.EQ);
        sourceTypeFilter.setFieldValues(Lists.newArrayList(TPMProofTimePeriodDetailFields.SOURCE_TYPE_ACTIVITY));


        // 开始时间小于等于当前时间的过滤条件
        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMProofTimePeriodDetailFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LTE);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        // 结束时间大于等于当前时间的过滤条件
        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMProofTimePeriodDetailFields.END_DATE);
        endDateFilter.setOperator(Operator.GTE);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        // 状态为正常的过滤条件
        Filter statusFilter = new Filter();
        statusFilter.setFieldName(CommonFields.LIFE_STATUS);
        statusFilter.setOperator(Operator.EQ);
        statusFilter.setFieldValues(Lists.newArrayList("normal"));

        query.setFilters(Lists.newArrayList(activityIdFilter, sourceTypeFilter, beginDateFilter, endDateFilter, statusFilter));

        log.info("查询举证时段数据，活动IDs: {}, 当前时间: {}", activityIds, now);
        List<String> fields = Lists.newArrayList(CommonFields.ID, CommonFields.NAME, TPMProofTimePeriodDetailFields.ACTIVITY_ID);
        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_PROOF_TIME_PERIOD_DETAIL_OBJ, query, fields);
    }
}
