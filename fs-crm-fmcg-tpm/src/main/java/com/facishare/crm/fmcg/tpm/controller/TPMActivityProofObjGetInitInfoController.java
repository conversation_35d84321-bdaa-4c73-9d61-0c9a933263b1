package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ProofCalculateType;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/6 下午5:25
 */
public class TPMActivityProofObjGetInitInfoController extends PreDefineController<TPMActivityProofObjGetInitInfoController.Arg, TPMActivityProofObjGetInitInfoController.Result> {


    private ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected Result doService(Arg arg) {

        IObjectData activity = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getActivityId(), ApiNames.TPM_ACTIVITY_OBJ);
        Result result = new Result();
        String activityType = activity.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
        ActivityTypeExt typeExt = activityTypeManager.find(controllerContext.getTenantId(), activityType);
        result.setCostConversionRatio(1D);
        result.setIsAgreementActivity(false);
        if (typeExt != null) {
            ActivityProofConfigEntity configEntity = typeExt.proofConfig();
            if (configEntity != null && configEntity.getCostCalculateConfig() != null && ProofCalculateType.BY_SESSIONS.value().equals(configEntity.getCostCalculateConfig().getCalculateType())) {
                result.setCostConversionRatio(Double.parseDouble(configEntity.getCostCalculateConfig().getRatio()));
            }
            result.setIsAgreementActivity(typeExt.agreementNode() != null);
        }
        return result;
    }

    @Data
    @ToString
    public static class Arg implements Serializable {
        @JSONField(name = "activity_id")
        @JsonProperty(value = "activity_id")
        @SerializedName("activity_id")
        private String activityId;
    }


    @Data
    @ToString
    public static class Result implements Serializable {

        @JSONField(name = "cost_conversion_ratio")
        @JsonProperty(value = "cost_conversion_ratio")
        @SerializedName("cost_conversion_ratio")
        private double costConversionRatio;


        @JSONField(name = "is_agreement_activity")
        @JsonProperty(value = "is_agreement_activity")
        @SerializedName("is_agreement_activity")
        private Boolean isAgreementActivity;

    }
}
