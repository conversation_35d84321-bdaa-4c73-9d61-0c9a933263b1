package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetAccountService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetOperator;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetSubjectService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IFiscalTimeService;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetDimensionEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IBudgetTypeManager;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.dao.pg.config.MetadataTransactional;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2023/3/24 18:18
 */
@Component
@SuppressWarnings("Duplicates")
public class CarryForwardDetailDataService {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private IBudgetTypeManager budgetTypeManager;
    @Resource
    private IBudgetAccountService budgetAccountService;
    @Resource
    private IFiscalTimeService fiscalTimeService;
    @Resource(name = "tpmOrganizationService")
    private OrganizationService organizationService;
    @Resource
    private IBudgetSubjectService budgetSubjectService;

    @MetadataTransactional
    public void failed(String tenantId, IObjectData data, String status, String message) {
        Map<String, Object> updater = Maps.newHashMap();
        updater.put(TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS, status);
        updater.put(TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_FAILURE_MESSAGE, String.format("结转操作失败，失败信息：%s", message));//ignorei18n
        doUpdate(tenantId, data, updater);
    }

    @MetadataTransactional
    public void doCarryForwardWithUnfreezeOrNot(String tenantId, IObjectData data, IBudgetOperator sourceOperator, IBudgetOperator targetOperator) {
        BigDecimal frozenAmount = sourceOperator.frozenAmount();

        BigDecimal carryForwardAmount;
        if (frozenAmount.compareTo(BigDecimal.ZERO) > 0) {
            carryForwardAmount = sourceOperator.unfreeze(BizType.CARRY_OVER_OUT);
        } else {
            carryForwardAmount = sourceOperator.operableAmount();
        }

        if (targetOperator == null) {
            //创建目标预算表
            IObjectData targetAccount = createBudget(sourceOperator);
            //初始化targetOperator
            targetOperator = BudgetOperatorFactory.initOperator(BizType.CARRY_OVER_IN, sourceOperator.getUser(), targetAccount.getId(), sourceOperator.getBusinessTraceId(), sourceOperator.getApprovalTraceId(), sourceOperator.getWhat());
            if (!targetOperator.tryLock()) {
                throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.BUS_CARRY_FORWARD_DETAIL_DATA_SERVICE_0), targetOperator.getAccount().getName()));
            }

        }
        BigDecimal targetAmount = targetOperator.realAmount().get(TPMBudgetAccountFields.AVAILABLE_AMOUNT);
        BigDecimal afterCarryForwardAmount = targetAmount.add(carryForwardAmount);

        sourceOperator.expenditure(carryForwardAmount);
        targetOperator.income(carryForwardAmount);

        sourceOperator.recalculate();
        targetOperator.recalculate();

        Map<String, Object> updater = Maps.newHashMap();
        updater.put(TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS, TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS__SUCCESS);
        updater.put(TPMBudgetCarryForwardDetailFields.AMOUNT, carryForwardAmount);
        updater.put(TPMBudgetCarryForwardDetailFields.AFTER_CARRY_FORWARD_AMOUNT, afterCarryForwardAmount);
        updater.put(TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_FAILURE_MESSAGE, "");
        doUpdate(tenantId, data, updater);
    }

    @MetadataTransactional
    public void freeze(String tenantId, IObjectData data, IBudgetOperator sourceOperator, IBudgetOperator targetOperator) {
        BigDecimal carryForwardAmount = sourceOperator.operableAmount();
        BigDecimal targetAmount = new BigDecimal("0");
        if (targetOperator != null) {
            targetAmount = targetOperator.realAmount().get(TPMBudgetAccountFields.AVAILABLE_AMOUNT);
        }
        BigDecimal afterCarryForwardAmount = targetAmount.add(carryForwardAmount);

        sourceOperator.freeze(carryForwardAmount);
        sourceOperator.recalculate();

        Map<String, Object> updater = Maps.newHashMap();
        updater.put(TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS, TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS__FROZEN);
        updater.put(TPMBudgetCarryForwardDetailFields.AMOUNT, carryForwardAmount);
        updater.put(TPMBudgetCarryForwardDetailFields.AFTER_CARRY_FORWARD_AMOUNT, afterCarryForwardAmount);
        updater.put(TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_FAILURE_MESSAGE, "");
        doUpdate(tenantId, data, updater);
    }

    @MetadataTransactional
    public void unfreeze(String tenantId, IObjectData data, IBudgetOperator sourceOperator) {
        sourceOperator.unfreeze(BizType.APPROVAL_BACK);
        sourceOperator.recalculate();

        Map<String, Object> updater = Maps.newHashMap();
        updater.put(TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS, TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS__UNFROZEN);
        updater.put(TPMBudgetCarryForwardDetailFields.AMOUNT, new BigDecimal("0"));
        updater.put(TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_FAILURE_MESSAGE, "");
        doUpdate(tenantId, data, updater);
    }

    private void doUpdate(String tenantId, IObjectData data, Map<String, Object> updater) {
        serviceFacade.updateWithMap(User.systemUser(tenantId), data, updater);
    }

    public List<IObjectData> loadDetails(String tenantId, String dataId, List<String> fields) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(-1);
        stq.setOffset(0);
        stq.setNeedReturnCountNum(false);
        stq.setNeedReturnQuote(false);
        stq.setSearchSource("db");

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(TPMBudgetCarryForwardDetailFields.BUDGET_CARRY_FORWARD_ID);
        masterFilter.setOperator(Operator.EQ);
        masterFilter.setFieldValues(Lists.newArrayList(dataId));

        stq.setFilters(Lists.newArrayList(masterFilter));

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.TPM_BUDGET_CARRY_FORWARD_DETAIL,
                stq,
                fields
        );
    }


    public IObjectData createBudget(IBudgetOperator sourceOperator) {
        IObjectData sourceAccount = sourceOperator.getAccount();
        Long nextPeriod = fiscalTimeService.nextPeriod(sourceOperator.getUser().getTenantId(), sourceOperator.getNode().getTimeDimension(), sourceAccount.get(TPMBudgetAccountFields.FORMAT_BUDGET_PERIOD + sourceOperator.getNode().getTimeDimension(), Long.class));

        IObjectData nextPeriodBudgetAccount = buildNextPeriodBudgetAccount(sourceOperator.getUser().getTenantId(), sourceOperator.getNode(), nextPeriod, sourceAccount);

        IObjectData parentBudgetAccount = findParentBudgetAccount(nextPeriodBudgetAccount);
        if (Objects.nonNull(parentBudgetAccount)) {
            nextPeriodBudgetAccount.set(TPMBudgetAccountFields.PARENT_ID, parentBudgetAccount.getId());
            return budgetAccountService.createBudgetAccount(User.systemUser(sourceOperator.getUser().getTenantId()), nextPeriodBudgetAccount, false, false, true, true);

        }

        List<IObjectData> allParentBudgetAccount = Lists.newArrayList();
        findOrBuildParentBudget(nextPeriodBudgetAccount, allParentBudgetAccount);
        String parentId = "";
        for (IObjectData objectData : allParentBudgetAccount) {
            if (StringUtils.isNotEmpty(parentId)) {
                objectData.set(TPMBudgetAccountFields.PARENT_ID, parentId);
            }
            IObjectData budgetAccount = budgetAccountService.createBudgetAccount(User.systemUser(sourceOperator.getUser().getTenantId()), objectData, false, false, true, true);
            parentId = budgetAccount.getId();
        }
        nextPeriodBudgetAccount.set(TPMBudgetAccountFields.PARENT_ID, parentId);
        return budgetAccountService.createBudgetAccount(User.systemUser(sourceOperator.getUser().getTenantId()), nextPeriodBudgetAccount, false, false, true, true);
    }

    private IObjectData findParentBudgetAccount(IObjectData childBudgetAccount) {
        String budgetTypeId = childBudgetAccount.get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class);
        BudgetTypePO budgetType = budgetTypeManager.get(childBudgetAccount.getTenantId(), budgetTypeId);
        String nodeId = childBudgetAccount.get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class);
        BudgetTypeNodeEntity childNode = budgetType.getNodes().stream().filter(v -> nodeId.equals(v.getNodeId())).findFirst().orElse(null);
        if (childNode == null) {
            throw new ValidateException("childNode budget node not found");
        }
        String parentNodeId = childNode.getParentNodeId();
        if (StringUtils.isEmpty(parentNodeId)) {
            return null;
        }
        BudgetTypeNodeEntity parentNode = budgetType.getNodes().stream().filter(node -> Objects.equals(parentNodeId, node.getNodeId())).findFirst().orElse(null);

        if (parentNode == null) {
            throw new ValidateException("parentNode budget node not found");
        }

        Long parentPeriod = fiscalTimeService.parentPeriod(childBudgetAccount.getTenantId(), parentNode.getTimeDimension(), childNode.getTimeDimension(), childBudgetAccount.get(TPMBudgetAccountFields.FORMAT_BUDGET_PERIOD + childNode.getTimeDimension(), Long.class));

        return findParentBudgetAccount(childBudgetAccount.getTenantId(), childBudgetAccount, childNode, parentNode, parentPeriod);
    }

    private void findOrBuildParentBudget(IObjectData childBudgetAccount, List<IObjectData> allParentBudgetAccount) {
        IObjectData parentBudgetAccount = findParentBudgetAccount(childBudgetAccount);
        if (parentBudgetAccount != null) {
            childBudgetAccount.set(TPMBudgetAccountFields.PARENT_ID, parentBudgetAccount.getId());
            return;
        }


        String budgetTypeId = childBudgetAccount.get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class);
        BudgetTypePO budgetType = budgetTypeManager.get(childBudgetAccount.getTenantId(), budgetTypeId);
        String nodeId = childBudgetAccount.get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class);
        BudgetTypeNodeEntity childNode = budgetType.getNodes().stream().filter(v -> nodeId.equals(v.getNodeId())).findFirst().orElse(null);
        if (childNode == null) {
            throw new ValidateException("budget node not found");
        }
        String parentNodeId = childNode.getParentNodeId();
        if (StringUtils.isEmpty(parentNodeId)) {
            return;
        }
        BudgetTypeNodeEntity parentNode = budgetType.getNodes().stream().filter(node -> Objects.equals(parentNodeId, node.getNodeId())).findFirst().orElse(null);

        if (parentNode == null) {
            throw new ValidateException("budget node not found");
        }

        Long parentPeriod = fiscalTimeService.parentPeriod(childBudgetAccount.getTenantId(), parentNode.getTimeDimension(), childNode.getTimeDimension(), childBudgetAccount.get(TPMBudgetAccountFields.FORMAT_BUDGET_PERIOD + childNode.getTimeDimension(), Long.class));


        parentBudgetAccount = buildBudgetAccount(childBudgetAccount.getTenantId(), parentNode, childNode, parentPeriod, childBudgetAccount);

        findOrBuildParentBudget(parentBudgetAccount, allParentBudgetAccount);

        allParentBudgetAccount.add(parentBudgetAccount);
    }

    private IObjectData buildNextPeriodBudgetAccount(String tenantId, BudgetTypeNodeEntity node, Long period, IObjectData childAccount) {
        String name = childAccount.get(TPMBudgetAccountFields.NAME, String.class) + I18N.text(I18NKeys.BUDGET_FOR_NEXT_PERIOD_SUFFIX);
        List<String> departments = CommonUtils.cast(childAccount.get(TPMBudgetAccountFields.BUDGET_DEPARTMENT), String.class);

        List<String> owners = CommonUtils.cast(childAccount.get(CommonFields.OWNER), String.class);

        String targetTypeId = childAccount.get(TPMBudgetDisassemblyFields.BUDGET_TYPE_ID, String.class);
        String controlTimeDimension = node.getControlTimeDimension();


        IObjectData data = budgetAccountService.buildDefaultValForCreate();
        data.setRecordType(node.getRecordType());
        data.setTenantId(tenantId);
        data.setOwner(owners);
        data.setCreatedBy(CollectionUtils.isEmpty(owners) ? "-10000" : owners.get(0));
        data.setDescribeApiName(ApiNames.TPM_BUDGET_ACCOUNT);
        data.set(TPMBudgetAccountFields.NAME, name);
        data.set(TPMBudgetAccountFields.BUDGET_DEPARTMENT, departments);

        data.set(TPMBudgetAccountFields.BUDGET_TYPE_ID, targetTypeId);
        data.set(TPMBudgetAccountFields.BUDGET_NODE_ID, node.getNodeId());
        data.set(TPMBudgetAccountFields.EFFECTIVE_PERIOD, node.getTimeDimension());
        if (!StringUtils.isEmpty(controlTimeDimension)) {
            data.set(TPMBudgetAccountFields.CONTROL_PERIOD, controlTimeDimension);
        }

        data.set(TPMBudgetAccountFields.FORMAT_BUDGET_PERIOD + node.getTimeDimension(), period);

        for (BudgetDimensionEntity dimension : node.getDimensions()) {
            String dimensionValue = childAccount.get(dimension.getApiName(), String.class);
            if (StringUtils.isNotEmpty(dimensionValue)) {
                data.set(dimension.getApiName(), dimensionValue);
            }
        }

        data.set(TPMBudgetAccountFields.BASE_AMOUNT, 0);
        data.set(TPMBudgetAccountFields.TOTAL_AMOUNT, 0);
        data.set(TPMBudgetAccountFields.AVAILABLE_AMOUNT, 0);
        data.set(TPMBudgetAccountFields.STATISTIC_DEPARTMENT_AMOUNT, 0);
        data.set(TPMBudgetAccountFields.BUDGET_STATUS, TPMBudgetAccountFields.BUDGET_STATUS__ENABLE);

        return data;
    }

    private IObjectData buildBudgetAccount(String tenantId, BudgetTypeNodeEntity parentNode, BudgetTypeNodeEntity childNode, Long period, IObjectData childAccount) {
        String name = childAccount.get(TPMBudgetAccountFields.NAME, String.class) + I18N.text(I18NKeys.BUDGET_FOR_FATHER_SUFFIX);
        List<String> departments = CommonUtils.cast(childAccount.get(TPMBudgetAccountFields.BUDGET_DEPARTMENT), String.class);

        if (parentNode.getDepartmentDimensionLevel() != childNode.getDepartmentDimensionLevel()) {
            DepartmentDto parentDepartment = organizationService.getParentDepartment(Integer.parseInt(tenantId), Integer.parseInt(departments.get(0)), parentNode.getDepartmentDimensionLevel());
            departments = Lists.newArrayList(String.valueOf(parentDepartment.getDepartmentId()));
        }

        List<String> owners = CommonUtils.cast(childAccount.get(CommonFields.OWNER), String.class);

        String targetTypeId = childAccount.get(TPMBudgetDisassemblyFields.BUDGET_TYPE_ID, String.class);
        String controlTimeDimension = parentNode.getControlTimeDimension();


        IObjectData data = budgetAccountService.buildDefaultValForCreate();
        data.setRecordType(parentNode.getRecordType());
        data.setTenantId(tenantId);
        data.setOwner(owners);
        data.setCreatedBy(CollectionUtils.isEmpty(owners) ? "-10000" : owners.get(0));
        data.setDescribeApiName(ApiNames.TPM_BUDGET_ACCOUNT);
        data.set(TPMBudgetAccountFields.NAME, name);
        data.set(TPMBudgetAccountFields.BUDGET_DEPARTMENT, departments);

        data.set(TPMBudgetAccountFields.BUDGET_TYPE_ID, targetTypeId);
        data.set(TPMBudgetAccountFields.BUDGET_NODE_ID, parentNode.getNodeId());
        data.set(TPMBudgetAccountFields.EFFECTIVE_PERIOD, parentNode.getTimeDimension());
        if (!StringUtils.isEmpty(controlTimeDimension)) {
            data.set(TPMBudgetAccountFields.CONTROL_PERIOD, controlTimeDimension);
        }

        data.set(TPMBudgetAccountFields.FORMAT_BUDGET_PERIOD + parentNode.getTimeDimension(), period);

        Map<String, Integer> dimensionLevelMap = childNode.getDimensions().stream().collect(Collectors.toMap(BudgetDimensionEntity::getApiName, BudgetDimensionEntity::getLevel));
        // 遍历所有设置的维度
        for (BudgetDimensionEntity dimension : parentNode.getDimensions()) {

            String childValue = childAccount.get(dimension.getApiName(), String.class);


            // 维度级别相等时的校验
            if (dimension.getLevel() == dimensionLevelMap.get(dimension.getApiName())) {
                data.set(dimension.getApiName(), childValue);
            } else {
                // 维度级别不等时的两个维度的校验
                if (dimension.getApiName().equals(TPMBudgetAccountFields.BUDGET_SUBJECT_ID)) {
                    data.set(dimension.getApiName(), budgetSubjectService.queryParentId(tenantId, childValue));
                } else if (dimension.getApiName().equals(TPMBudgetAccountFields.PRODUCT_CATEGORY_ID)) {
                    data.set(dimension.getApiName(), queryParentCategory(tenantId, childValue));
                } else {
                    throw new ValidateException("level dimension not support.");
                }
            }
        }


        data.set(TPMBudgetAccountFields.BASE_AMOUNT, 0);
        data.set(TPMBudgetAccountFields.TOTAL_AMOUNT, 0);
        data.set(TPMBudgetAccountFields.AVAILABLE_AMOUNT, 0);
        data.set(TPMBudgetAccountFields.STATISTIC_DEPARTMENT_AMOUNT, 0);
        data.set(TPMBudgetAccountFields.BUDGET_STATUS, TPMBudgetAccountFields.BUDGET_STATUS__ENABLE);

        return data;
    }

    private String queryParentCategory(String tenantId, String categoryId) {
        if (StringUtils.isEmpty(categoryId)) {
            throw new ValidateException("category is null");
        }
        User sys = User.systemUser(tenantId);

        IObjectData data = serviceFacade.findObjectDataIgnoreAll(sys, categoryId, ApiNames.PRODUCT_CATEGORY_OBJ);
        String parentId = data.get("pid", String.class);
        if (Strings.isNullOrEmpty(parentId)) {
            throw new ValidateException("parent category is null");
        }
        return parentId;
    }

    private IObjectData findParentBudgetAccount(String tenantId, IObjectData sourceBudgetAccount, BudgetTypeNodeEntity childNode, BudgetTypeNodeEntity parentNode, Long parentPeriod) {


        IFilter periodFilter = new Filter();
        periodFilter.setFieldName(TPMBudgetAccountFields.FORMAT_BUDGET_PERIOD + parentNode.getTimeDimension());
        periodFilter.setOperator(Operator.EQ);
        periodFilter.setFieldValues(Lists.newArrayList(String.valueOf(parentPeriod)));


        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(periodFilter);

        String subDepartmentId = CommonUtils.cast(sourceBudgetAccount.get(TPMBudgetAccountFields.BUDGET_DEPARTMENT), String.class).get(0);
        if (childNode.getDepartmentDimensionLevel() == parentNode.getDepartmentDimensionLevel()) {
            IFilter departmentFilter = new Filter();
            departmentFilter.setFieldName(TPMBudgetAccountFields.BUDGET_DEPARTMENT);
            departmentFilter.setOperator(Operator.EQ);
            departmentFilter.setFieldValues(Lists.newArrayList(subDepartmentId));

            stq.getFilters().add(departmentFilter);
        } else if (childNode.getDepartmentDimensionLevel() > parentNode.getDepartmentDimensionLevel()) {
            DepartmentDto parentDepartment = organizationService.getParentDepartment(Integer.parseInt(tenantId), Integer.parseInt(subDepartmentId), parentNode.getDepartmentDimensionLevel());

            IFilter departmentFilter = new Filter();
            departmentFilter.setFieldName(TPMBudgetAccountFields.BUDGET_DEPARTMENT);
            departmentFilter.setOperator(Operator.EQ);
            departmentFilter.setFieldValues(Lists.newArrayList(String.valueOf(parentDepartment.getDepartmentId())));

            stq.getFilters().add(departmentFilter);
        } else {
            throw new ValidateException("department level error");
        }


        Map<String, Integer> dimensionLevelMap = childNode.getDimensions().stream().collect(Collectors.toMap(BudgetDimensionEntity::getApiName, BudgetDimensionEntity::getLevel));
        // 遍历所有设置的维度
        for (BudgetDimensionEntity dimension : parentNode.getDimensions()) {

            String childValue = sourceBudgetAccount.get(dimension.getApiName(), String.class);


            // 维度级别相等时的校验
            if (dimension.getLevel() == dimensionLevelMap.get(dimension.getApiName())) {
                IFilter filter = new Filter();
                filter.setFieldName(dimension.getApiName());
                filter.setOperator(Operator.EQ);
                filter.setFieldValues(Lists.newArrayList(sourceBudgetAccount.get(dimension.getApiName(), String.class)));

                stq.getFilters().add(filter);

            } else {
                // 维度级别不等时的两个维度的校验
                if (dimension.getApiName().equals(TPMBudgetAccountFields.BUDGET_SUBJECT_ID)) {

                    IFilter filter = new Filter();
                    filter.setFieldName(dimension.getApiName());
                    filter.setOperator(Operator.EQ);
                    filter.setFieldValues(Lists.newArrayList(budgetSubjectService.queryParentId(tenantId, childValue)));

                    stq.getFilters().add(filter);

                } else if (dimension.getApiName().equals(TPMBudgetAccountFields.PRODUCT_CATEGORY_ID)) {

                    IFilter filter = new Filter();
                    filter.setFieldName(dimension.getApiName());
                    filter.setOperator(Operator.EQ);
                    filter.setFieldValues(Lists.newArrayList(queryParentCategory(tenantId, childValue)));

                    stq.getFilters().add(filter);
                } else {
                    throw new ValidateException("level dimension not support.");
                }
            }
        }


        stq.setLimit(1);
        List<IObjectData> data = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.TPM_BUDGET_ACCOUNT,
                stq,
                Lists.newArrayList("_id"));
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        return data.get(0);
    }
}