package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;

/**
 * author: wuyx
 * description: 
 * createTime: 2022/8/5 18:09
 */
public class TPMBudgetStatisticTableObjInvalidAction extends StandardInvalidAction {

    @Override
    protected void before(Arg arg) {
        throw new ValidateException(I18N.text(I18NKeys.BUDGET_STATISTIC_TABLE_OBJ_INVALID_ACTION_0));
    }
}
