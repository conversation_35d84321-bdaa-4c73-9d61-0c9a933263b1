package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/23/20 8:42 PM
 */
@Slf4j
@SuppressWarnings("Duplicates")
public class TPMActivityObjListHeaderController extends StandardListHeaderController {

    private final static String IMPORT_BUTTON = "Import";

    @Override
    protected Result after(Arg arg, Result result) {

        if ("list".equals(arg.getLayoutType()) && Boolean.TRUE.equals(arg.getIncludeLayout())) {

            ObjectDescribeDocument activityDescribeDocument = result.getObjectDescribe();
            ObjectDescribeExt activityDescribe = ObjectDescribeExt.of(ObjectDescribeExt.of(activityDescribeDocument).copyOnWrite());

            // proof record type support
            for (SelectOne selectOneField : activityDescribe.getSelectOneFields()) {
                if (selectOneField.getApiName().equals(TPMActivityFields.PROOF_RECORD_TYPE)) {
                    List<ISelectOption> options = selectOneField.getSelectOptions();
                    List<IRecordTypeOption> recordTypeOptions = serviceFacade.findRecordTypeOptionList(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_PROOF_OBJ, false);

                    String optionsIdentity = toSelectOptionsIdentity(options);
                    String recordTypesIdentity = toRecordTypesIdentity(recordTypeOptions);

                    log.info("options identity : {}, record types identity : {}", optionsIdentity, recordTypesIdentity);

                    if (!optionsIdentity.equals(recordTypesIdentity)) {
                        options.clear();
                        for (IRecordTypeOption recordTypeOption : recordTypeOptions) {
                            ISelectOption newOption = new SelectOption();
                            newOption.setLabel(recordTypeOption.getLabel());
                            newOption.setValue(recordTypeOption.getApiName());
                            options.add(newOption);
                        }
                        selectOneField.setSelectOptions(options);
                        IObjectDescribe updateResult = serviceFacade.updateFieldDescribe(activityDescribe, Lists.newArrayList(selectOneField));

                        log.info("update field : {}, update result : {}", selectOneField.toJsonString(), updateResult.toJsonString());
                    }
                }
            }

            // hide store range
            /*this.objectDescribeExt.removeFieldDescribe("store_range");
            if (CollectionUtils.isNotEmpty(result.getVisibleFields())) {
                result.getVisibleFields().remove("store_range");
            }*/
        }
        if (!TPMGrayUtils.isAllowActivityImportData(controllerContext.getTenantId())) {
            hideButtonFilter(arg, result);
        }
        return super.after(arg, result);
    }

    private void hideButtonFilter(Arg arg, Result result) {
        log.info("activityObj arg layoutAgentType is {}", arg.getLayoutAgentType());
        if (!"mobile".equals(arg.getLayoutAgentType())) {
            List<JSONObject> buttons = (List<JSONObject>) result.getLayout().get("buttons");
            result.getLayout().put("buttons", buttons.stream().filter(v -> !IMPORT_BUTTON.equals(v.getString("action"))).collect(Collectors.toList()));
        } else {
            ArrayList buttons = (ArrayList) result.getLayout().get("buttons");
            buttons.removeIf(button -> {
                Map btn = (Map) (button);
                return IMPORT_BUTTON.equals(btn.get("action"));
            });
        }
    }

    private String toSelectOptionsIdentity(List<ISelectOption> selectOptions) {
        return selectOptions
                .stream()
                .sorted(Comparator.comparing(ISelectOption::getValue))
                .map(m -> String.format("%s.%s", m.getValue(), m.getLabel()))
                .collect(Collectors.joining(","));
    }

    private String toRecordTypesIdentity(List<IRecordTypeOption> selectOptions) {
        return selectOptions
                .stream()
                .sorted(Comparator.comparing(IRecordTypeOption::getApiName))
                .map(m -> String.format("%s.%s", m.getApiName(), m.getLabel()))
                .collect(Collectors.joining(","));
    }

    //hide field
    @Override
    protected List<String> getAuthorizedFields() {
        List<String> list = super.getAuthorizedFields();
        list.remove("store_range");
        list.remove("cashing_product_range");
        list.remove("product_range");
        return list;
    }
}