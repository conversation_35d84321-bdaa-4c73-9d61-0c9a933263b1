package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetCarryForwardDetailFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetCarryForwardFields;
import com.facishare.crm.fmcg.tpm.business.BudgetOperatorFactory;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetOperator;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.tpm.service.abstraction.ITransactionProxy;
import com.facishare.crm.fmcg.tpm.utils.TraceUtil;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import groovy.lang.Tuple2;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Slf4j
@SuppressWarnings("Duplicates")
public class TPMBudgetCarryForwardObjFlowCompletedActionBak extends StandardFlowCompletedAction {

    private final ITransactionProxy transProxy = SpringUtil.getContext().getBean(ITransactionProxy.class);
    private final Map<String, CarryForwardOperators> operators = Maps.newHashMap();

    private IObjectData carryForwardData;
    private List<IObjectData> carryForwardDetails;
    private boolean carryForwardInvoked = false;

    @Override
    protected void before(Arg arg) {
        super.before(arg);

        String approvalTraceId = TraceUtil.getApprovalCallbackTraceId(this.arg.getCallbackData());
        if (Strings.isNullOrEmpty(approvalTraceId)) {
            return;
        }
        String businessTraceId = TraceUtil.getBusinessCallbackTraceId(this.arg.getCallbackData());
        log.info("approve trace id : {}, business trace id : {}", approvalTraceId, businessTraceId);

        this.carryForwardData = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getDataId(), ApiNames.TPM_BUDGET_CARRY_FORWARD);
        log.info("master data : {}", this.carryForwardData);
        this.carryForwardInvoked = TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__SUCCESS.equals(this.carryForwardData.get(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS, String.class));

        if (!this.carryForwardInvoked) {

            this.carryForwardDetails = queryDetails();
            log.info("details data : {}", this.carryForwardDetails);

            for (IObjectData detail : this.carryForwardDetails) {
                String sourceId = detail.get(TPMBudgetCarryForwardDetailFields.SOURCE_BUDGET_ACCOUNT_ID, String.class);
                String targetId = detail.get(TPMBudgetCarryForwardDetailFields.TARGET_BUDGET_ACCOUNT_ID, String.class);

                IBudgetOperator sourceOperator = BudgetOperatorFactory.initOperator(BizType.CARRY_OVER_OUT, actionContext.getUser(), sourceId, businessTraceId, approvalTraceId, this.carryForwardData);
                IBudgetOperator targetOperator = BudgetOperatorFactory.initOperator(BizType.CARRY_OVER_IN, actionContext.getUser(), targetId, businessTraceId, approvalTraceId, this.carryForwardData);

                CarryForwardOperators operator = CarryForwardOperators.builder().source(sourceOperator).target(targetOperator).build();

                if (operator.tryLock()) {
                    operators.put(sourceId, operator);
                } else {
                    throw new MetaDataBusinessException("try lock budget account fail.");
                }
            }
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        return transProxy.call(() -> {
            Result inner = super.doAct(arg);
            this.stopWatch.lap("framework.doAct");

            if (!this.carryForwardInvoked && Boolean.TRUE.equals(inner.getSuccess())) {
                if (arg.isPass()) {
                    try {
                        this.unfreezeAndDoCarryForward();
                        this.stopWatch.lap("unfreezeAndDoCarryForward");
                    } catch (Exception ex) {
                        log.error("unfreeze and do carry forward failed : ", ex);
                        this.setCarryForwardFailed();
                        this.stopWatch.lap("setCarryForwardFailed");
                    }
                } else {
                    this.unfreeze();
                    this.stopWatch.lap("unfreeze");
                }
            }
            return inner;
        });
    }

    private void setCarryForwardFailed() {
        Map<String, Object> updater = Maps.newHashMap();
        updater.put(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS, TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__FAILED);
        this.serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), this.carryForwardData, updater);
    }

    protected void unfreezeAndDoCarryForward() {
        Map<String, Tuple2<BigDecimal, BigDecimal>> amountMap = com.beust.jcommander.internal.Maps.newHashMap();

        for (CarryForwardOperators operator : operators.values()) {
            BigDecimal amount = operator.source.unfreeze(BizType.RELEASE);

            operator.source.expenditure(amount);
            operator.source.recalculate();

            BigDecimal targetAmount = operator.target.realAmount().get(TPMBudgetAccountFields.AVAILABLE_AMOUNT);
            operator.target.income(amount);
            operator.target.recalculate();

            amountMap.put(operator.getSource().getAccount().getId(), new Tuple2<>(amount, amount.add(targetAmount)));
        }

        Map<String, Object> updater = Maps.newHashMap();
        updater.put(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS, TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__SUCCESS);

        this.serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), this.carryForwardData, updater);

        for (IObjectData detail : this.carryForwardDetails) {
            String key = detail.get(TPMBudgetCarryForwardDetailFields.SOURCE_BUDGET_ACCOUNT_ID, String.class);
            Tuple2<BigDecimal, BigDecimal> amountValue = amountMap.get(key);
            detail.set(TPMBudgetCarryForwardDetailFields.AMOUNT, amountValue.getFirst());
            detail.set(TPMBudgetCarryForwardDetailFields.AFTER_CARRY_FORWARD_AMOUNT, amountValue.getSecond());
        }

        this.serviceFacade.batchUpdateByFields(
                User.systemUser(actionContext.getTenantId()),
                this.carryForwardDetails,
                Lists.newArrayList(
                        TPMBudgetCarryForwardDetailFields.AMOUNT,
                        TPMBudgetCarryForwardDetailFields.AFTER_CARRY_FORWARD_AMOUNT
                ));
    }

    protected void unfreeze() {
        for (CarryForwardOperators operator : operators.values()) {
            operator.source.unfreeze(BizType.APPROVAL_BACK);
            operator.source.recalculate();
        }
        Map<String, Object> updater = Maps.newHashMap();
        updater.put(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS, TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__UNFROZEN);

        this.serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), this.carryForwardData, updater);
    }

    @Override
    protected void finallyDo() {
        if (!this.carryForwardInvoked) {
            for (CarryForwardOperators operator : this.operators.values()) {
                operator.unlock();
            }
        }
        super.finallyDo();
    }

    @Data
    @Builder
    public static class CarryForwardOperators {

        private IBudgetOperator source;

        private IBudgetOperator target;

        private boolean tryLock() {
            return source.tryLock() && target.tryLock();
        }

        private void unlock() {
            source.unlock();
            target.unlock();
        }
    }

    private List<IObjectData> queryDetails() {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(200);
        stq.setOffset(0);
        stq.setNeedReturnCountNum(false);
        stq.setFindExplicitTotalNum(false);
        stq.setSearchSource("db");

        Filter masterIdFilter = new Filter();
        masterIdFilter.setFieldName(TPMBudgetCarryForwardDetailFields.BUDGET_CARRY_FORWARD_ID);
        masterIdFilter.setOperator(Operator.EQ);
        masterIdFilter.setFieldValues(Lists.newArrayList(arg.getDataId()));

        stq.setFilters(Lists.newArrayList(masterIdFilter));

        return serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_BUDGET_CARRY_FORWARD_DETAIL, stq).getData();
    }
}
