package com.facishare.crm.fmcg.tpm.business.enums;

public enum DisplayFormItemEnum {
    /**
     * 陈列层数
     */
    LAYER_NUMBER("陈列层数", "aiLayerNumber"), //ignorei18n

    /**
     * 排面数
     */
    ROW_NUMBER("排面数", "aiRowNumber"), //ignorei18n

    /**
     * 个数
     */
    COUNT_NUMBER("个数", "aiCountNumber"), //ignorei18n
    /**
     * 仅有地堆：判断可视面
     */
    VISIBLE_NUMBER("可视面", "aiVisibleNumber"), //ignorei18n

    /**
     * 割箱。组数
     */
    GROUP_NUMBER("组数", "aiGroupNumber"); //ignorei18n
    private final String displayName;
    private final String fieldKey;

    DisplayFormItemEnum(String displayName, String fieldKey) {
        this.displayName = displayName;
        this.fieldKey = fieldKey;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getFieldKey() {
        return fieldKey;
    }

    /**
     * 根据显示名称获取枚举值
     */
    public static DisplayFormItemEnum getByDisplayName(String displayName) {
        for (DisplayFormItemEnum field : values()) {
            if (field.displayName.equals(displayName)) {
                return field;
            }
        }
        return null;
    }
}