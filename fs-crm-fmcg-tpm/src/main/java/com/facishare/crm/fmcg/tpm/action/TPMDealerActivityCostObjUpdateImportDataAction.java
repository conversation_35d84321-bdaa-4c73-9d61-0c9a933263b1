package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * author: wuyx
 * description:
 * createTime: 2023/5/5 14:48
 */
@Slf4j
public class TPMDealerActivityCostObjUpdateImportDataAction extends StandardUpdateImportDataAction {

    public final List<String> DEFAULT_FIELD = Lists.newArrayList("name", "relevant_team", "_id");

    @Override
    protected void convertFields(List<ImportData> dataList) {
        super.convertFields(dataList);
        log.info("data:{}", JSON.toJSONString(dataList));
        List<ImportError> errorList = new ArrayList<>();

        if (!TPMGrayUtils.dealerActivityCostObjUpdateImport(actionContext.getTenantId())) {
            dataList.forEach(data -> {
                ImportError error = new ImportError();
                error.setRowNo(data.getRowNo());
                error.setErrorMessage(I18N.text(I18NKeys.ERRORMESSAGE_DEALER_ACTIVITY_COST_OBJ_UPDATE_IMPORT_DATA_ACTION_0));
                errorList.add(error);
            });
        } else {
            dataList.forEach(data -> {
                IObjectData obj = data.getData();
                Set<String> dataField = obj.getDataField();
                for (String field : dataField) {
                    String fieldData = obj.get(field, String.class);
                    if (DEFAULT_FIELD.contains(field)) {
                        continue;
                    }

                    if (!field.endsWith("__c") && fieldData != null) {
                        ImportError error = new ImportError();
                        error.setRowNo(data.getRowNo());
                        error.setErrorMessage(String.format(I18N.text(I18NKeys.ERRORMESSAGE_DEALER_ACTIVITY_COST_OBJ_UPDATE_IMPORT_DATA_ACTION_1), field));
                        errorList.add(error);

                        break;
                    }
                }
            });
        }
        mergeErrorList(errorList);
    }
}
