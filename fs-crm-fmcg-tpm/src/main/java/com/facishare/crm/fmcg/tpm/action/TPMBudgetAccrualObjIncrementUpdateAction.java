package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;

/**
 * <AUTHOR>
 * @date 2022/9/22 下午3:54
 */
public class TPMBudgetAccrualObjIncrementUpdateAction extends StandardIncrementUpdateAction {

    @Override
    protected void before(Arg arg) {
        throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCRUAL_OBJ_INCREMENT_UPDATE_ACTION_0));
    }
}
