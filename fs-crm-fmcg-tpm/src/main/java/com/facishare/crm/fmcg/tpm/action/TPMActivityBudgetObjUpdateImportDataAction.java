package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.TPMActivityBudgetFields;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/11 下午3:19
 */
@Slf4j
public class TPMActivityBudgetObjUpdateImportDataAction extends StandardUpdateImportDataAction {

    @Override
    protected void before(Arg arg) {
        log.info("start update budget.");
        super.before(arg);
    }

    @Override
    protected List<IObjectData> importData(List<IObjectData> validList) {
        return super.importData(validList);
    }

    @Override
    protected void convertFields(List<ImportData> dataList) {
        super.convertFields(dataList);
        log.info("data : {}", JSON.toJSONString(dataList));
        List<ImportError> errorList = new ArrayList<>();

        dataList.forEach(data -> {

            if (data.getData().get(TPMActivityBudgetFields.AMOUNT) != null) {
                ImportError error = new ImportError();
                error.setRowNo(data.getRowNo());
                error.setErrorMessage(I18N.text(I18NKeys.ERRORMESSAGE_ACTIVITY_BUDGET_OBJ_UPDATE_IMPORT_DATA_ACTION_0));
                errorList.add(error);
            } else if (data.getData().get(TPMActivityBudgetFields.TRANSFER_IN_AMOUNT) != null) {
                ImportError error = new ImportError();
                error.setRowNo(data.getRowNo());
                error.setErrorMessage(I18N.text(I18NKeys.ERRORMESSAGE_ACTIVITY_BUDGET_OBJ_UPDATE_IMPORT_DATA_ACTION_1));
                errorList.add(error);
            } else if (data.getData().get(TPMActivityBudgetFields.TRANSFER_OUT_AMOUNT) != null) {
                ImportError error = new ImportError();
                error.setRowNo(data.getRowNo());
                error.setErrorMessage(I18N.text(I18NKeys.ERRORMESSAGE_ACTIVITY_BUDGET_OBJ_UPDATE_IMPORT_DATA_ACTION_2));
                errorList.add(error);
            }

        });
        mergeErrorList(errorList);
    }
}
