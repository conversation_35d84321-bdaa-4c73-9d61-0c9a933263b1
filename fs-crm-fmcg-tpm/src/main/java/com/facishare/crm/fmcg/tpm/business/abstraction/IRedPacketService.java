package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.paas.metadata.api.IObjectData;

/**
 * Author: linmj
 * Date: 2023/10/16 18:34
 */
public interface IRedPacketService {

    boolean refreshRewardStatus(IObjectData redPacket);

    void refreshRedPacketInfo(String tenantId, String redPacketId);

    void refreshAccountInfo(String tenantId, String redPacketId);

    void refreshProcessingRedPacket(String tenantId);

    void refreshProcessingWithdraw(String tenantId);

    String getTopTenantId(String tenantCode, String environment);

    String getTenantCode(String tenantId);
}
