package com.facishare.crm.fmcg.tpm.service;

import com.facishare.crm.fmcg.tpm.service.abstraction.ITransactionProxy;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.dao.pg.config.MetadataTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.concurrent.Callable;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@MetadataTransactional
public class TransactionProxy implements ITransactionProxy {

    @Resource
    private TransactionProxy transactionProxy;

    @Override
    public <T> T call(Callable<T> callable) {
        try {
            return callable.call();
        } catch (ValidateException ex) {
            log.info("TransactionProxy.call cause validate exception : ", ex);
            throw ex;
        } catch (AppBusinessException ex) {
            log.info("TransactionProxy.call cause app business exception : ", ex);
            throw ex;
        } catch (Exception ex) {
            log.info("TransactionProxy.call cause unknown exception : ", ex);
            throw new MetaDataBusinessException("TransactionProxy.call cause unknown exception.", ex);
        }
    }

    @Override
    public void run(Runnable r) {
        try {
            r.run();
        } catch (Exception ex) {
            log.info("TransactionProxy.run cause unknown exception : ", ex);
            throw ex;
        }
    }

    @Override
    public void run(Runnable runnable,  Runnable exceptionRun) {
        try {
            runnable.run();
        } catch (Exception ex) {
            log.info("TransactionProxy.run cause unknown exception : ", ex);
            transactionProxy.noTransaction(exceptionRun);
            throw ex;
        }
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void noTransaction(Runnable runnable) {
        runnable.run();
    }


}
