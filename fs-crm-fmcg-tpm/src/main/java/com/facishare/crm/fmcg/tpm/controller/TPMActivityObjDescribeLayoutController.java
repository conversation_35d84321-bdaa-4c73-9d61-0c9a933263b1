package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.common.apiname.ActivityCustomerTypeEnum;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityCashingProductFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.constant.ScanCodeActionConstants;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityPlanConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.NodeType;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.utils.LayoutUtil;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDescribeLayoutController;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.DetailObjectListResult;
import com.facishare.paas.appframework.metadata.dto.FieldResult;
import com.facishare.paas.appframework.metadata.dto.RecordTypeLayoutStructure;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.ui.layout.FieldSection;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import de.lab4inf.math.util.Strings;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/23/20 8:42 PM
 */
@Slf4j
@SuppressWarnings("Duplicates")
public class TPMActivityObjDescribeLayoutController extends AbstractStandardDescribeLayoutController<TPMActivityObjDescribeLayoutController.Arg> {

    public static final String ADD_ACTION = "add";
    public static final String EDIT_ACTION = "edit";
    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);

    private boolean enableActivityCycleControl = true;
    private static final List<String> CYCLE_FIELDS = Lists.newArrayList(TPMActivityFields.BEGIN_DATE, TPMActivityFields.END_DATE);
    private String ACTIVITY_TYPE_TEMPLATE_ID = "";

    private boolean forbidRelatedCustomer = false;
    private boolean activityPlanRequired = false;
    private boolean enableActivityPlan = false;

    private boolean existProofNode = false;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        // 获取所选类型的配置项
        initData();
    }

    private void initData() {
        if (Strings.isNullOrEmpty(arg.getActivity_type_id())) {
            return;
        }
        ActivityTypeExt activityTypeExt = activityTypeManager.find(controllerContext.getTenantId(), arg.getActivity_type_id());
        forbidRelatedCustomer = Boolean.TRUE.equals(activityTypeExt.get().getForbidRelateCustomer());
        activityPlanRequired = activityPlanRequired(activityTypeExt);
        enableActivityPlan = isEnableActivityPlan(activityTypeExt);
        ACTIVITY_TYPE_TEMPLATE_ID = activityTypeExt.get().getTemplateId();
        ActivityPlanConfigEntity activityPlanConfigEntity = activityTypeExt.activityPlanConfig();
        if (activityPlanConfigEntity != null && activityPlanConfigEntity.getEnableActivityCycleControl() != null) {
            this.enableActivityCycleControl = activityPlanConfigEntity.getEnableActivityCycleControl();
            log.info("activity config enableActivityCycleControl is {}", enableActivityCycleControl);
        }

        ActivityNodeEntity activityNodeEntity = activityTypeExt.proofNode();
        if (Objects.nonNull(activityNodeEntity)) {
            existProofNode = true;
        }
    }


    @Override
    protected StandardDescribeLayoutController.Result after(TPMActivityObjDescribeLayoutController.Arg arg, StandardDescribeLayoutController.Result result) {
        if (Boolean.TRUE.equals(arg.getInclude_layout())) {

            Set<String> hideFields = Sets.newHashSet("mn_ai_rule__c", "proof_period");
            Set<String> readonlyFields = Sets.newHashSet(TPMActivityFields.ACTIVITY_TYPE, TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, TPMActivityFields.AVAILABLE_AMOUNT, TPMActivityFields.ACTIVITY_FROZEN_AMOUNT, TPMActivityFields.MODE_TYPE);
            Set<String> requiredFields = Sets.newHashSet();

            if (ADD_ACTION.equals(arg.getLayout_type())) {
                hideFields.add(TPMActivityFields.ACTIVITY_STATUS);
                if (enableActivityPlan) {
                    if (activityPlanRequired) {
                        requiredFields.add(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID);
                        readonlyFields.add(TPMActivityFields.DEALER_CASHING_TYPE);
                    }
                } else {
                    hideFields.add(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID);
                }
            } else if (EDIT_ACTION.equals(arg.getLayout_type())) {
                readonlyFields.add(TPMActivityFields.ACTIVITY_STATUS);
                if (enableActivityPlan) {
                    if (activityPlanRequired) {
                        requiredFields.add(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID);
                        readonlyFields.add(TPMActivityFields.DEALER_CASHING_TYPE);
                    }
                    readonlyFields.add(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID);

                } else {
                    hideFields.add(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID);
                }
            }

            log.info("overwrite layout - type : {}, enable : {}, hide : {}, readonly : {}, required : {}",
                    arg.getActivity_type_id(),
                    enableActivityPlan,
                    hideFields,
                    readonlyFields,
                    requiredFields);

            if (enableActivityCycleControl) {
                requiredFields.addAll(CYCLE_FIELDS);
            }

            if (forbidRelatedCustomer) {
                requiredFields.add(TPMActivityFields.CUSTOMER_TYPE);
            }

/*            if (existProofNode) {
                requiredFields.add(TPMActivityFields.PROOF_PERIOD);
            }*/

            List<IFormField> additionFields = Lists.newArrayList(getFormField(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, "object_reference"));
            if (StringUtil.isNotEmpty(arg.getRecordType_apiName()) && arg.getRecordType_apiName().startsWith("promotion")) {
                log.info("activity promotion layout");
                additionFields.add(getFormField(TPMActivityFields.ACCOUNT_UNIFY_LIMIT_AMOUNT, "currency"));
            }

            LayoutUtil.setLayoutFieldHiddenOrReadOnly(LayoutExt.of(result.getLayout().toLayout()), hideFields, readonlyFields, requiredFields, additionFields, null);
            addAutoCloseLayoutFieldAfterWriteOffCount(LayoutExt.of(result.getLayout().toLayout()));
            addPeriodTimeFieldSection(LayoutExt.of(result.getLayout().toLayout()));
            log.info("overwrite layout : {}", JSON.toJSONString(result.getLayout()));
            overrideDetailLayout(result, enableActivityPlan);
        }
        log.info("activity_type_template_id is value {}", ACTIVITY_TYPE_TEMPLATE_ID);
        StandardDescribeLayoutController.Result after = super.after(arg, result);
        ObjectDescribeDocument objectDescribe = ObjectDescribeDocument.of(result.getObjectDescribe().toObjectDescribe().copy());
        objectDescribe.put("activity_type_template_id", ACTIVITY_TYPE_TEMPLATE_ID);
        objectDescribe.put("enable_activity_cycle_control", enableActivityCycleControl);
        rebuildDescribe(objectDescribe);
        after.setObjectDescribe(objectDescribe);
        rebuildDescribe(result.getObjectDescribeExt());
        return after;
    }

    private void addPeriodTimeFieldSection(LayoutExt layout) {
        if (existProofNode) {
            try {
                FormComponentExt form = layout.getFormComponent().orElse(null);
                if (!Objects.isNull(form) && existActivityProofPeriodField(controllerContext.getTenantId())) {
                    IFieldSection fieldSection = getIFieldSection(
                            TPMActivityFields.PROOF_PERIOD
                            , I18N.text(I18NKeys.FMCG_TPM_ACTIVITY_OBJ_PROOF_INFO)
                            , Lists.newArrayList(getFormField(TPMActivityFields.PROOF_PERIOD, "use_range"))
                    );
                    List<IFieldSection> fieldSections = form.getFieldSections();
                    fieldSections.add(1, fieldSection);
                    form.setFieldSections(fieldSections);
                }
            } catch (Exception ex) {
                log.info("add layout group cause unknown exception  : ", ex);
            }
        }
    }

    private boolean existActivityProofPeriodField(String tenantId) {
        try {
            FieldResult fieldDescribe = serviceFacade.findCustomFieldDescribe(tenantId, ApiNames.TPM_ACTIVITY_OBJ, TPMActivityFields.PROOF_PERIOD);
            if (fieldDescribe == null) {
                return false;
            }

        } catch (Exception e) {
            return false;
        }
        return true;
    }

    private IFieldSection getIFieldSection(String groupName, String groupHeader, List<IFormField> formFields) {
        IFieldSection fieldSection = new FieldSection();
        fieldSection.setName(groupName);
        fieldSection.setFields(formFields);
        fieldSection.setShowHeader(true);
        fieldSection.setHeader(groupHeader);
        return fieldSection;
    }

    private void overrideDetailLayout(StandardDescribeLayoutController.Result result, boolean enableActivityPlan) {
        Set<String> detailReadonlyFields = Sets.newHashSet();
        Set<String> detailRemoveFields = Sets.newHashSet();
        detailReadonlyFields.add(TPMActivityCashingProductFields.PRODUCT_NAME);
        detailReadonlyFields.add(TPMActivityCashingProductFields.UNIFIED_CASE_PRODUCT_ID);


        List<DetailObjectListResult> detailObjectList = result.getDetailObjectList();
        for (DetailObjectListResult detailObjectListResult : detailObjectList) {
            String objectApiName = detailObjectListResult.getObjectApiName();
            if (Objects.equals(objectApiName, ApiNames.TPM_ACTIVITY_CASHING_PRODUCT_OBJ)) {
                for (RecordTypeLayoutStructure structure : detailObjectListResult.getLayoutList()) {
                    Map detailLayout = structure.getDetail_layout();
                    LayoutExt layoutExt = LayoutExt.of(detailLayout);

                    Optional<FormComponentExt> component = layoutExt.getFormComponent();
                    if (component.isPresent()) {
                        FormComponentExt formComponent = component.get();
                        for (IFieldSection fieldSection : formComponent.getFieldSections()) {
                            if (!CollectionUtils.isEmpty(detailRemoveFields)) {
                                fieldSection.setFields(fieldSection.getFields().stream().filter(iFormField -> !detailRemoveFields.contains(iFormField.getFieldName())).collect(Collectors.toList()));
                            }
                            for (IFormField field : fieldSection.getFields()) {
                                if (detailReadonlyFields.contains(field.getFieldName())) {
                                    field.setReadOnly(true);
                                }
                            }
                        }
                    }

                    layoutExt.setButtons(layoutExt.getButtons().stream().filter(iButton -> !Objects.equals(ApiNames.TPM_ACTIVITY_CASHING_PRODUCT_SCOPE_OBJ, iButton.get("lookup_object_name", String.class))).collect(Collectors.toList()));

                }
            }
        }
        if (forbidRelatedCustomer) {
            LayoutExt layoutExt = LayoutExt.of(result.getLayout());
            List<String> hiddenComponents = layoutExt.getHiddenComponents();
            try {
                layoutExt.setComponents(layoutExt.getComponents().stream().filter(iComponent -> !iComponent.get("api_name").equals("TPMActivityStoreObj_md_group_component")).collect(Collectors.toList()));
            } catch (MetadataServiceException e) {
                log.info("update err.", e);
            }
            hiddenComponents.add("TPMActivityStoreObj_md_group_component");
            layoutExt.setHiddenComponents(hiddenComponents);
            result.setDetailObjectList(result.getDetailObjectList().stream().filter(detailObjectListResult -> !Objects.equals(detailObjectListResult.getObjectApiName(), ApiNames.TPM_ACTIVITY_STORE_OBJ)).collect(Collectors.toList()));
        }
    }

    public void addAutoCloseLayoutFieldAfterWriteOffCount(LayoutExt layout) {
        try {
            FormComponentExt form = layout.getFormComponent().orElse(null);
            if (!Objects.isNull(form)) {
                List<IFieldSection> sections = form.getFieldSections();
                if (CollectionUtils.isNotEmpty(sections)) {
                    sections.forEach(section -> {
                        List<IFormField> fields = section.getFields();
                        if (CollectionUtils.isNotEmpty(fields)) {
                            int countIndex = -1;
                            int booleanIndex = -1;
                            for (int i = 0; i < fields.size(); i++) {
                                if (TPMActivityFields.MAX_WRITE_OFF_COUNT.equals(fields.get(i).getFieldName())) {
                                    countIndex = i;
                                } else if (TPMActivityFields.IS_AUTO_CLOSE.equals(fields.get(i).getFieldName())) {
                                    booleanIndex = i;
                                }
                            }
                            if (countIndex != -1 && booleanIndex == -1) {
                                fields.add(countIndex + 1, getFormField(TPMActivityFields.IS_AUTO_CLOSE, "true_or_false"));
                                section.setFields(fields);
                            }
                        }
                    });
                }
            }
        } catch (Exception ex) {
            log.info("override layout cause unknown exception  : ", ex);
        }
    }

    private void rebuildDescribe(ObjectDescribeDocument objectDescribe) {
        if (Objects.isNull(objectDescribe)) {
            return;
        }
        dealCustomerTypeField(objectDescribe);
        dealProductRangeFreshStandardField(objectDescribe);
    }

    private void dealProductRangeFreshStandardField(ObjectDescribeDocument objectDescribe) {
        SelectOneFieldDescribe productRangeFreshStandard = (SelectOneFieldDescribe) objectDescribe.toObjectDescribe().getFieldDescribe(TPMActivityFields.PRODUCT_RANGE_FRESH_STANDARD);
        if (productRangeFreshStandard == null) {
            log.info("productRangeFreshStandard is null");
            return;
        }
        ISelectOption option = productRangeFreshStandard.getSelectOptions().stream().filter(v -> TPMActivityFields.ProductRangeFreshStandard.NO_LIMIT.equals(v.getValue())).findFirst().orElse(null);
        if (option != null) {
            if (ScanCodeActionConstants.BIG_DATE_ACTIVITY_TYPE_TEMPLATE_ID.equals(ACTIVITY_TYPE_TEMPLATE_ID)) {
                productRangeFreshStandard.setSelectOptions(productRangeFreshStandard.getSelectOptions().stream().filter(v -> !TPMActivityFields.ProductRangeFreshStandard.NO_LIMIT.equals(v.getValue())).collect(Collectors.toList()));
                ObjectDescribeExt.of(objectDescribe).getFieldDescribeMap().put(TPMActivityFields.PRODUCT_RANGE_FRESH_STANDARD, productRangeFreshStandard);
            }
        }
    }

    private void dealCustomerTypeField(ObjectDescribeDocument objectDescribe) {
        SelectOneFieldDescribe customerTypeField = (SelectOneFieldDescribe) objectDescribe.toObjectDescribe().getFieldDescribe(TPMActivityFields.CUSTOMER_TYPE);
        if (customerTypeField == null) {
            log.info("customer type is null");
            return;
        }
        ISelectOption option = customerTypeField.getSelectOptions().stream().filter(v -> ActivityCustomerTypeEnum.BRAND.value().equals(v.getValue())).findFirst().orElse(null);
        if (option != null) {
            if (option.isNotUsable()) {
                option.setNotUsable(false);
                serviceFacade.updateFieldDescribe(objectDescribe.toObjectDescribe(), Lists.newArrayList(customerTypeField));
            }
            if (forbidRelatedCustomer) {
                customerTypeField.setDefaultValue(ActivityCustomerTypeEnum.BRAND.value());
                customerTypeField.setSelectOptions(Lists.newArrayList(option));
            } else {
                customerTypeField.setSelectOptions(customerTypeField.getSelectOptions().stream().filter(v -> !ActivityCustomerTypeEnum.BRAND.value().equals(v.getValue())).collect(Collectors.toList()));
            }
            ObjectDescribeExt.of(objectDescribe).getFieldDescribeMap().put(TPMActivityFields.CUSTOMER_TYPE, customerTypeField);
        }
    }

    private IFormField getFormField(String apiName, String type) {
        IFormField field = new FormField();
        field.setFieldName(apiName);
        field.setRequired(false);
        field.setReadOnly(false);
        field.setRenderType(type);
        return field;
    }

    private boolean isEnableActivityPlan(ActivityTypeExt activityType) {

        boolean enableActivityPlan = false;
        if (Objects.nonNull(activityType)) {
            if (!Objects.isNull(activityType.node(NodeType.PLAN_TEMPLATE))) {
                enableActivityPlan = true;
            }
        }
        return enableActivityPlan;
    }

    private boolean activityPlanRequired(ActivityTypeExt activityType) {

        boolean enableActivityPlan = false;
        if (Objects.nonNull(activityType)) {
            ActivityNodeEntity node = activityType.node(NodeType.PLAN_TEMPLATE);
            if (!Objects.isNull(node)) {
                if (Objects.nonNull(activityType.activityPlanConfig())) {
                    return Boolean.TRUE.equals(activityType.activityPlanConfig().getEnableRelationPreNodeRequired());
                }
            }
        }
        return enableActivityPlan;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @ToString
    public static class Arg extends StandardDescribeLayoutController.Arg implements Serializable {
        @JSONField(name = "M50")
        private String activity_type_id;
    }
}
