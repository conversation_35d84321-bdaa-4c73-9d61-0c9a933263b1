package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.business.abstraction.IStoreTagService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class StoreTagService implements IStoreTagService {

    @Resource
    private ServiceFacade serviceFacade;

    @Override
    public void handlerTag(String tenantId, String sourceApiName, String objectId, String action) {
        if (Strings.isNullOrEmpty(sourceApiName) || Strings.isNullOrEmpty(objectId)) {
            return;
        }
        // object_Jk6CW__c 活动申请明细 蒙牛
        if ("object_Jk6CW__c".equals(sourceApiName)) {
            handlerStoreTagByAgreement(tenantId, objectId, action);

        } else if ("CostApprove__c".equals(sourceApiName)) {
            handlerStoreTagByCostApprove(tenantId, objectId, action);
        }

    }

    private void handlerStoreTagByCostApprove(String tenantId, String objectId, String action) {
        log.info("handlerStoreTagByCostApprove, tenantId:{}, objectId:{}, action:{}", tenantId, objectId, action);
        List<String> fields = Lists.newArrayList(CommonFields.ID, CommonFields.TENANT_ID, CommonFields.NAME,
                CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.RECORD_TYPE, "store_id__c");
        IFilter approvalFilter = new Filter();
        approvalFilter.setFieldName("field_z1hrn__c");
        approvalFilter.setOperator(Operator.EQ);
        approvalFilter.setFieldValues(Lists.newArrayList(objectId));

        IFilter approvalStatusFilter = new Filter();
        approvalStatusFilter.setFieldName("field_69eAm__c");
        approvalStatusFilter.setOperator(Operator.EQ);
        approvalStatusFilter.setFieldValues(Lists.newArrayList("2"));

        IFilter isUseFilter = new Filter();
        isUseFilter.setFieldName("field_80Od3__c");
        isUseFilter.setOperator(Operator.EQ);
        isUseFilter.setFieldValues(Lists.newArrayList("true"));

        IFilter monthFilter = new Filter();
        monthFilter.setFieldName("field_J57e1__c");
        monthFilter.setOperator(Operator.EQ);
        monthFilter.setFieldValues(Lists.newArrayList(getTodayMonth()));

        SearchTemplateQuery searchTemplateQuery = QueryDataUtil.minimumQuery(approvalFilter, approvalStatusFilter, isUseFilter, monthFilter);
        QueryDataUtil.findAndConsume(serviceFacade, User.systemUser(tenantId), "object_Jk6CW__c", searchTemplateQuery, fields, data -> {
            log.info("handlerStoreTagByCostApprove, data size :{}", data.size());
            List<String> storeIds = data.stream().map(v -> v.get("store_id__c", String.class)).collect(Collectors.toList());
            batchHandlerStoreTag(tenantId, storeIds, action);
        });
    }

    private String getTodayMonth() {
        long timestamp = System.currentTimeMillis();
        ZonedDateTime zonedDateTime = Instant.ofEpochMilli(timestamp).atZone(ZoneOffset.ofHours(8));
        int year = zonedDateTime.getYear();
        int month = zonedDateTime.getMonthValue();
        log.info("today year:{},month:{}", year, month);
        return year + "-" + month;
    }

    private void handlerStoreTagByAgreement(String tenantId, String objectId, String action) {
        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(tenantId), objectId, "object_Jk6CW__c");
//        IObjectDescribe describe = serviceFacade.findObject(tenantId, "object_Jk6CW__c");
//        infraServiceFacade.fillQuoteFieldValue(User.systemUser(tenantId), Lists.newArrayList(objectData), describe, null, false);

//        // 月份  2024-6
//        String month = objectData.get("field_J57e1__c", String.class);
        // 申请审批状态  审批通过
        String approvalStatus = objectData.get("field_69eAm__c", String.class);
        // 财务是否中止  true
        Boolean isUse = objectData.get("field_80Od3__c", Boolean.class);
        String storeId = objectData.get("store_id__c", String.class);
        log.info("handlerStoreTagByAgreement,approvalStatus:{},isUse:{},storeId:{}", approvalStatus, isUse, storeId);
        if ("2".equals(approvalStatus) && isUse) {
            batchHandlerStoreTag(tenantId, Lists.newArrayList(storeId), action);
        }
    }

    private void batchHandlerStoreTag(String tenantId, List<String> storeIds, String action) {
        if (CollectionUtils.isEmpty(storeIds)) {
            return;
        }
        log.info("batch handler tag storeIds size :{}", storeIds.size());
        List<List<String>> storeIdPartition = Lists.partition(storeIds, 200);

        for (List<String> ids : storeIdPartition) {
            List<IObjectData> accountDataList = serviceFacade.findObjectDataByIds(tenantId, ids, ApiNames.ACCOUNT_OBJ);
            accountDataList.forEach(accountData -> {

                if ("add".equals(action)) {
                    List<String> customerLabel = CommonUtils.castIgnore(accountData.get("customer_label"), String.class);
                    // 不包含标签3，新增
                    if (!customerLabel.contains("3")) {
                        customerLabel.add("3");
                        accountData.set("customer_label", customerLabel);
                    }
                } else if ("remove".equals(action)) {
                    List<String> customerLabel = CommonUtils.castIgnore(accountData.get("customer_label"), String.class);
                    // 包含标签3，删除
                    if (customerLabel.contains("3")) {
                        customerLabel.remove("3");
                        accountData.set("customer_label", customerLabel);
                    }
                }
            });
            serviceFacade.batchUpdateByFields(User.systemUser(tenantId), accountDataList, Lists.newArrayList("customer_label"));
        }
    }
}
