package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.service.PackTransactionProxyImpl;
import com.facishare.crm.fmcg.tpm.service.abstraction.PackTransactionProxy;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.github.mybatis.util.InjectSchemaUtil;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/6/16 上午11:08
 */
public class TPMActivityObjTestController extends PreDefineController<TPMActivityObjTestController.Arg, TPMActivityObjTestController.Result> {

    private final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);
    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);
    private final SpecialTableMapper specialTableMapper = SpringUtil.getContext().getBean(SpecialTableMapper.class);
    private final DbRouterClient dbRouterClient = SpringUtil.getContext().getBean(DbRouterClient.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    @Transactional
    public Result doService(Arg arg) {
        /*String sql = "select * from fmcg_tpm_activity where tenant_id='" + controllerContext.getTenantId() + "' limit 10";
        List list = specialTableMapper.setTenantId(controllerContext.getTenantId()).findBySql(getSchemaSqlByTenantId(controllerContext.getTenantId(), sql));
        log.info("rst:{}" + JSON.toJSONString(list));
        Map<String, List<String>> codeMap = new HashMap<>();
        codeMap.put(ApiNames.TPM_ACTIVITY_OBJ, Lists.newArrayList("Add", "edit"));
        PrivilegeResult<CheckFunctionPrivilege.Result> codeRst = serviceFacade.batchObjectFuncPrivilegeCheck(controllerContext.getRequestContext(), codeMap);
        System.out.println(JSON.toJSONString(codeRst));
        return new Result(list);*/
        return null;
    }



    @Data
    @ToString
    public static class Arg implements Serializable {
        private String tableName;
    }

    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result implements Serializable {
        private Object result;
    }


}


