package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.RewardExceptionRecordFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRewardExceptionRecordService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.DescribeResult;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: linmj
 * Date: 2023/11/28 15:35
 */

@Slf4j
@Component
public class RewardExceptionRecordService implements IRewardExceptionRecordService {

    @Resource
    private ServiceFacade serviceFacade;

    @Override
    public void create(String tenantId) {
        String json;
        String listLayout;
        try {
            File file = ResourceUtils.getFile("classpath:tpm/module_reward/RewardExceptionRecordObj__c.json");
            json = new String(Files.readAllBytes(file.toPath()));
            listLayout = new String(Files.readAllBytes(ResourceUtils.getFile("classpath:tpm/module_reward/RewardExceptionRecordObj__cLayout.json").toPath()));
        } catch (FileNotFoundException e) {
            throw new MetaDataBusinessException("file not found.");
        } catch (IOException ex) {
            throw new MetaDataBusinessException("resource failed.");
        }
        DescribeResult describeResult = serviceFacade.createDescribe(User.systemUser(tenantId), json, null, listLayout, true, false);
    }

    @Override
    public void writeRecord(String tenantId, String bizCode, String bizType, String errorMessage, String status) {

        IObjectData existsData = getByBizCode(tenantId, bizCode);
        if (existsData == null && status.equals("1")) {
            IObjectData record = new ObjectData();
            record.setDescribeApiName(ApiNames.REWARD_EXCEPTION_RECORD_OBJ);
            record.setRecordType(CommonFields.RECORD_TYPE__DEFAULT);
            record.setOwner(Lists.newArrayList("-10000"));
            record.setTenantId(tenantId);
            record.set(RewardExceptionRecordFields.BIZ_CODE_ID, bizCode);
            record.set(RewardExceptionRecordFields.BIZ_TYPE, bizType);
            record.set(RewardExceptionRecordFields.ERROR_DETAIL_MESSAGE, errorMessage);
            record.set(RewardExceptionRecordFields.STATUS, status);
            try {
                try {
                    serviceFacade.saveObjectData(User.systemUser(tenantId), record);
                } catch (ObjectDefNotFoundError e) {
                    log.info("add data error", e);
                    create(tenantId);
                    serviceFacade.saveObjectData(User.systemUser(tenantId), record);
                }
            } catch (Exception e) {
                log.info("write err.", e);
            }
        } else if (existsData != null) {
            update(tenantId, existsData, status, errorMessage);
        }
    }

    @Override
    public IObjectData getByBizCode(String tenantId, String bizCode) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(1);

        Filter bizCodeFilter = new Filter();
        bizCodeFilter.setFieldName(RewardExceptionRecordFields.BIZ_CODE_ID);
        bizCodeFilter.setOperator(Operator.EQ);
        bizCodeFilter.setFieldValues(Lists.newArrayList(bizCode));

        query.setFilters(Lists.newArrayList(bizCodeFilter));
        try {
            List<IObjectData> dataList = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.REWARD_EXCEPTION_RECORD_OBJ, query);
            return CollectionUtils.isEmpty(dataList) ? null : dataList.get(0);
        } catch (ObjectDefNotFoundError e) {
            log.info("query data error", e);
            create(tenantId);
            List<IObjectData> dataList = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.REWARD_EXCEPTION_RECORD_OBJ, query);
            return CollectionUtils.isEmpty(dataList) ? null : dataList.get(0);
        }
    }

    @Override
    public void update(String tenantId, IObjectData record, String status, String message) {
       try {
           Map<String, Object> updateMap = new HashMap<>();

           updateMap.put(RewardExceptionRecordFields.STATUS, status);
           updateMap.put(RewardExceptionRecordFields.ERROR_DETAIL_MESSAGE, message);

           serviceFacade.updateWithMap(User.systemUser(tenantId), record, updateMap);
       }catch (Exception e){
           log.info("update err.", e);
       }
    }
}
