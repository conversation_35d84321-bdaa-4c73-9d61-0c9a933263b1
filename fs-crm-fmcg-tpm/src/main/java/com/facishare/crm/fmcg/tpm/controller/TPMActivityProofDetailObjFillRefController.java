package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityAgreementDetailFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityDetailFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.ICostStandardService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import de.lab4inf.math.util.Strings;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/14 下午2:52
 */
public class TPMActivityProofDetailObjFillRefController extends PreDefineController<TPMActivityProofDetailObjFillRefController.Arg, TPMActivityProofDetailObjFillRefController.Result> {

    private static final ICostStandardService costStandardService = SpringUtil.getContext().getBean(ICostStandardService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected Result doService(Arg arg) {
        Map<String, String> activityDetail2AgreementDetail = new HashMap<>();
        if (!Strings.isNullOrEmpty(arg.getAgreementId())) {
            SearchTemplateQuery agreementDetailQuery = new SearchTemplateQuery();

            agreementDetailQuery.setLimit(-1);
            agreementDetailQuery.setOffset(0);
            agreementDetailQuery.setSearchSource("db");

            Filter masterIdFilter = new Filter();
            masterIdFilter.setOperator(Operator.EQ);
            masterIdFilter.setFieldName(TPMActivityAgreementDetailFields.ACTIVITY_AGREEMENT_ID);
            masterIdFilter.setFieldValues(Lists.newArrayList(arg.getAgreementId()));

            agreementDetailQuery.setFilters(Lists.newArrayList(masterIdFilter));

            CommonUtils.queryData(serviceFacade, User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_DETAIL_OBJ, agreementDetailQuery)
                    .forEach(v -> activityDetail2AgreementDetail.put(v.get(TPMActivityAgreementDetailFields.ACTIVITY_DETAIL_ID, String.class), v.getId()));
        }
        Map<String, String> activityDetail2ActivityItem = new HashMap<>();
        serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), arg.getActivityDetailIds(), ApiNames.TPM_ACTIVITY_DETAIL_OBJ).forEach(v -> activityDetail2ActivityItem.put(v.getId(), v.get(TPMActivityDetailFields.ACTIVITY_ITEM_ID, String.class)));
        Result result = new Result();
        Map<String, Content> contentMap = new HashMap<>();
        result.setMap(contentMap);

        activityDetail2ActivityItem.forEach((detailId, itemId) -> {
            Content content = new Content();
            content.setAgreementDetailId(activityDetail2AgreementDetail.get(detailId));
            IObjectData costStandard = costStandardService.queryCostStandard(controllerContext.getTenantId(), arg.getStoreId(), itemId);
            if (costStandard != null) {
                content.setCostStandardId(costStandard.getId());
            }
            contentMap.put(detailId, content);
        });

        return result;
    }

    @Data
    @ToString
    static class Arg implements Serializable {

        @SerializedName("store_id")
        @JSONField(name = "store_id")
        @JsonProperty("store_id")
        private String storeId;

        @SerializedName("agreement_id")
        @JSONField(name = "agreement_id")
        @JsonProperty("agreement_id")
        private String agreementId;

        @SerializedName("activity_detail_ids")
        @JSONField(name = "activity_detail_ids")
        @JsonProperty("activity_detail_ids")
        private List<String> activityDetailIds;
    }


    @Data
    @ToString
    static class Result implements Serializable {
        private Map<String, Content> map;
    }

    @Data
    @ToString
    static class Content implements Serializable {

        @SerializedName("agreement_detail_id")
        @JSONField(name = "agreement_detail_id")
        @JsonProperty("agreement_detail_id")
        private String agreementDetailId;

        @SerializedName("cost_standard_id")
        @JSONField(name = "cost_standard_id")
        @JsonProperty("cost_standard_id")
        private String costStandardId;
    }

}
