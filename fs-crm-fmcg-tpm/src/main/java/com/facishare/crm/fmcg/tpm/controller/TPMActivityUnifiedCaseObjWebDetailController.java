package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityUnifiedCaseFields;
import com.facishare.crm.fmcg.common.utils.convert.UseRangeFieldDataRender;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/20 19:33
 */
public class TPMActivityUnifiedCaseObjWebDetailController extends StandardWebDetailController {

    private static final UseRangeFieldDataRender useRangeFieldDataRender = SpringUtil.getContext().getBean(UseRangeFieldDataRender.class);

    @Override
    protected Result after(Arg arg, Result result) {
        buttonFilter(result);
        fillActivityDate(result);
        fillStoreRange(result);
        return super.after(arg, result);
    }

    private void fillActivityDate(Result result) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(result.getData());
        Long beginDate = objectDataExt.get(TPMActivityUnifiedCaseFields.START_DATE, Long.class);
        Long endDate = objectDataExt.get(TPMActivityUnifiedCaseFields.END_DATE, Long.class);

        if (beginDate <= TimeUtils.MIN_DATE) {
            result.getData().put(TPMActivityUnifiedCaseFields.START_DATE, null);
        }
        if (endDate >= TimeUtils.MAX_DATE) {
            result.getData().put(TPMActivityUnifiedCaseFields.END_DATE, null);
        }
    }

    private void fillStoreRange(Result result) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(result.getData());
        JSONObject storeRangeOld = JSON.parseObject(objectDataExt.getStringValue(TPMActivityFields.STORE_RANGE));
        if (storeRangeOld == null || !"CONDITION".equalsIgnoreCase(storeRangeOld.getString("type"))) {
            return;
        }
        String storeRangeNew = useRangeFieldDataRender.render(objectDataExt.get(TPMActivityFields.STORE_RANGE), ApiNames.ACCOUNT_OBJ);
        if (!Strings.isNullOrEmpty(storeRangeNew)){
            result.getData().put(TPMActivityFields.STORE_RANGE, storeRangeNew);
        }
    }

    private void buttonFilter(Result result) {
        List components = (ArrayList) (result.getLayout().get("components"));
        String closedStatus = (String) result.getData().get(TPMActivityUnifiedCaseFields.CLOSE_STATUS);

        if (TPMActivityUnifiedCaseFields.CLOSE_STATUS__CLOSED.equals(closedStatus)) {
            for (Object component : components) {
                Map com = (Map) component;
                if ("head_info".equals(com.get("api_name"))) {
                    ArrayList buttons = (ArrayList) com.get("buttons");
                    buttons.removeIf(button -> {
                        Map btn = (Map) (button);
                        return ObjectAction.CLOSE_TPM_ACTIVITY.getActionCode().equals(btn.get("action"));
                    });
                }
            }
            if ("mobile".equals(arg.getLayoutAgentType())) {
                ArrayList buttons = (ArrayList) result.getLayout().get("buttons");
                buttons.removeIf(button -> {
                    Map btn = (Map) (button);
                    return ObjectAction.CLOSE_TPM_ACTIVITY.getActionCode().equals(btn.get("action"));
                });
            }
        }
    }
}
