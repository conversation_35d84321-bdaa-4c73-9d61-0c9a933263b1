package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.tpm.business.abstraction.IFundAccountService;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: wuyx
 * @description: TPM 客户范围
 * @createTime: 2021/12/29 11:04
 */
@SuppressWarnings("Duplicates")
public class TPMDealerActivityCostObjRelatedFundAccountListController extends StandardRelatedListController {


    private static final IFundAccountService fundAccountService = SpringUtil.getContext().getBean(IFundAccountService.class);


    /**
     * 重新设置上下文，将上下文改为客户对象
     *
     * @param arg 搜索客户入参
     */
    @Override
    protected void before(Arg arg) {
        this.controllerContext = new ControllerContext(
                controllerContext.getRequestContext(),
                ApiNames.FUND_ACCOUNT_OBJ,
                controllerContext.getMethodName());

        log.info("filter arg : {}", arg);

        super.before(arg);
    }

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        List<IFilter> filters = query.getFilters();
        List<IFilter> objectDescribeNameList = filters.stream().filter(iFilter -> "object_describe_api_name".equals(iFilter.getFieldName())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(objectDescribeNameList)) {
            IFilter filter = new Filter();
            filter.setFieldName("object_describe_api_name");
            filter.setConnector("AND");
            filter.setOperator(Operator.EQ);
            filter.setFieldValues(Lists.newArrayList(arg.getObjectApiName()));
            query.setFilters(Lists.newArrayList(filter));
        }
        overrideAccountQuery(query);

        super.beforeQueryData(query);
    }

    /**
     * @param query 搜索 query
     * @return 搜索结果
     */
    private void overrideAccountQuery(SearchTemplateQuery query) {
        String tenantId = controllerContext.getTenantId();
        List<String> fundAccountIds = fundAccountService.getFundAccountsInWriteOff(tenantId).stream().map(DBRecord::getId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(fundAccountIds)) {
            fundAccountIds.add("no_id");
        }

        String id = arg.getObjectData().getId();
        IObjectData data = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), id, ApiNames.TPM_DEALER_ACTIVITY_COST);
        String dealerCashingType = data.get(TPMDealerActivityCostFields.DEALER_CASHING_TYPE, String.class);

        if (Objects.equals(TPMActivityCashingProductFields.GOODS, dealerCashingType)) {
            Filter idFilter = new Filter();
            idFilter.setFieldName(CommonFields.ID);
            idFilter.setOperator(Operator.IN);
            idFilter.setFieldValues(fundAccountIds);
            query.getFilters().add(idFilter);
        } else {
            //现金时
            // (1 and 2) or (1 and 3 and 4)
            Wheres where1 = new Wheres();
            where1.setConnector("OR");
            Filter filer = new Filter();
            filer.setFieldName(CommonFields.ID);
            filer.setOperator(Operator.IN);
            filer.setFieldValues(fundAccountIds);

            Filter accountTypeFilter1 = new Filter();
            accountTypeFilter1.setFieldName(FundAccountFields.ACCOUNT_TYPE);
            accountTypeFilter1.setOperator(Operator.EQ);
            accountTypeFilter1.setFieldValues(Lists.newArrayList(FundAccountFields.ACCOUNT_TYPE__AMOUNT));

            where1.setFilters(Lists.newArrayList(filer, accountTypeFilter1));

            Wheres where2 = new Wheres();
            where2.setConnector("OR");
            Filter accountTypeFilter2 = new Filter();
            accountTypeFilter2.setFieldName(FundAccountFields.ACCOUNT_TYPE);
            accountTypeFilter2.setOperator(Operator.EQ);
            accountTypeFilter2.setFieldValues(Lists.newArrayList(FundAccountFields.ACCOUNT_TYPE__GOODS_AMOUNT));

            Filter accountTypeFilter3 = new Filter();
            accountTypeFilter3.setFieldName(FundAccountFields.ACCESS_MODULE);
            accountTypeFilter3.setOperator(Operator.EQ);
            accountTypeFilter3.setFieldValues(Lists.newArrayList(FundAccountFields.ACCESS_MODULE_DEFAULT));

            where2.setFilters(Lists.newArrayList(filer, accountTypeFilter2, accountTypeFilter3));


            query.setWheres(Lists.newArrayList(where1, where2));

            log.info("final query : {}", JSON.toJSONString(query));
        }

    }
}
