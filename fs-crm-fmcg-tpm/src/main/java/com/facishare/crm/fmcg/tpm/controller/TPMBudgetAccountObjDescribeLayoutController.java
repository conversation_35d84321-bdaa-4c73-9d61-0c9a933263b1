package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetAccountService;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDescribeLayoutController;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.lab4inf.math.util.Strings;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class TPMBudgetAccountObjDescribeLayoutController extends AbstractStandardDescribeLayoutController<TPMBudgetAccountObjDescribeLayoutController.Arg> {

    private final IBudgetAccountService budgetAccountService = SpringUtil.getContext().getBean(IBudgetAccountService.class);
    private final BudgetTypeDAO budgetTypeDAO = SpringUtil.getContext().getBean(BudgetTypeDAO.class);

    private String typeId;
    private String nodeId;

    @Override
    protected void before(Arg arg) {
        super.before(arg);

        if (!Strings.isNullOrEmpty(arg.getBudgetTemplateId())) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_OBJ_DESCRIBE_LAYOUT_CONTROLLER_0));
        }

        this.typeId = arg.getBudgetTypeId();
        this.nodeId = arg.getBudgetNodeId();

        if (!Strings.isNullOrEmpty(arg.getData_id())) {
            IObjectData data = serviceFacade.findObjectDataIgnoreAll(User.systemUser(controllerContext.getTenantId()), arg.getData_id(), arg.getApiname());
            this.typeId = data.get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class);
            this.nodeId = data.get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class);
        }
    }

    @Override
    protected StandardDescribeLayoutController.Result after(Arg arg, StandardDescribeLayoutController.Result result) {
        StandardDescribeLayoutController.Result inner = super.after(arg, result);
        if (!Strings.isNullOrEmpty(this.typeId) &&
                !Strings.isNullOrEmpty(this.nodeId)) {
            BudgetTypePO type = budgetTypeDAO.get(controllerContext.getTenantId(), this.typeId);
            if (Objects.isNull(type)) {
                throw new ValidateException("budget type not found.");
            }

            BudgetTypeNodeEntity node = type.getNodes().stream().filter(n -> n.getNodeId().equals(this.nodeId)).findFirst().orElse(null);
            if (Objects.isNull(node)) {
                throw new ValidateException("budget node not found.");
            }

            ILayout override = budgetAccountService.overrideLayout(inner.getLayout().toLayout(), node);
            inner.setLayout(LayoutDocument.of(override));
        }
        return inner;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Arg extends StandardDescribeLayoutController.Arg {

        @JSONField(name = "budget_type_id")
        @JsonProperty("budget_type_id")
        private String budgetTypeId;

        @JSONField(name = "budget_node_id")
        @JsonProperty("budget_node_id")
        private String budgetNodeId;

        @JSONField(name = "budget_template_id")
        @JsonProperty("budget_template_id")
        private String budgetTemplateId;
    }
}
