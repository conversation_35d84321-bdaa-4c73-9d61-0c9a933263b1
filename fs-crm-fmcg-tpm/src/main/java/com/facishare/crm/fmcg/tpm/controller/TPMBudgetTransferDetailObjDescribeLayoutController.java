package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDescribeLayoutController;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.ui.layout.IFieldSection;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class TPMBudgetTransferDetailObjDescribeLayoutController extends AbstractStandardDescribeLayoutController<StandardDescribeLayoutController.Arg> {

    @Override
    protected StandardDescribeLayoutController.Result after(StandardDescribeLayoutController.Arg arg, StandardDescribeLayoutController.Result result) {
        StandardDescribeLayoutController.Result layoutResult = super.after(arg, result);
        if ("add".equals(arg.getLayout_type())) {
            LayoutExt layoutExt = LayoutExt.of(layoutResult.getLayout());
            List<FormComponentExt> components = layoutExt.getFormComponents();
            for (FormComponentExt component : components) {
                for (IFieldSection fieldSection : component.getFieldSections()) {
                    fieldSection.setFields(
                            fieldSection.getFields().stream()
                                    .filter(field -> !"operation_status".equals(field.getFieldName()))
                                    .collect(Collectors.toList())
                    );
                }
            }
        }
        return layoutResult;
    }
}
