package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;


@SuppressWarnings("all")
public class TPMBudgetDisassemblyObjBulkInvalidAction extends StandardBulkInvalidAction {

    @Override
    protected void before(Arg arg) {
        throw new ValidateException("bulk invalid action not allowed!");
    }
}