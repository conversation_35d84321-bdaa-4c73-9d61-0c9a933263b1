package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import lombok.extern.slf4j.Slf4j;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;

/**
 * <AUTHOR>
 * @date 2022/6/30 下午5:48
 */
@Slf4j
public class TPMBudgetStatisticTableObjEditAction extends StandardEditAction {

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        String apiName = arg.getObjectData().toObjectData().getDescribeApiName();
        if (ApiNames.TPM_BUDGET_STATISTIC_TABLE.equals(apiName)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_STATISTIC_TABLE_OBJ_EDIT_ACTION_0));
        }
    }
}
