package com.facishare.crm.fmcg.tpm.business.manager;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofDetailFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofFields;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofDTO;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofDetailDTO;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofImageDTO;
import com.facishare.crm.fmcg.tpm.api.visit.VisitActionDataDTO;
import com.facishare.crm.fmcg.tpm.business.TPMDisplayReportService;
import com.facishare.crm.fmcg.tpm.cache.ActivityItemCache;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityDisplayImgDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityDisplayImgPO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.StatusType;
import com.facishare.crm.fmcg.tpm.service.CheckinService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@SuppressWarnings("Duplicates")
public class CheckinsManager {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private ActivityItemCache activityItemCache;
    @Resource
    private CheckinService checkinService;
    @Resource
    private ActivityDisplayImgDAO activityDisplayImgDAO;

    public void sendErrorMessageToCheckin(String tenantId, String proofId, boolean deleteErrorMessage) {
        if (deleteErrorMessage) {
            saveCheckinsAction(tenantId, proofId, null);
            return;
        }
        List<ActivityDisplayImgPO> allInfo = activityDisplayImgDAO.findByProofId(tenantId, proofId);
        if (CollectionUtils.isEmpty(allInfo)) {
            return;
        }
        List<ActivityDisplayImgPO> aiErrorInfos = allInfo.stream().filter(info ->
                        Objects.equals(info.getStatus(), StatusType.ERROR.value()) && StringUtils.isNotBlank(info.getErrorMessage()) && info.getErrorMessage().contains(TPMDisplayReportService.AI_ERROR_MESSAGE_PREFIX))
                .collect(Collectors.toList());
        List<ActivityDisplayImgPO> tpmErrorInfos = allInfo.stream().filter(info ->
                        Objects.equals(info.getStatus(), StatusType.ERROR.value()) && StringUtils.isNotBlank(info.getErrorMessage()) && info.getErrorMessage().contains(TPMDisplayReportService.TPM_ERROR_MESSAGE_PREFIX))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(aiErrorInfos)) {
            saveCheckinsAction(tenantId, proofId, aiErrorInfos.get(0).getErrorMessage());
            return;
        }
        if (CollectionUtils.isNotEmpty(tpmErrorInfos)) {
            saveCheckinsAction(tenantId, proofId, tpmErrorInfos.get(0).getErrorMessage());
        }
    }


    private void saveCheckinsAction(String tenantId, String proofId, String errorMsg) {
        IObjectData proofData = serviceFacade.findObjectData(User.systemUser(tenantId), proofId, ApiNames.TPM_ACTIVITY_PROOF_OBJ);
        String visitId = proofData.get(TPMActivityProofFields.VISIT_ID, String.class);
        String actionId = proofData.get(TPMActivityProofFields.ACTION_ID, String.class);

        if (Strings.isNullOrEmpty(visitId) || Strings.isNullOrEmpty(actionId)) {
            return;
        }

        String storeId = proofData.get(TPMActivityProofFields.STORE_ID, String.class);
        VisitActionDataDTO data = new VisitActionDataDTO();

        List<IObjectData> masterList = queryProof(tenantId, storeId, visitId, actionId);
        List<String> masterIds = masterList.stream().map(DBRecord::getId).collect(Collectors.toList());
        List<String> activityIds = masterList.stream().map(master -> (String) master.get(TPMActivityProofFields.ACTIVITY_ID)).distinct().collect(Collectors.toList());
        Map<String, IObjectData> activityMap = queryActivity(tenantId, activityIds).stream().collect(Collectors.toMap(DBRecord::getId, v -> v, (oldOne, newOne) -> oldOne));
        Map<String, List<IObjectData>> detailsMap = queryActivityProofDetails(tenantId, masterIds).stream().collect(Collectors.groupingBy(detail -> (String) detail.get(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID)));
        Map<String, String> itemNameMap = queryActivityItem(tenantId);
        data.setActivityProofList(Lists.newArrayList());

        for (IObjectData master : masterList) {
            ActivityProofDTO datum = new ActivityProofDTO();
            datum.setProofId(master.getId());
            datum.setRemark((String) master.get(TPMActivityProofFields.REMARK));
            String activityId = (String) master.get(TPMActivityProofFields.ACTIVITY_ID);
            if (activityMap.containsKey(activityId)) {
                datum.setActivityName(activityMap.get(activityId).getName());
            }
            datum.setImages(JSON.parseArray(JSON.toJSONString(master.get(TPMActivityProofFields.PROOF_IMAGES)), ActivityProofImageDTO.class));
            datum.setImagesTotalCount(CollectionUtils.isEmpty(datum.getImages()) ? 0 : datum.getImages().size());
            datum.setDetails(Lists.newArrayList());
            if (StringUtils.isNotBlank(errorMsg)) {
                datum.setErrorMessage(errorMsg);
            }
            if (detailsMap.containsKey(master.getId())) {
                List<IObjectData> details = detailsMap.get(master.getId());
                for (IObjectData detail : details) {
                    ActivityProofDetailDTO detailDatum = new ActivityProofDetailDTO();
                    String activityItemId = (String) detail.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID);
                    detailDatum.setName(itemNameMap.getOrDefault(activityItemId, "--"));
                    detailDatum.setAmount((String) detail.get(TPMActivityProofDetailFields.AMOUNT));
                    datum.getDetails().add(detailDatum);
                }
            }
            data.getActivityProofList().add(datum);
        }

        data.setActivityProofListSize(data.getActivityProofList().size());
        checkinService.updateProofAction(User.systemUser(tenantId), visitId, actionId, data);

    }

    private List<IObjectData> queryProof(String tenantId, String storeId, String visitId, String actionId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(6);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);

        if (!Strings.isNullOrEmpty(storeId)) {
            Filter storeIdFilter = new Filter();
            storeIdFilter.setFieldName(TPMActivityProofFields.STORE_ID);
            storeIdFilter.setOperator(Operator.EQ);
            storeIdFilter.setFieldValues(Lists.newArrayList(storeId));
            query.getFilters().add(storeIdFilter);
        }


        Filter visitIdFilter = new Filter();
        visitIdFilter.setFieldName(TPMActivityProofFields.VISIT_ID);
        visitIdFilter.setOperator(Operator.EQ);
        visitIdFilter.setFieldValues(Lists.newArrayList(visitId));
        query.getFilters().add(visitIdFilter);

        Filter actionIdFilter = new Filter();
        actionIdFilter.setFieldName(TPMActivityProofFields.ACTION_ID);
        actionIdFilter.setOperator(Operator.EQ);
        actionIdFilter.setFieldValues(Lists.newArrayList(actionId));
        query.getFilters().add(actionIdFilter);

        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_PROOF_OBJ, query, Lists.newArrayList(CommonFields.ID, TPMActivityProofFields.ACTIVITY_ID, TPMActivityProofFields.REMARK, TPMActivityProofFields.PROOF_IMAGES));
    }

    private List<IObjectData> queryActivity(String tenantId, List<String> ids) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(ids.size());
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);

        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(ids);

        query.setFilters(Lists.newArrayList(idFilter));
        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_OBJ, query, Lists.newArrayList(CommonFields.ID, CommonFields.NAME));
    }

    private List<IObjectData> queryActivityProofDetails(String tenantId, List<String> masterIds) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(-1);
        stq.setOffset(0);
        stq.setSearchSource("db");
        stq.setNeedReturnCountNum(false);

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID);
        masterFilter.setOperator(Operator.IN);
        masterFilter.setFieldValues(masterIds);

        stq.setFilters(Lists.newArrayList(masterFilter));
        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        TPMActivityProofDetailFields.ACTIVITY_PROOF_ID,
                        TPMActivityProofDetailFields.ACTIVITY_ITEM_ID,
                        TPMActivityProofDetailFields.AMOUNT
                ));
    }

    private Map<String, String> queryActivityItem(String tenantId) {
        return activityItemCache.get(tenantId);
    }


}
