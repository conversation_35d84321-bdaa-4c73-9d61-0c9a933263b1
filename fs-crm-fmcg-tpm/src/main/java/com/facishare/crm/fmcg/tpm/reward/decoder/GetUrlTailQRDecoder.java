package com.facishare.crm.fmcg.tpm.reward.decoder;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.FMCGSerialNumberFields;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.reward.abstraction.QRCodeDecoder;
import com.facishare.crm.fmcg.tpm.reward.annotation.Name;
import com.facishare.crm.fmcg.tpm.reward.dto.SerialNumberData;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * Author: linmj
 * Date: 2024/4/23 16:53
 */

@Name(name = "getUrlTailQRDecoder")
@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class GetUrlTailQRDecoder implements QRCodeDecoder {

    @Resource
    private ServiceFacade serviceFacade;

    @Override
    public SerialNumberData decode(String tenantId, String code) {
        SerialNumberData data = decodeInner(tenantId, code);
        if (Objects.nonNull(data)) {
            return data;
        }

        data = decodeOuter(tenantId, code);
        if (Objects.nonNull(data)) {
            return data;
        }

        return decodeCoupon(tenantId, code);
    }

    @Override
    public SerialNumberData decodeCoupon(String tenantId, String code) {
        if (Strings.isNullOrEmpty(code)) {
            return null;
        }

        IObjectData sn = findObjectData(tenantId, code, true);

        if (Objects.isNull(sn)) {
            return null;
        }

        return convert(SerialNumberData.COUPON_TYPE, code, sn);
    }

    @Override
    public SerialNumberData decodeOuter(String tenantId, String code) {
        if (Strings.isNullOrEmpty(code)) {
            return null;
        }

        IObjectData sn = findObjectData(tenantId, code, false);

        if (Objects.isNull(sn)) {
            return null;
        }

        return convert(SerialNumberData.OUTER_TYPE, code, sn);
    }

    @Override
    public SerialNumberData decodeInner(String tenantId, String code) {
        if (Strings.isNullOrEmpty(code)) {
            return null;
        }

        IObjectData sn = findObjectData(tenantId, code, false);

        if (Objects.isNull(sn)) {
            return null;
        }

        return convert(SerialNumberData.INNER_TYPE, code, sn);
    }

    private IObjectData findObjectData(String tenantId, String name, boolean isCoupon) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.NAME);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(name));

        SearchTemplateQuery query;
        if (isCoupon) {
            IFilter couponFilter = new Filter();
            couponFilter.setFieldName("code_type__c");
            couponFilter.setOperator(Operator.EQ);
            couponFilter.setFieldValues(Lists.newArrayList("coupon"));

            query = QueryDataUtil.minimumFindOneQuery(idFilter, couponFilter);
        } else {
            query = QueryDataUtil.minimumFindOneQuery(idFilter);
        }

        List<IObjectData> data = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.FMCG_SERIAL_NUMBER_OBJ,
                query,
                Lists.newArrayList("_id", CommonFields.TENANT_ID, FMCGSerialNumberFields.PRODUCT_ID, FMCGSerialNumberFields.MANUFACTURE_DATE)
        );

        if (CollectionUtils.isEmpty(data)) {
            return null;
        }

        return data.get(0);
    }

    private SerialNumberData convert(String type, String code, IObjectData snObj) {
        return SerialNumberData.builder()
                .type(type)
                .realCode(code)
                .snId(snObj.getId())
                .name(snObj.getName())
                .skuId(snObj.get(FMCGSerialNumberFields.PRODUCT_ID, String.class))
                .manufactureDate(snObj.get(FMCGSerialNumberFields.MANUFACTURE_DATE, Long.class))
                .data(snObj)
                .build();
    }
}
