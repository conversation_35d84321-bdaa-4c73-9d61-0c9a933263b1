package com.facishare.crm.fmcg.tpm.action;

import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.business.BudgetOperatorFactory;
import com.facishare.crm.fmcg.tpm.business.abstraction.*;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.tpm.business.enums.DisassemblyActionCode;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.service.abstraction.ITransactionProxy;
import com.facishare.crm.fmcg.tpm.utils.CurrencyUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.utils.TraceUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.math.BigDecimal;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@SuppressWarnings("Duplicates")
public class TPMBudgetDisassemblyObjAddAction extends StandardAddAction {

    private final BudgetTypeDAO budgetTypeDAO = SpringUtil.getContext().getBean(BudgetTypeDAO.class);
    private final ITransactionProxy transProxy = SpringUtil.getContext().getBean(ITransactionProxy.class);
    private final IBudgetCompareService budgetCompareService = SpringUtil.getContext().getBean(IBudgetCompareService.class);
    private final IBudgetDisassemblyService budgetDisassemblyService = SpringUtil.getContext().getBean(IBudgetDisassemblyService.class);
    private final IBudgetAccountService budgetAccountService = SpringUtil.getContext().getBean(IBudgetAccountService.class);
    private final IAsyncBudgetDisassemblyService asyncBudgetDisassemblyService = SpringUtil.getContext().getBean(IAsyncBudgetDisassemblyService.class);
    private final RedissonClient redissonCmd = SpringUtil.getContext().getBean("redissonCmd", RedissonClient.class);

    // 新增的子预算表（拆解新建）
    private final List<IObjectData> newAccounts = Lists.newArrayList();
    // 现有的子预算表的操作类（拆解转出）
    private final Map<String, IObjectData> targetAccountsGroupById = Maps.newHashMap();
    // 拆解源预算表
    private IObjectData sourceAccount;
    // 实际拆解总金额，后台重算
    private BigDecimal disassemblyAmount;
    // 拆解源预算表操作类
    private IBudgetOperator sourceAccountOperator;
    // 预算类型
    private BudgetTypePO budgetType;
    // 拆解源节点
    private BudgetTypeNodeEntity sourceNode;
    // 拆解目标节点
    private BudgetTypeNodeEntity targetNode;

    // 针对一个节点的拆解操作锁
    private RLock lockBySourceNode;
    private RLock lockByTargetNode;


    private static final String LOCK_KEY = "FMCG:TPM:BUDGET_DISASSEMBLY_ADD_LOCK:%s.%s";

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        stopWatch.lap("framework.before");

        // 加载并校验相关数据
        this.loadAndValidateRelatedData();
        stopWatch.lap("loadAndValidateRelatedData");

        // 校验主对象数据
        this.validateMasterData();
        stopWatch.lap("validateMasterData");

        this.setNewDetailsValue();
        stopWatch.lap("setNewDetailsValue");

        this.correctDetailsValue();
        stopWatch.lap("correctDetailsValue");

        // 校验从对象数据
        this.validateDetailData();
        stopWatch.lap("validateDetailData");

        //校验预算拆解--新建预算表，是否可以新建
        this.validateNewDetailsData();
        stopWatch.lap("preValidateAccount");

        // 锁定当前节点的拆解操作，保证同一时间一个节点仅有一个拆解在执行
        this.tryLockDisassemblyBySourceNode();
        stopWatch.lap("tryLockDisassemblyBySourceNode");

        // 锁定当前节点的拆解操作，保证同一时间一个节点仅有一个拆解在执行
        this.tryLockDisassemblyByTargetNode();
        stopWatch.lap("tryLockDisassemblyByTargetNode");
    }

    @Override
    protected Result doAct(Arg arg) {
        return transProxy.call(() -> {
            Result inner = super.doAct(arg);
            stopWatch.lap("super.doAct");

            // 准备辅助 Trace
            String traceId = inner.getObjectData().getId().toUpperCase(Locale.ROOT);
            this.prepareTrace(traceId);
            stopWatch.lap("prepareTrace");

            // 初始化拆解源预算操作类，并加锁
            this.prepareSourceAccountOperator(traceId, inner.getObjectData().toObjectData());
            stopWatch.lap("prepareSourceAccountOperator");

            // 在预算拆解源表中，冻结本次要拆解的金额，需要和拆解对象的新建保持在一个事务那，宁可多冻结，不能超发
            this.freezeDisassemblyAmount();
            stopWatch.lap("freezeDisassemblyAmount");

            asyncBudgetDisassemblyService.markMasterDisassemblyInfo(actionContext.getTenantId(), inner.getObjectData().toObjectData(), TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__FROZEN);
            stopWatch.lap("markMasterDisassemblyInfo");

            return inner;
        });
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result inner = super.after(arg, result);
        stopWatch.lap("super.after");

        // 如果不触发审批流，则直接进行拆解
        if (!isApprovalFlowStartSuccess(inner.getObjectData().getId())) {
            IObjectData what = inner.getObjectData().toObjectData();
            ParallelUtils.createParallelTask().submit(() -> {
                asyncBudgetDisassemblyService.doDisassembly(actionContext.getUser(), what.getId(), DisassemblyActionCode.ADD.value());
            }).run();
            stopWatch.lap("doDisassembly");
        }
        BuryService.asyncBudgetLog(actionContext.getTenantId(), actionContext.getUser().getUserIdInt(), BuryModule.Budget.BUDGET_DISASSEMBLY, BuryOperation.CREATE);
        stopWatch.lap("BuryService.asyncBudgetLog");

        return inner;
    }

    @Override
    protected void finallyDo() {
        // 解锁操作类
        this.unlockOperators();
        stopWatch.lap("unlockOperators");

        // 解锁预算节点拆解操作
        this.unlockDisassemblyBySourceNode();
        stopWatch.lap("unlockDisassemblyBySourceNode");

        this.unlockDisassemblyByTargetNode();
        stopWatch.lap("unlockDisassemblyByTargetNode");

        super.finallyDo();
        stopWatch.lap("framework.finallyDo");
    }

    private void setNewDetailsValue() {
        IObjectData masterData = arg.getObjectData().toObjectData();
        String sourceBudgetType = masterData.get(TPMBudgetDisassemblyFields.BUDGET_TYPE_ID, String.class);
        String targetBudgetNodeId = masterData.get(TPMBudgetDisassemblyFields.TARGET_BUDGET_NODE_ID, String.class);
        List<ObjectDataDocument> newDetailsDocuments = arg.getDetails().get(ApiNames.TPM_BUDGET_DISASSEMBLY_NEW_DETAIL_OBJ);
        if (CollectionUtils.isEmpty(newDetailsDocuments)) {
            return;
        }
        for (ObjectDataDocument objectDataDocument : newDetailsDocuments) {
            objectDataDocument.put(TPMBudgetDisassemblyNewDetailsFields.BUDGET_TYPE_ID, sourceBudgetType);
            objectDataDocument.put(TPMBudgetDisassemblyNewDetailsFields.BUDGET_NODE_ID, targetBudgetNodeId);
        }
    }

    private void correctDetailsValue() {
        arg.getObjectData().put(TPMBudgetDisassemblyFields.AVAILABLE_AMOUNT, new BigDecimal(sourceAccount.get(TPMBudgetAccountFields.AVAILABLE_AMOUNT, String.class)));

        budgetDisassemblyService.correctNewDetailsValue(arg.getDetails().get(ApiNames.TPM_BUDGET_DISASSEMBLY_NEW_DETAIL_OBJ), targetNode);

        List<ObjectDataDocument> existsDetailsDocuments = arg.getDetails().get(ApiNames.TPM_BUDGET_DISASSEMBLY_EXISTS_DETAIL_OBJ);
        if (CollectionUtils.isNotEmpty(existsDetailsDocuments)) {
            for (ObjectDataDocument existsDetailsDocument : existsDetailsDocuments) {
                String targetAccountId = (String) existsDetailsDocument.get(TPMBudgetDisassemblyExistsDetailsFields.BUDGET_ACCOUNT_ID);
                IObjectData targetAccount = targetAccountsGroupById.computeIfAbsent(targetAccountId, accountId -> budgetDisassemblyService.findData(actionContext.getTenantId(), accountId, ApiNames.TPM_BUDGET_ACCOUNT));
                budgetDisassemblyService.correctExsitsDetailsValue(existsDetailsDocument, targetNode, targetAccount);
            }
        }
    }

    private void unlockOperators() {
        if (Objects.nonNull(this.sourceAccountOperator)) {
            this.sourceAccountOperator.unlock();
        }
    }

    // region # 私有方法

    private void unlockDisassemblyBySourceNode() {
        if (Objects.nonNull(this.lockBySourceNode) && this.lockBySourceNode.isLocked() && this.lockBySourceNode.isHeldByCurrentThread()) {
            this.lockBySourceNode.unlock();
        }
    }

    private void unlockDisassemblyByTargetNode() {
        if (Objects.nonNull(this.lockByTargetNode) && this.lockByTargetNode.isLocked() && this.lockByTargetNode.isHeldByCurrentThread()) {
            this.lockByTargetNode.unlock();
        }
    }

    private void prepareTrace(String traceId) {
        this.customCallbackData.put(TraceUtil.FMCG_TPM_BUDGET_APPROVAL_CALLBACK_TRACE_ID_KEY, traceId);
        this.customCallbackData.put(TraceUtil.FMCG_TPM_BUDGET_BUSINESS_CALLBACK_TRACE_ID_KEY, traceId);
    }

    private void loadAndValidateRelatedData() {
        IObjectData master = arg.getObjectData().toObjectData();

        // 加载预算类型信息
        String typeId = master.get(TPMBudgetDisassemblyFields.BUDGET_TYPE_ID, String.class);
        if (Strings.isNullOrEmpty(typeId)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_TYPE_ID_IS_NOT_EMPTY));
        }
        this.budgetType = budgetTypeDAO.get(actionContext.getTenantId(), typeId);
        if (Objects.isNull(this.budgetType)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_BUDGET_TYPE_NOT_FIND));
        }

        // 加载拆解节点信息
        String sourceNodeId = master.get(TPMBudgetDisassemblyFields.SOURCE_BUDGET_NODE_ID, String.class);
        this.sourceNode = this.budgetType.getNodes().stream().filter(f -> f.getNodeId().equals(sourceNodeId)).findFirst().orElse(null);
        if (Objects.isNull(this.sourceNode)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_TRANS_IN_BUDGET_NODE_NOT_FIND));
        }

        // 加载拆解到的节点信息
        String targetNodeId = master.get(TPMBudgetDisassemblyFields.TARGET_BUDGET_NODE_ID, String.class);
        this.targetNode = this.budgetType.getNodes().stream().filter(f -> f.getNodeId().equals(targetNodeId)).findFirst().orElse(null);
        if (Objects.isNull(this.targetNode)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_TRANS_OUT_BUDGET_NODE_NOT_FIND));
        }

        // 判断节点父子关系
        if (!Objects.equals(this.targetNode.getParentNodeId(), this.sourceNode.getNodeId())) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_TRANS_OUT_NODE_IS_UP));
        }
    }

    private void validateMasterData() {

        String sourceAccountId = (String) arg.getObjectData().get(TPMBudgetDisassemblyFields.SOURCE_BUDGET_ACCOUNT_ID);
        this.sourceAccount = serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), sourceAccountId, ApiNames.TPM_BUDGET_ACCOUNT);
        //校验金额是否大于0
        String availableAmount = sourceAccount.get(TPMBudgetAccountFields.AVAILABLE_AMOUNT, String.class);
        if (TPMGrayUtils.disassemblyAllowZero(actionContext.getTenantId())) {
            if (new BigDecimal(availableAmount).compareTo(BigDecimal.ZERO) < 0) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_TRANS_OUT_AMOUNT_IS_GTEO_ZERO));
            }
        } else {
            if (new BigDecimal(availableAmount).compareTo(BigDecimal.ZERO) <= 0) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_TRANS_OUT_AMOUNT_IS_GT_ZERO));
            }
        }
        // 校验入参预算类型与预算表类型是否匹配
        String budgetTypeId = this.sourceAccount.get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class);
        if (!this.budgetType.getId().toString().equals(budgetTypeId)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_TRANS_OUT_BUDGET_TYPE_NOT_MATCH));
        }

        // 校验入参预算节点与预算表节点是否匹配
        String budgetNodeId = this.sourceAccount.get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class);
        if (!this.sourceNode.getNodeId().equals(budgetNodeId)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_TRANS_OUT_BUDGET_NODE_NOT_MATCH));
        }
    }

    private boolean hasAnyOneMatchSourceAccount(String masterId, List<IObjectData> existsDetails) {
        return existsDetails.stream().anyMatch(newDetail -> Objects.equals(masterId, newDetail.getId()));
    }

    private void validateDetailData() {
        List<IObjectData> newDetails = null;
        List<IObjectData> existsDetails = null;

        if (CollectionUtils.isNotEmpty(arg.getDetails().get(ApiNames.TPM_BUDGET_DISASSEMBLY_NEW_DETAIL_OBJ))) {
            newDetails = arg.getDetails().get(ApiNames.TPM_BUDGET_DISASSEMBLY_NEW_DETAIL_OBJ).stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(arg.getDetails().get(ApiNames.TPM_BUDGET_DISASSEMBLY_EXISTS_DETAIL_OBJ))) {
            existsDetails = arg.getDetails().get(ApiNames.TPM_BUDGET_DISASSEMBLY_EXISTS_DETAIL_OBJ).stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList());
        }

        // 至少有一个从对象
        if (CollectionUtils.isEmpty(newDetails) && CollectionUtils.isEmpty(existsDetails)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_TRANS_OUT_OR_IN_LEAST_ONE_EXIST));
        }

        this.disassemblyAmount = new BigDecimal("0");

        if (CollectionUtils.isNotEmpty(existsDetails)) {
            if (hasAnyOneMatchSourceAccount(sourceAccount.getId(), existsDetails)) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_TRANS_OUT_OR_IN_IS_EQUALS));
            }

            budgetDisassemblyService.existsDetailDuplicateValidate(existsDetails);

            List<String> accountIds = existsDetails.stream().map(m -> m.get(TPMBudgetDisassemblyExistsDetailsFields.BUDGET_ACCOUNT_ID, String.class)).collect(Collectors.toList());
            List<IObjectData> accounts = serviceFacade.findObjectDataByIdsIgnoreAll(actionContext.getTenantId(), accountIds, ApiNames.TPM_BUDGET_ACCOUNT);

            // 对已有的预算表只简单校验下是否是拆解源预算表的子预算表，以及类型，模版，节点信息是否相等即可
            for (IObjectData account : accounts) {
                if (!Objects.equals(account.get(TPMBudgetAccountFields.PARENT_ID, String.class), this.sourceAccount.getId())) {
                    throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_IS_NOT_SUB_BUDGET), account.getName(), this.sourceAccount.getName()));
                }
                if (!Objects.equals(account.get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class), this.budgetType.getId().toString())) {
                    throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_TYPE_VALIDATION_ERROR), account.getName()));
                }
                if (!Objects.equals(account.get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class), this.targetNode.getNodeId())) {
                    throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_NODE_VALIDATION_ERROR), account.getName()));
                }
            }

            for (IObjectData existsDetail : existsDetails) {
                String accountId = existsDetail.get(TPMBudgetDisassemblyExistsDetailsFields.BUDGET_ACCOUNT_ID, String.class);
                if (StringUtils.isEmpty(accountId)) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_TRANS_IN_IS_NOT_EMPTY));
                }
                BigDecimal cur = new BigDecimal(existsDetail.get(TPMBudgetDisassemblyNewDetailsFields.AMOUNT, String.class));
                if (CurrencyUtils.checkDecimalMoreThanMaxDecimalPlaces(cur, 2)) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_OPERATOR_AMOUNT_TOW_BOUND));
                }
                if (TPMGrayUtils.disassemblyAllowZero(actionContext.getTenantId())) {
                    if (cur.compareTo(BigDecimal.ZERO) < 0) {
                        throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_OPERATOR_AMOUNT_GTEO_ZERO));
                    }
                } else {
                    if (cur.compareTo(BigDecimal.ZERO) <= 0) {
                        throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_OPERATOR_AMOUNT_GT_ZERO));
                    }
                }
                this.disassemblyAmount = this.disassemblyAmount.add(cur);
            }

        }

        if (CollectionUtils.isNotEmpty(newDetails)) {

            budgetDisassemblyService.newDetailDuplicateValidateV2(actionContext.getTenantId(), newDetails, targetNode);

            // 对于新增的预算表，要严格校验所有信息，包括父子关系，以及维度关系，例如不能拆倒非我子部门的部门预算表中去，不能拆倒非我子科目的预算表中去
            for (IObjectData newDetail : newDetails) {
                newDetail.setOwner(Lists.newArrayList(actionContext.getUser().getUserId()));
                IObjectData newAccount = convertToNewAccount(newDetail);
                budgetCompareService.childAccountValidate(actionContext.getTenantId(), sourceNode, this.sourceAccount, targetNode, newAccount);
                this.newAccounts.add(newAccount);
            }

            for (IObjectData newDetail : newDetails) {
                BigDecimal cur = new BigDecimal(newDetail.get(TPMBudgetDisassemblyNewDetailsFields.AMOUNT, String.class));
                if (CurrencyUtils.checkDecimalMoreThanMaxDecimalPlaces(cur, 2)) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_OPERATOR_AMOUNT_TOW_BOUND));
                }
                if (TPMGrayUtils.disassemblyAllowZero(actionContext.getTenantId())) {
                    if (cur.compareTo(BigDecimal.ZERO) < 0) {
                        throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_OPERATOR_AMOUNT_GTEO_ZERO));
                    }
                } else {
                    if (cur.compareTo(BigDecimal.ZERO) <= 0) {
                        throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_OPERATOR_AMOUNT_GT_ZERO));
                    }
                }
                this.disassemblyAmount = this.disassemblyAmount.add(cur);
            }
        }

        arg.getObjectData().put(TPMBudgetDisassemblyFields.DISASSEMBLY_AMOUNT, this.disassemblyAmount.toString());
    }


    private void validateNewDetailsData() {
        if (CollectionUtils.isEmpty(this.newAccounts)) {
            return;
        }
        for (IObjectData newAccount : this.newAccounts) {
            budgetAccountService.preValidateAccount(User.systemUser(actionContext.getTenantId()), newAccount, true);
        }
    }

    private void tryLockDisassemblyBySourceNode() {
        String key = String.format(LOCK_KEY, actionContext.getTenantId(), arg.getObjectData().get(TPMBudgetDisassemblyFields.SOURCE_BUDGET_NODE_ID));
        this.lockBySourceNode = redissonCmd.getLock(key);

        try {
            if (!this.lockBySourceNode.tryLock(5L, 60L, TimeUnit.SECONDS)) {
                throw new ValidateException("System error, Please try again later.");
            }
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new ValidateException("Try lock disassembly by source node request cause 'InterruptedException' please try again later.");
        }
    }

    private void tryLockDisassemblyByTargetNode() {
        String key = String.format(LOCK_KEY, actionContext.getTenantId(), arg.getObjectData().get(TPMBudgetDisassemblyFields.TARGET_BUDGET_NODE_ID));
        this.lockByTargetNode = redissonCmd.getLock(key);
        try {
            if (!this.lockByTargetNode.tryLock(5L, 60L, TimeUnit.SECONDS)) {
                throw new ValidateException("System error, Please try again later.");
            }
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new ValidateException("Try lock disassembly request cause 'InterruptedException' please try again later.");
        }
    }

    private void prepareSourceAccountOperator(String traceId, IObjectData what) {
        this.sourceAccountOperator = BudgetOperatorFactory.initOperator(BizType.TAKE_APART_OUT, actionContext.getUser(), this.sourceAccount.getId(), traceId, traceId);
        if (this.sourceAccountOperator.tryLock()) {
            this.sourceAccountOperator.validateOperableAmount(this.disassemblyAmount);
        } else {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_TRANS_OUT_GET_LOCK_ERROR));
        }
        this.sourceAccountOperator.setWhat(what);
    }

    /**
     * 将拆解明细转换为预算表
     *
     * @param newDetail 拆解明细
     * @return 预算表
     */
    private IObjectData convertToNewAccount(IObjectData newDetail) {
        return budgetDisassemblyService.buildDataForCreateBudgetAccount(actionContext.getTenantId(), newDetail, sourceAccount, targetNode);
    }

    private void freezeDisassemblyAmount() {
        this.sourceAccountOperator.freeze(this.disassemblyAmount);
        this.sourceAccountOperator.recalculate();
    }

    // endregion
}