package com.facishare.crm.fmcg.tpm.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.service.abstraction.TPMEnterpriseService;
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseArg;
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseResult;
import com.facishare.uc.api.model.fscore.SimpleEnterprise;
import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
import com.facishare.uc.api.model.fscore.result.GetSimpleEnterpriseResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * author: wuyx
 * description:
 * createTime: 2022/8/30 15:45
 */
@Service
public class TPMEnterpriseServiceImpl implements TPMEnterpriseService {
    public static final Logger log = LoggerFactory.getLogger(TPMEnterpriseServiceImpl.class);

    @Resource
    private EnterpriseEditionService enterpriseEditionService;

    @Override
    public boolean isActiveEnterprise(Integer tenantId) {
        try {
            GetSimpleEnterpriseArg arg = new GetSimpleEnterpriseArg();
            arg.setEnterpriseId(tenantId);
            GetSimpleEnterpriseResult result = enterpriseEditionService.getSimpleEnterprise(arg);
            return result.getSimpleEnterprise() != null && result.getSimpleEnterprise().getRunStatus() == 2;
        } catch (Exception e) {
            log.info("get tenant info err.{}", tenantId, e);
            return false;
        }
    }

    @Override
    public Map<String, Boolean> batchIsActiveEnterprise(List<Integer> tenantIds) {
        Map<String, Boolean> tenantIdIsActiveMap = Maps.newHashMap();
        try {
            BatchGetSimpleEnterpriseArg arg = new BatchGetSimpleEnterpriseArg();
            arg.setEnterpriseIds(tenantIds);
            BatchGetSimpleEnterpriseResult result = enterpriseEditionService.batchGetSimpleEnterprise(arg);
            List<SimpleEnterprise> simpleEnterpriseList = result.getSimpleEnterpriseList();
            if (CollectionUtils.isNotEmpty(simpleEnterpriseList)) {
                tenantIdIsActiveMap = simpleEnterpriseList.stream()
                        .collect(Collectors.toMap(re -> String.valueOf(re.getEnterpriseId()), re -> re.getRunStatus() == SimpleEnterprise.RUN_STATUS_NORMAL));
            }
        } catch (Exception e) {
            log.info("batch getSimpleEnterprise err: tenantIds={}", JSON.toJSONString(tenantIds), e);
        }
        return tenantIdIsActiveMap;
    }
}
