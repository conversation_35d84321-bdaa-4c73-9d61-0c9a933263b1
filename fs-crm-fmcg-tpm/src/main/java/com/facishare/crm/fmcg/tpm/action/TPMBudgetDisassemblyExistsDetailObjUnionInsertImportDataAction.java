package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetDisassemblyFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetDisassemblyNewDetailsFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetDisassemblyService;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


@Slf4j
public class TPMBudgetDisassemblyExistsDetailObjUnionInsertImportDataAction extends StandardUnionInsertImportDataAction {

    private final IBudgetDisassemblyService budgetDisassemblyService = SpringUtil.getContext().getBean(IBudgetDisassemblyService.class);

    private final Map<String, IObjectData> masterDataMap = Maps.newHashMap();

    private final Map<String, List<IObjectData>> existsDetailsGroupByMasterId = Maps.newHashMap();
    private final Map<IObjectData, List<IObjectData>> ARG = Maps.newHashMap();
    private static final String PK_ID = "_PK_ID";

    @Override
    protected void before(Arg arg) {
        log.info("import TPMBudgetDisassemblyExistsDetail arg:{}", arg);
        super.before(arg);
        this.dataList.forEach(data -> {
            data.getData().set(PK_ID, data.getRowNo());
        });
    }

    @Override
    protected void convertFields(List<ImportData> dataList) {
        super.convertFields(dataList);
        List<ImportError> errorList = Lists.newArrayList();
        Set<String> exceptionMasterIds = Sets.newHashSet();

        List<String> masterDataIds = dataList.stream().map(v -> v.getData().get(TPMBudgetDisassemblyNewDetailsFields.BUDGET_DISASSEMBLY_ID, String.class)).distinct().collect(Collectors.toList());
        for (ImportData obj : dataList) {
            String masterDataId = obj.getData().get(TPMBudgetDisassemblyNewDetailsFields.BUDGET_DISASSEMBLY_ID, String.class);
            IObjectData masterData = masterDataMap.get(masterDataId);
            if (masterData == null) {
                IObjectData masterDataFromDb = budgetDisassemblyService.findData(actionContext.getTenantId(), masterDataId, ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ);
                if (masterDataFromDb == null) {
                    exceptionMasterIds.add(masterDataId);
                    ImportError error = new ImportError();
                    error.setRowNo(obj.getRowNo());
                    error.setErrorMessage(I18N.text(I18NKeys.ERRORMESSAGE_BUDGET_DISASSEMBLY_EXISTS_DETAIL_OBJ_UNION_INSERT_IMPORT_DATA_ACTION_0));
                    errorList.add(error);
                    continue;
                }
                masterDataMap.put(masterDataId, masterDataFromDb);
            }

            if (existsDetailsGroupByMasterId.get(masterDataId) == null) {
                existsDetailsGroupByMasterId.put(masterDataId, Lists.newArrayList(obj.getData()));
            } else {
                existsDetailsGroupByMasterId.get(masterDataId).add(obj.getData());
            }
        }

        for (String masterDataId : masterDataIds) {
            if (exceptionMasterIds.contains(masterDataId)) {
                continue;
            }
            ARG.put(masterDataMap.get(masterDataId), existsDetailsGroupByMasterId.get(masterDataId));
        }

        mergeErrorList(errorList);
    }

    @Override
    protected List<IObjectData> importData(List<IObjectData> validList) {
        log.info("import data custom:{}", JSON.toJSONString(validList));


        for (Map.Entry<IObjectData, List<IObjectData>> arg : ARG.entrySet()) {
            IObjectData masterData = arg.getKey();
            List<IObjectData> existsDetailData = arg.getValue();
            BigDecimal disassemblyAmount = new BigDecimal("0");

            for (IObjectData existsDetailDatum : existsDetailData) {


                BigDecimal cur = new BigDecimal(existsDetailDatum.get(TPMBudgetDisassemblyNewDetailsFields.AMOUNT, String.class));
                disassemblyAmount = disassemblyAmount.add(cur);
            }

            masterData.set(TPMBudgetDisassemblyFields.DISASSEMBLY_AMOUNT, disassemblyAmount);
        }

        List<IObjectData> validateData = new ArrayList<>();
        List<ImportError> errorList = new ArrayList<>();
        for (Map.Entry<IObjectData, List<IObjectData>> actionArg : ARG.entrySet()) {
            List<Integer> rowList = actionArg.getValue().stream().map(v -> v.get(PK_ID, Integer.class)).collect(Collectors.toList());

            ActionContext addActionContext = new ActionContext(actionContext.getRequestContext(), ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ, "Edit");
            addActionContext.setAttribute("triggerWorkflow", arg.getIsWorkFlowEnabled());
            addActionContext.setAttribute("triggerFlow", arg.getIsApprovalFlowEnabled());
            addActionContext.setAttribute("isFromImport", "true");
            BaseObjectSaveAction.Arg saveArg = new BaseObjectSaveAction.Arg();
            saveArg.setObjectData(ObjectDataDocument.of(actionArg.getKey()));
            Map<String, List<ObjectDataDocument>> details = Maps.newHashMap();

            details.put(ApiNames.TPM_BUDGET_DISASSEMBLY_EXISTS_DETAIL_OBJ, actionArg.getValue().stream().map(ObjectDataDocument::of).collect(Collectors.toList()));
            saveArg.setDetails(details);
            try {
                BaseObjectSaveAction.Result result = serviceFacade.triggerAction(addActionContext, saveArg, BaseObjectSaveAction.Result.class);
                validateData.add(result.getObjectData().toObjectData());

            } catch (Exception ex) {
                log.error("TPMBudgetDisassemblyExistsDetailObjUnionInsertImportDataAction trigger TPMBudgetDisassemblyObjEditAction throws exception.", ex);
                for (Integer row : rowList) {
                    ImportError error = new ImportError();
                    log.info("row:{}", row);
                    error.setRowNo(row);
                    error.setErrorMessage(ex.getMessage());
                    errorList.add(error);
                }

            }
        }

        mergeErrorList(errorList);
        actionContext.setAttribute("triggerWorkflow", false);
        actionContext.setAttribute("triggerFlow", false);
        return validateData;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        return super.after(arg, result);
    }


}
