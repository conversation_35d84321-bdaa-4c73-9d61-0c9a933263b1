package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.tpm.api.common.Range;
import com.facishare.crm.fmcg.tpm.business.abstraction.IProofService;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofFrequencyConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.LimitSpanEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ProofFrequencyType;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.*;

/**
 * <AUTHOR>
 * @date 2022/1/14 下午4:08
 */
//IgnoreI18nFile
@Component
@SuppressWarnings("Duplicates")
public class ProofService implements IProofService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProofService.class);

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private ActivityTypeManager activityTypeManager;


    @Override
    public void validateProofTimeAndFrequent(String tenantId, String storeId, IObjectData activity, IObjectData agreement, int tipMode) {

        String activityType = (String) activity.get(TPMActivityFields.ACTIVITY_TYPE);
        if (Strings.isNullOrEmpty(activityType)) {
            LOGGER.warn("meet a no activity type activity:{}", activity);
            return;
        }
        ActivityTypeExt activityTypeExt = activityTypeManager.find(tenantId, activityType);
        ActivityProofFrequencyConfigEntity frequencyConfigEntity = activityTypeExt.proofFrequencyConfig();
        if (frequencyConfigEntity == null) {
            LOGGER.info("activity type({}) has no frequent config.", activityType);
            return;
        }
        long now = System.currentTimeMillis();
        Range<Long> activityTimeRange = new Range<>(activity.get(TPMActivityFields.BEGIN_DATE, Long.class), activity.get(TPMActivityFields.END_DATE, Long.class));
        Range<Long> queryTimeRange;
        if (agreement != null) {
            queryTimeRange = new Range<>(agreement.get(TPMActivityAgreementFields.BEGIN_DATE, Long.class), agreement.get(TPMActivityAgreementFields.END_DATE, Long.class));
        } else {
            queryTimeRange = new Range<>(activityTimeRange);
        }

        int timesLimit = Integer.parseInt(frequencyConfigEntity.getFrequencyLimit());

        if (queryTimeRange.getFrom() > now || queryTimeRange.getTo() < now) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.PROOF_FREQUENT_VALIDATE_TIME_OUT_OF_RANGE), activity.getName()));
        }

        boolean isMatch = false;
        if (!CollectionUtils.isEmpty(frequencyConfigEntity.getLimitDays())) {
            int nowDay = LocalDate.now().getDayOfMonth();
            for (LimitSpanEntity dayLimit : frequencyConfigEntity.getLimitDays()) {
                if (Integer.parseInt(dayLimit.getFrom()) <= nowDay && Integer.parseInt(dayLimit.getTo()) >= nowDay) {
                    isMatch = true;
                    break;
                }
            }
            if (!isMatch && tipMode == 1) {
                StringBuilder dateSb = new StringBuilder();
                frequencyConfigEntity.getLimitDays().forEach(day -> dateSb.append(day.getFrom()).append("日").append("-").append(day.getTo()).append("日").append("，"));
                throw new ValidateException(String.format(I18N.text(I18NKeys.PROOF_DATE_VALIDATE), dateSb.substring(0, dateSb.length() - 1)));
            }
        } else {
            isMatch = true;
        }

        if (isMatch) {
            if (!CollectionUtils.isEmpty(frequencyConfigEntity.getLimitHours())) {
                isMatch = false;
                LocalTime localNowTime = LocalTime.now().withSecond(0).withNano(0);
                for (LimitSpanEntity timeEntity : frequencyConfigEntity.getLimitHours()) {
                    LocalTime fromTime = LocalTime.of(Integer.parseInt(timeEntity.getFrom().substring(0, 2)), Integer.parseInt(timeEntity.getFrom().substring(2)));
                    LocalTime toTime = LocalTime.of(Integer.parseInt(timeEntity.getTo().substring(0, 2)), Integer.parseInt(timeEntity.getTo().substring(2)));
                    if (localNowTime.compareTo(toTime) <= 0 && localNowTime.compareTo(fromTime) >= 0) {
                        isMatch = true;
                        break;
                    }
                }
                if (!isMatch && tipMode == 1) {
                    StringBuilder timeSb = new StringBuilder();
                    frequencyConfigEntity.getLimitHours().forEach(time -> timeSb.append(convertTimeFormat(time.getFrom())).append("-").append(convertTimeFormat(time.getTo())).append("，"));
                    throw new ValidateException(String.format(I18N.text(I18NKeys.PROOF_TIME_VALIDATE), timeSb.substring(0, timeSb.length() - 1)));
                }
            } else {
                isMatch = true;
            }
        }

        if (!isMatch && tipMode == 2) {
            StringBuilder dateTimeSb = new StringBuilder();
            if (!CollectionUtils.isEmpty(frequencyConfigEntity.getLimitDays())) {
                dateTimeSb.append("的");
                frequencyConfigEntity.getLimitDays().forEach(day -> dateTimeSb.append(day.getFrom()).append("日").append("至").append(day.getTo()).append("日").append("、"));
                dateTimeSb.deleteCharAt(dateTimeSb.length() - 1);
            }
            if (!CollectionUtils.isEmpty(frequencyConfigEntity.getLimitHours())) {
                dateTimeSb.append("的");
                frequencyConfigEntity.getLimitHours().forEach(time -> dateTimeSb.append(convertTimeFormat(time.getFrom())).append("至").append(convertTimeFormat(time.getTo())).append("、"));
                dateTimeSb.deleteCharAt(dateTimeSb.length() - 1);
            }
            throw new ValidateException(String.format(I18N.text(I18NKeys.PROOF_DATE_TIME_VALIDATE), dateTimeSb.toString()));
        }

        if (timesLimit == 0) {
            return;
        }
        ZoneOffset east8 = ZoneOffset.of("+8");

        String frequencyType = frequencyConfigEntity.getFrequencyType();
        long start = -1, end = -1;
        String circleType;
        LocalDate localNowTime = LocalDate.now();
        switch (ProofFrequencyType.of(frequencyType)) {
            case DAY:
                start = localNowTime.atStartOfDay().toInstant(east8).toEpochMilli();
                end = localNowTime.plusDays(1).atStartOfDay().toInstant(east8).toEpochMilli();
                circleType = ProofFrequencyType.DAY.i18nKey() == null ? ProofFrequencyType.DAY.label() : I18N.text(ProofFrequencyType.DAY.i18nKey());
                break;
            case WEEK:
                LocalDate baseWeek = localNowTime.with(DayOfWeek.MONDAY);
                start = baseWeek.minusWeeks(1).atStartOfDay().toInstant(east8).toEpochMilli();
                end = baseWeek.plusWeeks(1).atStartOfDay().toInstant(east8).toEpochMilli();
                circleType = ProofFrequencyType.WEEK.i18nKey() == null ? ProofFrequencyType.WEEK.label() : I18N.text(ProofFrequencyType.WEEK.i18nKey());
                break;
            case MONTH:
                LocalDate baseMonth = localNowTime.withDayOfMonth(1);
                start = baseMonth.atStartOfDay().toInstant(east8).toEpochMilli();
                end = baseMonth.plusMonths(1).atStartOfDay().toInstant(east8).toEpochMilli();
                circleType = ProofFrequencyType.MONTH.i18nKey() == null ? ProofFrequencyType.MONTH.label() : I18N.text(ProofFrequencyType.MONTH.i18nKey());
                break;
            case TWO_MONTH:
                LocalDate activityStartDate = LocalDateTime.ofEpochSecond(activityTimeRange.getFrom() / 1000, 0, east8).toLocalDate();
                int activityMonth = activityStartDate.getYear() * 12 + activityStartDate.getMonthValue();
                int nowMonth = localNowTime.getYear() * 12 + localNowTime.getMonthValue();
                int absMonth = Math.abs(nowMonth - activityMonth) / 2 * 2;
                LocalDate base2Month = activityStartDate.plusMonths(absMonth).withDayOfMonth(1);
                start = base2Month.atStartOfDay().toInstant(east8).toEpochMilli();
                end = base2Month.plusMonths(2).atStartOfDay().toInstant(east8).toEpochMilli();
                circleType = ProofFrequencyType.TWO_MONTH.i18nKey() == null ? ProofFrequencyType.TWO_MONTH.label() : I18N.text(ProofFrequencyType.TWO_MONTH.i18nKey());
                break;
            case ACTIVITY:
            default:
                circleType = ProofFrequencyType.ACTIVITY.i18nKey() == null ? ProofFrequencyType.ACTIVITY.label() : I18N.text(ProofFrequencyType.ACTIVITY.i18nKey());
        }
        if (start != -1)
            queryTimeRange.setFrom(Math.max(queryTimeRange.getFrom(), start));
        if (end != -1)
            queryTimeRange.setTo(Math.min(queryTimeRange.getTo(), end));

        int count = countProof(tenantId, storeId, activity.getId(), queryTimeRange.getFrom(), queryTimeRange.getTo()).intValue();

        if (count >= timesLimit) {
            if (tipMode == 1) {
                throw new ValidateException(String.format(I18N.text(I18NKeys.PROOF_FREQUENT_VALIDATE), circleType, count));
            } else {
                throw new ValidateException(String.format(I18N.text(I18NKeys.PROOF_FREQUENT_VALIDATE_MODE2), circleType, count));
            }
        }
    }


    private BigDecimal countProof(String tenantId, String storeId, String activityId, long begin, long end) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnCountNum(true);
        query.setFindExplicitTotalNum(true);

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityProofFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter createTimeFilter = new Filter();
        createTimeFilter.setFieldName(CommonFields.CREATE_TIME);
        createTimeFilter.setOperator(Operator.BETWEEN);
        createTimeFilter.setFieldValues(Lists.newArrayList(String.valueOf(begin), String.valueOf(end)));

        query.setFilters(Lists.newArrayList(activityFilter, createTimeFilter));


        if (!Strings.isNullOrEmpty(storeId)) {
            Filter storeFilter = new Filter();
            storeFilter.setFieldName(TPMActivityProofFields.STORE_ID);
            storeFilter.setOperator(Operator.EQ);
            storeFilter.setFieldValues(Lists.newArrayList(storeId));
        }

        return BigDecimal.valueOf(serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query).getTotalNumber());
    }

    private String convertTimeFormat(String sourceStr) {
        return sourceStr.substring(0, 2) + ':' + sourceStr.substring(2);
    }
}
