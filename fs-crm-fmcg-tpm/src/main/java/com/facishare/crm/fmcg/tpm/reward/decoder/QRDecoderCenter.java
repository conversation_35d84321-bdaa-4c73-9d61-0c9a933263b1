package com.facishare.crm.fmcg.tpm.reward.decoder;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.fmcg.tpm.reward.abstraction.QRCodeDecoder;
import com.facishare.crm.fmcg.tpm.reward.annotation.Name;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import org.apache.commons.collections4.MapUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Author: linmj
 * Date: 2024/4/23 17:05
 */

public class QRDecoderCenter {

    public static final Map<String, QRCodeDecoder> DECODER_MAP = new HashMap<>();

    public static Map<String, String> TENANT_CODE_TO_DECODER_MAP = new HashMap<>();

    static {
        ConfigFactory.getConfig("fs-fmcg-tpm-config").addListener(config -> {
            String tenantCodeMap = config.get("TENANT_CODE_TO_DECODER_MAP");
            if (!Strings.isNullOrEmpty(tenantCodeMap)) {
                JSONObject map = JSONObject.parseObject(tenantCodeMap);
                TENANT_CODE_TO_DECODER_MAP = map.toJavaObject(new TypeReference<Map<String, String>>() {
                });
            }
        });
    }

    public static QRCodeDecoder getDecoderByName(String name) {
        if (MapUtils.isEmpty(DECODER_MAP)) {
            synchronized (DECODER_MAP) {
                if (MapUtils.isEmpty(DECODER_MAP)) {
                    fillDecodeMap();
                }
            }
        }

        return DECODER_MAP.getOrDefault(name, SpringUtil.getContext().getBean(MengNiuQRCodeDecoder.class));
    }

    private static void fillDecodeMap() {
        Map<String, QRCodeDecoder> innerMap = SpringUtil.getContext().getBeansOfType(QRCodeDecoder.class);
        if (MapUtils.isNotEmpty(innerMap)) {
            innerMap.values().forEach(decoder -> {
                Name nameAnnotation = decoder.getClass().getAnnotation(Name.class);
                if (nameAnnotation != null) {
                    DECODER_MAP.put(nameAnnotation.name(), decoder);
                }
            });
        }
    }


    public static QRCodeDecoder getDecoder(String tenantCode) {
        return getDecoderByName(TENANT_CODE_TO_DECODER_MAP.get(tenantCode));
    }
}
