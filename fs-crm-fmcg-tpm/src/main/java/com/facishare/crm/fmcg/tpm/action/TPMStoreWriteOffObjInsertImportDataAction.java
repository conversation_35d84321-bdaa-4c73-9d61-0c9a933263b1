package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;


@Slf4j
public class TPMStoreWriteOffObjInsertImportDataAction extends StandardInsertImportDataAction {

    @Override
    protected void before(Arg arg) {
        log.info("TPMStoreWriteOffObjInsertImportDataAction start ...");
        super.before(arg);
    }

    @Override
    protected void convertFields(List<ImportData> dataList) {
        super.convertFields(dataList);
        log.info("data size:{}", dataList.size());
        List<ImportError> errorList = new ArrayList<>();

        dataList.forEach(data -> {
            ImportError error = new ImportError();
            error.setRowNo(data.getRowNo());
            error.setErrorMessage(I18N.text(I18NKeys.ERRORMESSAGE_STORE_WRITE_OFF_OBJ_INSERT_IMPORT_DATA_ACTION_0));
            errorList.add(error);
        });
        mergeErrorList(errorList);
    }
}
