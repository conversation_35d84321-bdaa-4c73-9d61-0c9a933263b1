package com.facishare.crm.fmcg.tpm.common.constant;

import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/7/11 上午11:56
 */
public interface BudgetAccountConstants {

    DefaultComponentFieldSection BUDGET_EFFECTIVE_DIMENSION_SECTION = new DefaultComponentFieldSection("budget_effective_dimension_field_section__c", I18NKeys.FMCG_BUDGET_EFFECTIVE_DIMENSION_SECTION, true, 2, "ltr");

    DefaultComponentFieldSection BUDGET_PERIOD_SECTION = new DefaultComponentFieldSection("budget_period_field_section__c", I18NKeys.FMCG_BUDGET_PERIOD_SECTION, true, 2, "ltr");

    @Data
    @ToString
    @AllArgsConstructor
    class DefaultComponentFieldSection {

        public String apiName;

        public String header;

        public Boolean showHeader;

        public Integer column;

        public String tabIndex;
    }
}