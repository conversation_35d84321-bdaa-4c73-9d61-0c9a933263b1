package com.facishare.crm.fmcg.tpm.business.enums;

import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * AI检测能力配置枚举
 * 用于处理detectCapabilityMap中的键值对
 */
@Getter
@Slf4j
public enum DetectCapabilityEnum {

    /**
     * 商品sku/排面数识别
     */
    IS_OPEN_PRODUCT_ROW_NUMBER("isOpenProductRowNumber", "formData"),

    /**
     * 商品陈列组数
     */
    IS_OPEN_GROUP_NUMBER("isOpenGroupNumber", ""),

    /**
     * 货架层数识别
     */
    IS_OPEN_LAYER_NUMBER("isOpenLayerNumber", ""),

    /**
     * 商品单位识别
     */
    OPEN_SKU_UNIT("openSkuUnit", ""),

    /**
     * 价格识别
     */
    IS_OPEN_PRICES("isOpenPrices", ""),

    /**
     * 商品陈列场景
     */
    IS_OPEN_SCENE_DETECT("isOpenSceneDetect", ""),

    /**
     * 商品陈列形式
     */
    IS_OPEN_DISPLAY_FORM("isOpenDisplayForm", ""),

    /**
     * 物料识别
     */
    IS_POSM_DETECT("isPOSMDetect", "posm");

    private final String key;

    private final String detectResultKey;

    DetectCapabilityEnum(String key, String detectResultKey) {

        this.key = key;
        this.detectResultKey = detectResultKey;
    }

    /**
     * 根据key获取枚举
     *
     * @param key 配置键名
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static DetectCapabilityEnum fromKey(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }

        for (DetectCapabilityEnum capability : values()) {
            if (capability.key.equals(key)) {
                return capability;
            }
        }

        return null;
    }

    /**
     * 获取TPM活动类型对应的AI检测能力配置
     *
     * @return 对应的AI检测能力配置
     */
    public static List<String> getTPMDetectCapabilityList() {
        return Lists.newArrayList(
                IS_OPEN_PRODUCT_ROW_NUMBER.key
                , IS_OPEN_DISPLAY_FORM.key
                , IS_POSM_DETECT.key
        );
    }

} 