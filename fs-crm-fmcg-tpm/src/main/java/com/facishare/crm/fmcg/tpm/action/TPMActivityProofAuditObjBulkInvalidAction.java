package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofAuditFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofFields;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofAuditSourceConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/8/4 下午3:35
 */
@Slf4j
public class TPMActivityProofAuditObjBulkInvalidAction extends StandardBulkInvalidAction {


    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);

    @Override
    protected void before(Arg arg) {

        List<IObjectData> audits = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), arg.getDataIds(), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);
        List<String> names = Lists.newArrayList();
        audits.forEach(audit -> {
            String costId = audit.get(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID, String.class);

            if (!Strings.isNullOrEmpty(costId)) {
                names.add(audit.getName());
            }
        });
        if(!CollectionUtils.isEmpty(names)){
            throw new ValidateException(I18N.text(I18NKeys.AUDIT_INVALID_FAIL_DUE_TO_RELATED_BY_COST) +names);
        }
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result result1 = super.after(arg, result);
        Set<ObjectDataDocument> all = new HashSet<>(result1.getObjectDataList());
        all.removeAll(result1.getFailureObjectDataList());
        all.forEach(audit->{

            ActivityTypeExt activityTypeExt = activityTypeManager.findByActivityId(actionContext.getTenantId(),(String) audit.get(TPMActivityProofAuditFields.ACTIVITY_ID));

            ActivityProofAuditSourceConfigEntity sourceConfigEntity = activityTypeExt.auditSourceConfig();
            if(ApiNames.TPM_ACTIVITY_PROOF_OBJ.equals(sourceConfigEntity.getMasterApiName())){
                String proofId = (String) audit.get(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID);
                IObjectData proof = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), proofId, ApiNames.TPM_ACTIVITY_PROOF_OBJ);
                Map<String, Object> updateMap = new HashMap<>();
                updateMap.put(TPMActivityProofFields.AUDIT_STATUS, "schedule");
                updateMap.put(TPMActivityProofFields.RANDOM_AUDIT_STATUS,null);
                serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), proof, updateMap);
            }else {
                if(!Strings.isNullOrEmpty(sourceConfigEntity.getAuditStatusApiName())){
                    String auditedId = (String) audit.get(sourceConfigEntity.getReferenceAuditSourceFieldApiName());
                    Map<String,Object> updateMap = new HashMap<>(2);
                    IObjectData auditedObject = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()),auditedId,sourceConfigEntity.getMasterApiName());
                    updateMap.put(sourceConfigEntity.getAuditStatusApiName(),"schedule");
                    serviceFacade.updateWithMap (User.systemUser(actionContext.getTenantId()), auditedObject,updateMap);
                }else {
                    log.info("activity type {} is no audit status field",activityTypeExt.get().getId().toString());
                }
            }

        });
        return result1;
    }
}
