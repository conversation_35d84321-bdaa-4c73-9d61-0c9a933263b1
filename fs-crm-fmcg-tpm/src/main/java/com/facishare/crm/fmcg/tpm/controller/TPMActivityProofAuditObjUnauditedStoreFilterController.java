package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.tpm.api.proof.UnauditedStoreFilter;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofFields;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofAuditSourceConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.AuditModeType;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.ToString;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/2/20 4:12 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityProofAuditObjUnauditedStoreFilterController extends PreDefineController<UnauditedStoreFilter.Arg, UnauditedStoreFilter.Result> {


    private ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);


    @SneakyThrows
    @Override
    protected UnauditedStoreFilter.Result doService(UnauditedStoreFilter.Arg arg) {
        log.info("unaudited store filter arg : {}", JSON.toJSONString(arg));

        UnauditedStoreFilter.Result result = new UnauditedStoreFilter.Result();

        List<String> storeIds = getStoreList(arg.getActivityId(), arg.getDealerId(), arg.getBegin(), arg.getEnd(), arg.getIdList());


        Map<String, IObjectData> storeMap = queryStoreMap(controllerContext, storeIds);

        for (Map.Entry<String, IObjectData> storeEntry : storeMap.entrySet()) {
            UnauditedStoreFilter.UnauditedStoreVO datum = new UnauditedStoreFilter.UnauditedStoreVO();
            String storeId = storeEntry.getKey();
            datum.setStoreData(ObjectDataExt.of(storeMap.get(storeId)).toMap());
            result.getData().put(storeEntry.getKey(), datum);
        }
        //埋点
        if ((arg.getBegin() != null && arg.getBegin() > 0) || (arg.getEnd() != null && arg.getEnd() > 0)) {
            BuryService.asyncTpmLog(Integer.valueOf(controllerContext.getTenantId()), Integer.parseInt(controllerContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_ACTIVITY_PROOF_AUDIT, String.format(BuryOperation.SOMETHING_FILTER, "scene_audit_time"), false);
        }
        if (!StringUtils.isNotEmpty(arg.getDealerId())) {
            BuryService.asyncTpmLog(Integer.valueOf(controllerContext.getTenantId()), Integer.parseInt(controllerContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_ACTIVITY_PROOF_AUDIT, String.format(BuryOperation.SOMETHING_FILTER, "scene_audit_dealer"), false);
        }
        if (!StringUtils.isNotEmpty(arg.getActivityId())) {
            BuryService.asyncTpmLog(Integer.valueOf(controllerContext.getTenantId()), Integer.parseInt(controllerContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_ACTIVITY_PROOF_AUDIT, String.format(BuryOperation.SOMETHING_FILTER, "scene_audit_activity"), false);
        }
        return result;
    }


    private Map<String, IObjectData> queryStoreMap(ControllerContext context, List<String> idList) {
        if (idList.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, IObjectData> result = new HashMap<>();
        List<IObjectData> data = serviceFacade.findObjectDataByIds(context.getTenantId(), idList, ApiNames.ACCOUNT_OBJ);
        for (IObjectData datum : data) {
            result.put(datum.getId(), datum);
        }
        return result;
    }


    private List<String> getStoreList(
            String activityId,
            String dealerId,
            Long begin,
            Long end,
            List<String> idList) throws TimeoutException {
        List<String> storeIds = Collections.synchronizedList(new ArrayList<>());
        Queue<AuditedEntity> entityQueue = new ConcurrentLinkedDeque<>();
        if (!Strings.isNullOrEmpty(activityId)) {
            ActivityTypeExt activityTypeExt = activityTypeManager.findByActivityId(controllerContext.getTenantId(), activityId);
            if (activityTypeExt.auditSourceConfig() != null) {
                entityQueue.add(fromEntity(activityTypeExt));
            }
        } else {
            Set<String> visitedSet = new HashSet<>();
            List<ActivityTypeExt> activityTypeExts = activityTypeManager.queryActivityTypeContainsAudit(controllerContext.getTenantId());
            activityTypeExts.forEach(v -> entityQueue.add(fromEntity(v)));
        }
        if (CollectionUtils.isEmpty(entityQueue)) {
            return storeIds;
        }
        List<String> queryIds = new CopyOnWriteArrayList<>(idList);

      /*  AuditedEntity entity;
        while ((entity = entityQueue.poll()) != null) {
            queryStoreByAuditedEntity(entity, queryIds, storeIds, activityId, dealerId, begin, end);
        }*/

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        for (int i = 0; i < 2; i++) {
            parallelTask.submit(() -> {
                AuditedEntity entity;
                while ((entity = entityQueue.poll()) != null) {
                    queryStoreByAuditedEntity(entity, queryIds, storeIds, activityId, dealerId, begin, end);
                }
            });
        }
        parallelTask.await(10000, TimeUnit.MILLISECONDS);
        return storeIds;
    }

    private void queryStoreByAuditedEntity(AuditedEntity auditedEntity, List<String> storeIds, List<String> finalStoreIds, String activityId, String dealerId, Long start, Long end) {

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(2000);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter storeFilter = new Filter();
        storeFilter.setFieldName(auditedEntity.getStoreApiName());
        storeFilter.setOperator(Operator.IN);
        storeFilter.setFieldValues(new ArrayList<>(storeIds));

        //多线程 快照为空 返回
        if (CollectionUtils.isEmpty(storeFilter.getFieldValues())) {
            return;
        }

        query.setFilters(Lists.newArrayList(storeFilter));

        if (start != null && end != null && start > 0 && end > 0) {
            Filter createFilter = new Filter();
            createFilter.setFieldName(CommonFields.CREATE_TIME);
            createFilter.setOperator(Operator.BETWEEN);
            createFilter.setFieldValues(Lists.newArrayList(start.toString(), end.toString()));
            query.getFilters().add(createFilter);
        }

        if (!Strings.isNullOrEmpty(dealerId)) {
            if (Strings.isNullOrEmpty(auditedEntity.getDealerApiName())) {
                return;
            } else {
                Filter dealerFilter = new Filter();
                dealerFilter.setFieldName(auditedEntity.getDealerApiName());
                dealerFilter.setOperator(Operator.EQ);
                dealerFilter.setFieldValues(Lists.newArrayList(dealerId));
                query.getFilters().add(dealerFilter);
            }
        }

        if (!Strings.isNullOrEmpty(activityId)) {
            Filter activityFilter = new Filter();
            activityFilter.setFieldName(auditedEntity.getActivityApiName());
            activityFilter.setOperator(Operator.EQ);
            activityFilter.setFieldValues(Lists.newArrayList(activityId));
            query.getFilters().add(activityFilter);
        } else {
            List<String> activityIds = getActivityIds(auditedEntity.getActivityTypeId());
            if (!CollectionUtils.isEmpty(activityIds)) {
                Filter activityFilter = new Filter();
                activityFilter.setFieldName(auditedEntity.getActivityApiName());
                activityFilter.setOperator(Operator.IN);
                activityFilter.setFieldValues(activityIds);
                query.getFilters().add(activityFilter);
            } else {
                return;
            }
        }


        Filter auditStatusFilter = new Filter();
        auditStatusFilter.setFieldName(auditedEntity.getAuditStatusApiName());
        auditStatusFilter.setOperator(Operator.EQ);
        auditStatusFilter.setFieldValues(Lists.newArrayList("schedule"));

        if (ApiNames.TPM_ACTIVITY_PROOF_OBJ.equals(auditedEntity.getObjectApiName())) {
            Wheres disRandomWheres = new Wheres();
            Wheres unRandomWheres = new Wheres();

            Filter randomFilter = new Filter();
            randomFilter.setFieldName(TPMActivityProofFields.RANDOM_AUDIT_STATUS);
            randomFilter.setOperator(Operator.EQ);
            randomFilter.setFieldValues(Lists.newArrayList(TPMActivityProofFields.RANDOM_AUDIT_STATUS__UNCHECKED));
            unRandomWheres.setFilters(Lists.newArrayList(randomFilter));

            Filter disRandomFilter = new Filter();
            disRandomFilter.setFieldName(TPMActivityProofFields.RANDOM_AUDIT_STATUS);
            disRandomFilter.setOperator(Operator.IS);
            disRandomFilter.setFieldValues(Lists.newArrayList());
            disRandomWheres.setFilters(Lists.newArrayList(disRandomFilter, auditStatusFilter));

            if (AuditModeType.RANDOM.value().equals(auditedEntity.getAuditMode())) {
                query.setWheres(Lists.newArrayList(unRandomWheres));
            } else {
                query.setWheres(Lists.newArrayList(disRandomWheres));
            }
        } else {
            query.getFilters().add(auditStatusFilter);
        }

        List<IObjectData> objectDataList = serviceFacade.aggregateFindBySearchQuery(controllerContext.getTenantId(), query, auditedEntity.getObjectApiName(), auditedEntity.getStoreApiName(), "count", "");
        for (IObjectData objectData : objectDataList) {
            Integer count = objectData.get("groupbycount", Integer.class);
            String key = objectData.get(auditedEntity.getStoreApiName(), String.class);
            if (count != null && count > 0) {
                storeIds.remove(key);
                finalStoreIds.add(key);
            }
        }
    }

    private AuditedEntity fromEntity(ActivityTypeExt ext) {
        ActivityProofAuditSourceConfigEntity config = ext.auditSourceConfig();
        AuditedEntity entity = new AuditedEntity();
        entity.setActivityApiName(config.getReferenceActivityFieldApiName());
        entity.setDealerApiName(config.getDealerFieldApiName());
        entity.setObjectApiName(config.getMasterApiName());
        entity.setStoreApiName(config.getAccountFieldApiName());
        entity.setAuditStatusApiName(config.getAuditStatusApiName());
        entity.setActivityTypeId(ext.get().getId().toString());
        entity.setAuditMode(ext.auditModeConfig().getAuditMode());
        if (Strings.isNullOrEmpty(entity.getAuditStatusApiName())) {
            if (ApiNames.TPM_ACTIVITY_PROOF_OBJ.equals(config.getMasterApiName())) {
                entity.setAuditStatusApiName(TPMActivityProofFields.AUDIT_STATUS);
            } else {
                entity.setAuditStatusApiName("audit_status__c");
            }
        }
        return entity;
    }


    @Data
    @ToString
    private class AuditedEntity {
        private String activityApiName;
        private String dealerApiName;
        private String objectApiName;
        private String storeApiName;
        private String auditStatusApiName;
        private String activityTypeId;
        private String auditMode;
    }


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }

    private List<String> getActivityIds(String activityTypeId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter closeStatus = new Filter();
        closeStatus.setFieldName(TPMActivityFields.CLOSE_STATUS);
        closeStatus.setOperator(Operator.NEQ);
        closeStatus.setFieldValues(Lists.newArrayList(TPMActivityFields.CLOSE_STATUS__CLOSED));

        Filter typeFilter = new Filter();
        typeFilter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
        typeFilter.setOperator(Operator.EQ);
        typeFilter.setFieldValues(Lists.newArrayList(activityTypeId));

        query.setFilters(Lists.newArrayList(closeStatus, typeFilter));
        return CommonUtils.queryData(serviceFacade, User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query, Lists.newArrayList(CommonFields.ID)).stream().map(DBRecord::getId).collect(Collectors.toList());
    }
}