package com.facishare.crm.fmcg.tpm.reward.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;

import java.io.Serializable;

/**
 * Author: linmj
 * Date: 2024/3/27 19:49
 */
public interface ConsumerReward {

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Arg extends WeChatArg implements Serializable {

        private String code;
    }


    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result implements Serializable {
        private String status;

        @JSONField(name = "sn_information")
        @JsonProperty(value = "sn_information")
        @SerializedName("sn_information")
        private SnInformation snInformation;

        @JSONField(name = "red_packet_information")
        @JsonProperty(value = "red_packet_information")
        @SerializedName("red_packet_information")
        private RedPacketInformation redPacketInformation;

        @JSONField(name = "physical_reward_information")
        @JsonProperty(value = "physical_reward_information")
        @SerializedName("physical_reward_information")
        private PhysicalRewardInformation physicalRewardInformation;
    }
}
