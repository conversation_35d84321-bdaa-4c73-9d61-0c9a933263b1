package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.crm.fmcg.tpm.business.dto.GetValidActivitiesByDealerIdResult;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/2/16 下午4:50
 */
public interface IUnifiedActivityCommonLogicBusiness {

    void checkIfUnifiedActivityContainsActivityType(IObjectData unifiedActivity, String activityType);

    void checkDealerIsInTheRangeOfUnifiedActivityLimits(String tenantId, IObjectData unifiedActivity, IObjectData dealer);

    void checkDepartmentIsUnderTheRangeOfUnifiedActivityLimits(String tenantId, IObjectData unifiedActivity, List<String> departments);

    void checkTimeRangeIsInTheRangeOfUnifiedActivityLimits(IObjectData unifiedActivity, long startDate, long endDate);

    Set<String> getDealerIdsOfUnifiedActivity(String tenantId, IObjectData unifiedActivity);

    boolean isNotFitUnifiedActivity(String tenantId, Map<String, Set<String>> unifiedActivity2DealerIds, IObjectData activity, String storeId, String dealerId);

    void recalculateUnifiedAmountField(String tenantId, IObjectData unifiedActivity);

    void recalculateUnifiedAmountField(String tenantId, String unifiedActivityId);

    GetValidActivitiesByDealerIdResult getValidActivitiesByDealerId(String tenantId, List<IObjectData> activities, String dealerId);
}
