package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/11 上午11:25
 */
public class TPMActivityBudgetObjWebDetailController extends StandardWebDetailController {

    @Override
    protected Result after(Arg arg, Result result) {
        List<Object> components = (List<Object>) result.getLayout().get("component");

        return super.after(arg, result);
    }
}
