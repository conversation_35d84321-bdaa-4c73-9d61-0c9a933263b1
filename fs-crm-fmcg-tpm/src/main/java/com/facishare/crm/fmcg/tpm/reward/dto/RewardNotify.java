package com.facishare.crm.fmcg.tpm.reward.dto;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

public interface RewardNotify {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "app_id")
        @JsonProperty(value = "app_id")
        @SerializedName("app_id")
        private String appId;

        @JSONField(name = "message_id")
        @JsonProperty(value = "message_id")
        @SerializedName("message_id")
        private String messageId;

        private JSONObject data;
    }

    @Data
    @ToString
    class Result implements Serializable {

        private int code;

        private String message;

        private ResultData data;

        public static Result success(String id) {
            Result result = new Result();
            result.setCode(200);
            result.setMessage("success");
            result.setData(ResultData.builder().id(id).build());
            return result;
        }

        public static Result fail(int code, String message) {
            Result result = new Result();
            result.setCode(code);
            result.setMessage(message);
            return result;
        }
    }

    @Data
    @ToString
    @Builder
    class ResultData implements Serializable {

        private String id;
    }
}
