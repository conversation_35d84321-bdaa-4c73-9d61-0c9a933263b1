package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.apiname.TPMDealerActivityCostFields;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.business.UnifiedActivityCommonLogicBusiness;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityWriteOffConfigEntity;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/12/16 下午4:02
 */
@SuppressWarnings("Duplicates")
public class TPMDealerActivityCostObjIncrementUpdateAction extends StandardIncrementUpdateAction {

    private static final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);

    private static final String SEND_BUDGET_IDS = "SEND_BUDGET_IDS:%s";

    private static final String SEND_ACTIVITY_IDS = "SEND_ACTIVITY_IDS:%s";

    private final UnifiedActivityCommonLogicBusiness unifiedActivityCommonLogicBusiness = SpringUtil.getContext().getBean(UnifiedActivityCommonLogicBusiness.class);
    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);
    private boolean enableOverWriteOff = false;

    @Override
    protected void before(Arg arg) {
        initData();
        validateActivity(arg);
        super.before(arg);
    }

    private void initData() {
        IObjectData cost = serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), arg.getData().getId(), ApiNames.TPM_DEALER_ACTIVITY_COST);
        String activityId = cost.get(TPMDealerActivityCostFields.ACTIVITY_ID, String.class);
        if (Strings.isNullOrEmpty(activityId)) {
            return;
        }
        ActivityTypeExt activityType = activityTypeManager.findByActivityId(actionContext.getTenantId(), activityId);
        if (Objects.nonNull(activityType)) {
            ActivityWriteOffConfigEntity activityWriteOffConfigEntity = activityType.writeOffConfig();
            if (activityWriteOffConfigEntity != null && activityWriteOffConfigEntity.getEnableOverWriteOff() != null) {
                this.enableOverWriteOff = activityWriteOffConfigEntity.getEnableOverWriteOff();
                log.info("dealer activity cost enableOverWriteOff is {}", this.enableOverWriteOff);
            }
        }
    }


    @Override
    protected Result after(Arg arg, Result result) {
        Result rst = super.after(arg, result);
        //anno
        List<String> activityIds = actionContext.getAttribute(String.format(SEND_ACTIVITY_IDS, actionContext.getTenantId()));
        if (!CollectionUtils.isEmpty(activityIds)) {
            activityIds.forEach(activityId -> {
                budgetService.calculateActivity(actionContext.getTenantId(), activityId);
            });
            unifiedActivityCommonLogicBusiness.recalculateUnifiedAmountField(actionContext.getTenantId(), this.dbObjectData.get(TPMDealerActivityCostFields.ACTIVITY_UNIFIED_CASE_ID, String.class));
        } else {
            IObjectData resultObjectData = this.dbObjectData;
            String activityId = resultObjectData.get(TPMDealerActivityCostFields.ACTIVITY_ID, String.class);
            if (!Strings.isNullOrEmpty(activityId)) {
                budgetService.calculateActivity(actionContext.getTenantId(), activityId);
                unifiedActivityCommonLogicBusiness.recalculateUnifiedAmountField(actionContext.getTenantId(), resultObjectData.get(TPMDealerActivityCostFields.ACTIVITY_UNIFIED_CASE_ID, String.class));
            }
        }
        List<String> budgetIds = actionContext.getAttribute(String.format(SEND_BUDGET_IDS, actionContext.getTenantId()));
        if (!CollectionUtils.isEmpty(budgetIds)) {
            budgetIds.forEach(budgetId -> budgetService.calculateBudget(actionContext.getTenantId(), budgetId));
        }
        return rst;
    }

    private void validateActivity(Arg arg) {
        String tenantId = actionContext.getTenantId();

        if (budgetService.isOpenBudge(Integer.parseInt(tenantId)) && !TPMGrayUtils.excessDeductionForCost(tenantId)) {
            if (arg.getData().get(TPMDealerActivityCostFields.CONFIRMED_AMOUNT) == null)
                return;
            if (Strings.isNullOrEmpty((String) arg.getData().get(TPMDealerActivityCostFields.CONFIRMED_AMOUNT)))
                return;
            IObjectData cost = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getData().getId(), ApiNames.TPM_DEALER_ACTIVITY_COST);
            String activityId = (String) cost.get(TPMDealerActivityCostFields.ACTIVITY_ID);

            actionContext.setAttribute(String.format(SEND_ACTIVITY_IDS, tenantId), Lists.newArrayList(activityId));
            IObjectData activity = serviceFacade.findObjectData(User.systemUser(tenantId), activityId, ApiNames.TPM_ACTIVITY_OBJ);
            String budgetId = activity.get(TPMActivityFields.BUDGET_TABLE, String.class, "");
            if (Strings.isNullOrEmpty(budgetId)) {
                return;
            }
            actionContext.setAttribute(String.format(SEND_BUDGET_IDS, tenantId), Lists.newArrayList(budgetId));
            budgetService.tryLockBudget(actionContext, budgetId);
            Map<String, Double> amountMap = budgetService.getActivityAmountFields(tenantId, activityId);
            BigDecimal actualUsedAmount = BigDecimal.valueOf(amountMap.get(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT)).setScale(3, RoundingMode.HALF_UP);
            BigDecimal frozenAmount = BigDecimal.valueOf(amountMap.getOrDefault(TPMActivityFields.ACTIVITY_FROZEN_AMOUNT, 0.0)).setScale(3, RoundingMode.HALF_UP);
            BigDecimal total = activity.get(TPMActivityFields.ACTIVITY_AMOUNT, BigDecimal.class, BigDecimal.ZERO).setScale(3, RoundingMode.HALF_UP);
            BigDecimal beforeConfirmedAmount = cost.get(TPMDealerActivityCostFields.CONFIRMED_AMOUNT, BigDecimal.class, BigDecimal.ZERO).setScale(3, RoundingMode.HALF_UP);
            //如果是编辑审批，需要从snapshot里面获取锁定的数值
            if ("in_change".equals(cost.get(CommonFields.LIFE_STATUS))) {
                BigDecimal usedAmount = BigDecimal.valueOf(amountMap.getOrDefault(String.format("%s:%s", TPMDealerActivityCostFields.CONFIRMED_AMOUNT, cost.getId()), beforeConfirmedAmount.doubleValue())).setScale(3, RoundingMode.HALF_UP);
                beforeConfirmedAmount = beforeConfirmedAmount.max(usedAmount);
            }
            BigDecimal nowConfirmedAmount = arg.getData().toObjectData().get(TPMDealerActivityCostFields.CONFIRMED_AMOUNT, BigDecimal.class, BigDecimal.ZERO).setScale(3, RoundingMode.HALF_UP);
            if (total.subtract(frozenAmount).subtract(actualUsedAmount).add(beforeConfirmedAmount).subtract(nowConfirmedAmount).compareTo(BigDecimal.ZERO) < 0
                    && !enableOverWriteOff) {
                throw new ValidateException(I18N.text(I18NKeys.AUDIT_AMOUNT_CAN_NOT_OVER_THE_ACTIVITY_AMOUNT));
            }
        }
    }

    @Override
    protected void finallyDo() {
        try {
            super.finallyDo();
        } finally {
            budgetService.unLockBudget(actionContext);
        }
    }
}
