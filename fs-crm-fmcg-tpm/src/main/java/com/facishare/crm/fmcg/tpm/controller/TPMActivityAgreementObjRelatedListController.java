package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.SalesOrderObjFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityAgreementFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPM2Service;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;

import java.util.Objects;

public class TPMActivityAgreementObjRelatedListController extends StandardRelatedListController {

    public static final ITPM2Service tpm2Service = SpringUtil.getContext().getBean(ITPM2Service.class);
    public static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);
    private boolean isTpm2Tenant = false;

    public static String YINLU_FILTER_TIME = "1716911999999";

    static {
        ConfigFactory.getConfig("gray-rel-fmcg", iConfig -> {
            String json = iConfig.get("YINLU_QUERY_ACTIVITY_FILTER_TIME");
            if (Strings.isNullOrEmpty(json)) {
                return;
            }
            YINLU_FILTER_TIME = json;
        });
    }

    @Override
    protected void before(Arg arg) {
        this.isTpm2Tenant = tpm2Service.isTPM2Tenant(Integer.valueOf(controllerContext.getTenantId()));
        super.before(arg);
    }


    @Override
    protected Result doService(Arg arg) {
        return super.doService(arg);
    }

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        if (TPMGrayUtils.TPMUseEs(controllerContext.getTenantId())) {
            query.setSearchSource("es");
        }
        super.beforeQueryData(overrideActivityAgreementQuery(query));
    }

    private SearchTemplateQuery overrideActivityAgreementQuery(SearchTemplateQuery query) {
        if (!this.isTpm2Tenant) {
            return query;
        }

        ObjectDataDocument objectData = arg.getObjectData();
        if (Objects.isNull(objectData)) {
            return query;
        }

        String objectApiName = (String) objectData.get("object_describe_api_name");
        if (!ApiNames.SALES_ORDER_OBJ.equals(objectApiName)) {
            return query;
        }

        String accountId = (String) objectData.get("account_id");
        if (Strings.isNullOrEmpty(accountId)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_AGREEMENT_OBJ_RELATED_LIST_CONTROLLER_0));
        }

        String activityId = (String) objectData.get(SalesOrderObjFields.ACTIVITY_ID);
        if (Strings.isNullOrEmpty(activityId)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_AGREEMENT_OBJ_RELATED_LIST_CONTROLLER_1));
        }

        ActivityTypeExt activityTypeExt = activityTypeManager.findByActivityId(controllerContext.getTenantId(), activityId);
        if (Objects.isNull(activityTypeExt)) {
            return query;
        }

        // 活动方案下的活动协议
        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityAgreementFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));
        query.getFilters().add(activityFilter);

        //不等于未生效
        Filter activityAgreementStatusFilter = new Filter();
        activityAgreementStatusFilter.setFieldName(TPMActivityAgreementFields.AGREEMENT_STATUS);
        activityAgreementStatusFilter.setOperator(Operator.IN);
        activityAgreementStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS, TPMActivityAgreementFields.AGREEMENT_STATUS__END));
        query.getFilters().add(activityAgreementStatusFilter);

        //协议客户
        Filter storeFilter = new Filter();
        storeFilter.setFieldName(TPMActivityAgreementFields.STORE_ID);
        storeFilter.setOperator(Operator.EQ);
        storeFilter.setFieldValues(Lists.newArrayList(accountId));
        query.getFilters().add(storeFilter);


        IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);

        // 活动协议签订方式为【业代签订-需门店确认】时，仅支持选到，门店确认状态 = 已确认 的协议
        if (!Objects.isNull(describe) && !Objects.isNull(describe.getFieldDescribe(TPMActivityAgreementFields.SIGNING_MODE))) {
            Filter activityAgreementSignTypeFilter = new Filter();
            activityAgreementSignTypeFilter.setFieldName(TPMActivityAgreementFields.SIGNING_MODE);
            activityAgreementSignTypeFilter.setOperator(Operator.EQ);
            activityAgreementSignTypeFilter.setFieldValues(Lists.newArrayList(TPMActivityAgreementFields.SIGNING_MODE__AGENT_SIGNING));
            query.getFilters().add(activityAgreementSignTypeFilter);

            Filter activityAgreementStoreFilter = new Filter();
            activityAgreementStoreFilter.setFieldName(TPMActivityAgreementFields.STORE_CONFIRM_STATUS);
            activityAgreementStoreFilter.setOperator(Operator.EQ);
            activityAgreementStoreFilter.setFieldValues(Lists.newArrayList(TPMActivityAgreementFields.STORE_CONFIRM_STATUS__CONFIRMED));
            query.getFilters().add(activityAgreementStoreFilter);
        }
        return query;
    }
}
