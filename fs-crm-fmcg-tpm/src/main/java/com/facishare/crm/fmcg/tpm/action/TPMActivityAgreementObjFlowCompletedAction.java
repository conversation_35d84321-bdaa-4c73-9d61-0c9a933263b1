package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/7/30 11:25
 */
@Slf4j
public class TPMActivityAgreementObjFlowCompletedAction extends StandardFlowCompletedAction {

    private final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);

    private static final List<String> DIRECT_RETURN_STATUS = Lists.newArrayList(TPMActivityAgreementFields.AGREEMENT_STATUS__END, TPMActivityAgreementFields.AGREEMENT_STATUS__INVALID, TPMActivityAgreementFields.AGREEMENT_STATUS__CLOSE);

    @Override
    protected void before(Arg arg) {
        log.info("TPMActivityAgreement flow complete call back parameters : {}", arg);
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        if (TPMGrayUtils.isYinLu(actionContext.getTenantId())) {
            return super.after(arg, result);
        }
        log.info("TPMActivityAgreement flow complete call back arg : {}", JSON.toJSONString(arg));

        IObjectData agreement = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getDataId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);

        String status = calculateAgreementStatus(agreement);

        Map<String, Object> updateField = new HashMap<>();
        updateField.put(TPMActivityAgreementFields.AGREEMENT_STATUS, status);
        log.info("TPMActivityAgreement flow complete update agreement id:{} ,map : {}", agreement.getId(), updateField);
        serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), agreement, updateField);

        if (TPMActivityAgreementFields.AGREEMENT_STATUS__END.equals(status) || TPMActivityAgreementFields.AGREEMENT_STATUS__CLOSE.equals(status)) {
            storeBusiness.updateStoreLabel(actionContext.getTenantId(), Lists.newArrayList(agreement.get(TPMActivityAgreementFields.STORE_ID, String.class)), AccountFields.CUSTOMER_LABEL_AGREEMENT_STORE, 0);
        } else if (TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS.equals(status)) {
            storeBusiness.updateStoreLabel(actionContext.getTenantId(), Lists.newArrayList(agreement.get(TPMActivityAgreementFields.STORE_ID, String.class)), AccountFields.CUSTOMER_LABEL_AGREEMENT_STORE, 1);
        }
        return super.after(arg, result);
    }

    @Override
    protected Result doAct(Arg arg) {
        return super.doAct(arg);
    }

    @Override
    protected void doOtherAction() {
        if (Objects.equals(ApprovalFlowTriggerType.CLOSE_ACTIVITY_AGREEMENT.getTriggerTypeCode(), arg.getTriggerType())) {
            log.info("TPMActivityAgreement flow complete doOtherAction ");
            IObjectData afterDoActAgreement = serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), arg.getDataId(), arg.getDescribeApiName());
            Map<String, Object> update = new HashMap<>();
            update.put(CommonFields.LIFE_STATUS, CommonFields.LIFE_STATUS__NORMAL);

            if (arg.isPass()) {
                long closeTime = System.currentTimeMillis();
                update.put(TPMActivityAgreementFields.AGREEMENT_STATUS, TPMActivityAgreementFields.AGREEMENT_STATUS__CLOSE);
                update.put(TPMActivityAgreementFields.CLOSE_TIME, closeTime);
                afterDoActAgreement.set(TPMActivityAgreementFields.AGREEMENT_STATUS, TPMActivityAgreementFields.AGREEMENT_STATUS__CLOSE);
                afterDoActAgreement.set(TPMActivityAgreementFields.CLOSE_TIME, closeTime);
            } else {
                String status = calculateAgreementStatus(afterDoActAgreement);
                update.put(TPMActivityAgreementFields.AGREEMENT_STATUS, status);
            }
            log.info("TPMActivityAgreement flow complete doOtherAction update agreement id:{} ,map : {}", afterDoActAgreement.getId(), update);
            serviceFacade.updateWithMap(actionContext.getUser(), afterDoActAgreement, update);
        }
    }

    private String calculateAgreementStatus(IObjectData agreement) {
        Long begin = agreement.get(TPMActivityAgreementFields.BEGIN_DATE, Long.class);
        Long end = agreement.get(TPMActivityAgreementFields.END_DATE, Long.class);

        String lifeStatus = agreement.get(CommonFields.LIFE_STATUS, String.class);
        long now = System.currentTimeMillis();

        String status = agreement.get(TPMActivityAgreementFields.AGREEMENT_STATUS, String.class);
        if (DIRECT_RETURN_STATUS.contains(status)) {
            return status;
        }

        if (now < begin) {
            status = TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE;
        } else if (now < end) {
            if (ObjectLifeStatus.NORMAL.getCode().equals(lifeStatus)) {
                status = TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS;
            } else {
                status = TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE;
            }
        } else {
            status = TPMActivityAgreementFields.AGREEMENT_STATUS__END;
        }
        return status;
    }
}
