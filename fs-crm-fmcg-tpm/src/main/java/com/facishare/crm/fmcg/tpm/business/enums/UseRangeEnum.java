package com.facishare.crm.fmcg.tpm.business.enums;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Author: linmj
 * Date: 2023/4/19 11:04
 */
public enum UseRangeEnum {
    /**
     * 全部类型
     */
    ALL("ALL"),
    /**
     * 指定门店
     */
    FIXED("FIXED"),
    /**
     * 条件选择
     */
    CONDITION("CONDITION");

    private String value;

    private static final Map<String, UseRangeEnum> VALUE_MAP = Stream.of(values()).collect(Collectors.toMap(UseRangeEnum::value, v -> v, (a, b) -> a));

    private UseRangeEnum(String value) {
        this.value = value;
    }

    public String value() {
        return this.value;
    }

    public UseRangeEnum of(String value) {
        return VALUE_MAP.get(value);
    }
}
