package com.facishare.crm.fmcg.tpm.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.crm.fmcg.tpm.business.enums.BudgetDetailOperateMark;
import com.facishare.crm.fmcg.tpm.service.abstraction.BudgetConsumeFlowService;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.SqlEscaper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/15 下午2:33
 */
@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class BudgetConsumeFlowServiceImpl implements BudgetConsumeFlowService {

    @Resource
    private SpecialTableMapper specialTableMapper;


    @Override
    public Boolean isExistedBudgetConsumeFlowData(String tenantId, String ruleId) {
        // 查询 业务对象消费流水是否有未执行完的 （冻结再扣减）
        String sql = "select\n" +
                "    *\n" +
                "\t from\n" +
                "    (\n" +
                "    select\n" +
                "        biz_trace_id,\n" +
                "        sum(case main_type when 'freeze' then coalesce(-amount, 0) when 'unfreeze' then coalesce(amount, 0) else 0 end ) frozen_amount\n" +
                "    from\n" +
                "        fmcg_tpm_budget_account_detail\n" +
                "    where\n" +
                "        tenant_id = '#{tenantId}'\n" +
                "        and is_deleted = 0\n" +
                "        and biz_trace_id like 'BUDGET_CONSUME_RULE:#{ruleId}:%'\n" +
                "        and (detail_status is null\n" +
                "            or detail_status = 'include')\n" +
                "    group by\n" +
                "        biz_trace_id\n" +
                "    ) tmp_table\n" +
                "\t where\n" +
                "    frozen_amount != 0";
        String replaceSql = sql.replace("#{tenantId}", SqlEscaper.pg_escape(tenantId)).replace("#{ruleId}", SqlEscaper.pg_escape(ruleId));
        List<Map> bySql = (specialTableMapper.setTenantId(tenantId)).findBySql(replaceSql);
        return CollectionUtils.isEmpty(bySql) && isContainsOperateBeforeApproval(tenantId, ruleId);
    }

    private boolean isContainsOperateBeforeApproval(String tenantId, String ruleId) {
        String sql = "\n" +
                "select\n" +
                "\t\tid,\n" +
                "\toperate_mark_detail\n" +
                "from\n" +
                "\t\tfmcg_tpm_budget_account_detail\n" +
                "where\n" +
                "\t\ttenant_id = '#{tenantId}'\n" +
                "\tand is_deleted = 0\n" +
                "\tand operate_mark_detail = 'operate_before_approval'\n" +
                "\tand biz_trace_id like 'BUDGET_CONSUME_RULE:#{ruleId}:%'\n" +
                "\tand (detail_status is null\n" +
                "\t\tor detail_status = 'include')\n";
        String replaceSql = sql.replace("#{tenantId}", SqlEscaper.pg_escape(tenantId)).replace("#{ruleId}", SqlEscaper.pg_escape(ruleId));
        List<Map> bySql = (specialTableMapper.setTenantId(tenantId)).findBySql(replaceSql);
        return CollectionUtils.isEmpty(bySql);
    }
}
