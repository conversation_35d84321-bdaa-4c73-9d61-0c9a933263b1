package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.TPMActivityUnifiedCaseFields;
import com.facishare.crm.fmcg.tpm.business.TPM2Service;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;

/**
 * <AUTHOR>
 * @date 2023/2/20 19:33
 */
public class TPMActivityUnifiedCaseObjListController extends StandardListController {

    private TPM2Service tpm2Service = SpringUtil.getContext().getBean(TPM2Service.class);

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        if (TPMGrayUtils.TPMUseEs(controllerContext.getTenantId())) {
            query.setSearchSource("es");
        }
        super.beforeQueryData(query);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        if (tpm2Service.isTPM2Tenant(Integer.valueOf(controllerContext.getTenantId()))) {
            realButton(arg, result);
            fillActivityDate(result);
        }
        return super.after(arg, result);
    }

    private void fillActivityDate(Result result) {
        for (ObjectDataDocument obj : result.getDataList()) {
            Long beginDate = (Long) obj.get(TPMActivityUnifiedCaseFields.START_DATE);
            Long endDate = (Long) obj.get(TPMActivityUnifiedCaseFields.END_DATE);

            if (beginDate <= TimeUtils.MIN_DATE) {
                obj.put(TPMActivityUnifiedCaseFields.START_DATE, null);
            }
            if (endDate >= TimeUtils.MAX_DATE) {
                obj.put(TPMActivityUnifiedCaseFields.END_DATE, null);
            }
        }
    }

    private void realButton(Arg arg, Result result) {
        if (arg.isIncludeButtonInfo()) {
            if (result.getButtonInfo().getButtonMap() != null) {
                for (ObjectDataDocument obj : result.getDataList()) {
                    String closedStatus = (String) obj.get(TPMActivityUnifiedCaseFields.CLOSE_STATUS);
                    if (TPMActivityUnifiedCaseFields.CLOSE_STATUS__CLOSED.equals(closedStatus)) {
                        if (result.getButtonInfo().getButtonMap().containsKey(obj.getId())) {
                            result.getButtonInfo().getButtonMap().get(obj.getId()).remove(ObjectAction.CLOSE_TPM_ACTIVITY.getButtonApiName());
                        }
                    }
                }
            }
        }
    }
}
