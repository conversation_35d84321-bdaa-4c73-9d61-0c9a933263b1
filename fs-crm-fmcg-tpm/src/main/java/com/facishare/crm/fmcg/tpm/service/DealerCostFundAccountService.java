package com.facishare.crm.fmcg.tpm.service;

import com.facishare.crm.fmcg.tpm.api.plugin.DealerActivityCostEnterAccount;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDealerActivityCostRebateService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IDealerActivityCostEnterAccountService;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
@Slf4j
public class DealerCostFundAccountService {
    @Resource
    private IDealerActivityCostRebateService dealerActivityCostRebateService;
    @Resource
    private IDealerActivityCostEnterAccountService dealerActivityCostEnterAccountService;

    public void doAct(DealerActivityCostEnterAccount.Arg arg, ActionContext actionContext) {
        IObjectData rebate = dealerActivityCostRebateService.initRebate(arg);
        dealerActivityCostEnterAccountService.autoEnterAccount(rebate, arg);

    }


}
