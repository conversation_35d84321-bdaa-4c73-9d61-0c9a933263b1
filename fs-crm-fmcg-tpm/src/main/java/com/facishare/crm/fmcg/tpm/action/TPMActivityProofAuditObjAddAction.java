package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofDTO;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofDetailDTO;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofImageDTO;
import com.facishare.crm.fmcg.tpm.api.visit.VisitActionDataDTO;
import com.facishare.crm.fmcg.tpm.business.abstraction.IActivityService;
import com.facishare.crm.fmcg.tpm.cache.ActivityItemCache;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofAuditSourceConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.CheckinService;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.paas.data.PaasAddTeamMember;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/12 2:47 PM
 */
@SuppressWarnings("Duplicates,unused")
@Slf4j
public class TPMActivityProofAuditObjAddAction extends StandardAddAction {

    private static final CheckinService checkinService = SpringUtil.getContext().getBean(CheckinService.class);
    private static final PaasDataProxy paasDataProxy = SpringUtil.getContext().getBean(PaasDataProxy.class);
    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);
    private static final ActivityItemCache activityItemCache = SpringUtil.getContext().getBean(ActivityItemCache.class);

    private final IActivityService activityService = SpringUtil.getContext().getBean(IActivityService.class);

    private ActivityTypeExt activityType;
    private IObjectData activity;
    private boolean isAudit;

    @Override
    protected void before(Arg arg) {
        this.loadRelatedData();
        stopWatch.lap("loadRelatedData");

        this.setDefaultValue();
        stopWatch.lap("setDefaultValue");

        this.validateReferenceProofFieldRequired();
        stopWatch.lap("validateReferenceProofFieldRequired");

        this.auditedValidate();
        stopWatch.lap("auditedCheck");

        this.activityValidate();
        stopWatch.lap("activityValidate");

        super.before(arg);
        stopWatch.lap("super.before");
    }

    private void activityValidate() {
        String lifeStatus = this.activity.get(CommonFields.LIFE_STATUS, String.class);
        if (!CommonFields.LIFE_STATUS__NORMAL.equals(lifeStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_AUDIT_OBJ_ADD_ACTION_0));
        }

        String closedStatus = (String) this.activity.get(TPMActivityFields.CLOSED_STATUS);
        if (TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closedStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_AUDIT_OBJ_ADD_ACTION_1));
        }

        String activityStatus = (String) this.activity.get(TPMActivityFields.ACTIVITY_STATUS);
        if (TPMActivityFields.ACTIVITY_STATUS__CLOSED.equals(activityStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_AUDIT_OBJ_ADD_ACTION_2));
        }

        activityService.ifAllowCreateDataDueToOnceWriteOff(actionContext.getTenantId(), this.activity);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result inner = super.after(arg, result);
        stopWatch.lap("updateProofStatusAfterAuditSuccess");

        if (this.isAudit) {
            ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
            if (!TPMGrayUtils.isSkipSaveCheckinProofData(actionContext.getTenantId())) {
                if (TPMGrayUtils.isAsyncSaveCheckinProofData(actionContext.getTenantId())) {
                    task.submit(MonitorTaskWrapper.wrap(() -> saveCheckinsAction(inner)));
                } else {
                    saveCheckinsAction(inner);
                    stopWatch.lap("saveCheckinsAction");
                }
            }
            task.submit(MonitorTaskWrapper.wrap(() -> this.updateSourceData(inner)));
            task.submit(MonitorTaskWrapper.wrap(() -> this.lockSourceData(inner)));
            task.submit(MonitorTaskWrapper.wrap(() -> this.addTeamMember(inner)));
            task.run();

            this.asyncAddTpmLog(result);
            stopWatch.lap("asyncAddTpmLog");
        }

        return inner;
    }

    private void saveCheckinsAction(Result result) {
        String visitId = (String) result.getObjectData().get(TPMActivityProofAuditFields.VISIT_ID);
        String actionId = (String) result.getObjectData().get(TPMActivityProofAuditFields.ACTION_ID);
        String storeId = (String) result.getObjectData().get(TPMActivityProofAuditFields.STORE_ID);

        if (!Strings.isNullOrEmpty(visitId) && !Strings.isNullOrEmpty(actionId)) {
            VisitActionDataDTO data = new VisitActionDataDTO();
            List<IObjectData> masterList = queryProof(actionContext, storeId, visitId, actionId);
            List<String> masterIds = masterList.stream().map(DBRecord::getId).collect(Collectors.toList());
            List<String> activityIds = masterList.stream().map(master -> (String) master.get(TPMActivityProofAuditFields.ACTIVITY_ID)).collect(Collectors.toList());
            Map<String, IObjectData> activityMap = queryActivity(actionContext, activityIds).stream().collect(Collectors.toMap(DBRecord::getId, v -> v));
            Map<String, List<IObjectData>> detailsMap = queryActivityProofDetails(actionContext, masterIds).stream().collect(Collectors.groupingBy(detail -> (String) detail.get(TPMActivityProofAuditDetailFields.ACTIVITY_PROOF_AUDIT_ID)));
            Map<String, String> itemNameMap = queryActivityItem();
            data.setActivityProofList(Lists.newArrayList());
            for (IObjectData master : masterList) {
                ActivityProofDTO datum = new ActivityProofDTO();
                datum.setProofId(master.getId());
                datum.setRemark((String) master.get(TPMActivityProofAuditFields.OPINION));
                datum.setAuditStatus((String) master.get(TPMActivityProofAuditFields.AUDIT_STATUS));
                datum.setOpinion((String) master.get(TPMActivityProofAuditFields.OPINION));
                if (!Strings.isNullOrEmpty((String) master.get(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS))) {
                    datum.setRandomAuditStatus((String) master.get(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS));
                }
                String activityId = (String) master.get(TPMActivityProofAuditFields.ACTIVITY_ID);
                if (activityMap.containsKey(activityId)) {
                    datum.setActivityName(activityMap.get(activityId).getName());
                }
                if (master.get(TPMActivityProofAuditFields.AUDIT_IMAGES) != null) {
                    datum.setImages(JSON.parseArray(JSON.toJSONString(master.get(TPMActivityProofAuditFields.AUDIT_IMAGES)), ActivityProofImageDTO.class));
                } else {
                    datum.setImages(Lists.newArrayList());
                }
                datum.setImagesTotalCount(CollectionUtils.isEmpty(datum.getImages()) ? 0L : datum.getImages().size());
                datum.setDetails(Lists.newArrayList());
                if (detailsMap.containsKey(master.getId())) {
                    List<IObjectData> details = detailsMap.get(master.getId());
                    for (IObjectData detail : details) {
                        ActivityProofDetailDTO detailDatum = new ActivityProofDetailDTO();
                        String activityItemId = (String) detail.get(TPMActivityProofAuditDetailFields.ACTIVITY_ITEM_ID);
                        detailDatum.setName(itemNameMap.getOrDefault(activityItemId, "--"));
                        detailDatum.setAmount((String) detail.get(TPMActivityProofAuditDetailFields.AMOUNT));
                        detailDatum.setSubtotal((String) detail.get(TPMActivityProofAuditDetailFields.AUDIT_SUBTOTAL));
                        datum.getDetails().add(detailDatum);
                    }
                }
                data.getActivityProofList().add(datum);
            }
            data.setActivityProofListSize(data.getActivityProofList().size());
            String updateActionResult = checkinService.updateProofAuditAction(actionContext.getUser(), visitId, actionId, data);
            result.getObjectData().put("__update_action_result", updateActionResult);
        }
    }

    private void addTeamMember(List<String> dataIds, List<String> userIds) {
        PaasAddTeamMember.Arg arg = new PaasAddTeamMember.Arg();
        arg.setDataIds(dataIds);
        arg.setOutTeamMemberEmployee(Lists.newArrayList());
        arg.setOtherObjects(Lists.newArrayList());
        arg.setTemMemberRole("");
        arg.setTeamMemberRoleList(Lists.newArrayList("4"));
        arg.setTeamMemberPermissionType("2");
        arg.setTeamMemberEmployee(userIds);
        PaasAddTeamMember.Result result = paasDataProxy.addTeamMember(Integer.parseInt(actionContext.getTenantId()), -10000, ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, arg);

        log.info("add team member result : {}", result);
    }

    private Map<String, String> queryActivityItem() {
        return activityItemCache.get(actionContext.getTenantId());
    }

    private List<IObjectData> queryActivityProofDetails(ActionContext context, List<String> masterIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(TPMActivityProofAuditDetailFields.ACTIVITY_PROOF_AUDIT_ID);
        masterFilter.setOperator(Operator.IN);
        masterFilter.setFieldValues(masterIds);

        query.setFilters(Lists.newArrayList(masterFilter));
        return QueryDataUtil.find(
                serviceFacade,
                context.getTenantId(),
                ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ,
                query,
                Lists.newArrayList(
                        CommonFields.ID,
                        TPMActivityProofAuditDetailFields.ACTIVITY_PROOF_AUDIT_ID,
                        TPMActivityProofAuditDetailFields.ACTIVITY_ITEM_ID,
                        TPMActivityProofAuditDetailFields.AMOUNT,
                        TPMActivityProofAuditDetailFields.AUDIT_SUBTOTAL
                ));
    }

    private List<IObjectData> queryActivity(ActionContext context, List<String> ids) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);

        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(ids);

        query.setFilters(Lists.newArrayList(idFilter));
        return QueryDataUtil.find(serviceFacade, context.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, query, Lists.newArrayList(CommonFields.ID, CommonFields.NAME));
    }

    private List<IObjectData> queryProof(ActionContext context, String storeId, String visitId, String actionId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);


        if (!Strings.isNullOrEmpty(storeId)) {
            Filter storeIdFilter = new Filter();
            storeIdFilter.setFieldName(TPMActivityProofAuditFields.STORE_ID);
            storeIdFilter.setOperator(Operator.EQ);
            storeIdFilter.setFieldValues(Lists.newArrayList(storeId));
            query.getFilters().add(storeIdFilter);
        }


        Filter visitIdFilter = new Filter();
        visitIdFilter.setFieldName(TPMActivityProofAuditFields.VISIT_ID);
        visitIdFilter.setOperator(Operator.EQ);
        visitIdFilter.setFieldValues(Lists.newArrayList(visitId));
        query.getFilters().add(visitIdFilter);

        Filter actionIdFilter = new Filter();
        actionIdFilter.setFieldName(TPMActivityProofAuditFields.ACTION_ID);
        actionIdFilter.setOperator(Operator.EQ);
        actionIdFilter.setFieldValues(Lists.newArrayList(actionId));
        query.getFilters().add(actionIdFilter);


        return QueryDataUtil.find(
                serviceFacade,
                context.getTenantId(),
                ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ,
                query,
                Lists.newArrayList(
                        CommonFields.ID,
                        TPMActivityProofAuditFields.OPINION,
                        TPMActivityProofAuditFields.AUDIT_STATUS,
                        TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS,
                        TPMActivityProofAuditFields.ACTIVITY_ID,
                        TPMActivityProofAuditFields.AUDIT_IMAGES
                )
        );
    }

    private void loadRelatedData() {
        String activityId = (String) arg.getObjectData().get(TPMActivityProofAuditFields.ACTIVITY_ID);

        this.activityType = activityTypeManager.findByActivityId(actionContext.getTenantId(), activityId);
        this.activity = serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);
        String recordType = (String) arg.getObjectData().get(CommonFields.RECORD_TYPE);
        this.isAudit = activityType.auditNode().getObjectRecordType().equals(recordType);
    }

    private void validateReferenceProofFieldRequired() {
        String referenceFieldApiName = this.activityType.auditSourceConfig().getReferenceAuditSourceFieldApiName();
        if (Strings.isNullOrEmpty((String) arg.getObjectData().get(referenceFieldApiName))) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.ACTIVITY_PROOF_AUDIT_OBJ_ADD_ACTION_3), referenceFieldApiName));
        }
    }

    private void auditedValidate() {
        if (!this.isAudit) {
            return;
        }
        if (TPMActivityFields.CLOSE_STATUS__CLOSED.equals(this.activity.get(TPMActivityFields.CLOSED_STATUS, String.class))) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_AUDIT_OBJ_ADD_ACTION_4));
        }
        ActivityProofAuditSourceConfigEntity source = this.activityType.auditSourceConfig();
        String auditedId = (String) arg.getObjectData().get(source.getReferenceAuditSourceFieldApiName());

        if (!Strings.isNullOrEmpty(auditedId)) {

            SearchTemplateQuery query = new SearchTemplateQuery();

            query.setLimit(1);
            query.setOffset(0);
            query.setSearchSource("db");
            query.setNeedReturnCountNum(false);

            Filter sourceFilter = new Filter();
            sourceFilter.setFieldName(source.getReferenceAuditSourceFieldApiName());
            sourceFilter.setFieldValues(Lists.newArrayList(auditedId));
            sourceFilter.setOperator(Operator.EQ);

            Filter recordTypeFilter = new Filter();
            recordTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
            recordTypeFilter.setFieldValues(Lists.newArrayList(this.activityType.auditNode().getObjectRecordType()));
            recordTypeFilter.setOperator(Operator.EQ);

            query.setFilters(Lists.newArrayList(sourceFilter, recordTypeFilter));
            if (!QueryDataUtil.find(serviceFacade, actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, query, Lists.newArrayList("_id")).isEmpty()) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_AUDIT_OBJ_ADD_ACTION_5));
            }
        }
    }

    private void setDefaultValue() {
        String auditStatus = (String) arg.getObjectData().get(TPMActivityProofAuditFields.AUDIT_STATUS);
        if (!auditStatus.equals(TPMActivityProofAuditFields.AUDIT_STATUS__PASS) && !auditStatus.equals(TPMActivityProofAuditFields.AUDIT_STATUS__REJECT)) {
            throw new ValidateException("audit status can not be null");
        }

        List<String> auditor = CommonUtils.cast(arg.getObjectData().get(TPMActivityProofAuditFields.AUDITOR), String.class);
        String randomAuditStatus = (String) arg.getObjectData().get(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS);
        if (CollectionUtils.isEmpty(auditor) && Strings.isNullOrEmpty(randomAuditStatus)) {
            arg.getObjectData().put(TPMActivityProofAuditFields.AUDITOR, Lists.newArrayList(actionContext.getUser().getUpstreamOwnerIdOrUserId()));
        } else if (CollectionUtils.isEmpty(auditor) && !Strings.isNullOrEmpty(randomAuditStatus)) {
            arg.getObjectData().put(TPMActivityProofAuditFields.AUDITOR, Lists.newArrayList("-10000"));
        }

        Long auditTime = arg.getObjectData().toObjectData().get(TPMActivityProofAuditFields.AUDIT_TIME, Long.class);
        if (Objects.isNull(auditTime)) {
            arg.getObjectData().put(TPMActivityProofAuditFields.AUDIT_TIME, System.currentTimeMillis());
        }

        arg.getDetails().forEach((k, v) -> {
            if (ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ.equals(k)) {
                List<String> activityProofDetailIds = new ArrayList<>();
                v.forEach(detail -> {
                    String activityProofDetail = (String) detail.get(TPMActivityProofAuditDetailFields.ACTIVITY_PROOF_DETAIL_ID);
                    if (Strings.isNullOrEmpty(activityProofDetail)) {
                        return;
                    }
                    activityProofDetailIds.add(activityProofDetail);
                });
                if (CollectionUtils.isEmpty(activityProofDetailIds)) {
                    return;
                }
                Map<String, IObjectData> activityItemObjMap = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), activityProofDetailIds, ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ)
                        .stream().collect(Collectors.toMap(DBRecord::getId, Function.identity()));

                v.forEach(detail -> {
                    String activityProofDetail = (String) detail.get(TPMActivityProofAuditDetailFields.ACTIVITY_PROOF_DETAIL_ID);
                    if (Strings.isNullOrEmpty(activityProofDetail) || !activityItemObjMap.containsKey(activityProofDetail)) {
                        return;
                    }
                    IObjectData activityItemObj = activityItemObjMap.get(activityProofDetail);
                    String auditItem = (String) detail.get(TPMActivityProofAuditDetailFields.AUDIT_ITEM);
                    if (Strings.isNullOrEmpty(auditItem)) {
                        detail.put(TPMActivityProofAuditDetailFields.AUDIT_ITEM, activityItemObj.getName());
                    }
                    // 补充有默认值的字段的值、web端的检核是客开的页面，不会走默认值带出
                    // 活动项目
                    String activityDetailId = (String) detail.get(TPMActivityProofAuditDetailFields.ACTIVITY_DETAIL_ID);
                    if (Strings.isNullOrEmpty(activityDetailId)) {
                        detail.put(TPMActivityProofAuditDetailFields.ACTIVITY_DETAIL_ID, activityItemObj.get(TPMActivityProofDetailFields.ACTIVITY_DETAIL_ID));
                    }
                    // 费用项目
                    String activityItemId = (String) detail.get(TPMActivityProofAuditDetailFields.ACTIVITY_ITEM_ID);
                    if (Strings.isNullOrEmpty(activityItemId)) {
                        detail.put(TPMActivityProofAuditDetailFields.ACTIVITY_ITEM_ID, activityItemObj.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID));
                    }
                    // 活动协议项目
                    String agreementDetailId = (String) detail.get(TPMActivityProofAuditDetailFields.ACTIVITY_AGREEMENT_DETAIL_ID);
                    if (Strings.isNullOrEmpty(agreementDetailId)) {
                        detail.put(TPMActivityProofAuditDetailFields.ACTIVITY_AGREEMENT_DETAIL_ID, activityItemObj.get(TPMActivityProofDetailFields.ACTIVITY_AGREEMENT_DETAIL_ID));
                    }
                });
            }
        });
    }

    private void addTeamMember(Result result) {
        try {
            List<String> members = Lists.newArrayList();
            members.addAll(CommonUtils.cast(result.getObjectData().get(TPMActivityProofAuditFields.AUDITOR), String.class)
                    .stream().filter(f -> !f.equals("-10000")).collect(Collectors.toList()));
            members.addAll(CommonUtils.cast(result.getObjectData().get(TPMActivityProofAuditFields.INSPECTOR), String.class)
                    .stream().filter(f -> !f.equals("-10000")).collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(members)) {
                addTeamMember(Lists.newArrayList(result.getObjectData().getId()), members);
            }
        } catch (Exception ex) {
            log.error("add team member cause unknown exception : ", ex);
        }
    }

    private void updateSourceData(Result result) {
        try {
            ActivityProofAuditSourceConfigEntity sourceConfigEntity = this.activityType.auditSourceConfig();
            if (ApiNames.TPM_ACTIVITY_PROOF_OBJ.equals(sourceConfigEntity.getMasterApiName())) {
                String activityProofId = (String) result.getObjectData().get(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID);
                if (!Strings.isNullOrEmpty(activityProofId)) {
                    IObjectData proof = serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), activityProofId, ApiNames.TPM_ACTIVITY_PROOF_OBJ);
                    Map<String, Object> updateMap = new HashMap<>(4);
                    updateMap.put(TPMActivityProofFields.AUDIT_STATUS, result.getObjectData().get(TPMActivityProofAuditFields.AUDIT_STATUS));
                    if (!Strings.isNullOrEmpty((String) result.getObjectData().get(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS))) {
                        updateMap.put(TPMActivityProofFields.RANDOM_AUDIT_STATUS, result.getObjectData().get(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS));
                    }
                    serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), proof, updateMap);
                }
            } else {
                if (!Strings.isNullOrEmpty(sourceConfigEntity.getAuditStatusApiName())) {
                    String auditedId = (String) result.getObjectData().get(sourceConfigEntity.getReferenceAuditSourceFieldApiName());
                    Map<String, Object> updateMap = new HashMap<>(2);
                    IObjectData auditedObject = serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), auditedId, sourceConfigEntity.getMasterApiName());
                    updateMap.put(sourceConfigEntity.getAuditStatusApiName(), result.getObjectData().get(TPMActivityProofAuditFields.AUDIT_STATUS));
                    serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), auditedObject, updateMap);
                } else {
                    log.info("activity type {} is no audit status field", activityType.get().getId().toString());
                }
            }
        } catch (Exception ex) {
            log.error("update source data cause unknown exception : ", ex);
        }
    }

    private void lockSourceData(Result result) {
        try {
            ActivityProofAuditSourceConfigEntity source = this.activityType.auditSourceConfig();
            String referenceFieldApiName = source.getReferenceAuditSourceFieldApiName();
            String apiName = source.getMasterApiName();
            String dataId = (String) result.getObjectData().get(referenceFieldApiName);
            IObjectData sourceData = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), dataId, apiName);
            serviceFacade.bulkLockObjectData(Lists.newArrayList(sourceData), true, "default_lock_rule", User.systemUser(actionContext.getTenantId()));
        } catch (Exception ex) {
            log.error("lock source data cause unknown exception : ", ex);
        }
    }

    private void asyncAddTpmLog(Result result) {
        try {
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.valueOf(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_ACTIVITY_PROOF_AUDIT, BuryOperation.CREATE, true);
            BigDecimal auditTotal = result.getObjectData().toObjectData().get(TPMActivityProofAuditFields.AUDIT_TOTAL, BigDecimal.class, BigDecimal.ZERO);
            BuryService.asyncTpmAmountLog(Integer.valueOf(actionContext.getTenantId()), actionContext.getUser().getUserIdInt(), BuryModule.TPM_AMOUNT.AUDIT_TOTAL, BuryOperation.CREATE, auditTotal.doubleValue());
        } catch (Exception ex) {
            log.warn("async add TPM log error : {}", actionContext.getTenantId(), ex);
        }
    }
}
