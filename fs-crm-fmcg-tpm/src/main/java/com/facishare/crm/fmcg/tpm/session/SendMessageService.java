package com.facishare.crm.fmcg.tpm.session;

import com.fxiaoke.model.message.*;

/**
 * author: wuyx
 * description: 通过消息平台发送消息
 * createTime: 2022/2/11 11:36
 */
public interface SendMessageService {

    /**
     * 发送文本消息
     */
    void sendTextMessage(SendTextMessageArg arg);

    /**
     * 发送文本链接（不带格式）消息
     */
    void sendTextLinkMessage(SendTextLinkMessageArg arg);

    /**
     * 发送文本卡片（带格式）消息
     */
    void sendTextCardMessage(SendTextCardMessageArg arg);

    /**
     * 发送图文消息
     */
    void sendImageCardMessage(SendImageCardMessageArg arg);

    /**
     * 清理应用通知漂数
     */
    void cleanNotReadCount(CleanNotReadCountArg arg);
}
