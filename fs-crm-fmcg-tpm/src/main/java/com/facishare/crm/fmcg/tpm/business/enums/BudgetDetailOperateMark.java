package com.facishare.crm.fmcg.tpm.business.enums;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
public enum BudgetDetailOperateMark {
    APPROVAL_COMPLETE("审批操作完成", "approval_complete"),
    CONSUME_DIRECT_DEDUCTION("消费直接扣减场景", "consume_direct_deduction"),
    CONSUME_FREEZE_THEN_DEDUCTION_FIRST("先冻结后扣减场景-冻结", "consume_freeze_then_deduction_first"),
    CONSUME_FREEZE_THEN_DEDUCTION_SECOND("先冻结后扣减-扣减场景", "consume_freeze_then_deduction_second"),
    CLOSE_ACTIVITY("结案返还","close_activity"),
    COMPLETED_UNFREEZE("完全解冻", "completed_freeze"),
    OPERATE_BEFORE_APPROVAL("审批前的操作","operate_before_approval"),
    APPROVAL_ROLLBACK("审批回退","approval_rollback"),
    SCRIPT_DELETE("脚本删除", "script_delete"),
    MIDDLE_RELEASE("中间释放", "middle_release"),;

    private static final Map<String, BudgetDetailOperateMark> innerMap = Stream.of(values()).collect(Collectors.toMap(BudgetDetailOperateMark::value, v -> v));

    BudgetDetailOperateMark(String label, String value) {
        this.label = label;
        this.value = value;
    }

    private final String label;
    private final String value;

    public String value() {
        return this.value;
    }

    public String label() {
        return this.label;
    }

    public static BudgetDetailOperateMark of(String value) {
        return innerMap.get(value);
    }
}
