package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.business.ListHeaderBusiness;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 4:39 PM
 */
public class TPMBudgetAccountDetailObjListHeaderController extends StandardListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {
        ListHeaderBusiness.listHeaderFilter(controllerContext.getTenantId(), ApiNames.TPM_BUDGET_ACCOUNT_DETAIL, result);
        return super.after(arg, result);
    }
}
