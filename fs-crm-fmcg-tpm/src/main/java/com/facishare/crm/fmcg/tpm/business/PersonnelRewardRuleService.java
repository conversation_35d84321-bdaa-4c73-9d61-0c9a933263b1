package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.tpm.business.abstraction.IPersonnelRewardRuleService;
import com.facishare.crm.fmcg.tpm.web.condition.ConditionAdapter;
import com.facishare.crm.fmcg.tpm.web.condition.model.ConditionDto;
import com.facishare.crm.fmcg.tpm.web.contract.model.PersonRewardRuleTriggerConditionVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.PersonRewardRuleWhereConditionVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class PersonnelRewardRuleService implements IPersonnelRewardRuleService {

    @Resource
    private ConditionAdapter conditionAdapter;

    @Override
    public String buildConditionCode(String tenantId, Integer employeeId, String apiName, String ruleCode, List<PersonRewardRuleWhereConditionVO> whereConditions) {
        if (CollectionUtils.isEmpty(whereConditions)) {
            return null;
        }
        List<ConditionDto> conditionDtoList = new ArrayList<>();
        int i = 0;
        // (0 and 1) or (2 and 3)
        StringBuilder pattern = new StringBuilder();
        for (PersonRewardRuleWhereConditionVO whereCondition : whereConditions) {
            if (StringUtils.isNotEmpty(pattern.toString())) {
                pattern.append(" or ");
            }
            List<String> rowList = new ArrayList<>();
            for (PersonRewardRuleTriggerConditionVO triggerCondition : whereCondition.getTriggerConditions()) {
                ConditionDto conditionDto = new ConditionDto();
                conditionDto.setFieldName(triggerCondition.getFieldName());
                conditionDto.setValues(triggerCondition.getFieldValues());
                conditionDto.setOperator(triggerCondition.getOperator());
                conditionDto.setFieldType(triggerCondition.getType());
                conditionDto.setRowNo(i++);
                conditionDtoList.add(conditionDto);
                rowList.add(String.valueOf(conditionDto.getRowNo()));
            }
            if (CollectionUtils.isEmpty(rowList)) {
                continue;
            }
            String order;
            if (rowList.size() > 1) {
                order = String.join(" and ", rowList);
            } else {
                order = rowList.get(0);
            }
            pattern.append("(").append(order).append(")");
        }
        if (CollectionUtils.isEmpty(conditionDtoList) && StringUtils.isEmpty(pattern.toString())) {
            return null;
        }
        if (StringUtils.isEmpty(ruleCode)) {
            //设置 生成条件的code
            return conditionAdapter.publish(Integer.valueOf(tenantId), employeeId, apiName, pattern.toString(), conditionDtoList);
        } else {
            // RULE UPDATE
            conditionAdapter.update(Integer.valueOf(tenantId), employeeId, apiName, ruleCode, pattern.toString(), conditionDtoList);
            return ruleCode;
        }

    }
}
