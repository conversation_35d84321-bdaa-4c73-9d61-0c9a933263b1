package com.facishare.crm.fmcg.tpm.business.dto;

import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/10/20 17:40
 */
@Data
@ToString
@Builder
@SuppressWarnings("Duplicates")
public class FiscalQuarter implements Serializable {

    private int year;

    private int quarter;

    private long start;

    private long end;

    private List<FiscalMonth> month;

    public static FiscalQuarter of(int year, int quarter, List<FiscalMonth> month) {
        if (CollectionUtils.isEmpty(month)) {
            throw new MetaDataBusinessException("quarter month empty.");
        }
        return FiscalQuarter
                .builder()
                .year(year)
                .quarter(quarter)
                .month(month)
                .start(month.stream().mapToLong(FiscalMonth::getStart).min().orElse(0))
                .end(month.stream().mapToLong(FiscalMonth::getEnd).max().orElse(0))
                .build();
    }
}
