package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

/**
 * <AUTHOR>
 * @date 2022/9/26 下午5:35
 */
public class TPMStoreWriteOffObjListController extends StandardListController {

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        if (TPMGrayUtils.TPMStoreUseEs(controllerContext.getTenantId())) {
            query.setSearchSource("es");
        }
        super.beforeQueryData(query);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        removeMobileButton(arg, result);
        return super.after(arg, result);
    }

    private void removeMobileButton(Arg arg, Result result) {
        log.info("isMobileOrH5Request result :" + RequestUtil.isMobileOrH5Request());
        if (RequestUtil.isMobileOrH5Request()) {
            if (arg.isIncludeButtonInfo()) {
                if (result.getButtonInfo().getButtonMap() != null) {
                    for (ObjectDataDocument obj : result.getDataList()) {
                        if (result.getButtonInfo().getButtonMap().containsKey(obj.getId())) {
                            result.getButtonInfo().getButtonMap().get(obj.getId()).remove(ObjectAction.COST_WRITE_OFF.getButtonApiName());
                        }
                    }
                }
            }
        }
    }
}
