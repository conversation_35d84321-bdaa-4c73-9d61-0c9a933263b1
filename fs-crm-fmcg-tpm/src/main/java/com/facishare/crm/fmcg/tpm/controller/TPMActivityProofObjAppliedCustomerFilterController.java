package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.api.proof.AppliedCustomerFilter;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofAuditFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofFields;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/2/20 4:12 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityProofObjAppliedCustomerFilterController extends PreDefineController<AppliedCustomerFilter.Arg, AppliedCustomerFilter.Result> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }

    @Override
    protected AppliedCustomerFilter.Result doService(AppliedCustomerFilter.Arg arg) {

        log.info("applied customer filter arg : {}", JSON.toJSONString(arg));

        if (CollectionUtils.isEmpty(arg.getCheckinIdList())) {
            // throw exception when customer id list is null or empty
            throw new ValidateException("Stop this madness remote call. HaiZi!");
        }

        List<IObjectData> data = queryProof(controllerContext, arg.getBegin(), arg.getEnd(), arg.getActivityId(), arg.getDealerId(), arg.getCheckinIdList());
        List<String> checkinsIdList = data.stream().map(m -> (String) m.get(TPMActivityProofFields.VISIT_ID)).collect(Collectors.toList());

        log.info("result checkins id list : {}", checkinsIdList);

        return AppliedCustomerFilter.Result.builder()
                .checkinIdList(checkinsIdList)
                .build();
    }

    private List<IObjectData> queryProof(ControllerContext context, Long begin, Long end, String activityId, String dealerId, List<String> visitIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter visitIdFilter = new Filter();
        visitIdFilter.setFieldName(TPMActivityProofAuditFields.VISIT_ID);
        visitIdFilter.setOperator(Operator.IN);
        visitIdFilter.setFieldValues(visitIds);

        query.setFilters(Lists.newArrayList(visitIdFilter));

        if (!Strings.isNullOrEmpty(activityId)) {
            Filter activityIdFilter = new Filter();
            activityIdFilter.setFieldName(TPMActivityProofAuditFields.ACTIVITY_ID);
            activityIdFilter.setOperator(Operator.EQ);
            activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

            query.getFilters().add(activityIdFilter);
        }

        if (!Strings.isNullOrEmpty(dealerId)) {
            Filter dealerIdFilter = new Filter();
            dealerIdFilter.setFieldName(TPMActivityProofAuditFields.DEALER_ID);
            dealerIdFilter.setOperator(Operator.EQ);
            dealerIdFilter.setFieldValues(Lists.newArrayList(dealerId));

            query.getFilters().add(dealerIdFilter);
        }

        if (begin != null && end != null && begin > 0 && end > 0) {
            Filter timeFilter = new Filter();
            timeFilter.setFieldName(CommonFields.CREATE_TIME);
            timeFilter.setOperator(Operator.BETWEEN);
            timeFilter.setFieldValues(Lists.newArrayList(String.valueOf(begin), String.valueOf(end)));

            query.getFilters().add(timeFilter);
        }

        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query);
    }
}