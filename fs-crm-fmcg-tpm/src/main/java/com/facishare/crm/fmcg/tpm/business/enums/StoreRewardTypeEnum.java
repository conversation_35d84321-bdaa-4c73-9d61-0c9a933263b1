package com.facishare.crm.fmcg.tpm.business.enums;

/**
 * 门店奖励类型枚举
 * Author: Generated
 * Date: 2025-08-11
 */
public enum StoreRewardTypeEnum {
    RED_PACKET("red_packet", "红包"),
    COUPON("coupon", "优惠券"),
    POINTS("points", "积分"),
    PHYSICAL_GIFT("physical_gift", "实物奖品"),
    DISCOUNT("discount", "折扣"),
    CASH_BACK("cash_back", "返现");

    private final String code;

    private final String description;

    StoreRewardTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String code() {
        return code;
    }

    public String description() {
        return description;
    }
}
