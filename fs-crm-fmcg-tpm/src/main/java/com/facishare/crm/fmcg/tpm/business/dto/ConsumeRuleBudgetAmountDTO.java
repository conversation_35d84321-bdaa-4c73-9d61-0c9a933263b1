package com.facishare.crm.fmcg.tpm.business.dto;


import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetNewConsumeRulePO;
import com.facishare.crm.fmcg.tpm.utils.Pair;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Map;

@Data
@ToString
public class ConsumeRuleBudgetAmountDTO {

    public ConsumeRuleBudgetAmountDTO(BudgetNewConsumeRulePO budgetNewConsumeRulePO, String businessId, String traceId) {
        this.budgetNewConsumeRulePO = budgetNewConsumeRulePO;
        this.traceId = traceId;
        this.businessId = businessId;
    }

    private BudgetNewConsumeRulePO budgetNewConsumeRulePO;

    private String businessId;

    private String traceId;

    private Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap;

    private Map<String, IObjectData> withholdingMoneyMap;
}
