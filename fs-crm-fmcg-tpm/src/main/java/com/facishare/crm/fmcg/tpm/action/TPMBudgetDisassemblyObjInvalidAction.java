package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;


@SuppressWarnings("all")
public class TPMBudgetDisassemblyObjInvalidAction extends StandardInvalidAction {

    @Override
    protected void before(Arg arg) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        int offset = 0;
        query.setOffset(offset);
        query.setLimit(1);


        Filter filter = new Filter();
        filter.setFieldName("budget_disassembly_id");
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(arg.getObjectDataId()));


        query.setFilters(Lists.newArrayList(filter));

        List<IObjectData> newDetailObj = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_BUDGET_DISASSEMBLY_NEW_DETAIL_OBJ, query).getData();
        List<IObjectData> existDetailObj = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_BUDGET_DISASSEMBLY_EXISTS_DETAIL_OBJ, query).getData();

        if (CollectionUtils.isEmpty(newDetailObj) && CollectionUtils.isEmpty(existDetailObj)) {
            //全是空可以删除
            super.before(arg);
            return;
        }
        throw new ValidateException("invalid action not allowed!");
    }
}