package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSON;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetStatisticTableFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetStatisticTableService;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetDimensionEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ControlStrategyType;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.web.contract.BudgetStatisticTableRefresh;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.BaseService;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * author: wuyx
 * description:
 * createTime: 2022/8/5 14:44
 */
//IgnoreI18nFile
@Slf4j
@Service
public class BudgetStatisticTableService extends BaseService implements IBudgetStatisticTableService {

    @Resource(name = "tpmOrganizationService")
    private OrganizationService organizationService;

    @Resource
    private BudgetTypeDAO budgetTypeDAO;

    @Resource
    private RedissonClient redissonCmd;

    protected static final Map<Integer, Integer> START_MONTH_OF_QUARTER = Maps.newHashMap();

    static {
        START_MONTH_OF_QUARTER.put(1, Calendar.JANUARY);
        START_MONTH_OF_QUARTER.put(2, Calendar.APRIL);
        START_MONTH_OF_QUARTER.put(3, Calendar.JULY);
        START_MONTH_OF_QUARTER.put(4, Calendar.OCTOBER);
    }

    @Override
    public void asyncDoStatistic(String tenantId, IObjectData account) {
        ParallelUtils.createParallelTask().submit(MonitorTaskWrapper.wrap(() -> doStatistic(tenantId, account))).run();
    }

    @Override
    public void asyncDoStatistic(String tenantId, List<IObjectData> accounts) {
        if (CollectionUtils.isNotEmpty(accounts)) {
            ParallelUtils.createParallelTask().submit(MonitorTaskWrapper.wrap(() -> {
                for (IObjectData account : accounts) {
                    doStatistic(tenantId, account);
                }
            })).run();
        }
    }

    private void doStatistic(String tenantId, IObjectData account) {
        try {
            String nodeId = account.get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class);
            BudgetTypeNodeEntity node = budgetTypeDAO.getNodeById(tenantId, nodeId);

            IObjectDescribe describe = serviceFacade.findObject(tenantId, ApiNames.TPM_BUDGET_ACCOUNT);

            List<IObjectData> budgetAccountList = Lists.newArrayList(account);
            serviceFacade.fillObjectDataWithRefObject(describe, budgetAccountList, User.systemUser(tenantId), null, false);

            if (ControlStrategyType.UNLIMITED.value().equals(node.getControlStrategy())) {
                return;
            }
            if (ControlStrategyType.FULL_LIMIT.value().equals(node.getControlStrategy())) {
                node.setControlDimensions(node.getDimensions());
            }

            List<String> dimensions = node.getDimensions().stream().map(BudgetDimensionEntity::getApiName).collect(Collectors.toList());
            List<String> controlDimensions = node.getControlDimensions().stream().map(BudgetDimensionEntity::getApiName).collect(Collectors.toList());

            String timeDimension = Strings.isNullOrEmpty(node.getTimeDimension()) ? "" : node.getTimeDimension();
            String controlTimeDimension = Strings.isNullOrEmpty(node.getControlTimeDimension()) ? "" : node.getControlTimeDimension();

            if (!CollectionUtils.isEqualCollection(dimensions, controlDimensions) || !timeDimension.equals(controlTimeDimension)) {
                String id = findOrCreateStatisticsTable(tenantId, timeDimension, controlTimeDimension, controlDimensions, account);

                Map<String, Object> updateMap = Maps.newHashMap();
                updateMap.put(TPMBudgetAccountFields.BUDGET_STATISTIC_TABLE_ID, id);

                log.info("update statistic  arg - account : {}, map : {}", account, updateMap);
                IObjectData updateResult = serviceFacade.updateWithMap(User.systemUser(tenantId), account, updateMap);
                log.info("update statistic result : {}", updateResult);
            }
        } catch (Exception ex) {
            log.error("do statistic cause unknown exception : ", ex);
        }
    }

    private String findOrCreateStatisticsTable(
            String tenantId,
            String timeDimension,
            String controlTimeDimension,
            List<String> controlDimensions,
            IObjectData account) {
        long controlTimeValue = calculateControlTimeDimensionValue(timeDimension, controlTimeDimension, account);
        try {
            if (tryLock(tenantId, account, controlDimensions, controlTimeDimension, controlTimeValue)) {
                String id = findStatisticsTable(tenantId, controlTimeDimension, controlDimensions, controlTimeValue, account);
                if (!Strings.isNullOrEmpty(id)) {
                    return id;
                }
                return createStatisticsTable(tenantId, controlTimeDimension, controlDimensions, controlTimeValue, account);
            } else {
                throw new MetaDataBusinessException(String.format("try lock budget statistic fail - %s", account.getId()));
            }
        } finally {
            unlock(tenantId, account, controlDimensions, controlTimeDimension, controlTimeValue);
        }
    }

    private void unlock(String tenantId, IObjectData account, List<String> controlDimensions, String controlTimeDimension, long controlTimeValue) {
        String key = initLockKey(tenantId, account, controlDimensions, controlTimeDimension, controlTimeValue);
        RLock lock = redissonCmd.getLock(key);
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
        log.info("unlock budget statistic : {}", key);
    }

    private boolean tryLock(String tenantId, IObjectData account, List<String> controlDimensions, String controlTimeDimension, long controlTimeValue) {
        String key = initLockKey(tenantId, account, controlDimensions, controlTimeDimension, controlTimeValue);
        RLock lock = redissonCmd.getLock(key);
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            return true;
        }
        try {
            boolean result = lock.tryLock(30, 120, TimeUnit.SECONDS);
            log.info("try lock finished - key : {}, result : {}.", key, result);
            return result;
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new MetaDataBusinessException(String.format("try lock budget statistic cause thread interrupt exception - %s", key));
        }
    }

    private String initLockKey(String tenantId, IObjectData account, List<String> controlDimensions, String controlTimeDimension, long controlTimeValue) {
        StringBuilder key = new StringBuilder();
        key.append(tenantId);
        key.append(".");
        String budgetTypeId = account.get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class);
        key.append(budgetTypeId);
        key.append("_");
        String budgetNodeId = account.get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class);
        key.append(budgetNodeId);
        key.append(".");
        List<String> departments = CommonUtils.cast(account.get(TPMBudgetAccountFields.BUDGET_DEPARTMENT), String.class);
        if (CollectionUtils.isNotEmpty(departments)) {
            key.append(String.format("D_%s", departments.get(0)));
            key.append(".");
        }
        key.append(controlTimeDimension);
        key.append("_");
        key.append(controlTimeValue);
        key.append(".");
        controlDimensions = controlDimensions.stream().sorted().map(m -> String.format("%s_%s", m, account.get(m, String.class))).collect(Collectors.toList());
        key.append(String.join(".", controlDimensions));
        return String.format("FMCG.BUDGET_STA_ADD_KEY.%s", Base64Utils.encodeToString(key.toString().getBytes(StandardCharsets.UTF_8)));
    }

    @Override
    public BudgetStatisticTableRefresh.Result refresh(String tenantId, User user, String id) {
        if (Strings.isNullOrEmpty(id)) {
            throw new ValidateException("statistic table id can not be null or empty.");
        }
        IObjectData data = serviceFacade.findObjectData(User.systemUser(tenantId), id, ApiNames.TPM_BUDGET_STATISTIC_TABLE);
        if (Objects.isNull(data)) {
            throw new ValidateException("statistic table not exists.");
        }

        List<IObjectData> accounts = queryBudgetAccountsByStatisticTable(id, tenantId);
        if (CollectionUtils.isEmpty(accounts)) {
            return new BudgetStatisticTableRefresh.Result();
        }
        updateBudgetStatisticTable(id, tenantId, accounts);
        return new BudgetStatisticTableRefresh.Result();
    }

    private void updateBudgetStatisticTable(String id, String tenantId, List<IObjectData> budgetAccountsByStatisticTable) {
        BigDecimal availableAmount = budgetAccountsByStatisticTable.stream().map(data -> data.get(TPMBudgetAccountFields.AVAILABLE_AMOUNT, BigDecimal.class)).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal frozenAmount = budgetAccountsByStatisticTable.stream().map(data -> data.get(TPMBudgetAccountFields.FROZEN_AMOUNT, BigDecimal.class)).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal usedAmount = budgetAccountsByStatisticTable.stream().map(data -> data.get(TPMBudgetAccountFields.USED_AMOUNT, BigDecimal.class)).reduce(BigDecimal.ZERO, BigDecimal::add);

        IObjectData updateArg = new ObjectData();
        updateArg.setId(id);
        updateArg.setDescribeApiName(ApiNames.TPM_BUDGET_STATISTIC_TABLE);
        updateArg.set(TPMBudgetStatisticTableFields.AVAILABLE_AMOUNT, availableAmount);
        updateArg.set(TPMBudgetStatisticTableFields.FROZEN_AMOUNT, frozenAmount);
        updateArg.set(TPMBudgetStatisticTableFields.USED_AMOUNT, usedAmount);
        updateArg.set(TPMBudgetStatisticTableFields.TOTAL_AMOUNT, availableAmount.add(frozenAmount).add(usedAmount));
        IObjectData objectData = serviceFacade.updateObjectData(User.systemUser(tenantId), updateArg, true);
        if (Objects.isNull(objectData)) {
            log.info("update fail - update arg : {}", JSON.toJSONString(updateArg));
        }
    }

    private List<IObjectData> queryBudgetAccountsByStatisticTable(String id, String tenantId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setNeedReturnCountNum(false);
        Filter bizTraceIdFilter = new Filter();
        bizTraceIdFilter.setFieldName(TPMBudgetAccountFields.BUDGET_STATISTIC_TABLE_ID);
        bizTraceIdFilter.setOperator(Operator.EQ);
        bizTraceIdFilter.setFieldValues(Lists.newArrayList(id));
        query.setFilters(Lists.newArrayList(bizTraceIdFilter));
        query.setSearchSource("db");

        List<String> needCheckFields = Lists.newArrayList(CommonFields.ID, TPMBudgetAccountFields.BUDGET_STATISTIC_TABLE_ID, TPMBudgetAccountFields.AVAILABLE_AMOUNT, TPMBudgetAccountFields.FROZEN_AMOUNT, TPMBudgetAccountFields.USED_AMOUNT);
        return serviceFacade.findBySearchQueryWithFieldsIgnoreAll(User.systemUser(tenantId), ApiNames.TPM_BUDGET_ACCOUNT, query, needCheckFields).getData();
    }

    private long calculateControlTimeDimensionValue(String timeDimension, String controlTimeDimension, IObjectData account) {
        long time = account.get(String.format("budget_period_%s", timeDimension), Long.class);
        if (time == 0L) {
            throw new ValidateException("budget period value can not be empty.");
        }
        return correctPeriodTime(controlTimeDimension, time);
    }

    private String findStatisticsTable(
            String tenantId,
            String controlTimeDimension,
            List<String> controlDimensions,
            long controlTimeValue,
            IObjectData account) {
        List<String> departments = CommonUtils.cast(account.get(TPMBudgetAccountFields.BUDGET_DEPARTMENT), String.class);

        String budgetNodeId = account.get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class);
        String budgetTypeId = account.get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class);

        SearchTemplateQuery stq = new SearchTemplateQuery();
        stq.setNeedReturnCountNum(false);
        stq.setLimit(1);
        stq.setOffset(0);
        stq.setSearchSource("db");

        Filter departmentFilter = new Filter();
        departmentFilter.setFieldName(TPMBudgetStatisticTableFields.BUDGET_DEPARTMENT);
        departmentFilter.setOperator(Operator.EQ);
        departmentFilter.setFieldValues(departments);
        stq.getFilters().add(departmentFilter);

        Filter budgetNodeFilter = new Filter();
        budgetNodeFilter.setFieldName(TPMBudgetStatisticTableFields.BUDGET_NODE_ID);
        budgetNodeFilter.setOperator(Operator.EQ);
        budgetNodeFilter.setFieldValues(Lists.newArrayList(budgetNodeId));
        stq.getFilters().add(budgetNodeFilter);

        Filter budgetTypeFilter = new Filter();
        budgetTypeFilter.setFieldName(TPMBudgetStatisticTableFields.BUDGET_TYPE_ID);
        budgetTypeFilter.setOperator(Operator.EQ);
        budgetTypeFilter.setFieldValues(Lists.newArrayList(budgetTypeId));
        stq.getFilters().add(budgetTypeFilter);

        if (CollectionUtils.isNotEmpty(controlDimensions)) {
            for (String controlDimension : controlDimensions) {
                Filter dimensionFilter = new Filter();
                dimensionFilter.setFieldName(controlDimension);
                dimensionFilter.setOperator(Operator.EQ);
                dimensionFilter.setFieldValues(Lists.newArrayList(account.get(controlDimension, String.class)));
                stq.getFilters().add(dimensionFilter);
            }
        }

        Filter timeDimensionFilter = new Filter();
        timeDimensionFilter.setFieldName(String.format("budget_period_%s", controlTimeDimension));
        timeDimensionFilter.setOperator(Operator.EQ);
        timeDimensionFilter.setFieldValues(Lists.newArrayList(String.valueOf(controlTimeValue)));
        stq.getFilters().add(timeDimensionFilter);

        log.info("do statistic findOrCreateStatisticsTable  tenantId = {}  stq ={} ", tenantId, JSON.toJSONString(stq));
        List<IObjectData> data = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(User.systemUser(tenantId), ApiNames.TPM_BUDGET_STATISTIC_TABLE, stq, Lists.newArrayList("_id")).getData();
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        return data.get(0).getId();
    }

    private String createStatisticsTable(
            String tenantId,
            String controlTimeDimension,
            List<String> controlDimensions,
            long controlTimeValue,
            IObjectData account) {

        IObjectData data = new ObjectData();
        data.setRecordType("default__c");
        data.setTenantId(tenantId);
        data.setOwner(account.getOwner());
        data.setDescribeApiName(ApiNames.TPM_BUDGET_STATISTIC_TABLE);
        data.set(TPMBudgetStatisticTableFields.STATISTIC_PERIOD, account.get(TPMBudgetAccountFields.CONTROL_PERIOD, String.class));
        data.set(TPMBudgetStatisticTableFields.BUDGET_TYPE_ID, account.get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class));
        data.set(TPMBudgetStatisticTableFields.BUDGET_NODE_ID, account.get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class));
        data.set(TPMBudgetStatisticTableFields.BUDGET_DEPARTMENT, account.get(TPMBudgetAccountFields.BUDGET_DEPARTMENT));
        data.set(String.format("budget_period_%s", controlTimeDimension), controlTimeValue);

        StringBuilder dimensionNames = new StringBuilder();

        if (CollectionUtils.isNotEmpty(controlDimensions)) {
            for (String controlDimension : controlDimensions) {
                data.set(controlDimension, account.get(controlDimension, String.class));

                String dimensionName = account.get(controlDimension + "__r", String.class);
                if (Strings.isNullOrEmpty(dimensionName)) {
                    continue;
                }
                if (dimensionNames.length() == 0) {
                    dimensionNames.append(dimensionName);
                } else {
                    dimensionNames.append("-").append(dimensionName);
                }
            }
        }

        List<String> departments = CommonUtils.cast(account.get(TPMBudgetAccountFields.BUDGET_DEPARTMENT), String.class);
        int departmentId = Integer.parseInt(departments.get(0));
        DepartmentDto department = organizationService.getDepartment(Integer.parseInt(tenantId), departmentId);
        String controlPeriod = calculateControlPeriodStr(controlTimeDimension, controlTimeValue);
        data.set(TPMBudgetStatisticTableFields.BUDGET_STATISTIC_TABLE, String.format("%s%s%s预算汇总表", controlPeriod, department.getName(), dimensionNames.toString()));

        IObjectData saveResult = serviceFacade.saveObjectData(User.systemUser(tenantId), data);
        if (Objects.isNull(saveResult)) {
            throw new MetaDataBusinessException("save statistic table fail.");
        }

        BuryService.asyncBudgetLog(tenantId, -10000, BuryModule.Budget.BUDGET_STATISTIC_TABLE, BuryOperation.CREATE);
        return saveResult.getId();
    }

    @NotNull
    private String calculateControlPeriodStr(String controlTimeDimension, long controlTimeValue) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(controlTimeValue);
        switch (controlTimeDimension) {
            case "quarter":
                return String.format("%s年%s", calendar.get(Calendar.YEAR), getQuarterByMonth(calendar.get(Calendar.MONTH) + 1));
            case "month":
                return String.format("%s年%s月", calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) + 1);
            case "year":
                return String.format("%s年", calendar.get(Calendar.YEAR));
            default:
                return "";
        }
    }

    private String getQuarterByMonth(int month) {
        if (month <= 0 || month > 12) {
            return "";
        }
        if (month <= 3) {
            return "Q1";
        }
        if (month <= 6) {
            return "Q2";
        }
        if (month <= 9) {
            return "Q3";
        }
        return "Q4";
    }

    public long correctPeriodTime(String dim, long time) {
        Calendar cal = Calendar.getInstance();

        cal.setTimeInMillis(time);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);

        long v;
        switch (dim) {
            case "year":
                cal.set(Calendar.MONTH, Calendar.JANUARY);
                v = cal.getTimeInMillis();
                break;
            case "month":
                v = cal.getTimeInMillis();
                cal.add(Calendar.MONTH, 1);
                break;
            case "quarter":
                cal.set(Calendar.MONTH, startMonthOfQuarter(monthToQuarter(cal.get(Calendar.MONTH))));
                v = cal.getTimeInMillis();
                cal.add(Calendar.MONTH, 3);
                break;
            default:
                v = 0;
                break;
        }
        return v;
    }

    private int startMonthOfQuarter(int quarter) {
        return START_MONTH_OF_QUARTER.get(quarter);
    }

    private int monthToQuarter(int month) {
        if (month >= 0 && month <= 2) {
            return 1;
        } else if (month >= 3 && month <= 5) {
            return 2;
        } else if (month >= 6 && month <= 8) {
            return 3;
        } else if (month >= 9 && month <= 11) {
            return 4;
        }
        return 0;
    }
}
