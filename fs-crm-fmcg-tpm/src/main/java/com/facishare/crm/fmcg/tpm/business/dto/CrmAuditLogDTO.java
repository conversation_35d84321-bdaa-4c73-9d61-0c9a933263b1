package com.facishare.crm.fmcg.tpm.business.dto;

import io.protostuff.Tag;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/12/8 上午10:51
 */
@Data
@Builder
public class CrmAuditLogDTO {

    @Tag(1)
    private String appName; // 通过-D process.name配置服务名称
    @Tag(2)
    private String traceId; // 分布式跟踪id
    @Tag(3)
    private String tenantId; // 租户ei信息
    @Tag(4)
    private String ea; // 比如 fs，如果不设置，可以根据tenantId进行反查
    @Tag(5)
    private String userId; // 用户Id，比如 fs.3687
    @Tag(6)
    long createTime; // 消息产生时间
    @Tag(7)
    long cost; // 执行花费时间
    @Tag(8)
    private String action; // 执行的操作类型，比如create/update/delete等
    @Tag(9)
    private String status; // 状态：配失败、等待执行、执行成功、执行失败
    @Tag(10)
    private String objectApiNames; // 空格或者逗号分割的对象名称
    @Tag(11)
    private String objectIds; // 空格或者逗号分割的对象id
    @Tag(12)
    private String message; // 一句简单描述
    @Tag(13)
    private String error; // 错误描述
    @Tag(14)
    private String extra; // 更多辅助信息
    @Tag(15)
    int num; // 影响的记录条数
    @Tag(16)
    private String profile; //环境区分信息
    @Tag(17)
    private String serverIp; // 发出日志的ip
    @Tag(19)
    private String parameters; //参数
}



