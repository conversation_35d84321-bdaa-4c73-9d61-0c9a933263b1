package com.facishare.crm.fmcg.tpm.business.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.MongoPO;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * Author: linmj
 * Date: 2023/11/21 16:42
 */

@Data
@ToString
public class FilterDTO {

    @JsonProperty(value =  "field_name")
    @SerializedName( "field_name")
    @JSONField(name = "field_name")
    private String fieldName;

    @JsonProperty(value =  "operator")
    @SerializedName( "operator")
    @JSONField(name = "operator")
    private String operator;


    @JsonProperty(value =  "field_values")
    @SerializedName( "field_values")
    @JSONField(name = "field_values")
    private List fieldValues;


    public Operator getOperator() {
        return null == this.operator ? null : Operator.valueOf(this.operator);
    }
}
