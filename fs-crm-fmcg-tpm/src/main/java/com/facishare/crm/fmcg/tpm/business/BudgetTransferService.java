package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetOccupationDetailFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetTransferDetailFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetAccountDetailService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetCalculateService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetOccupyService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetTransferService;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.tpm.business.enums.MainType;
import com.facishare.crm.fmcg.tpm.common.constant.BudgetTransferDetailConstants;
import com.facishare.crm.fmcg.tpm.service.abstraction.ITransactionProxy;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/7/27 上午11:12
 */

@Slf4j
@Component
public class BudgetTransferService implements IBudgetTransferService {

    @Resource
    private IBudgetCalculateService budgetCalculateService;

    @Resource
    private IBudgetOccupyService budgetOccupyService;

    @Autowired
    private ITransactionProxy transactionProxy;

    @Resource
    private IBudgetAccountDetailService budgetAccountDetailService;

    @Resource
    private ServiceFacade serviceFacade;

    @Override
    public void realMoneyDeduction(IObjectData resultObjectData, ActionContext actionContext) {
        User systemUser = User.systemUser(actionContext.getTenantId());
        String recordType = resultObjectData.getRecordType();
        String approvalId = actionContext.getAttribute(BudgetTransferDetailConstants.TPM_APPROVAL_ID);
        String bizTraceId = actionContext.getAttribute(BudgetTransferDetailConstants.TPM_BIZ_TRACE_ID);
        boolean isApproval = Boolean.TRUE.equals(actionContext.getAttribute(BudgetTransferDetailConstants.IS_APPROVAL));
        log.info("is approval:{}",isApproval);
        transactionProxy.run(() -> {
            String occupyId = actionContext.getAttribute(BudgetTransferDetailConstants.OCCUPY_ID);
            IObjectData occupyMoneyObj = null;
            BigDecimal amount;
            if (Strings.isNullOrEmpty(occupyId) && BudgetTransferDetailConstants.NEED_OCCUPY_RECORD_TYPE_SET.contains(recordType)) {
                log.info("can not find occupy money.");
                return;
            }
            if (BudgetTransferDetailConstants.NEED_OCCUPY_RECORD_TYPE_SET.contains(recordType)) {
                occupyMoneyObj = budgetOccupyService.get(actionContext.getTenantId(), occupyId);
                amount = occupyMoneyObj.get(TPMBudgetOccupationDetailFields.AMOUNT, BigDecimal.class);
            } else {
                amount = resultObjectData.get(TPMBudgetTransferDetailFields.AMOUNT, BigDecimal.class);
            }
            String fromBudgetId = resultObjectData.get(TPMBudgetTransferDetailFields.TRANSFER_OUT_BUDGET_ACCOUNT_ID, String.class);
            String toBudgetId = resultObjectData.get(TPMBudgetTransferDetailFields.TRANSFER_IN_BUDGET_ACCOUNT_ID, String.class);
            IObjectData transferOutBudget = null;
            IObjectData transferInBudget = null;
            if (!Strings.isNullOrEmpty(fromBudgetId)) {
                transferOutBudget = serviceFacade.findObjectDataIgnoreAll(systemUser, fromBudgetId, ApiNames.TPM_BUDGET_ACCOUNT);
            }
            if (!Strings.isNullOrEmpty(toBudgetId)) {
                transferInBudget = serviceFacade.findObjectDataIgnoreAll(systemUser, toBudgetId, ApiNames.TPM_BUDGET_ACCOUNT);
            }
            if (!isApproval) {
                //没有审批的情况
                if (BudgetTransferDetailConstants.NEED_OCCUPY_RECORD_TYPE_SET.contains(recordType) && transferOutBudget != null) {
                    BizType transferOutBizType = budgetAccountDetailService.getTransferDetailBusinessType(recordType, true, false);
                    log.info("transfer out");
                    budgetAccountDetailService.add(
                            actionContext.getUser(),
                            fromBudgetId,
                            MainType.EXPENDITURE,
                            transferOutBizType,
                            amount,
                            resultObjectData,
                            bizTraceId);
                }
                if (BudgetTransferDetailConstants.NEED_ADD_RECORD_TYPE_SET.contains(recordType) && transferInBudget != null) {
                    BizType transferInBizType = budgetAccountDetailService.getTransferDetailBusinessType(recordType, false, false);
                    log.info("transfer in");
                    budgetAccountDetailService.add(
                            actionContext.getUser(),
                            toBudgetId,
                            MainType.INCOME,
                            transferInBizType,
                            amount,
                            resultObjectData,
                            bizTraceId);
                }
            } else {
                //有审批的情况
                if (BudgetTransferDetailConstants.NEED_OCCUPY_RECORD_TYPE_SET.contains(recordType) && transferOutBudget != null) {
                    BizType transferOutBizType = budgetAccountDetailService.getTransferDetailBusinessType(recordType, true, true);
                    log.info("transfer out");
                    budgetAccountDetailService.add(
                            actionContext.getUser(),
                            fromBudgetId,
                            MainType.FREEZE,
                            transferOutBizType,
                            amount,
                            resultObjectData,
                            bizTraceId,
                            approvalId);
                }
            }
            if (!Strings.isNullOrEmpty(occupyId)) {
                budgetOccupyService.release(systemUser.getTenantId(), Lists.newArrayList(occupyId));
            }
            budgetCalculateService.recalculateBudgetAmount(systemUser, fromBudgetId);
            budgetCalculateService.recalculateBudgetAmount(systemUser, toBudgetId);
            actionContext.setAttribute(BudgetTransferDetailConstants.REAL_DEDUCTION_FLAG, true);
        });
    }
}
