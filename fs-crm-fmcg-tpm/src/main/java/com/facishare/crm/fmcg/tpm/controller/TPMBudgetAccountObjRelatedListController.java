package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetCarryForwardService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetCompareService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IFiscalTimeService;
import com.facishare.crm.fmcg.tpm.business.dto.CarryForwardDetailDataDocument;
import com.facishare.crm.fmcg.tpm.business.enums.CarryForwardType;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 如果后续的需求需要改这里的代码，找庆飞先
 * <p>
 * create by @yangqf
 * create time 2020/11/10 4:39 PM
 */
@SuppressWarnings("Duplicates")
public class TPMBudgetAccountObjRelatedListController extends StandardRelatedListController {

    private final BudgetTypeDAO budgetTypeDAO = SpringUtil.getContext().getBean(BudgetTypeDAO.class);
    private final IBudgetCarryForwardService budgetCarryForwardService = SpringUtil.getContext().getBean(IBudgetCarryForwardService.class);
    private final IBudgetCompareService budgetCompareService = SpringUtil.getContext().getBean(IBudgetCompareService.class);
    private final IFiscalTimeService fiscalTimeService = SpringUtil.getContext().getBean(IFiscalTimeService.class);
    private final OrganizationService tpmOrganizationService = SpringUtil.getContext().getBean(OrganizationService.class);

    protected static final String CARRY_FORWARD_DETAIL_DATA_MAGIC_KEY = "__carry_forward_detail_data";
    /**
     * carry forward information
     */
    private boolean isCarryForward = false;
    private boolean isDisassemblyMasterObj = false;
    private boolean isDisassemblyExistDetailObj = false;
    private BudgetTypePO carryForwardBudgetType;
    private BudgetTypeNodeEntity carryForwardBudgetNode;
    private Long carryForwardSourcePeriod;
    private Long carryForwardTargetPeriod;
    private Map<String, CarryForwardDetailDataDocument> carryForwardMap;
    private BudgetTypeNodeEntity sourceNode;
    private BudgetTypeNodeEntity targetNode;
    private BudgetTypePO budgetType;

    @Override
    protected void before(Arg arg) {
        super.before(arg);

        // for carry forward
        loadDataForCarryForward();

        loadDataForDisassemblyMasterObj();
        loadDataForDisassemblyExistDetailObj();
    }

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        super.beforeQueryData(query);

        // for carry forward
        overrideQueryForCarryForward(query);
        overrideQueryForDisassemblyMasterData(query);
        overrideQueryForDisassemblyExistsDetail(query);
        overrideQueryForTransferDetail(query);
    }

    @Override
    protected Result after(Arg arg, Result result) {

        // for carry forward
        fillCarryForwardDetailData(result);
        return super.after(arg, result);
    }

    private void fillCarryForwardDetailData(Result result) {
        if (!this.isCarryForward) {
            return;
        }
        for (ObjectDataDocument objectDataDocument : result.getDataList()) {
            if (this.carryForwardMap.containsKey(objectDataDocument.getId())) {
                objectDataDocument.put(CARRY_FORWARD_DETAIL_DATA_MAGIC_KEY, this.carryForwardMap.get(objectDataDocument.getId()));
            }
        }
    }

    private void overrideQueryForTransferDetail(SearchTemplateQuery query) {
        if (Objects.isNull(arg.getObjectData())) {
            return;
        }
        if (TPMGrayUtils.skipBudgetTransferRangeCheck(controllerContext.getTenantId())) {
            return;
        }
        IObjectData callerData = arg.getObjectData().toObjectData();
        if (Objects.equals(callerData.getDescribeApiName(), ApiNames.TPM_BUDGET_TRANSFER_DETAIL)) {
            if (TPMBudgetTransferDetailFields.RECORD_TYPE_DEFAULT.equals(callerData.getRecordType())) {
                if ("target_related_list_TPMBudgetTransferDetailObj_Out_TPMBudgetAccountObj__c".equals(arg.getRelatedListName())) {
                    transferAddTheSameParentLimitFilter(query, callerData.get(TPMBudgetTransferDetailFields.TRANSFER_IN_BUDGET_ACCOUNT_ID, String.class));
                } else if ("target_related_list_TPMBudgetTransferDetailObj_In_TPMBudgetAccountObj__c".equals(arg.getRelatedListName())) {
                    transferAddTheSameParentLimitFilter(query, callerData.get(TPMBudgetTransferDetailFields.TRANSFER_OUT_BUDGET_ACCOUNT_ID, String.class));
                }
            }
        }
    }

    private void transferAddTheSameParentLimitFilter(SearchTemplateQuery query, String theOtherBudgetId) {
        if (!Strings.isNullOrEmpty(theOtherBudgetId)) {
            IObjectData theOtherBudget = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), theOtherBudgetId, ApiNames.TPM_BUDGET_ACCOUNT);
            String parentId = theOtherBudget.get(TPMBudgetAccountFields.PARENT_ID, String.class);
            if (Strings.isNullOrEmpty(parentId)) {
                addFilters(query.getFilters(), TPMBudgetAccountFields.PARENT_ID, Operator.IS, Lists.newArrayList());
                addFilters(query.getFilters(), TPMBudgetAccountFields.BUDGET_TYPE_ID, Operator.EQ, Lists.newArrayList(theOtherBudget.get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class)));
            } else {
                addFilters(query.getFilters(), TPMBudgetAccountFields.PARENT_ID, Operator.EQ, Lists.newArrayList(parentId));
            }
            addFilters(query.getFilters(), CommonFields.ID, Operator.NEQ, Lists.newArrayList(theOtherBudgetId));
        }
    }

    private void overrideQueryForCarryForward(SearchTemplateQuery query) {
        if (!this.isCarryForward) {
            return;
        }

        if (CollectionUtils.isEmpty(query.getFilters())) {
            query.setFilters(Lists.newArrayList());
        }

        this.carryForwardMap = queryCarryForwardMap();

        List<String> sourceIds = Lists.newArrayList(this.carryForwardMap.keySet());
        if (this.carryForwardMap.isEmpty()) {
            sourceIds = Lists.newArrayList("");
        }

        IFilter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(sourceIds);

        query.getFilters().add(idFilter);
    }

    private void overrideQueryForDisassemblyMasterData(SearchTemplateQuery query) {
        if (!this.isDisassemblyMasterObj) {
            return;
        }

        if (CollectionUtils.isEmpty(query.getFilters())) {
            query.setFilters(Lists.newArrayList());
        }

        List<IFilter> filters = Lists.newArrayList();

        addFilters(filters, TPMBudgetAccountFields.BUDGET_TYPE_ID, Operator.EQ, Lists.newArrayList(budgetType.getId().toString()));
        addFilters(filters, TPMBudgetAccountFields.BUDGET_NODE_ID, Operator.EQ, Lists.newArrayList(sourceNode.getNodeId()));
        addFilters(filters, CommonFields.LIFE_STATUS, Operator.EQ, Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));
        addFilters(filters, TPMBudgetAccountFields.BUDGET_STATUS, Operator.EQ, Lists.newArrayList(TPMBudgetAccountFields.BUDGET_STATUS__ENABLE));
        query.getFilters().addAll(filters);
    }

    private void overrideQueryForDisassemblyExistsDetail(SearchTemplateQuery query) {
        if (!this.isDisassemblyExistDetailObj) {
            return;
        }

        if (CollectionUtils.isEmpty(query.getFilters())) {
            query.setFilters(Lists.newArrayList());
        }
        IObjectData masterData = arg.getObjectData().toObjectData();
        String sourceAccountId = masterData.get(TPMBudgetDisassemblyFields.SOURCE_BUDGET_ACCOUNT_ID, String.class);
        if (Objects.isNull(sourceAccountId)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_OBJ_RELATED_LIST_CONTROLLER_0));
        }
        IObjectData sourceAccount = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), sourceAccountId, ApiNames.TPM_BUDGET_ACCOUNT);
        if (sourceAccount == null) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_OBJ_RELATED_LIST_CONTROLLER_1));
        }

        List<String> budgetDepartment = sourceAccount.get(TPMBudgetAccountFields.BUDGET_DEPARTMENT, List.class);
        if (CollectionUtils.isEmpty(budgetDepartment)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_OBJ_RELATED_LIST_CONTROLLER_2));
        }

        List<String> childrenDepartmentIds = tpmOrganizationService.queryLowerDepartmentIds(Integer.parseInt(controllerContext.getTenantId()), Integer.parseInt(budgetDepartment.get(0)))
                .stream().map(String::valueOf).collect(Collectors.toList());
        childrenDepartmentIds.add(budgetDepartment.get(0));


        List<IFilter> filters = Lists.newArrayList();

        addFilters(filters, TPMBudgetAccountFields.BUDGET_TYPE_ID, Operator.EQ, Lists.newArrayList(budgetType.getId().toString()));

        addFilters(filters, TPMBudgetAccountFields.BUDGET_NODE_ID, Operator.EQ, Lists.newArrayList(targetNode.getNodeId()));

        //同部门或子部门
        addFilters(filters, TPMBudgetAccountFields.BUDGET_DEPARTMENT, Operator.IN, childrenDepartmentIds);

        //是转出表的子表
        addFilters(filters, "parent_id", Operator.EQ, Lists.newArrayList(sourceAccount.getId()));

        //同级时段或子时段
        addFilters(filters, TPMBudgetAccountFields.EFFECTIVE_PERIOD, Operator.IN, getSubPeriod(sourceNode.getTimeDimension()));

        String sourcePeriodApiName = String.format("budget_period_%s", sourceNode.getTimeDimension());
        String targetPeriodApiName = String.format("budget_period_%s", targetNode.getTimeDimension());
        //拆解转入表的预算时段需要大于等于等于转出表
        addFilters(filters, targetPeriodApiName, Operator.GTE, Lists.newArrayList(sourceAccount.get(sourcePeriodApiName, String.class)));


        query.getFilters().addAll(filters);
    }

    private List<String> getSubPeriod(String dimension) {
        switch (dimension) {
            case "year": {
                return Lists.newArrayList("year", "quarter", "month");
            }
            case "quarter": {
                return Lists.newArrayList("quarter", "month");
            }
            case "month": {
                return Lists.newArrayList("month");
            }
            default:
                return new ArrayList<>();
        }
    }

    private List<String> getAllChildSubjectIds(String tenantId, String parentSubjectId) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(200);


        List<IFilter> filters = Lists.newArrayList();
        addFilters(filters, TPMBudgetBusinessSubjectFields.PARENT_SUBJECT_ID, Operator.EQ, Lists.newArrayList(parentSubjectId));

        searchTemplateQuery.setFilters(filters);


        List<IObjectData> childSubject = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_BUDGET_ACCOUNT, searchTemplateQuery);

        return childSubject.stream().map(IObjectData::getId).collect(Collectors.toList());
    }

    private void addFilters(List<IFilter> filters, String apiName, Operator operator, List<String> values) {
        IFilter filter = new Filter();
        filter.setFieldName(apiName);
        filter.setOperator(operator);
        filter.setFieldValues(values);

        filters.add(filter);

    }

    private Map<String, CarryForwardDetailDataDocument> queryCarryForwardMap() {
        List<CarryForwardDetailDataDocument> details = budgetCarryForwardService.loadDetailData(
                this.carryForwardBudgetType,
                this.carryForwardBudgetNode,
                CarryForwardType.CARRY_FORWARD_WITHOUT_NEXT_PERIOD_BUDGET_OR_CREATE,
                this.carryForwardSourcePeriod,
                this.carryForwardTargetPeriod);

        return details.stream().collect(Collectors.toMap(CarryForwardDetailDataDocument::getSourceBudgetAccountId, v -> v));
    }

    private void loadDataForCarryForward() {
        if (!"target_related_list_TPMBudgetCarryForwardDetailObj_from__c".equals(arg.getRelatedListName())) {
            return;
        }

        String objectDescribeApiName = (String) arg.getObjectData().get("object_describe_api_name");
        if (!"TPMBudgetCarryForwardDetailObj".equals(objectDescribeApiName)) {
            return;
        }

        this.isCarryForward = true;

        if (Objects.isNull(arg.getMasterData())) {
            throw new ValidateException("master data can not be null.");
        }

        String typeId = (String) arg.getMasterData().get(TPMBudgetCarryForwardFields.BUDGET_TYPE_ID);
        if (Strings.isNullOrEmpty(typeId)) {
            throw new ValidateException("please select [budget_type_id] of master data.");
        }
        this.carryForwardBudgetType = budgetTypeDAO.get(controllerContext.getTenantId(), typeId);
        if (Objects.isNull(this.carryForwardBudgetType)) {
            throw new ValidateException("budget type not exists.");
        }

        String nodeId = (String) arg.getMasterData().get(TPMBudgetCarryForwardFields.BUDGET_NODE_ID);
        if (Strings.isNullOrEmpty(nodeId)) {
            throw new ValidateException("please select [budget_node_id] of master data.");
        }
        this.carryForwardBudgetNode = this.carryForwardBudgetType.getNodes().stream().filter(f -> f.getNodeId().equals(nodeId)).findFirst().orElse(null);
        if (Objects.isNull(this.carryForwardBudgetNode)) {
            throw new ValidateException("budget node not exists.");
        }

        String sourcePeriodFieldApiName = String.format("source_%s", this.carryForwardBudgetNode.getTimeDimension());
        String targetPeriodFieldApiName = String.format("target_%s", this.carryForwardBudgetNode.getTimeDimension());

        this.carryForwardSourcePeriod = (Long) arg.getMasterData().get(sourcePeriodFieldApiName);
        if (Objects.isNull(this.carryForwardSourcePeriod)) {
            throw new ValidateException("source period empty.");
        }

        this.carryForwardTargetPeriod = (Long) arg.getMasterData().get(targetPeriodFieldApiName);
        if (Objects.isNull(this.carryForwardTargetPeriod)) {
            throw new ValidateException("target period empty.");
        }

        if (fiscalTimeService.notNextPeriod(controllerContext.getTenantId(), this.carryForwardBudgetNode.getTimeDimension(), this.carryForwardSourcePeriod, this.carryForwardTargetPeriod)) {
            throw new ValidateException("target period must after source period.");
        }
    }

    private void loadDataForDisassemblyMasterObj() {
        if (!"target_related_list_TPMBudgetDisassemblyObj_TPMBudgetAccountObj__c".equals(arg.getRelatedListName())) {
            return;
        }

        String objectDescribeApiName = (String) arg.getObjectData().get("object_describe_api_name");
        if (!"TPMBudgetDisassemblyObj".equals(objectDescribeApiName)) {
            return;
        }

        this.isDisassemblyMasterObj = true;

        IObjectData masterData = arg.getObjectData().toObjectData();

        // 加载预算类型信息
        String typeId = masterData.get(TPMBudgetDisassemblyFields.BUDGET_TYPE_ID, String.class);
        if (Strings.isNullOrEmpty(typeId)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_OBJ_RELATED_LIST_CONTROLLER_3));
        }
        this.budgetType = budgetTypeDAO.get(controllerContext.getTenantId(), typeId);
        if (Objects.isNull(this.budgetType)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_OBJ_RELATED_LIST_CONTROLLER_4));
        }
        //加载拆解转出预算节点信息
        String sourceNodeId = masterData.get(TPMBudgetDisassemblyFields.SOURCE_BUDGET_NODE_ID, String.class);
        this.sourceNode = this.budgetType.getNodes().stream().filter(f -> f.getNodeId().equals(sourceNodeId)).findFirst().orElse(null);
        if (Objects.isNull(this.sourceNode)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_OBJ_RELATED_LIST_CONTROLLER_5));
        }


    }

    private void loadDataForDisassemblyExistDetailObj() {
        if (!"target_related_list_TPMBudgetDisassemblyExistsDetailObj_TPMBudgetAccountObj__c".equals(arg.getRelatedListName())) {
            return;
        }

        String objectDescribeApiName = (String) arg.getObjectData().get("object_describe_api_name");
        if (!"TPMBudgetDisassemblyExistsDetailObj".equals(objectDescribeApiName)) {
            return;
        }

        this.isDisassemblyExistDetailObj = true;

        IObjectData masterData = arg.getObjectData().toObjectData();

        // 加载预算类型信息
        String typeId = masterData.get(TPMBudgetDisassemblyFields.BUDGET_TYPE_ID, String.class);
        if (Strings.isNullOrEmpty(typeId)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_OBJ_RELATED_LIST_CONTROLLER_6));
        }
        this.budgetType = budgetTypeDAO.get(controllerContext.getTenantId(), typeId);
        if (Objects.isNull(this.budgetType)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_OBJ_RELATED_LIST_CONTROLLER_7));
        }

        //加载拆解转出预算节点信息
        String sourceNodeId = masterData.get(TPMBudgetDisassemblyFields.SOURCE_BUDGET_NODE_ID, String.class);
        this.sourceNode = this.budgetType.getNodes().stream().filter(f -> f.getNodeId().equals(sourceNodeId)).findFirst().orElse(null);
        if (Objects.isNull(this.sourceNode)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_OBJ_RELATED_LIST_CONTROLLER_8));
        }

        //加载拆解转入预算节点信息
        String targetNodeId = masterData.get(TPMBudgetDisassemblyFields.TARGET_BUDGET_NODE_ID, String.class);
        this.targetNode = this.budgetType.getNodes().stream().filter(f -> f.getNodeId().equals(targetNodeId)).findFirst().orElse(null);
        if (Objects.isNull(this.targetNode)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_OBJ_RELATED_LIST_CONTROLLER_9));
        }

        if (!Objects.equals(targetNode.getParentNodeId(), sourceNode.getNodeId())) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_ACCOUNT_OBJ_RELATED_LIST_CONTROLLER_10), sourceNode.getName()));
        }
    }
}
