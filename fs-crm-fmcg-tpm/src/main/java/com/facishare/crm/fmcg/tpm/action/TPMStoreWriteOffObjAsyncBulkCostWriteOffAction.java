package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.web.service.StoreWriteOffService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAsyncBulkAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@SuppressWarnings("Duplicates")
public class TPMStoreWriteOffObjAsyncBulkCostWriteOffAction extends AbstractStandardAsyncBulkAction<TPMStoreWriteOffObjAsyncBulkCostWriteOffAction.Arg, TPMStoreWriteOffObjAsyncBulkCostWriteOffAction.Result> {

    private final StoreWriteOffService storeWriteOffService = SpringUtil.getContext().getBean(StoreWriteOffService.class);

    private IObjectData storeWriteOffData;

    private String message;

    private Boolean flag;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.COST_WRITE_OFF.getActionCode());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.COST_WRITE_OFF.getButtonApiName();
    }

    @Override
    protected String getActionCode() {
        return null;
    }

    @Override
    protected String getDataIdByParam(Result result) {
        return null;
    }

    @Override
    protected List<Result> getButtonParams() {
        return null;
    }


    @Override
    protected void init() {
        super.init();
        storeWriteOffData = this.dataList.get(0);
    }

    @Data
    public static class Arg {

        private List<String> dataIds;


        public static Arg of(List<String> dataIds) {
            Arg arg = new Arg();
            arg.setDataIds(dataIds);
            return arg;
        }
    }


    @Data
    public static class Result {
        private ObjectDataDocument objectData;

        @SerializedName("validation_result")
        @JSONField(name = "validation_result")
        @JsonProperty("validation_result")
        private Boolean validationResult;

        @SerializedName("validation_message")
        @JSONField(name = "validation_message")
        @JsonProperty("validation_message")
        private String validationMessage;

        public static Result of(Boolean flag, String message) {
            Result result = new Result();
            result.setValidationResult(flag);
            result.setValidationMessage(message);
            return result;
        }
    }

}
