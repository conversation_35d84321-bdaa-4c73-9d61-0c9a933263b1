package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDealerActivityCostRebateService;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.NodeType;
import com.facishare.crm.fmcg.tpm.utils.LayoutUtil;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IDealerActivityCostEnterAccountService;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDescribeLayoutController;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.dto.DetailObjectListResult;
import com.facishare.paas.appframework.metadata.dto.RecordTypeLayoutStructure;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.paas.metadata.util.Tuple;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@SuppressWarnings("all")
@Slf4j
public class TPMDealerActivityCostObjDescribeLayoutController extends AbstractStandardDescribeLayoutController<TPMDealerActivityCostObjDescribeLayoutController.Arg> {

    public static final Set<String> CASHING_PRODUCT_READ_ONLY_FIELD_API_NAME = Sets.newHashSet();
    public static final Set<String> DEALER_COST_READ_ONLY_FIELD_API_NAME = Sets.newHashSet();
    public static final Set<String> CASHING_PRODUCT_REQUIRED_FIELD_API_NAME = Sets.newHashSet();
    public static final Set<String> DEALER_COST_ADD_EDIT_REQUIRED_FIELD_API_NAME = Sets.newHashSet();
    public static final Set<String> FLOW_LAYOUT_REQUIRED_FIELD_API_NAME = Sets.newHashSet();
    public static final Set<String> REMOVE_FIELD_API_NAME = Sets.newHashSet();
    private static final Set<String> ENTER_ACCOUNT_DISABLE_COMMON_REMOVE_FIELDS = Sets.newHashSet();
    public static final Set<String> FLOW_ENTER_ACCOUNT_ENABLE_CASH_REMOVE_FIELD_API_NAME = Sets.newHashSet();
    public static final Set<String> FLOW_ENTER_ACCOUNT_DISABLE_CASH_REMOVE_FIELD_API_NAME = Sets.newHashSet();
    public static final Set<String> FLOW_ENTER_ACCOUNT_ENABLE_GOODS_REMOVE_FIELD_API_NAME = Sets.newHashSet();
    public static final Set<String> FLOW_ENTER_ACCOUNT_DISABLE_GOODS_REMOVE_FIELD_API_NAME = Sets.newHashSet();
    private static final ActivityTypeDAO activityTypeDao = SpringUtil.getContext().getBean(ActivityTypeDAO.class);
    private static final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);
    private static final IDealerActivityCostEnterAccountService dealerActivityCostEnterAccountService = SpringUtil.getContext().getBean(IDealerActivityCostEnterAccountService.class);
    private static final IDealerActivityCostRebateService dealerActivityCostRebateService = SpringUtil.getContext().getBean(IDealerActivityCostRebateService.class);
    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);

    static {

        setReadOnlyFieldApiName();
        setRequiredFieldApiName();
        setRemoveFieldApiName();
    }

    private IObjectData activity;
    private String cashingType;
    private String activityTypeId;
    private IObjectData dealerCost;
    private boolean chargeUpAccountStatus = false;

    @Override
    protected StandardDescribeLayoutController.Result after(Arg arg, StandardDescribeLayoutController.Result result) {
        List<String> removeList = new ArrayList<>(REMOVE_FIELD_API_NAME);
        StandardDescribeLayoutController.Result layoutResult = super.after(arg, result);
        List<ILayout> byTypesIncludeFlowLayout = serviceFacade.getLayoutLogicService().findByTypesIncludeFlowLayout(controllerContext.getTenantId(),
                ApiNames.TPM_DEALER_ACTIVITY_COST, Lists.newArrayList(LayoutTypes.DETAIL));
        List<String> flowLayoutApiNames = byTypesIncludeFlowLayout.stream().filter(iLayout -> Objects.equals(iLayout.getNamespace(), "flow"))
                .map(ILayout::getName).collect(Collectors.toList());
        String activityId = "";
        String accountId = "";
        boolean removeUniteCase = false;

        if (flowLayoutApiNames.contains(arg.getLayoutApiName())) {
            String dataId = arg.getData_id();
            dealerCost = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), dataId, ApiNames.TPM_DEALER_ACTIVITY_COST);
            accountId = dealerCost.get(TPMDealerActivityCostFields.DEALER_ID, String.class);
            activityId = dealerCost.get(TPMDealerActivityCostFields.ACTIVITY_ID, String.class);
        } else {
            activityId = arg.getActivityId();
        }
        if (!StringUtils.isEmpty(activityId)) {
            activity = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);
            activityTypeId = activity.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
            ActivityTypeExt activityTypeExt = activityTypeManager.find(controllerContext.getTenantId(), activityTypeId);
            removeUniteCase = removeUniteCaseField(activityTypeExt);
            chargeUpAccountStatus = dealerActivityCostEnterAccountService.isOpenEnterAccount(controllerContext.getTenantId(), activityTypeId);
        }
        if (removeUniteCase) {
            removeList.add(TPMDealerActivityCostFields.ACTIVITY_UNIFIED_CASE_ID);
        }

        //流程布局:核销费用必填,申请兑付数只读
        if (flowLayoutApiNames.contains(arg.getLayoutApiName())) {
            try {
                boolean isDealer = storeBusiness.isDealer(controllerContext.getTenantId(), accountId);
                if (isDealer) {
                    cashingType = activity.get(TPMActivityFields.DEALER_CASHING_TYPE, String.class);
                } else {
                    cashingType = activity.get(TPMActivityFields.STORE_CASHING_TYPE, String.class);
                }
                boolean editDefaultEnterAccount = dealerActivityCostEnterAccountService.isAllowEditDefaultEnterAccount(controllerContext.getTenantId(), activityTypeId);
                LayoutExt layout = LayoutExt.of(result.getLayout().toLayout());
                FormComponentExt form = layout.getFormComponent().orElse(null);
                if (!Objects.isNull(form)) {
                    List<IFieldSection> sections = form.getFieldSections();
                    for (IFieldSection section : sections) {
                        if (chargeUpAccountStatus) {
                            if (TPMActivityCashingProductFields.GOODS.equals(cashingType)) {
                                removeFiled(section, FLOW_ENTER_ACCOUNT_ENABLE_GOODS_REMOVE_FIELD_API_NAME);
                            } else {
                                removeFiled(section, FLOW_ENTER_ACCOUNT_ENABLE_CASH_REMOVE_FIELD_API_NAME);
                            }
                        } else {
                            if (TPMActivityCashingProductFields.GOODS.equals(cashingType)) {
                                removeFiled(section, FLOW_ENTER_ACCOUNT_DISABLE_GOODS_REMOVE_FIELD_API_NAME);
                            } else {
                                removeFiled(section, FLOW_ENTER_ACCOUNT_DISABLE_CASH_REMOVE_FIELD_API_NAME);
                            }
                        }
                        for (IFormField field : section.getFields()) {
                            if (TPMDealerActivityCostFields.GOODS_PAY_USAGE.equals(field.getFieldName())) {
                                field.setReadOnly(true);
                            }
                            String defaultEnterAccount = dealerActivityCostEnterAccountService.getDefaultEnterAccountByType(controllerContext.getTenantId(), activityTypeId, cashingType);
                            if (!StringUtils.isEmpty(defaultEnterAccount)) {
                                if (!Boolean.TRUE.equals(editDefaultEnterAccount)) {
                                    if (TPMDealerActivityCostFields.CASHING_FUND_ACCOUNT_ID.equals(field.getFieldName())) {
                                        field.setReadOnly(true);
                                    }
                                }
                            }
                            if (FLOW_LAYOUT_REQUIRED_FIELD_API_NAME.contains(field.getFieldName())) {
                                field.setRequired(true);
                            }
                            if (dealerCost != null && !StringUtils.isEmpty(dealerCost.get(TPMDealerActivityCostFields.CASHING_FUND_ACCOUNT_ID, String.class))) {
                                String defalutAccountAccessModule = defalutAccountAccessModule(dealerCost);
                                if (FundAccountFields.ACCESS_MODULE_BACK.equals(defalutAccountAccessModule)) {
                                    if (TPMDealerActivityCostFields.CASH_USAGE.equals(field.getFieldName()) || TPMDealerActivityCostFields.EFFECTIVE_PERIOD.equals(field.getFieldName())) {
                                        field.setRequired(true);
                                    }
                                }
                            }

                        }
                    }
                }
            } catch (Exception ex) {
                log.info("override TPMDealerActivityCostObj layout cause unknown exception  : ", ex);
            }
        } else {
            try {
                LayoutExt layout = LayoutExt.of(result.getLayout().toLayout());
                FormComponentExt form = layout.getFormComponent().orElse(null);
                if (!Objects.isNull(form)) {
                    List<IFieldSection> sections = form.getFieldSections();
                    for (IFieldSection section : sections) {
                        if ("add".equals(arg.getLayout_type()) || ("edit".equals(arg.getLayout_type()) && !flowLayoutApiNames.contains(arg.getLayoutApiName()))) {
                            section.setFields(section.getFields().stream()
                                    .filter(field -> !REMOVE_FIELD_API_NAME.contains(field.getFieldName()))
                                    .collect(Collectors.toList()));
                        }
                        section.getFields().forEach(field -> {
                            if (DEALER_COST_READ_ONLY_FIELD_API_NAME.contains(field.getFieldName())) {
                                field.setReadOnly(true);
                            }
                            if (DEALER_COST_ADD_EDIT_REQUIRED_FIELD_API_NAME.contains(field.getFieldName())) {
                                field.setRequired(true);
                            }

                        });
                    }
                }
            } catch (Exception ex) {
                log.info("override TPMDealerActivityCostObj layout cause unknown exception  : ", ex);
            }
        }
        try {
            List<DetailObjectListResult> detailObjectList = result.getDetailObjectList();
            for (DetailObjectListResult detailObjectListResult : detailObjectList) {
                String objectApiName = detailObjectListResult.getObjectApiName();
                if (Objects.equals(objectApiName, ApiNames.TPM_DEALER_ACTIVITY_CASHING_PRODUCT_OBJ)) {
                    for (RecordTypeLayoutStructure structure : detailObjectListResult.getLayoutList()) {
                        Map detailLayout = structure.getDetail_layout();
                        LayoutExt layoutExt = LayoutExt.of(detailLayout);
                        Optional<FormComponentExt> component = layoutExt.getFormComponent();
                        if (component.isPresent()) {
                            FormComponentExt formComponent = component.get();
                            for (IFieldSection fieldSection : formComponent.getFieldSections()) {
                                if ("add".equals(arg.getLayout_type()) || ("edit".equals(arg.getLayout_type()) && !flowLayoutApiNames.contains(arg.getLayoutApiName()))) {
                                    fieldSection.setFields(fieldSection.getFields().stream()
                                            .filter(field -> !REMOVE_FIELD_API_NAME.contains(field.getFieldName()))
                                            .collect(Collectors.toList()));
                                }
                                for (IFormField field : fieldSection.getFields()) {
                                    if (CASHING_PRODUCT_READ_ONLY_FIELD_API_NAME.contains(field.getFieldName())) {
                                        field.setReadOnly(true);
                                    }
                                    if (CASHING_PRODUCT_REQUIRED_FIELD_API_NAME.contains(field.getFieldName())) {
                                        field.setRequired(true);
                                    }
                                    if (chargeUpAccountStatus) {
                                        if (TPMDealerActivityCashingProductFields.COST_CASHING_QUANTITY.equals(field.getFieldName())) {
                                            field.setRequired(true);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception ex) {
            log.info("override TPMDealerActivityCostObj detail layout cause unknown exception  : ", ex);
        }

        if (Boolean.TRUE.equals(arg.getInclude_layout())) {
            Set<String> hideFields = Sets.newHashSet();
            Set<String> readonlyFields = Sets.newHashSet(TPMDealerActivityCostFields.DEALER_CASHING_TYPE, TPMDealerActivityCostFields.ACTIVITY_TYPE);
            Set<String> requiredFields = Sets.newHashSet();
            if (removeUniteCase) {
                hideFields.add(TPMDealerActivityCostFields.ACTIVITY_UNIFIED_CASE_ID);
            } else {
                readonlyFields.add(TPMDealerActivityCostFields.ACTIVITY_UNIFIED_CASE_ID);
                requiredFields.add(TPMDealerActivityCostFields.ACTIVITY_UNIFIED_CASE_ID);
            }
            List<IFormField> additionFields = Lists.newArrayList();
            if (flowLayoutApiNames.contains(arg.getLayoutApiName())) {
                additionFields = getAdditionsFields();
            }
            LayoutUtil.setLayoutFieldHiddenOrReadOnly(LayoutExt.of(result.getLayout().toLayout()), hideFields, readonlyFields, requiredFields, additionFields, "group_yHXpN__c");
        }

        //流程布局隐藏从对象
        if (flowLayoutApiNames.contains(arg.getLayoutApiName())) {
            if (TPMActivityCashingProductFields.GOODS.equals(cashingType)) {
                return layoutResult;
            }
            List<DetailObjectListResult> detailObjectList = layoutResult.getDetailObjectList();
            layoutResult.setDetailObjectList(detailObjectList.stream().filter(detailObjectListResult ->
                            !Objects.equals(detailObjectListResult.getObjectApiName(), ApiNames.TPM_DEALER_ACTIVITY_CASHING_PRODUCT_OBJ))
                    .collect(Collectors.toList()));


            try {
                LayoutExt layout = LayoutExt.of(layoutResult.getLayout().toLayout());
                layout.setComponents(layout.getComponents().stream().filter(iComponent -> !Objects.equals(iComponent.get("ref_object_api_name"),
                        ApiNames.TPM_DEALER_ACTIVITY_CASHING_PRODUCT_OBJ)).collect(Collectors.toList()));
                layoutResult.setLayout(LayoutDocument.of(layout));
            } catch (Exception ex) {
                log.info("override TPMDealerActivityCostObj layout cause unknown exception  : ", ex);
            }
        }

        return layoutResult;
    }

    private void removeFiled(IFieldSection section, Set<String> removeFileds) {
        section.setFields(section.getFields().stream()
                .filter(field -> !removeFileds.contains(field.getFieldName()))
                .collect(Collectors.toList()));
    }

    private List<IFormField> getAdditionsFields() {
        List<Tuple<String, String>> additionFields = Lists.newArrayList();
        boolean openRebate = dealerActivityCostRebateService.isOpenRebate(controllerContext.getTenantId());
        if (openRebate) {
            if (TPMActivityCashingProductFields.GOODS.equals(cashingType)) {
                additionFields = Lists.newArrayList(new Tuple<>(TPMDealerActivityCostFields.CONFIRMED_AMOUNT, "currency"), new Tuple<>(TPMDealerActivityCostFields.CASHING_FUND_ACCOUNT_ID, "object_reference"));
            } else {
                additionFields = Lists.newArrayList(new Tuple<>(TPMDealerActivityCostFields.CONFIRMED_AMOUNT, "currency"), new Tuple<>(TPMDealerActivityCostFields.CASHING_FUND_ACCOUNT_ID, "object_reference"),
                        new Tuple<>(TPMDealerActivityCostFields.CASH_USAGE, "select_one"), new Tuple<>(TPMDealerActivityCostFields.EFFECTIVE_PERIOD, "number"));
            }
        } else {
            additionFields = Lists.newArrayList(new Tuple<>(TPMDealerActivityCostFields.CONFIRMED_AMOUNT, "currency"));
        }
        return additionFields.stream().map(fieldAndType -> {
            return getFormField(fieldAndType.getFirst(), fieldAndType.getSecond());
        }).collect(Collectors.toList());
    }

    private IFormField getFormField(String apiName, String type) {
        IFormField field = new FormField();
        field.setFieldName(apiName);
        field.setRequired(false);
        field.setReadOnly(false);
        field.setRenderType(type);
        return field;
    }

    private String defalutAccountAccessModule(IObjectData dealerCost) {

        IObjectData account = serviceFacade.findObjectDataIncludeDeleted(User.systemUser(controllerContext.getTenantId()),
                dealerCost.get(TPMDealerActivityCostFields.CASHING_FUND_ACCOUNT_ID, String.class), ApiNames.FUND_ACCOUNT_OBJ);

        return account.get(FundAccountFields.ACCESS_MODULE, String.class);
    }

    private boolean removeUniteCaseField(ActivityTypeExt activityType) {

        boolean remove = false;
        if (Objects.nonNull(activityType)) {
            ActivityNodeEntity node = activityType.node(NodeType.PLAN_TEMPLATE);
            if (Objects.isNull(node)) {
                return true;
            }
            if (Objects.nonNull(activityType.activityPlanConfig())) {
                return !Boolean.TRUE.equals(activityType.activityPlanConfig().getEnableRelationPreNodeRequired());
            }

        }
        return remove;
    }

    private static void setRemoveFieldApiName() {
        ENTER_ACCOUNT_DISABLE_COMMON_REMOVE_FIELDS.add(TPMDealerActivityCashingProductFields.COST_CASHING_TOTAL_PRICE);
        ENTER_ACCOUNT_DISABLE_COMMON_REMOVE_FIELDS.add(TPMDealerActivityCostFields.GOODS_PAY_NUMBER);
        ENTER_ACCOUNT_DISABLE_COMMON_REMOVE_FIELDS.add(TPMDealerActivityCostFields.CASHING_FUND_ACCOUNT_ID);
        ENTER_ACCOUNT_DISABLE_COMMON_REMOVE_FIELDS.add(TPMDealerActivityCostFields.CASH_USAGE);
        ENTER_ACCOUNT_DISABLE_COMMON_REMOVE_FIELDS.add(TPMDealerActivityCostFields.ENTER_ACCOUNT_WAY);
        ENTER_ACCOUNT_DISABLE_COMMON_REMOVE_FIELDS.add(TPMDealerActivityCostFields.EFFECTIVE_PERIOD);
        ENTER_ACCOUNT_DISABLE_COMMON_REMOVE_FIELDS.add(TPMDealerActivityCostFields.EFFECTIVE_DATE);
        ENTER_ACCOUNT_DISABLE_COMMON_REMOVE_FIELDS.add(TPMDealerActivityCostFields.EXPIRING_DATE);
        ENTER_ACCOUNT_DISABLE_COMMON_REMOVE_FIELDS.add(TPMDealerActivityCostFields.ENTER_ACCOUNT);

        REMOVE_FIELD_API_NAME.add(TPMDealerActivityCashingProductFields.COST_CASHING_QUANTITY);
        REMOVE_FIELD_API_NAME.add(TPMDealerActivityCostFields.GOODS_PAY_USAGE);
        REMOVE_FIELD_API_NAME.addAll(ENTER_ACCOUNT_DISABLE_COMMON_REMOVE_FIELDS);

        FLOW_ENTER_ACCOUNT_DISABLE_CASH_REMOVE_FIELD_API_NAME.addAll(REMOVE_FIELD_API_NAME);
        FLOW_ENTER_ACCOUNT_DISABLE_CASH_REMOVE_FIELD_API_NAME.add(TPMDealerActivityCostFields.GOODS_PAY_USAGE);
        FLOW_ENTER_ACCOUNT_DISABLE_CASH_REMOVE_FIELD_API_NAME.add(TPMDealerActivityCostFields.AUDITED_AMOUNT);


        FLOW_ENTER_ACCOUNT_DISABLE_GOODS_REMOVE_FIELD_API_NAME.addAll(ENTER_ACCOUNT_DISABLE_COMMON_REMOVE_FIELDS);
        FLOW_ENTER_ACCOUNT_DISABLE_GOODS_REMOVE_FIELD_API_NAME.add(TPMDealerActivityCostFields.AUDITED_AMOUNT);
        FLOW_ENTER_ACCOUNT_DISABLE_GOODS_REMOVE_FIELD_API_NAME.add(TPMDealerActivityCostFields.GOODS_PAY_USAGE);


        FLOW_ENTER_ACCOUNT_ENABLE_CASH_REMOVE_FIELD_API_NAME.add(TPMDealerActivityCostFields.ENTER_ACCOUNT_WAY);
        FLOW_ENTER_ACCOUNT_ENABLE_CASH_REMOVE_FIELD_API_NAME.add(TPMDealerActivityCostFields.EFFECTIVE_DATE);
        FLOW_ENTER_ACCOUNT_ENABLE_CASH_REMOVE_FIELD_API_NAME.add(TPMDealerActivityCostFields.EXPIRING_DATE);
        FLOW_ENTER_ACCOUNT_ENABLE_CASH_REMOVE_FIELD_API_NAME.add(TPMDealerActivityCostFields.GOODS_PAY_USAGE);
        FLOW_ENTER_ACCOUNT_ENABLE_CASH_REMOVE_FIELD_API_NAME.add(TPMDealerActivityCostFields.GOODS_PAY_NUMBER);
        FLOW_ENTER_ACCOUNT_ENABLE_CASH_REMOVE_FIELD_API_NAME.add(TPMDealerActivityCostFields.AUDITED_AMOUNT);


        FLOW_ENTER_ACCOUNT_ENABLE_GOODS_REMOVE_FIELD_API_NAME.add(TPMDealerActivityCostFields.CASH_USAGE);
        FLOW_ENTER_ACCOUNT_ENABLE_GOODS_REMOVE_FIELD_API_NAME.add(TPMDealerActivityCostFields.ENTER_ACCOUNT_WAY);
        FLOW_ENTER_ACCOUNT_ENABLE_GOODS_REMOVE_FIELD_API_NAME.add(TPMDealerActivityCostFields.EFFECTIVE_DATE);
        FLOW_ENTER_ACCOUNT_ENABLE_GOODS_REMOVE_FIELD_API_NAME.add(TPMDealerActivityCostFields.EXPIRING_DATE);
        FLOW_ENTER_ACCOUNT_ENABLE_GOODS_REMOVE_FIELD_API_NAME.add(TPMDealerActivityCostFields.GOODS_PAY_NUMBER);
        FLOW_ENTER_ACCOUNT_ENABLE_GOODS_REMOVE_FIELD_API_NAME.add(TPMDealerActivityCostFields.AUDITED_AMOUNT);
    }

    private static void setReadOnlyFieldApiName() {
        CASHING_PRODUCT_READ_ONLY_FIELD_API_NAME.add(TPMDealerActivityCashingProductFields.ACTIVITY_PRODUCT_ID);
        CASHING_PRODUCT_READ_ONLY_FIELD_API_NAME.add(TPMDealerActivityCashingProductFields.PRICE);
        CASHING_PRODUCT_READ_ONLY_FIELD_API_NAME.add(TPMDealerActivityCashingProductFields.UNIT);

        DEALER_COST_READ_ONLY_FIELD_API_NAME.add(TPMDealerActivityCostFields.CASHING_FUND_ACCOUNT_ID);

    }

    private static void setRequiredFieldApiName() {
        CASHING_PRODUCT_REQUIRED_FIELD_API_NAME.add(TPMDealerActivityCashingProductFields.PRODUCT_ID);

        // DEALER_COST_ADD_EDIT_REQUIRED_FIELD_API_NAME.add(TPMDealerActivityCostFields.DEALER_ID);

        FLOW_LAYOUT_REQUIRED_FIELD_API_NAME.add(TPMDealerActivityCostFields.CONFIRMED_AMOUNT);
        FLOW_LAYOUT_REQUIRED_FIELD_API_NAME.add(TPMDealerActivityCostFields.GOODS_PAY_NUMBER);
        FLOW_LAYOUT_REQUIRED_FIELD_API_NAME.add(TPMDealerActivityCostFields.CASHING_FUND_ACCOUNT_ID);

    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Arg extends StandardDescribeLayoutController.Arg {

        @JSONField(name = "activity_id")
        @JsonProperty("activity_id")
        private String activityId;
    }
}
