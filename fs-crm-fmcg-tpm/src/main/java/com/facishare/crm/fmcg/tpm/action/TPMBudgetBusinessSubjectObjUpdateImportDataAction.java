package com.facishare.crm.fmcg.tpm.action;

import com.beust.jcommander.internal.Lists;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetBusinessSubjectFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetActivitySubjectService;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportDataAction;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/18 下午3:01
 */
public class TPMBudgetBusinessSubjectObjUpdateImportDataAction extends StandardUpdateImportDataAction {

    private IBudgetActivitySubjectService budgetActivitySubjectService = SpringUtil.getContext().getBean(IBudgetActivitySubjectService.class);


    @Override
    protected Result after(Arg arg, Result result) {
        dealSubjectLevel();
        return super.after(arg, result);
    }

    private void dealSubjectLevel() {
        if (!CollectionUtils.isEmpty(this.actualList)) {
            Set<String> topSubjectIds = this.actualList.stream().filter(v -> Strings.isNullOrEmpty(v.get(TPMBudgetBusinessSubjectFields.PARENT_SUBJECT_ID, String.class))).map(DBRecord::getId).collect(Collectors.toSet());
            List<String> parentSubjectIds = this.actualList.stream().map(v -> v.get(TPMBudgetBusinessSubjectFields.PARENT_SUBJECT_ID, String.class)).filter(Objects::nonNull).collect(Collectors.toList());
            List<IObjectData> firstLevelSubjects = new ArrayList<>();
            while (!CollectionUtils.isEmpty(parentSubjectIds)) {
                parentSubjectIds = parentSubjectIds.stream().filter(v -> !topSubjectIds.contains(v)).collect(Collectors.toList());
                List<IObjectData> parentList = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), parentSubjectIds, ApiNames.TPM_BUDGET_BUSINESS_SUBJECT);
                parentSubjectIds.clear();
                for (IObjectData subject : parentList) {
                    String parentId = subject.get(TPMBudgetBusinessSubjectFields.PARENT_SUBJECT_ID, String.class);
                    Integer level = subject.get(TPMBudgetBusinessSubjectFields.SUBJECT_LEVEL, Integer.class);
                    if (Strings.isNullOrEmpty(parentId)) {
                        topSubjectIds.add(subject.getId());
                        if (level == null) {
                            firstLevelSubjects.add(subject);
                            subject.set(TPMBudgetBusinessSubjectFields.SUBJECT_LEVEL, 1);
                        }
                    } else if (level == null) {
                        parentSubjectIds.add(parentId);
                    } else {
                        topSubjectIds.add(parentId);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(firstLevelSubjects)) {
                serviceFacade.batchUpdateByFields(actionContext.getUser(), firstLevelSubjects, Lists.newArrayList(TPMBudgetBusinessSubjectFields.SUBJECT_LEVEL));
            }

            ParallelUtils.createParallelTask().submit(() -> {
                Map<String, IObjectData> topSubjects = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), new ArrayList<>(topSubjectIds), ApiNames.TPM_BUDGET_BUSINESS_SUBJECT)
                        .stream().collect(Collectors.toMap(IObjectData::getId, v -> v));
                topSubjects.forEach((subjectId, subject) -> budgetActivitySubjectService.resetSubjectLevel(actionContext.getUser(), subjectId, subject.get(TPMBudgetBusinessSubjectFields.SUBJECT_LEVEL, Integer.class, 1) + 1, Lists.newArrayList(subjectId)));
            }).run();
        }
    }
}

