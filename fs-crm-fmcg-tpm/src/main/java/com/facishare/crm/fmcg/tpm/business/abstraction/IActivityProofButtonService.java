package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDetailController;
import com.facishare.paas.appframework.core.predef.controller.BaseListController;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.exception.MetadataServiceException;

import java.util.List;

public interface IActivityProofButtonService {

    BaseListController.ButtonInfo addProofCustomButton(String tenantId, User user, boolean includeButtonInfo, BaseListController.ButtonInfo buttonInfo, List<ObjectDataDocument> dataList);

    void addProofWebDetailCustomButton(String tenantId, User user, AbstractStandardDetailController.Arg arg, AbstractStandardDetailController.Result result) throws MetadataServiceException;

    void filterRioStoreConfirmButton(User user, String activityId, String layoutAgentType, AbstractStandardDetailController.Result result) throws MetadataServiceException;

    void hideRioStoreConfirmButtonInList(String tenantId,StandardListController.Arg arg, BaseListController.Result result) throws MetadataServiceException;
}
