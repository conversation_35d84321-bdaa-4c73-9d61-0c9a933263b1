package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IAccountLock;
import com.facishare.crm.fmcg.tpm.business.abstraction.IFiscalTimeService;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetDimensionEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.web.manager.BudgetTypeManager;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/10/11 14:41
 */
@Component
@SuppressWarnings("Duplicates")
@Slf4j
public class AccountLock implements IAccountLock {

    public static final long LOCK_WAIT = 6;
    public static final long LOCK_LEASE = 120;

    public static final String SINGLE_ACCOUNT_LOCK_KEY_TEMPLATE = "FMCG:TPM:SINGLE_ACCOUNT:LOCK:%s:%s";
    public static final String PARENT_ACCOUNT_LOCK_KEY_TEMPLATE = "FMCG:TPM:PARENT_ACCOUNT:LOCK:%s:%s";
    public static final String SAME_DIMENSION_ACCOUNT_LOCK_KEY_TEMPLATE = "FMCG:TPM:SAME_DIMENSION_ACCOUNT:LOCK:%s:%s:%s";

    @Resource
    private RedissonClient redissonCmd;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private BudgetTypeManager budgetTypeManager;
    @Resource
    private IFiscalTimeService fiscalTimeService;

    @Override
    public boolean tryLock(String tenantId, IObjectData data, BudgetTypeNodeEntity node) {
        return tryLock(tenantId, data, node, LOCK_WAIT);
    }

    @Override
    public boolean tryLock(String tenantId, IObjectData data, BudgetTypeNodeEntity node, long wait) {
        String parentId = data.get(TPMBudgetAccountFields.PARENT_ID, String.class);
        switch (node.getControlStrategy()) {
            case "full_limit":
                // 强控 : 锁指定预算表
                return tryLockSingleAccount(tenantId, data.getId(), wait, LOCK_LEASE);
            case "unlimited":
                // 不控制 : 锁父预算表，无父预算表时锁指定预算表
                if (Strings.isNullOrEmpty(parentId)) {
                    return tryLockSingleAccount(tenantId, data.getId(), wait, LOCK_LEASE);
                } else {
                    return tryLockParentAccount(tenantId, parentId, wait, LOCK_LEASE);
                }
            case "custom_dimension_limit":
                // 自定义维度 : 锁父预算表并锁定同维度预算表
                return tryLockParentAccount(tenantId, parentId, wait, LOCK_LEASE) && tryLockSameDimensionAccount(tenantId, data, node, wait, LOCK_LEASE);
            default:
                return false;
        }
    }

    @Override
    public boolean tryLock(String tenantId, String id) {
        IObjectData account = serviceFacade.findObjectData(User.systemUser(tenantId), id, ApiNames.TPM_BUDGET_ACCOUNT);
        String typeId = account.get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class);
        String nodeId = account.get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class);
        BudgetTypeNodeEntity node = budgetTypeManager.getNode(tenantId, typeId, nodeId);

        return tryLock(tenantId, account, node);
    }

    @Override
    public boolean tryLock(String tenantId, String id, long wait) {
        IObjectData account = serviceFacade.findObjectData(User.systemUser(tenantId), id, ApiNames.TPM_BUDGET_ACCOUNT);
        String typeId = account.get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class);
        String nodeId = account.get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class);
        BudgetTypeNodeEntity node = budgetTypeManager.getNode(tenantId, typeId, nodeId);

        return tryLock(tenantId, account, node, wait);
    }

    private boolean reentrantLock(String key, long wait, long lease) {
        RLock lock = redissonCmd.getLock(key);
        log.info("[budget_lock] try lock budget : {}", key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            return true;
        }
        try {
            return lock.tryLock(wait, lease, TimeUnit.SECONDS);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_ACCOUNT_LOCK_0), key));
        }
    }

    private void unlock(String key) {
        RLock lock = redissonCmd.getLock(key);
        log.info("[budget_lock] unlock budget : {}", key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    private boolean tryLockParentAccount(String tenantId, String id, long wait, long lease) {
        String key = String.format(PARENT_ACCOUNT_LOCK_KEY_TEMPLATE, tenantId, id);
        return reentrantLock(key, wait, lease);
    }

    private boolean tryLockSingleAccount(String tenantId, String id, long wait, long lease) {
        String key = String.format(SINGLE_ACCOUNT_LOCK_KEY_TEMPLATE, tenantId, id);
        return reentrantLock(key, wait, lease);
    }

    private boolean tryLockSameDimensionAccount(String tenantId, IObjectData data, BudgetTypeNodeEntity node, long wait, long lease) {
        String dimensionKey = getDimensionKey(tenantId, data, node);
        String key = String.format(SAME_DIMENSION_ACCOUNT_LOCK_KEY_TEMPLATE, tenantId, node.getNodeId(), dimensionKey);
        return reentrantLock(key, wait, lease);
    }

    @Override
    public void unlock(String tenantId, String id) {
        IObjectData account = serviceFacade.findObjectData(User.systemUser(tenantId), id, ApiNames.TPM_BUDGET_ACCOUNT);
        String typeId = account.get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class);
        String nodeId = account.get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class);
        BudgetTypeNodeEntity node = budgetTypeManager.getNode(tenantId, typeId, nodeId);

        unlock(tenantId, account, node);
    }

    @Override
    public boolean tryLock(RequestContext requestContext, String tenantId, IObjectData data, BudgetTypeNodeEntity node) {
        requestContext.setAttribute(String.format("budget_lock:%s:%s", tenantId, data.getId()), node);
        return tryLock(tenantId, data, node);
    }

    @Override
    public void unlock(RequestContext requestContext) {
        requestContext.getAttributes().forEach((k, v) -> {
            if (k instanceof String && ((String) k).startsWith("budget_lock:")) {
                String[] keys = ((String) k).split(":");
                unlock(keys[1], keys[2]);
            }
        });
    }

    @Override
    public void unlock(String tenantId, IObjectData data, BudgetTypeNodeEntity node) {
        String parentId = data.get(TPMBudgetAccountFields.PARENT_ID, String.class);
        switch (node.getControlStrategy()) {
            case "full_limit":
                unlockSingleAccount(tenantId, data.getId());
                break;
            case "unlimited":
                if (Strings.isNullOrEmpty(parentId)) {
                    unlockSingleAccount(tenantId, data.getId());
                } else {
                    unlockParentAccount(tenantId, parentId);
                }
                break;
            case "custom_dimension_limit":
                unlockParentAccount(tenantId, parentId);
                unlockSameDimensionAccount(tenantId, data, node);
                break;
            default:
                break;
        }
    }

    private void unlockSingleAccount(String tenantId, String id) {
        unlock(String.format(SINGLE_ACCOUNT_LOCK_KEY_TEMPLATE, tenantId, id));
    }

    @NotNull
    private String getDimensionKey(String tenantId, IObjectData data, BudgetTypeNodeEntity node) {
        String department = CommonUtils.cast(data.get(TPMBudgetAccountFields.BUDGET_DEPARTMENT), String.class).get(0);
        String periodFieldApiName = "budget_period_" + node.getTimeDimension();
        String time = String.valueOf(fiscalTimeService.correctPeriodTime(tenantId, node.getTimeDimension(), data.get(periodFieldApiName, Long.class)));

        StringBuilder dimensionKey = new StringBuilder(String.format("D-%s.T-%s", department, time));
        List<String> apiNames = node.getDimensions().stream().map(BudgetDimensionEntity::getApiName).sorted().collect(Collectors.toList());
        for (String apiName : apiNames) {
            String value = data.get(apiName, String.class);
            dimensionKey.append(String.format(".%s-%s", apiName, value));
        }
        return dimensionKey.toString();
    }

    private void unlockSameDimensionAccount(String tenantId, IObjectData data, BudgetTypeNodeEntity node) {
        String dimensionKey = getDimensionKey(tenantId, data, node);
        unlock(String.format(SAME_DIMENSION_ACCOUNT_LOCK_KEY_TEMPLATE, tenantId, node.getNodeId(), dimensionKey));
    }

    private void unlockParentAccount(String tenantId, String id) {
        unlock(String.format(PARENT_ACCOUNT_LOCK_KEY_TEMPLATE, tenantId, id));
    }
}
