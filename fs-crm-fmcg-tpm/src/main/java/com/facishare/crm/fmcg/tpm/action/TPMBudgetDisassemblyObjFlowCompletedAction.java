package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountDetailFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetDisassemblyFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IAsyncBudgetDisassemblyService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetAccountDetailService;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.tpm.business.enums.DisassemblyActionCode;
import com.facishare.crm.fmcg.tpm.business.enums.MainType;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.crm.fmcg.tpm.utils.TraceUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Sets;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@SuppressWarnings("all")
public class TPMBudgetDisassemblyObjFlowCompletedAction extends StandardFlowCompletedAction {

    private final BudgetTypeDAO budgetTypeDAO = SpringUtil.getContext().getBean(BudgetTypeDAO.class);
    private final IBudgetAccountDetailService budgetAccountDetailService = SpringUtil.getContext().getBean(IBudgetAccountDetailService.class);
    private final IAsyncBudgetDisassemblyService asyncBudgetDisassemblyService = SpringUtil.getContext().getBean(IAsyncBudgetDisassemblyService.class);
    private IObjectData masterData;

    private final Set<String> APPROVAL_TRIGGER_TYPE = Sets.newHashSet("1", "2");

    private boolean isEditDisassemblyCustomField = false;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        log.info("disassembly approve before start...");
        masterData = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getDataId(), ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ);

        if (TPMGrayUtils.isAllowEditDisassemblyCustomField(actionContext.getTenantId())) {
            List<IObjectData> details = budgetAccountDetailService.queryDetailsByRelatedData(actionContext.getTenantId(), ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ, this.dbData.getId());
            if (!CollectionUtils.isEmpty(details)) {
                String budgetId = masterData.get(TPMBudgetDisassemblyFields.SOURCE_BUDGET_ACCOUNT_ID, String.class);
                //查到的明细 有此条拆解转出表的：支出，拆解-转出  记录才说明这条数据已经拆解成功过
                List<IObjectData> collect = details.stream().filter(obj -> Objects.equals(obj.get("budget_account_id", String.class), budgetId) &&
                                Objects.equals(obj.get(TPMBudgetAccountDetailFields.BUSINESS_TYPE), BizType.TAKE_APART_OUT.value()) &&
                                Objects.equals(obj.get(TPMBudgetAccountDetailFields.MAIN_TYPE), MainType.EXPENDITURE.value()))
                        .collect(Collectors.toList());
                log.info("collect:{}", JSON.toJSONString(collect));
                if (!CollectionUtils.isEmpty(collect)) {
                    //存在拆解明细说明已经通过审批拆解完成一次，这次只能修改自定义字段
                    log.info("isEditDisassemblyCustomField is true");
                    isEditDisassemblyCustomField = true;
                    return;
                }
            }
        }
        String approvalTraceId = TraceUtil.getApprovalCallbackTraceId(this.arg.getCallbackData());
        if (Strings.isNullOrEmpty(approvalTraceId)) {
            log.info("disassembly approvalTraceId is null.");
            throw new MetaDataBusinessException("approvalTraceId null");
        }
        String businessTraceId = TraceUtil.getBusinessCallbackTraceId(this.arg.getCallbackData());
        if (Strings.isNullOrEmpty(businessTraceId)) {
            log.info("disassembly businessTraceId is null.");
            throw new MetaDataBusinessException("businessTraceId null");
        }
        log.info("disassembly approve trace id : {}, business trace id : {}", approvalTraceId, businessTraceId);
        String sourceAccountId = masterData.get(TPMBudgetDisassemblyFields.SOURCE_BUDGET_ACCOUNT_ID, String.class);
        IObjectData sourceAccount = serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), sourceAccountId, ApiNames.TPM_BUDGET_ACCOUNT);

        String typeId = masterData.get(TPMBudgetDisassemblyFields.BUDGET_TYPE_ID, String.class);
        BudgetTypePO budgetType = budgetTypeDAO.get(actionContext.getTenantId(), typeId);
        if (Objects.isNull(budgetType)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_OBJ_FLOW_COMPLETED_ACTION_0));
        }
        String targetNodeId = masterData.get(TPMBudgetDisassemblyFields.TARGET_BUDGET_NODE_ID, String.class);
        BudgetTypeNodeEntity targetNode = budgetType.getNodes().stream().filter(f -> f.getNodeId().equals(targetNodeId)).findFirst().orElse(null);
        if (Objects.isNull(targetNode)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_OBJ_FLOW_COMPLETED_ACTION_1));
        }


    }

    @Override
    protected Result doAct(Arg arg) {
        log.info("disassembly flow completed doActStart...");
        Result innerResult = super.doAct(arg);
        if (isEditDisassemblyCustomField) {
            return innerResult;
        }

        if (Boolean.TRUE.equals(innerResult.getSuccess())) {
            if (APPROVAL_TRIGGER_TYPE.contains(arg.getTriggerType()) && arg.isPass()) {
                this.doDisassembly();
            } else {
                //cancel
                //解冻
                this.unfreeze();
            }
        }

        return innerResult;

    }

    private void unfreeze() {
        ParallelUtils.createParallelTask().submit(() -> {
            asyncBudgetDisassemblyService.unFrozen(actionContext.getUser(), arg.getDataId());
        }).run();

    }

    private void doDisassembly() {
        log.info("async disassembly doDisassembly...");
        ParallelUtils.createParallelTask().submit(() -> {
            asyncBudgetDisassemblyService.doDisassembly(actionContext.getUser(), arg.getDataId(), DisassemblyActionCode.FLOW_COMPLETED.value());
        }).run();
    }
}
