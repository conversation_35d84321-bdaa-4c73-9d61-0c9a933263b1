package com.facishare.crm.fmcg.tpm.facade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.fmcg.common.adapter.dto.pay.TransferDetail;
import com.facishare.crm.fmcg.tpm.api.script.CleanMongoData;
import com.facishare.crm.fmcg.tpm.api.script.CleanTPMMongo;
import com.facishare.crm.fmcg.tpm.api.script.CopyTPMMongo;
import com.facishare.crm.fmcg.tpm.business.BudgetAccountDetailService;
import com.facishare.crm.fmcg.tpm.business.BudgetCalculateService;
import com.facishare.crm.fmcg.tpm.business.BudgetProvisionService;
import com.facishare.crm.fmcg.tpm.business.RedPacketService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetConsumeV2Service;
import com.facishare.crm.fmcg.tpm.business.abstraction.IEnableCacheService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IMengNiuAICalculateService;
import com.facishare.crm.fmcg.tpm.dao.paas.BudgetAccountDetailMapper;
import com.facishare.crm.fmcg.tpm.retry.distribute.RetryTaskDistribute;
import com.facishare.crm.fmcg.tpm.service.abstraction.ScriptService;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.fxiaoke.functions.utils.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/8 下午2:46
 */

@Service
@ServiceModule("script_inner_service")
public class ScriptInnerService {

    @Resource
    private ScriptService scriptService;

    @Resource
    private BudgetCalculateService budgetCalculateService;

    @Resource
    private BudgetAccountDetailMapper budgetAccountDetailMapper;

    @Resource
    private IEnableCacheService iEnableCacheService;

    @Resource
    private BudgetAccountDetailService budgetAccountDetailService;

    @Resource
    private RetryTaskDistribute retryTaskDistribute;

    @Resource
    private RedPacketService redPacketService;
    @Resource
    private BudgetProvisionService budgetProvisionService;

    @Resource
    private IBudgetConsumeV2Service budgetConsumeV2Service;

    @Resource
    private IMengNiuAICalculateService mengNiuAICalculateService;

    @ServiceMethod("cover_null_unique_id")
    public String coverNullUniqueId(List<String> tenantIds, ServiceContext serviceContext) {
        scriptService.setDefaultUniqueId(tenantIds);
        return "success";
    }

    @ServiceMethod("copy_tpm_mongo")
    public CopyTPMMongo.Result copyTPMMongo(CopyTPMMongo.Arg arg, ServiceContext serviceContext) {
        scriptService.copyMongo(arg.getFromTenantId(), arg.getToTenantId());
        return new CopyTPMMongo.Result();
    }

    @ServiceMethod("clean_tpm_mongo")
    public String cleanTPMMongo(CleanTPMMongo.Arg arg, ServiceContext serviceContext) {
        scriptService.cleanTPMMongo(arg.getTenantIds(), arg.isForceDelete());
        return "success";
    }

    @ServiceMethod("clean_mongo_data")
    public String cleanMongoData(CleanMongoData.Arg arg, ServiceContext serviceContext) {
        scriptService.cleanTPMMongo2(arg.getTenantId(), arg.getDb(), arg.getPageSize(), arg.getMaxSize());
        return "success";
    }

    @ServiceMethod("recalculate_budget")
    public String recalculateBudget(JSONObject arg, ServiceContext serviceContext) {
        budgetCalculateService.recalculateBudgetAmount(User.systemUser(arg.getString("tenant_id")), arg.getString("budget_id"));
        return "success";
    }

    @ServiceMethod("update_budget_detail_amount")
    public String updateBudgetDetailAmount(JSONObject arg, ServiceContext serviceContext) {
        String tenantId = arg.getString("tenant_id");
        String id = arg.getString("id");
        String amount = arg.getString("amount");
        if (!Strings.isDigit(tenantId) || !Strings.isDigit(amount.replace(".", "")) || id.length() != 24 || id.contains("#") || id.contains("-") || id.contains("/")) {
            return "fuck";
        }
        budgetAccountDetailMapper.updateBudgetDetailMessage(tenantId, id, new BigDecimal(amount));
        return "success";
    }

    @ServiceMethod("update_store_range")
    public String updateStoreRange(JSONObject arg, ServiceContext serviceContext) {
        return JSON.toJSONString(scriptService.updateStoreRange(arg.getObject("tenant_ids", new TypeReference<List<String>>() {
        }), arg.getBooleanValue("force_update")));
    }

    @ServiceMethod("reset_enable_cache_by_account")
    public String resetCacheByAccount(JSONObject arg, ServiceContext serviceContext) {
        iEnableCacheService.resetCacheByAccount(serviceContext.getTenantId(), arg.getString("account_id"), arg.getString("change_type"), arg.getObject("change_fields", new TypeReference<List<String>>() {
        }));
        return "success";
    }


    @ServiceMethod("update_activity_default_value")
    public String updateActivityAndWriteOffDefaultValue(JSONObject arg, ServiceContext serviceContext) {
        scriptService.updateActivityAndWriteOffDefaultValue(arg.getObject("tenant_ids", new TypeReference<List<String>>() {
        }));
        return "success";
    }

    @ServiceMethod("update_activity_cost_activity_type_value")
    public String updateActivityCostActivityTypeValue(JSONObject arg, ServiceContext serviceContext) {
        scriptService.updateActivityCostObjActivityTypeField(arg.getObject("tenant_ids", new TypeReference<List<String>>() {
        }));
        return "success";
    }


    @ServiceMethod("add_activity_store_if_activity_has_dealer")
    public String addActivityStoreIfActivityHasDealer(JSONObject arg, ServiceContext serviceContext) {
        scriptService.addActivityStoreIfActivityHasDealer(arg.getObject("tenant_ids", new TypeReference<List<String>>() {
        }));
        return "success";
    }

    @ServiceMethod("delete_budget_details")
    public String deleteBudgetDetails(JSONObject arg, ServiceContext serviceContext) {
        budgetAccountDetailService.deleteUselessBudgetDetail(arg.getString("tenant_id"), arg.getObject("detail_ids", new TypeReference<List<String>>() {
        }));
        return "success";
    }

    @ServiceMethod("let_field_repeat")
    public String letFieldRepeat(JSONObject arg, ServiceContext serviceContext) {
        String apiName = arg.getString("describe_api_name") == null ? "TPMBudgetBusinessSubjectObj" : arg.getString("describe_api_name");
        String fieldApiName = arg.getString("field_api_name") == null ? "name" : arg.getString("field_api_name");
        return scriptService.changeObjectFieldSupportRepeat(serviceContext.getTenantId(), apiName, fieldApiName);
    }

    @ServiceMethod("handle_retry_task")
    public String retryTask(JSONObject arg, ServiceContext serviceContext) {
        retryTaskDistribute.handler(arg.getString("handler"), arg.getString("task_id"));
        return "success";
    }

    @ServiceMethod("wx_transfer")
    public String wxTransfer(JSONObject arg, ServiceContext serviceContext) {
        return scriptService.wxTransfer(arg.getString("tenant_id"), arg.getJSONObject("transfer_data"));
    }

    @ServiceMethod("query_wx_transfer_detail")
    public List<TransferDetail> queryTransferDetails(JSONObject arg, ServiceContext serviceContext) {
        return scriptService.queryTransferDetails(arg.getString("tenant_id"), arg.getString("batch_transfer_id"));
    }

    @ServiceMethod("update_object")
    public String updateObject(JSONObject arg, ServiceContext serviceContext) {
        return scriptService.updateObject(arg.getString("tenant_id"), arg.getString("apiName"), arg.getString("objectId"));
    }

    @ServiceMethod("query_by_sql")
    public List<Map> queryByTenant(JSONObject arg, ServiceContext serviceContext) {
        return scriptService.queryByTenant(serviceContext.getTenantId(), arg.getString("sql"));
    }

    @ServiceMethod("refresh_processing_red_packet")
    public String refreshProcessingRedPacket(JSONObject arg, ServiceContext serviceContext) {
        redPacketService.refreshProcessingRedPacket(serviceContext.getTenantId());
        return "success";
    }

    @ServiceMethod("refresh_processing_withdraw")
    public String refreshProcessingWithdraw(JSONObject arg, ServiceContext serviceContext) {
        redPacketService.refreshProcessingWithdraw(serviceContext.getTenantId());
        return "success";
    }

    @ServiceMethod("budget_provision_occupy")
    public String budgetProvisionOccupy(JSONObject arg, ServiceContext serviceContext) {
        budgetProvisionService.occupyBudgetByProvision(serviceContext.getTenantId());
        return "success";
    }

    @ServiceMethod("calculate_mengniu_ai")
    public Boolean calculateMengNiuAi(JSONObject arg, ServiceContext serviceContext) {
        return mengNiuAICalculateService.isFitMengNiuAiRule(arg.getString("activity_tenant_id"), arg.getString("activity_data_id"), arg.getString("tenant_id"),
                arg.getString("rule_String"), arg.getString("trigger_api_name"), arg.getString("trigger_object_id"), arg.getBooleanValue("is_write_error"));
    }

    @ServiceMethod("fillFreshStandard")
    public String fillFreshStandard(JSONObject arg, ServiceContext serviceContext) {
        scriptService.fillFreshStandard(serviceContext.getTenantId(), arg.getLong("create_time"));
        return "success";
    }


    @ServiceMethod("productRangeTest")
    public Map<String, Boolean> productRangeTest(JSONObject arg, ServiceContext serviceContext) {
        return scriptService.productRangeTest(arg.getString("tenant_id"), arg.getJSONArray("activity_ids").toJavaList(String.class), arg.getString("product_id"), arg.getString("serial_number_id"));
    }

    @ServiceMethod("changeActivityRewardPersonId")
    public String changeActivityRewardPersonId(JSONObject arg, ServiceContext serviceContext) {
        scriptService.changeActivityRewardPersonId(arg.get("tenant_id").toString());
        return "success";
    }

    @ServiceMethod("batchTriggerSelfDefineReward")
    public List batchTriggerSelfDefineReward(JSONObject arg, ServiceContext serviceContext) {

        return scriptService.batchTriggerSelfDefineReward(arg.get("tenant_id").toString(), arg.getJSONArray("sn_ids").toJavaList(String.class));
    }

    @ServiceMethod("triggerMiddleRelease")
    public void triggerMiddleRelease(JSONObject arg, ServiceContext serviceContext) {
        String tenantId = arg.get("tenant_id").toString();
        String apiName = arg.get("describe_api_name").toString();
        budgetConsumeV2Service.middleRelease(User.systemUser(tenantId), apiName, arg.get("data_id").toString(), arg.get("action_mode").toString());
    }

    @ServiceMethod("filterActivityByAccount")
    public List<String> filterActivityByAccount(JSONObject arg, ServiceContext serviceContext) {
        return scriptService.filterActivityByAccount(
                arg.getString("tenant_id"), arg.getString("user_id"),
                arg.getString("store_id"), arg.getJSONArray("activity_types").toJavaList(String.class),
                arg.getJSONArray("department_ids").toJavaList(String.class));
    }

    @ServiceMethod("callBackByPhysicalReward")
    public String callBackByPhysicalReward(JSONObject arg, ServiceContext serviceContext) {
        return scriptService.callBackByPhysicalReward(
                arg.getString("tenant_id"), arg.getJSONArray("physical_reward_ids").toJavaList(String.class));
    }


}
