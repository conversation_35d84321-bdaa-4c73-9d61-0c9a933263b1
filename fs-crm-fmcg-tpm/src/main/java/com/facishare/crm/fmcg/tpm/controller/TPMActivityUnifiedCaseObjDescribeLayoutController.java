package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetNewConsumeRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDescribeLayoutController;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/13 19:33
 */
@Slf4j
@SuppressWarnings("Duplicates")
public class TPMActivityUnifiedCaseObjDescribeLayoutController extends AbstractStandardDescribeLayoutController<TPMActivityUnifiedCaseObjDescribeLayoutController.Arg> {

    private final BudgetNewConsumeRuleDAO budgetNewConsumeRuleDAO = SpringUtil.getContext().getBean(BudgetNewConsumeRuleDAO.class);
    private final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);
    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);

    public static final String EDIT_ACTION = "edit";
    private String ACTIVITY_TYPE_TEMPLATE_ID = "";

    public static final List<String> FIELDS = Lists.newArrayList();
    public static final List<String> READ_FIELDS = Lists.newArrayList();


    static {
        FIELDS.add(TPMActivityUnifiedCaseFields.START_DATE);
        FIELDS.add(TPMActivityUnifiedCaseFields.ACTIVITY_DAYS);
        FIELDS.add(TPMActivityUnifiedCaseFields.WRITE_OFF_CASH);

        READ_FIELDS.add(TPMActivityUnifiedCaseFields.OCCUPY_AMOUNT);
        READ_FIELDS.add(TPMActivityUnifiedCaseFields.ACTIVITY_ACTUAL_AMOUNT);
        READ_FIELDS.add(TPMActivityUnifiedCaseFields.AVAILABLE_AMOUNT);
        READ_FIELDS.add(TPMActivityUnifiedCaseFields.CLOSE_STATUS);
        READ_FIELDS.add(TPMActivityUnifiedCaseFields.CLOSE_TIME);
        READ_FIELDS.add(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS);
        READ_FIELDS.add(TPMActivityUnifiedCaseFields.MODE_TYPE);
    }

    @Override
    protected void before(Arg arg) {

        super.before(arg);
        initData();
    }

    private void initData() {
        if (Strings.isNullOrEmpty(arg.getActivity_type_id())) {
            return;
        }
        ActivityTypeExt activityTypeExt = activityTypeManager.find(controllerContext.getTenantId(), arg.getActivity_type_id());
        ACTIVITY_TYPE_TEMPLATE_ID = activityTypeExt.get().getTemplateId();
    }


    @Override
    protected StandardDescribeLayoutController.Result after(Arg arg, StandardDescribeLayoutController.Result result) {

        setReadOnlyField(result, READ_FIELDS);
        if (EDIT_ACTION.equals(arg.getLayout_type()) && Boolean.TRUE.equals(arg.getInclude_layout())) {
            log.info("overrideEditActionLayout");
            overrideEditActionLayout(arg, result);
        }
        log.info("activity_type_template_id is value {}", ACTIVITY_TYPE_TEMPLATE_ID);
        StandardDescribeLayoutController.Result after = super.after(arg, result);
        ObjectDescribeDocument objectDescribe = after.getObjectDescribe();
        objectDescribe.put("activity_type_template_id", ACTIVITY_TYPE_TEMPLATE_ID);
        after.setObjectDescribe(objectDescribe);
        return after;
    }

    private void overrideEditActionLayout(Arg arg, StandardDescribeLayoutController.Result result) {
        //存在已申请，结束日期，参与部门，归属部门, 说明，附件，金额可编辑，其他预设字段只读
        List<IObjectData> objectData = queryActivityRelation(arg);
        if (!objectData.isEmpty()) {
            setReadPackageField(result);
        }

        // 存在预算流水，设置规则映射的字段为 只读
        List<IObjectData> data = queryBudgetRelation(arg);
        if (!data.isEmpty()) {
            setReadRelationField(result, data);
        }
    }

    private void setReadRelationField(StandardDescribeLayoutController.Result result, List<IObjectData> data) {
        String bizTrace = data.stream().map(v -> v.get(TPMBudgetAccountDetailFields.BIZ_TRACE_ID, String.class)).findFirst().orElse("");
        if (bizTrace.startsWith(TPMBudgetAccountDetailFields.BizCode.BUDGET_CONSUME_RULE.value())) {
            //BUDGET_CONSUME_RULE:6396d60780fb6106aa777a32:cbf2f7f8-560f-487a-a628-8a84b4dbf5e0
            log.info("bizTrace value : {}", bizTrace);
            String[] traceSplit = bizTrace.split(":");
            if (traceSplit.length > 1) {
                String ruleId = traceSplit[1];
                // 查找相关的规则，找到规则映射的字段，
                List<String> ruleField = getRuleField(budgetNewConsumeRuleDAO.get(controllerContext.getTenantId(), ruleId));
                setReadOnlyField(result, ruleField.stream().distinct().collect(Collectors.toList()));
            }
        }
    }

    private List<String> getRuleField(BudgetNewConsumeRulePO consumeRulePO) {
        List<String> fieldList = Lists.newArrayList();
        if (consumeRulePO == null) {
            return fieldList;
        }
        String budgetMethod = consumeRulePO.getBudgetMethod();
        // 手动选择 manual / 自动映射 automatic
        if (BudgetMethodEnum.AUTOMATIC.value().equals(budgetMethod)) {
            for (BudgetTableAutomaticNodeEntity automaticNode : consumeRulePO.getBudgetTableAutomaticNodes()) {
                List<String> targetFields = automaticNode.getFieldRelation().stream().map(BudgetFieldRelationEntity::getTargetField).collect(Collectors.toList());
                fieldList.addAll(targetFields);
            }
        } else if (BudgetMethodEnum.MANUAL.value().equals(budgetMethod)) {
            List<BudgetTableManualNodeEntity> manualNodes = consumeRulePO.getBudgetTableManualNodes();
            for (BudgetTableManualNodeEntity manualNode : manualNodes) {
                BudgetTableDeDuctEntity ductEntity = manualNode.getBudgetTableDeDuctEntity();
                if (ductEntity != null && ductEntity.getApiName().equals(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ)) {
                    fieldList.add(ductEntity.getAmountField());
                }
                BudgetTableFrozenEntity frozenEntity = manualNode.getBudgetTableFrozenEntity();
                if (frozenEntity != null && frozenEntity.getApiName().equals(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ)) {
                    fieldList.add(frozenEntity.getAmountField());
                }
            }
        }
        return fieldList;
    }

    private void setReadOnlyField(StandardDescribeLayoutController.Result result, List<String> readField) {
        try {
            log.info("set read only field is {}", readField);
            LayoutExt layout = LayoutExt.of(result.getLayout().toLayout());
            FormComponentExt form = layout.getFormComponent().orElse(null);
            if (!Objects.isNull(form)) {
                List<IFieldSection> sections = form.getFieldSections();
                for (IFieldSection section : sections) {
                    section.setFields(section.getFields().stream().filter(field -> !field.getFieldName().equals(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS)).collect(Collectors.toList()));
                    section.getFields().forEach(field -> {
                        if (readField.contains(field.getFieldName())) {
                            field.setReadOnly(true);
                        }
                        if (TPMActivityUnifiedCaseFields.ACTIVITY_TYPE.equals(field.getFieldName())) {
                            field.setReadOnly(true);
                        }
                    });
                }
            }
        } catch (Exception ex) {
            log.info("override TPMActivityUnifiedCaseObj layout cause unknown exception  : ", ex);
        }
    }

    private void setReadPackageField(StandardDescribeLayoutController.Result result) {
        try {
            LayoutExt layout = LayoutExt.of(result.getLayout().toLayout());
            FormComponentExt form = layout.getFormComponent().orElse(null);
            if (!Objects.isNull(form)) {
                List<IFieldSection> sections = form.getFieldSections();
                for (IFieldSection section : sections) {
//                    section.setFields(section.getFields().stream().filter(field -> !field.getFieldName().equals(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS)).collect(Collectors.toList()));
                    section.setFields(section.getFields().stream().filter(field -> !field.getFieldName().equals(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS)).collect(Collectors.toList()));
                    section.getFields().forEach(field -> {
                        if (FIELDS.contains(field.getFieldName())) {
                            field.setReadOnly(true);
                        }
                    });
                }
            }
        } catch (Exception ex) {
            log.info("override TPMActivityUnifiedCaseObj layout cause unknown exception  : ", ex);
        }
    }

    private List<IObjectData> queryBudgetRelation(Arg arg) {
        if (!budgetService.isOpenBudge(Integer.parseInt(controllerContext.getTenantId()))) {
            return Collections.emptyList();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);

        Filter apiNameFilter = new Filter();
        apiNameFilter.setFieldName(TPMBudgetAccountDetailFields.RELATED_OBJECT_API_NAME);
        apiNameFilter.setOperator(Operator.EQ);
        apiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ));

        Filter idFilter = new Filter();
        idFilter.setFieldName(TPMBudgetAccountDetailFields.RELATED_OBJECT_DATA_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(arg.getData_id()));

        Filter detailStatusFilter = new Filter();
        detailStatusFilter.setFieldName(TPMBudgetAccountDetailFields.DETAIL_STATUS);
        detailStatusFilter.setOperator(Operator.EQ);
        detailStatusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountDetailFields.DetailStatus.INCLUDE));

        query.setFilters(Lists.newArrayList(apiNameFilter, idFilter, detailStatusFilter));

        return serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_BUDGET_ACCOUNT_DETAIL, query).getData();
    }


    private List<IObjectData> queryActivityRelation(Arg arg) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);

        Filter idFilter = new Filter();
        idFilter.setFieldName(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(arg.getData_id()));

        Filter deletedFilter = new Filter();
        deletedFilter.setFieldName(CommonFields.IS_DELETED);
        deletedFilter.setOperator(Operator.EQ);
        deletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(idFilter, deletedFilter));

        return serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query).getData();
    }


    @EqualsAndHashCode(callSuper = true)
    @Data
    @ToString
    public static class Arg extends StandardDescribeLayoutController.Arg implements Serializable {
        @JSONField(name = "M50")
        private String activity_type_id;
    }

}
