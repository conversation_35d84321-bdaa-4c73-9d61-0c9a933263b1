package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.tpm.utils.FormatUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * author: wuyx
 * description:
 * createTime: 2022/7/20 18:31
 */
public class TPMBudgetTransferDetailObjBulkInvalidAction extends StandardBulkInvalidAction {

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        bulkValidateBudgetStatus();
    }

    private void bulkValidateBudgetStatus() {
        List<String> enableList = Lists.newArrayList();
        List<String> disableList = Lists.newArrayList();
        for (IObjectData transfer : this.dataList) {
            String lifeStatus = transfer.get(CommonFields.LIFE_STATUS, String.class);
            if (!CommonFields.LIFE_STATUS__INEFFECTIVE.equals(lifeStatus)) {
                enableList.add(transfer.getName());
            }
        }
        if (!enableList.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TRANSFER_DETAIL_OBJ_BULK_INVALID_ACTION_0) + FormatUtil.join(disableList, ","));
        }
    }

}
