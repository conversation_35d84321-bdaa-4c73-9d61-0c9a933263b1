package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkEditByFieldAction;


@SuppressWarnings("all")
public class TPMBudgetDisassemblyObjBulkEditByFieldAction extends StandardBulkEditByFieldAction {

    @Override
    protected void before(Arg arg) {
        throw new ValidateException("bulk edit by field not allowed!");
    }
}