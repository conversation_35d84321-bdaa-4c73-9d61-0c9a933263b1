package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.business.abstraction.IActivityProofButtonService;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;

public class TPMActivityProofObjRelatedListController extends StandardRelatedListController {
    private IActivityProofButtonService activityProofButtonService = SpringUtil.getContext().getBean(IActivityProofButtonService.class);

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        if (TPMGrayUtils.TPMUseEs(controllerContext.getTenantId())) {
            query.setSearchSource("es");
        }
        super.beforeQueryData(query);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        fakeButton(result);
        return super.after(arg, result);
    }

    private void fakeButton(Result result) {

        User user = controllerContext.getUser();
        if (user.isOutUser()) {
            user = User.systemUser(controllerContext.getTenantId());
        }
        ButtonInfo buttonInfo = result.getButtonInfo();
        List<ObjectDataDocument> dataList = result.getDataList();
        String tenantId = controllerContext.getTenantId();

        ButtonInfo buttonInfoResult = activityProofButtonService.addProofCustomButton(tenantId, user, true, buttonInfo, dataList);
        result.setButtonInfo(buttonInfoResult);
    }
}
