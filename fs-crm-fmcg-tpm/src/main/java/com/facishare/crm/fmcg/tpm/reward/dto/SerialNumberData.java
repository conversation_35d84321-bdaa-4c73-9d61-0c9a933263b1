package com.facishare.crm.fmcg.tpm.reward.dto;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@Builder
public class SerialNumberData implements Serializable {

    public static final String OUTER_TYPE = "outer";
    public static final String INNER_TYPE = "inner";
    public static final String COUPON_TYPE = "coupon";

    private String realCode;

    private String snId;

    private String skuId;

    private String name;

    private String type;

    private Long manufactureDate;

    private IObjectData data;
}