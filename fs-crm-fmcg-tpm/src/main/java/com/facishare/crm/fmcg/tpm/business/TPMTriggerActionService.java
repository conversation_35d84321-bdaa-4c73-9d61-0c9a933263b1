package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.tpm.business.abstraction.ITPMTriggerActionService;
import com.facishare.crm.fmcg.tpm.web.contract.TriggerAction;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.BaseService;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TPMTriggerActionService extends BaseService implements ITPMTriggerActionService {

    public static final String REQUEST_FROM = "request_from";
    public static final String REQUEST_APP_NAME = "TPM";

    @Override
    public BaseObjectSaveAction.Result triggerAction(TriggerAction.Arg arg) {

        ActionContext addActionContext = new ActionContext(
                RequestContext.builder().tenantId(arg.getUser().getTenantId()).user(arg.getUser()).build(),
                arg.getApiName(),
                arg.getActionName()
        );

        addActionContext.setAttribute("triggerWorkflow", arg.isTriggerWorkflow());
        addActionContext.setAttribute("triggerFlow", arg.isTriggerFlow());

        BaseObjectSaveAction.Arg saveArg = new BaseObjectSaveAction.Arg();
        saveArg.setObjectData(ObjectDataDocument.of(arg.getObjectData()));

        if (CollectionUtils.isNotEmpty(arg.getDetails())) {
            Map<String, List<ObjectDataDocument>> detailsMap = Maps.newHashMap();
            detailsMap.put(arg.getDetailApiName(), arg.getDetails().stream().map(ObjectDataDocument::of).collect(Collectors.toList()));
            saveArg.setDetails(detailsMap);
        }
        BaseObjectSaveAction.Result result;
        try {
            result = serviceFacade.triggerAction(addActionContext, saveArg, BaseObjectSaveAction.Result.class);
        } catch (Exception exception) {
            log.info("triggerAction error is {}", exception.getMessage());
            throw exception;
        }

        return result;
    }
}
