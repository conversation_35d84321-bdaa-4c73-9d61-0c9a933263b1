package com.facishare.crm.fmcg.tpm.business;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityAgreementFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityDetailFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.api.agreement.YLActivityDetailBulkCreate;
import com.facishare.crm.fmcg.tpm.api.agreement.YLAgreementBulkCreate;
import com.facishare.crm.fmcg.tpm.api.agreement.YLAgreementStatusUpdate;
import com.facishare.crm.fmcg.tpm.business.abstraction.YLTPMService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.BaseService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
//IgnoreI18nFile
@Service
@Slf4j
public class YLTPMServiceImpl extends BaseService implements YLTPMService {
    public static final Map<String, String> activityStatusMap = new HashMap<>();

    static {
        activityStatusMap.put("schedule", "未生效");
        activityStatusMap.put("in_progress", "已生效");
        activityStatusMap.put("end", "已过期");
        activityStatusMap.put("other", "其他");
    }

    @Override
    public YLAgreementBulkCreate.Result agreementBulkCreate(YLAgreementBulkCreate.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        if (Objects.isNull(arg)) {
            throw new ValidateException(I18N.text(I18NKeys.Y_L_SERVICE_IMPL_0));
        }
        for (YLAgreementBulkCreate.SingleArg singleArg : arg.getArgs()) {
            if (Objects.isNull(singleArg.getObjectData())) {
                throw new ValidateException(I18N.text(I18NKeys.Y_L_SERVICE_IMPL_1));
            }
            String linkWhite = ObjectDataDocument.of(singleArg.getObjectData()).toObjectData().get("link_whitelist__c", String.class);
            if (StringUtils.isEmpty(linkWhite)) {
                throw new ValidateException(I18N.text(I18NKeys.Y_L_SERVICE_IMPL_2));
            }
        }
        ParallelUtils.createParallelTask().submit(() -> {
        doCreateAgreement(context, arg);
        }).run();
        return new YLAgreementBulkCreate.Result();
    }

    @Override
    public YLAgreementStatusUpdate.Result agreementStatusUpdate(YLAgreementStatusUpdate.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        if (!TPMGrayUtils.isYinLu(context.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKeys.Y_L_SERVICE_IMPL_3));
        }
        List<String> agreementStatusOptions = getAgreementStatusOptions(context.getTenantId());

        for (YLAgreementStatusUpdate.SingleArg singleArg : arg.getArgs()) {
            if (!agreementStatusOptions.contains(singleArg.getAgreementStatus())) {
                throw new ValidateException(I18N.text(I18NKeys.Y_L_SERVICE_IMPL_4));
            }
        }
        List<IObjectData> agreements = serviceFacade.findObjectDataByIdsIgnoreAll(context.getTenantId(), arg.getArgs().stream().map(YLAgreementStatusUpdate.SingleArg::getId).collect(Collectors.toList()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        Map<String, IObjectData> agreementMapById = agreements.stream().collect(Collectors.toMap(DBRecord::getId, v -> v, (o, n) -> n));
        List<YLAgreementStatusUpdate.SingleResult> failMessages = Lists.newArrayList();
        IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        for (YLAgreementStatusUpdate.SingleArg singleArg : arg.getArgs()) {
            IObjectData agreement = agreementMapById.get(singleArg.getId());
            String statusBeforeUpdate = activityStatusMap.get(agreement.get(TPMActivityAgreementFields.AGREEMENT_STATUS, String.class));
            try {
                validateIsAllowUpdateAgreementStatus(agreement, singleArg.getAgreementStatus());

                Map<String, Object> updater = Maps.newHashMap();
                updater.put(TPMActivityAgreementFields.AGREEMENT_STATUS, singleArg.getAgreementStatus());
                serviceFacade.updateWithMap(User.systemUser(context.getTenantId()), agreement, updater);

                serviceFacade.logWithCustomMessage(User.systemUser(context.getTenantId()), EventType.MODIFY, ActionType.MODIFY, describe, agreement,
                        String.format("协议状态修改成功:状态由[%S]修改为[%s]", statusBeforeUpdate, activityStatusMap.get(singleArg.getAgreementStatus())));
            } catch (ValidateException vx) {
                log.info("validate error,id:{},", singleArg.getId(), vx);
                builderErrorMsg(failMessages, singleArg.getId(), vx.getMessage());

                serviceFacade.logWithCustomMessage(User.systemUser(context.getTenantId()), EventType.MODIFY, ActionType.MODIFY, describe, agreement,
                        String.format("协议状态修改失败：%s", vx.getMessage()));
            } catch (Exception ex) {
                log.info("update agreement status error,id:{},", singleArg.getId(), ex);

                builderErrorMsg(failMessages, singleArg.getId(), ex.getMessage());
                serviceFacade.logWithCustomMessage(User.systemUser(context.getTenantId()), EventType.MODIFY, ActionType.MODIFY, describe, agreement,
                        String.format("协议状态修改失败：%s", ex.getMessage()));

            }
        }
        return new YLAgreementStatusUpdate.Result(failMessages);
    }

    private void validateIsAllowUpdateAgreementStatus(IObjectData agreement, String updateStatus) {
        String oldStatus = agreement.get(TPMActivityAgreementFields.AGREEMENT_STATUS, String.class);
        if (TPMActivityAgreementFields.AGREEMENT_STATUS__INVALID.equals(oldStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.Y_L_SERVICE_IMPL_5));
        }

        long begin = (long) agreement.get(TPMActivityAgreementFields.BEGIN_DATE);
        long end = (long) agreement.get(TPMActivityAgreementFields.END_DATE);

        long now = System.currentTimeMillis();
        String status;
        if (now < begin) {
            status = TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE;
        } else if (now < end) {
            status = TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS;
        } else {
            status = TPMActivityAgreementFields.AGREEMENT_STATUS__END;
        }

        if (!Objects.equals(updateStatus, status)) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.Y_L_SERVICE_IMPL_6), updateStatus));
        }

    }

    private void builderErrorMsg(List<YLAgreementStatusUpdate.SingleResult> failMessages, String id, String message) {
        YLAgreementStatusUpdate.SingleResult singleResult = new YLAgreementStatusUpdate.SingleResult();
        singleResult.setId(id);
        singleResult.setFailMessage(message);
        failMessages.add(singleResult);
    }

    @Override
    public YLActivityDetailBulkCreate.Result activityDetailBulkCreate(YLActivityDetailBulkCreate.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        if (!TPMGrayUtils.isYinLu(context.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKeys.Y_L_SERVICE_IMPL_7));
        }
        if (CollectionUtils.isEmpty(arg.getArgs())) {
            return YLActivityDetailBulkCreate.Result.builder().build();
        }
        List<IObjectData> activityDetails = Lists.newArrayList();
        for (Map<String, Object> objectMap : arg.getArgs()) {
            IObjectData object = ObjectDataDocument.of(objectMap).toObjectData();

            object.setTenantId(context.getTenantId());
            object.setDescribeApiName(ApiNames.TPM_ACTIVITY_DETAIL_OBJ);
            object.set(CommonFields.CREATE_BY, Lists.newArrayList(context.getEmployeeId()));
            object.set(CommonFields.OWNER, Lists.newArrayList(context.getEmployeeId()));
            object.set(CommonFields.RECORD_TYPE, MultiRecordType.RECORD_TYPE_DEFAULT);

            String activityId = object.get(TPMActivityDetailFields.ACTIVITY_ID, String.class);
            IObjectData activity = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);
            if (Objects.isNull(activity)) {
                throw new ValidateException(I18N.text(I18NKeys.Y_L_SERVICE_IMPL_8));
            }

            String activityItemId = object.get(TPMActivityDetailFields.ACTIVITY_ITEM_ID, String.class);
            IObjectData activityItem = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), activityItemId, ApiNames.TPM_ACTIVITY_ITEM_OBJ);
            if (Objects.isNull(activityItem)) {
                throw new ValidateException(I18N.text(I18NKeys.Y_L_SERVICE_IMPL_9));
            }

            Boolean isReportItemQuantity = object.get(TPMActivityDetailFields.IS_REPORT_ITEM_QUANTITY, Boolean.class);
            if (Objects.isNull(isReportItemQuantity)) {
                throw new ValidateException(I18N.text(I18NKeys.Y_L_SERVICE_IMPL_10));
            }

            object.set(CommonFields.NAME, activityItem.getName());


            activityDetails.add(object);
        }
        return YLActivityDetailBulkCreate.Result.builder().data(serviceFacade.bulkSaveObjectData(activityDetails, User.systemUser(context.getTenantId()), true)).build();
    }

    private List<String> getAgreementStatusOptions(String tenantId) {
        IObjectDescribe describe = serviceFacade.findObject(tenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        for (SelectOne selectOneField : describeExt.getSelectOneFields()) {
            if (TPMActivityAgreementFields.AGREEMENT_STATUS.equals(selectOneField.getApiName())) {
                return selectOneField.getSelectOptions().stream().map(ISelectOption::getValue).collect(Collectors.toList());
            }
        }
        return Lists.newArrayList();
    }

    private void doCreateAgreement(ApiContext context, YLAgreementBulkCreate.Arg arg) {
        try {
            RequestContext requestContext = RequestContext.builder().tenantId(context.getTenantId()).user(User.systemUser(context.getTenantId())).build();
            requestContext.setAttribute(RequestContext.Attributes.TRIGGER_WORK_FLOW, arg.getTriggerWorkflow());
            requestContext.setAttribute(RequestContext.Attributes.TRIGGER_FLOW, arg.getTriggerApproval());

            RequestContextManager.setContext(requestContext);


            for (YLAgreementBulkCreate.SingleArg singleArg : arg.getArgs()) {
                String linkWhiteId = null;
                try {
                    linkWhiteId = ObjectDataDocument.of(singleArg.getObjectData()).toObjectData().get("link_whitelist__c", String.class);
                    ActionContext addActionContext = new ActionContext(RequestContext.builder().tenantId(context.getTenantId()).user(User.systemUser(context.getTenantId())).build(),
                            ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, ObjectAction.CREATE.getActionCode());
                    addActionContext.setAttribute("triggerWorkflow", arg.getTriggerWorkflow());
                    addActionContext.setAttribute("triggerFlow", arg.getTriggerApproval());
                    BaseObjectSaveAction.Arg saveArg = new BaseObjectSaveAction.Arg();
                    saveArg.setObjectData(ObjectDataDocument.of(singleArg.getObjectData()));
                    if (singleArg.getDetails() != null) {
                        Map<String, List<ObjectDataDocument>> details = new HashMap<>();
                        for (Map.Entry<String, List<Map<String, Object>>> entry : singleArg.getDetails().entrySet()) {
                            details.put(entry.getKey(), entry.getValue().stream().map(ObjectDataDocument::of).collect(Collectors.toList()));
                        }
                        saveArg.setDetails(details);
                    }

                    BaseObjectSaveAction.OptionInfo option = new BaseObjectSaveAction.OptionInfo();
                    option.setUseValidationRule(singleArg.getUseValidationRule());
                    option.setSkipFuncValidate(singleArg.getSkipFuncValidation());
                    saveArg.setOptionInfo(option);

                    serviceFacade.triggerAction(addActionContext, saveArg, BaseObjectSaveAction.Result.class);
                    doSuccess(context.getTenantId(), linkWhiteId);
                } catch (ValidateException | MetaDataBusinessException validateException) {
                    doFail(context.getTenantId(), linkWhiteId, validateException.getMessage());
                    log.info("create agreement business fail,", validateException);
                } catch (Exception ex) {
                    doFail(context.getTenantId(), linkWhiteId, "在处理过程中出现了未知异常，请您修正数据后重新点击【发布白名单活动】按钮进行重试。");
                    log.info("create agreement unknown exception,", ex);
                }
            }
        } finally {
            RequestContextManager.removeContext();
        }
    }

    private void doFail(String tenantId, String dataId, String message) {
        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(tenantId), dataId, "display_store_whitelist__c");
        markMessage(tenantId, objectData, TPMActivityAgreementFields.CREATE_AGREEMENT_STATUS__FAIL, message, null);
        doLog(tenantId, objectData, String.format("创建协议失败：%s", message));
    }

    private void doSuccess(String tenantId, String dataId) {
        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(tenantId), dataId, "display_store_whitelist__c");
        markMessage(tenantId, objectData, TPMActivityAgreementFields.CREATE_AGREEMENT_STATUS__SUCCESS, null, System.currentTimeMillis());
        doLog(tenantId, objectData, "创建协议成功");
    }

    private void markMessage(String tenantId, IObjectData objectData, String status, String message, Long completedTime) {

        Map<String, Object> updater = Maps.newHashMap();
        updater.put(TPMActivityAgreementFields.CREATE_AGREEMENT_STATUS__C, status);
        if (!StringUtils.isEmpty(message)) {
            updater.put(TPMActivityAgreementFields.CREATE_AGREEMENT_ERR_MSG__C, message);
        }
        if (Objects.nonNull(completedTime)) {
            updater.put(TPMActivityAgreementFields.CREATE_AGREEMENT_COMPLETED_TIME__C, completedTime);
        }
        serviceFacade.updateWithMap(User.systemUser(tenantId), objectData, updater);
    }

    private void doLog(String tenantId, IObjectData objectData, String message) {
        IObjectDescribe describe = serviceFacade.findObject(tenantId, "display_store_whitelist__c");
        serviceFacade.logWithCustomMessage(User.systemUser(tenantId), EventType.MODIFY, ActionType.MODIFY, describe, objectData, message);
    }
}
