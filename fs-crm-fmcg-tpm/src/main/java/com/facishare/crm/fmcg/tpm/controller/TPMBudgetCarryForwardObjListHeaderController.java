package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@SuppressWarnings("all")
public class TPMBudgetCarryForwardObjListHeaderController extends StandardListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {
        result.setButtons(
                result.getButtons()
                        .stream()
                        .filter(btn -> !btn.toLayoutButton().getAction().equals("AsyncBulkInvalid"))
                        .collect(Collectors.toList())

        );

        result.getLayout().toLayout().setButtons(
                result.getLayout()
                        .toLayout()
                        .getButtons()
                        .stream()
                        .filter(btn -> !btn.getAction().equals("Import"))
                        .collect(Collectors.toList())
        );

        return super.after(arg, result);
    }
}
