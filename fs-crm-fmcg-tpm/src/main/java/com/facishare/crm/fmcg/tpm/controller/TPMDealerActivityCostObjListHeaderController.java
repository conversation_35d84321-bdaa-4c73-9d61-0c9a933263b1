package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 4:39 PM
 */
@SuppressWarnings("Duplicates")
public class TPMDealerActivityCostObjListHeaderController extends StandardListHeaderController {


    private static final Set<String> filterButtonSet = Sets.newHashSet(ObjectAction.ENTER_ACCOUNT.getButtonApiName(), ObjectAction.CANCEL_ENTRY.getButtonApiName(), ObjectAction.BATCH_IMPORT.getButtonApiName());
    private static final Set<String> filterAccountButtonSet = Sets.newHashSet(ObjectAction.ENTER_ACCOUNT.getButtonApiName(), ObjectAction.CANCEL_ENTRY.getButtonApiName());

    @Override
    protected Result after(Arg arg, Result result) {
        if (TPMGrayUtils.dealerActivityCostObjUpdateImport(controllerContext.getTenantId())) {
            result.getButtons().removeIf(buttonDocument -> filterAccountButtonSet.contains((String) buttonDocument.get("api_name")));
            return super.after(arg, result);
        }
        result.getButtons().removeIf(buttonDocument -> filterButtonSet.contains((String) buttonDocument.get("api_name")));

        List<JSONObject> buttons = CommonUtils.cast(result.getLayout().get("buttons"), JSONObject.class);
        result.getLayout().put("buttons", buttons.stream()
                .filter(button -> !button.getString("action").equals("Import"))
                .collect(Collectors.toList()));

        return super.after(arg, result);
    }
}
