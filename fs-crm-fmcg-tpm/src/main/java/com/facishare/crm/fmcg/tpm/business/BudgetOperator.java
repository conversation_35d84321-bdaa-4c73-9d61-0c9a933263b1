package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountDetailFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.business.abstraction.*;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.tpm.business.enums.BudgetDetailOperateMark;
import com.facishare.crm.fmcg.tpm.business.enums.MainType;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ControlStrategyType;
import com.facishare.crm.fmcg.tpm.dao.paas.BudgetAccountDetailMapper;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IBudgetTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.dao.pg.config.MetadataTransactional;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import groovy.lang.Tuple2;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/8/3 11:02
 */
//IgnoreI18nFile
@Component
@Scope(value = BeanDefinition.SCOPE_PROTOTYPE)
@SuppressWarnings("Duplicates")
@Slf4j
public class BudgetOperator implements IBudgetOperator {

    public static final BigDecimal ZERO = new BigDecimal("0");

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private IAccountLock accountLock;
    @Resource
    private IBudgetCalculateService budgetCalculateService;
    @Resource
    private IBudgetOccupyService budgetOccupyService;
    @Resource
    private IBudgetAccountDetailService budgetAccountDetailService;
    @Resource
    private IBudgetTypeManager budgetTypeManager;
    @Resource
    private IFiscalTimeService fiscalTimeService;
    @Resource
    private BudgetAccountDetailMapper budgetAccountDetailMapper;

    @Setter
    @Getter
    private IObjectData what = null;
    @Getter
    private IObjectData account = null;
    @Getter
    private BudgetTypeNodeEntity node = null;

    private BizType biz = null;
    @Getter
    private String businessTraceId = null;
    @Getter
    private String approvalTraceId = null;
    private User user = null;
    private User sys = null;

    @Override
    public void init(BizType biz, User user, String id, String businessTraceId, String approvalTraceId, IObjectData relatedObjectData) {
        init(biz, user, id, businessTraceId, approvalTraceId, relatedObjectData, true, true);
    }

    @Override
    public void init(BizType biz, User user, String id, String businessTraceId, String approvalTraceId, IObjectData what, boolean isValidateBudgetEnable, boolean isValidateWhat) {

        if (Objects.isNull(biz)) {
            throw new MetaDataBusinessException("context biz can not be null.");
        }
        this.biz = biz;

        if (Objects.isNull(user)) {
            throw new MetaDataBusinessException("context user can not be null.");
        }
        this.user = user;
        this.sys = User.systemUser(user.getTenantId());

        if (Strings.isNullOrEmpty(id)) {
            throw new MetaDataBusinessException("budget account id can not be null.");
        }
        this.account = this.serviceFacade.findObjectDataIgnoreAll(sys, id, ApiNames.TPM_BUDGET_ACCOUNT);
        if (isValidateBudgetEnable && !TPMBudgetAccountFields.BUDGET_STATUS__ENABLE.equalsIgnoreCase(this.account.get(TPMBudgetAccountFields.BUDGET_STATUS, String.class))) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_OPERATOR_0));
        }
        if (!CommonFields.LIFE_STATUS__NORMAL.equalsIgnoreCase(this.account.get(CommonFields.LIFE_STATUS, String.class))) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_OPERATOR_1));
        }

        this.node = this.budgetTypeManager.getNode(
                this.sys.getTenantId(),
                this.account.get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class),
                this.account.get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class)
        );

        if (Strings.isNullOrEmpty(businessTraceId)) {
            throw new MetaDataBusinessException("business trace id must be defined.");
        }
        this.businessTraceId = businessTraceId;

        if (Strings.isNullOrEmpty(approvalTraceId)) {
            throw new MetaDataBusinessException("approval trace id must be defined.");
        }
        this.approvalTraceId = approvalTraceId;

        if (Objects.isNull(what)) {
            throw new MetaDataBusinessException("what object data can not be null.");
        }
        if (Strings.isNullOrEmpty(what.getId())) {
            throw new MetaDataBusinessException("what object data [_id] can not be null");
        }
        if (isValidateWhat && isValidateBudgetEnable && Strings.isNullOrEmpty(what.getName())) {
            throw new MetaDataBusinessException("what object data [name] can not be null");
        }
        if (Strings.isNullOrEmpty(what.getDescribeApiName())) {
            throw new MetaDataBusinessException("what object data [describe_api_name] can not be null");
        }
        this.what = what;
    }

    @Override
    public void init(BizType biz, User user, String id, String businessTraceId, String approvalTraceId) {
        if (Objects.isNull(biz)) {
            throw new MetaDataBusinessException("context biz can not be null.");
        }
        this.biz = biz;

        if (Objects.isNull(user)) {
            throw new MetaDataBusinessException("context user can not be null.");
        }
        this.user = user;
        this.sys = User.systemUser(user.getTenantId());
        if (Strings.isNullOrEmpty(id)) {
            throw new MetaDataBusinessException("budget account id can not be null.");
        }
        this.account = this.serviceFacade.findObjectDataIgnoreAll(sys, id, ApiNames.TPM_BUDGET_ACCOUNT);
        if (!TPMBudgetAccountFields.BUDGET_STATUS__ENABLE.equalsIgnoreCase(this.account.get(TPMBudgetAccountFields.BUDGET_STATUS, String.class))) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_OPERATOR_2));
        }

        if (!CommonFields.LIFE_STATUS__NORMAL.equalsIgnoreCase(this.account.get(CommonFields.LIFE_STATUS, String.class))) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_OPERATOR_3));
        }

        this.node = this.budgetTypeManager.getNode(
                this.sys.getTenantId(),
                this.account.get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class),
                this.account.get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class)
        );

        if (Strings.isNullOrEmpty(businessTraceId)) {
            throw new MetaDataBusinessException("business trace id must be defined.");
        }
        this.businessTraceId = businessTraceId;

        if (Strings.isNullOrEmpty(approvalTraceId)) {
            throw new MetaDataBusinessException("approval trace id must be defined.");
        }
        this.approvalTraceId = approvalTraceId;
    }

    public boolean tryLock() {
        return accountLock.tryLock(this.sys.getTenantId(), this.account, this.node);
    }

    @Override
    public boolean tryLock(long wait) {
        return accountLock.tryLock(this.sys.getTenantId(), this.account, this.node, wait);
    }

    @Override
    public void unlock() {
        accountLock.unlock(this.sys.getTenantId(), this.account, this.node);
    }

    @Override
    public void validateOperableAmount(BigDecimal operationAmount) {
        BigDecimal operableAmount = operableAmount();
        if (operableAmount.compareTo(operationAmount) < 0) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_OPERATOR_4), this.account.getName(), operableAmount.toString()));
        }
    }

    @Override
    public BigDecimal operableAmount() {
        if (ControlStrategyType.CUSTOM_DIMENSION_LIMIT.value().equals(this.node.getControlStrategy())) {
            // 同维度相关预算表可用总金额
            List<IObjectData> relatedAccounts = queryRelatedAccounts();
            BigDecimal realRelatedAvailableAmount = batchCalculateAvailableAmount(relatedAccounts.stream().map(DBRecord::getId).collect(Collectors.toList()));

            // 当前预算表的可用总金额
            BigDecimal availableAmount = realAmount().get(TPMBudgetAccountFields.AVAILABLE_AMOUNT);
            BigDecimal realAvailableAmount = availableAmount.subtract(occupiedAmount());

            // 兄弟预算表可用总金额
            String parentId = this.account.get(TPMBudgetAccountFields.PARENT_ID, String.class);
            if (!Strings.isNullOrEmpty(parentId)) {
                List<IObjectData> brotherAccounts = queryBrotherAccounts();
                BigDecimal realBrotherAvailableAmount = batchCalculateAvailableAmount(brotherAccounts.stream().map(DBRecord::getId).collect(Collectors.toList()));
                return min(realRelatedAvailableAmount, realAvailableAmount, realBrotherAvailableAmount);
            } else {
                return min(realRelatedAvailableAmount, realAvailableAmount);
            }
        } else if (ControlStrategyType.UNLIMITED.value().equals(this.node.getControlStrategy())) {
            // 当前预算表的可用总金额
            BigDecimal availableAmount = realAmount().get(TPMBudgetAccountFields.AVAILABLE_AMOUNT);
            BigDecimal realAvailableAmount = availableAmount.subtract(occupiedAmount());

            String parentId = this.account.get(TPMBudgetAccountFields.PARENT_ID, String.class);
            if (!Strings.isNullOrEmpty(parentId)) {
                // 兄弟预算表可用总金额
                List<IObjectData> brotherAccounts = queryBrotherAccounts();
                BigDecimal realBrotherAvailableAmount = batchCalculateAvailableAmount(brotherAccounts.stream().map(DBRecord::getId).collect(Collectors.toList()));
                return min(realAvailableAmount, realBrotherAvailableAmount);
            } else {
                return realAvailableAmount;
            }
        } else if (ControlStrategyType.FULL_LIMIT.value().equals(this.node.getControlStrategy())) {
            // 汇总金额
            BigDecimal availableAmount = realAmount().get(TPMBudgetAccountFields.AVAILABLE_AMOUNT);
            // 真实可用金额 = 汇总金额中的可用金额 - 占用金额
            return availableAmount.subtract(occupiedAmount());
        } else {
            throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_OPERATOR_5), this.account.getName()));
        }
    }

    @Override
    public void validateConsumableAmount(BigDecimal consumeAmount) {
        BigDecimal consumableAmount = consumableAmount();
        if (consumableAmount.compareTo(consumeAmount) < 0) {
            if (TPMGrayUtils.budgetValidateMessageDesensitization(this.user.getTenantId())) {
                throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_OPERATOR_6), this.account.getName()));
            } else {
                throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_OPERATOR_7), this.account.getName(), consumableAmount.setScale(2, RoundingMode.DOWN), consumeAmount.setScale(2, RoundingMode.DOWN)));
            }
        }
    }

    @Override
    public void validateConsumableAmount(Map<String, BigDecimal> batchConsumeAmount) {
        if (!batchConsumeAmount.containsKey(this.account.getId())) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_OPERATOR_8));
        }

        if (ControlStrategyType.CUSTOM_DIMENSION_LIMIT.value().equals(this.node.getControlStrategy())) {
            List<IObjectData> relatedAccounts = queryRelatedAccounts();
            List<String> relatedAccountIds = relatedAccounts.stream().map(DBRecord::getId).collect(Collectors.toList());
            BigDecimal realRelatedAvailableAmount = batchCalculateAvailableAmount(relatedAccountIds);
            BigDecimal relatedConsumeAmount = calculateBatchConsumeAmount(relatedAccountIds, batchConsumeAmount);
            if (realRelatedAvailableAmount.compareTo(relatedConsumeAmount) < 0) {
                if (TPMGrayUtils.budgetValidateMessageDesensitization(this.user.getTenantId())) {
                    throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_OPERATOR_9), this.account.getName()));
                } else {
                    throw new ValidateException(String.format("预算表[%s]同维度预算表汇总可消费金额不足，当前可消费金额：%s，尝试消费金额：%s。",
                            this.account.getName(),
                            realRelatedAvailableAmount,
                            relatedConsumeAmount
                    ));
                }
            }

            String parentId = this.account.get(TPMBudgetAccountFields.PARENT_ID, String.class);
            if (!Strings.isNullOrEmpty(parentId)) {
                List<IObjectData> brotherAccounts = queryBrotherAccounts();
                List<String> brotherAccountIds = brotherAccounts.stream().map(DBRecord::getId).collect(Collectors.toList());
                BigDecimal realBrotherAvailableAmount = batchCalculateAvailableAmount(brotherAccountIds);
                BigDecimal brotherConsumeAmount = calculateBatchConsumeAmount(brotherAccountIds, batchConsumeAmount);
                if (realBrotherAvailableAmount.compareTo(brotherConsumeAmount) < 0) {
                    if (TPMGrayUtils.budgetValidateMessageDesensitization(this.user.getTenantId())) {
                        throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_OPERATOR_10), this.account.getName()));
                    } else {
                        throw new ValidateException(String.format("预算表[%s]兄弟预算表汇总可消费金额不足，当前可消费金额：%s，尝试消费金额：%s。",
                                this.account.getName(),
                                realBrotherAvailableAmount,
                                brotherConsumeAmount
                        ));
                    }
                }
            }
        } else if (ControlStrategyType.UNLIMITED.value().equals(this.node.getControlStrategy())) {
            String parentId = this.account.get(TPMBudgetAccountFields.PARENT_ID, String.class);
            if (!Strings.isNullOrEmpty(parentId)) {
                List<IObjectData> brotherAccounts = queryBrotherAccounts();
                List<String> brotherAccountIds = brotherAccounts.stream().map(DBRecord::getId).collect(Collectors.toList());
                BigDecimal realBrotherAvailableAmount = batchCalculateAvailableAmount(brotherAccountIds);
                BigDecimal brotherConsumeAmount = calculateBatchConsumeAmount(brotherAccountIds, batchConsumeAmount);
                if (realBrotherAvailableAmount.compareTo(brotherConsumeAmount) < 0) {
                    if (TPMGrayUtils.budgetValidateMessageDesensitization(this.user.getTenantId())) {
                        throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_OPERATOR_11), this.account.getName()));
                    } else {
                        throw new ValidateException(String.format("预算表[%s]兄弟预算表汇总可消费金额不足，当前可消费金额：%s，尝试消费金额：%s。",
                                this.account.getName(),
                                realBrotherAvailableAmount,
                                brotherConsumeAmount
                        ));
                    }
                }
            } else {
                BigDecimal availableAmount = realAmount().get(TPMBudgetAccountFields.AVAILABLE_AMOUNT);
                BigDecimal consumeAmount = batchConsumeAmount.get(this.account.getId());
                if (availableAmount.compareTo(consumeAmount) < 0) {
                    if (TPMGrayUtils.budgetValidateMessageDesensitization(this.user.getTenantId())) {
                        throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_OPERATOR_12), this.account.getName()));
                    } else {
                        throw new ValidateException(String.format("预算表[%s]可消费金额不足，当前可消费金额：%s，尝试消费金额：%s。",
                                this.account.getName(),
                                availableAmount,
                                consumeAmount));
                    }
                }
            }
        } else if (ControlStrategyType.FULL_LIMIT.value().equals(this.node.getControlStrategy())) {
            BigDecimal availableAmount = realAmount().get(TPMBudgetAccountFields.AVAILABLE_AMOUNT);
            BigDecimal consumeAmount = batchConsumeAmount.get(this.account.getId());
            if (availableAmount.compareTo(consumeAmount) < 0) {
                if (TPMGrayUtils.budgetValidateMessageDesensitization(this.user.getTenantId())) {
                    throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_OPERATOR_13), this.account.getName()));
                } else {
                    throw new ValidateException(String.format("预算表[%s]可消费金额不足，当前可消费金额：%s，尝试消费金额：%s。",
                            this.account.getName(),
                            availableAmount,
                            consumeAmount));
                }
            }
        } else {
            throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_OPERATOR_14), this.account.getName()));
        }
    }

    private BigDecimal calculateBatchConsumeAmount(List<String> accountIds, Map<String, BigDecimal> batchConsumeAmount) {
        BigDecimal amount = new BigDecimal("0");
        for (String accountId : accountIds) {
            if (batchConsumeAmount.containsKey(accountId)) {
                amount = amount.add(batchConsumeAmount.get(accountId));
            }
        }
        return amount;
    }

    @Override
    public BigDecimal consumableAmount() {
        if (ControlStrategyType.CUSTOM_DIMENSION_LIMIT.value().equals(this.node.getControlStrategy())) {
            List<IObjectData> relatedAccounts = queryRelatedAccounts();
            BigDecimal realRelatedAvailableAmount = batchCalculateAvailableAmount(relatedAccounts.stream().map(DBRecord::getId).collect(Collectors.toList()));

            String parentId = this.account.get(TPMBudgetAccountFields.PARENT_ID, String.class);
            if (!Strings.isNullOrEmpty(parentId)) {
                List<IObjectData> brotherAccounts = queryBrotherAccounts();
                BigDecimal realBrotherAvailableAmount = batchCalculateAvailableAmount(brotherAccounts.stream().map(DBRecord::getId).collect(Collectors.toList()));
                return min(realRelatedAvailableAmount, realBrotherAvailableAmount);
            } else {
                return realRelatedAvailableAmount;
            }
        } else if (ControlStrategyType.UNLIMITED.value().equals(this.node.getControlStrategy())) {
            String parentId = this.account.get(TPMBudgetAccountFields.PARENT_ID, String.class);
            if (!Strings.isNullOrEmpty(parentId)) {
                List<IObjectData> brotherAccounts = queryBrotherAccounts();
                return batchCalculateAvailableAmount(brotherAccounts.stream().map(DBRecord::getId).collect(Collectors.toList()));
            } else {
                BigDecimal availableAmount = realAmount().get(TPMBudgetAccountFields.AVAILABLE_AMOUNT);
                return availableAmount.subtract(occupiedAmount());
            }
        } else if (ControlStrategyType.FULL_LIMIT.value().equals(this.node.getControlStrategy())) {
            BigDecimal availableAmount = realAmount().get(TPMBudgetAccountFields.AVAILABLE_AMOUNT);
            return availableAmount.subtract(occupiedAmount());
        } else {
            throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_OPERATOR_15), this.account.getName()));
        }
    }

    @Override
    public void validateWriteOffAmount(BigDecimal amount) {
        BigDecimal frozenAmount = frozenAmount();
        if (frozenAmount.compareTo(amount) < 0) {
            throw new ValidateException(String.format("预算表[%s]可核销金额不足，当前可核销金额：%s，尝试核销金额：%s。",
                    this.account.getName(),
                    frozenAmount,
                    amount));
        }
    }

    @Override
    public void validateOverLimitWriteOffAmount(BigDecimal amount) {
        BigDecimal frozenAmount = frozenAmount();
        BigDecimal consumableAmount = consumableAmount();

        BigDecimal writeOffAmount = frozenAmount.add(consumableAmount);

        if (writeOffAmount.compareTo(amount) < 0) {
            throw new ValidateException(String.format("预算表[%s]可核销金额不足，当前可核销金额：%s，尝试核销金额：%s。",
                    this.account.getName(),
                    writeOffAmount,
                    amount.toString()));
        }
    }

    @Override
    public BigDecimal frozenAmount() {
        IObjectData freezeDetail = findFreezeDetail();
        if (Objects.isNull(freezeDetail)) {
            return BigDecimal.ZERO;
        }
        BigDecimal amount = freezeDetail.get(TPMBudgetAccountDetailFields.AMOUNT, BigDecimal.class);
        List<IObjectData> unfreezeDetails = findUnfreezeDetails(freezeDetail.getId());
        for (IObjectData unfreezeDetail : unfreezeDetails) {
            BigDecimal unfreezeAmount = unfreezeDetail.get(TPMBudgetAccountDetailFields.AMOUNT, BigDecimal.class);
            amount = amount.subtract(unfreezeAmount);
        }
        return amount;
    }

    @Override
    public Map<String, BigDecimal> realAmount() {
        return budgetAccountDetailMapper.setTenantId(this.user.getTenantId()).statisticMoney(this.user.getTenantId(), this.getAccount().getId());
    }

    @Override
    @MetadataTransactional
    public void recalculate() {
        budgetCalculateService.recalculateBudgetAmount(sys, this.getAccount().getId());
    }

    @Override
    @MetadataTransactional
    public IObjectData freeze(BigDecimal amount) {
        if (existsFreezeDetail()) {
            throw new MetaDataBusinessException("freeze record already exists.");
        }
        if (TPMGrayUtils.disassemblyAllowZero(this.sys.getTenantId())) {
            if (amount.compareTo(ZERO) < 0) {
                throw new MetaDataBusinessException("freeze amount must be a value bigger than or equal zero.");
            }
        } else {
            if (amount.compareTo(ZERO) <= 0) {
                throw new MetaDataBusinessException("freeze amount must be a value bigger than zero.");
            }
        }
        return addDetail(MainType.FREEZE, amount);
    }

    @Override
    @MetadataTransactional
    public BigDecimal unfreeze(BizType unfreezeBizType) {
        IObjectData freezeDetail = findFreezeDetail();
        if (Objects.isNull(freezeDetail)) {
            throw new MetaDataBusinessException("freeze detail can not be found, unfreeze budget failed.");
        }
        BigDecimal amount = freezeDetail.get(TPMBudgetAccountDetailFields.AMOUNT, BigDecimal.class);
        List<IObjectData> unfreezeDetails = findUnfreezeDetails(freezeDetail.getId());
        for (IObjectData unfreezeDetail : unfreezeDetails) {
            BigDecimal unfreezeAmount = unfreezeDetail.get(TPMBudgetAccountDetailFields.AMOUNT, BigDecimal.class);
            amount = amount.subtract(unfreezeAmount);
        }
        budgetAccountDetailService.updateOperateMark(this.user, freezeDetail, BudgetDetailOperateMark.COMPLETED_UNFREEZE);
        addUnfreezeDetail(unfreezeBizType, amount, freezeDetail);
        return amount;
    }

    @Override
    @MetadataTransactional
    public BigDecimal unfreeze(BizType unfreezeBizType, BigDecimal amount) {
        IObjectData detail = findFreezeDetail();
        if (Objects.isNull(detail)) {
            throw new MetaDataBusinessException("freeze detail can not be found, unfreeze budget failed.");
        }
        addUnfreezeDetail(unfreezeBizType, amount, detail);
        return amount;
    }

    @Override
    @MetadataTransactional
    public IObjectData unfreeze(BigDecimal amount, BizType unfreezeBizType) {
        IObjectData detail = findFreezeDetailByBusinessId();
        return addUnfreezeDetail(unfreezeBizType, amount, detail);
    }

    @Override
    @MetadataTransactional
    public IObjectData expenditure(BigDecimal amount) {
        return addDetail(MainType.EXPENDITURE, amount);
    }

    private IObjectData addDetail(MainType mainType, BigDecimal amount) {
        return budgetAccountDetailService.add(
                sys,
                this.getAccount().getId(),
                mainType,
                this.biz,
                amount,
                this.what,
                this.businessTraceId,
                this.approvalTraceId
        );
    }

    @Override
    @MetadataTransactional
    public IObjectData income(BigDecimal amount) {
        return addDetail(MainType.INCOME, amount);
    }

    @Override
    @MetadataTransactional
    public void freezeRemainingAmount() {
        this.freeze(this.realAmount().get(TPMBudgetAccountFields.AVAILABLE_AMOUNT));
    }

    @Override
    @MetadataTransactional
    public IObjectData occupy(BigDecimal amount) {
        if (this.what != null) {
            return budgetOccupyService.occupy(this.sys.getTenantId(), this.account.getId(), amount, "", this.businessTraceId, this.approvalTraceId, null, this.what.getDescribeApiName(), this.what.getId());
        }
        return budgetOccupyService.occupy(
                this.sys.getTenantId(),
                this.getAccount().getId(),
                this.businessTraceId,
                this.approvalTraceId,
                amount
        );
    }

    @Override
    @MetadataTransactional
    public void releaseOccupy() {
        budgetOccupyService.release(this.sys.getTenantId(), this.getAccount().getId(), this.businessTraceId, this.approvalTraceId);
    }

    @Override
    public User getUser() {
        return this.user;
    }

    private List<IObjectData> queryFreezeDetail() {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(1);
        stq.setOffset(0);
        stq.setSearchSource("db");
        stq.setNeedReturnCountNum(false);

        Filter budgetAccountIdFilter = new Filter();
        budgetAccountIdFilter.setFieldName(TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID);
        budgetAccountIdFilter.setOperator(Operator.EQ);
        budgetAccountIdFilter.setFieldValues(Lists.newArrayList(this.getAccount().getId()));

        Filter mainTypeFilter = new Filter();
        mainTypeFilter.setFieldName(TPMBudgetAccountDetailFields.MAIN_TYPE);
        mainTypeFilter.setOperator(Operator.EQ);
        mainTypeFilter.setFieldValues(Lists.newArrayList(MainType.FREEZE.value()));

        Filter approvalTraceFilter = new Filter();
        approvalTraceFilter.setFieldName(TPMBudgetAccountDetailFields.APPROVAL_TRACE_ID);
        approvalTraceFilter.setOperator(Operator.EQ);
        approvalTraceFilter.setFieldValues(Lists.newArrayList(this.approvalTraceId));

        Filter businessTraceFilter = new Filter();
        businessTraceFilter.setFieldName(TPMBudgetAccountDetailFields.BIZ_TRACE_ID);
        businessTraceFilter.setOperator(Operator.EQ);
        businessTraceFilter.setFieldValues(Lists.newArrayList(this.businessTraceId));

        Filter detailStatusFilter = new Filter();
        detailStatusFilter.setFieldName(TPMBudgetAccountDetailFields.DETAIL_STATUS);
        detailStatusFilter.setOperator(Operator.EQ);
        detailStatusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountDetailFields.DetailStatus.INCLUDE));

        Filter operateMarkEmptyFilter = new Filter();
        operateMarkEmptyFilter.setFieldName(TPMBudgetAccountDetailFields.OPERATE_MARK_DETAIL);
        operateMarkEmptyFilter.setOperator(Operator.IS);
        operateMarkEmptyFilter.setFieldValues(Lists.newArrayList());

        Filter operateMarkNeqFilter = new Filter();
        operateMarkNeqFilter.setFieldName(TPMBudgetAccountDetailFields.OPERATE_MARK_DETAIL);
        operateMarkNeqFilter.setOperator(Operator.NEQ);
        operateMarkNeqFilter.setFieldValues(Lists.newArrayList(BudgetDetailOperateMark.COMPLETED_UNFREEZE.value()));

        stq.setFilters(Lists.newArrayList(budgetAccountIdFilter, mainTypeFilter, approvalTraceFilter, businessTraceFilter, detailStatusFilter, operateMarkEmptyFilter, operateMarkNeqFilter));
        stq.setPattern("1 and 2 and 3 and 4 and 5 and ( 6 or 7 )");

        return QueryDataUtil.find(this.serviceFacade, this.sys.getTenantId(), ApiNames.TPM_BUDGET_ACCOUNT_DETAIL, stq, Lists.newArrayList(
                CommonFields.ID,
                CommonFields.TENANT_ID,
                CommonFields.OBJECT_DESCRIBE_API_NAME,
                TPMBudgetAccountDetailFields.AMOUNT
        ));
    }

    private List<IObjectData> queryFreezeDetailByBusinessId() {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(1);
        stq.setOffset(0);
        stq.setSearchSource("db");
        stq.setNeedReturnCountNum(false);

        Filter budgetAccountIdFilter = new Filter();
        budgetAccountIdFilter.setFieldName(TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID);
        budgetAccountIdFilter.setOperator(Operator.EQ);
        budgetAccountIdFilter.setFieldValues(Lists.newArrayList(this.getAccount().getId()));

        Filter mainTypeFilter = new Filter();
        mainTypeFilter.setFieldName(TPMBudgetAccountDetailFields.MAIN_TYPE);
        mainTypeFilter.setOperator(Operator.EQ);
        mainTypeFilter.setFieldValues(Lists.newArrayList(MainType.FREEZE.value()));

        Filter businessTraceFilter = new Filter();
        businessTraceFilter.setFieldName(TPMBudgetAccountDetailFields.BIZ_TRACE_ID);
        businessTraceFilter.setOperator(Operator.EQ);
        businessTraceFilter.setFieldValues(Lists.newArrayList(this.businessTraceId));

        Filter detailStatusFilter = new Filter();
        detailStatusFilter.setFieldName(TPMBudgetAccountDetailFields.DETAIL_STATUS);
        detailStatusFilter.setOperator(Operator.EQ);
        detailStatusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountDetailFields.DetailStatus.INCLUDE));

        Filter operateMarkEmptyFilter = new Filter();
        operateMarkEmptyFilter.setFieldName(TPMBudgetAccountDetailFields.OPERATE_MARK_DETAIL);
        operateMarkEmptyFilter.setOperator(Operator.IS);
        operateMarkEmptyFilter.setFieldValues(Lists.newArrayList());

        Filter operateMarkNeqFilter = new Filter();
        operateMarkNeqFilter.setFieldName(TPMBudgetAccountDetailFields.OPERATE_MARK_DETAIL);
        operateMarkNeqFilter.setOperator(Operator.NEQ);
        operateMarkNeqFilter.setFieldValues(Lists.newArrayList(BudgetDetailOperateMark.COMPLETED_UNFREEZE.value()));

        stq.setFilters(Lists.newArrayList(budgetAccountIdFilter, mainTypeFilter, businessTraceFilter, detailStatusFilter, operateMarkEmptyFilter, operateMarkNeqFilter));
        stq.setPattern("1 and 2 and 3 and 4 and ( 5 or 6 )");

        return QueryDataUtil.find(this.serviceFacade, this.sys.getTenantId(), ApiNames.TPM_BUDGET_ACCOUNT_DETAIL, stq, Lists.newArrayList(
                CommonFields.ID,
                CommonFields.TENANT_ID,
                CommonFields.OBJECT_DESCRIBE_API_NAME,
                TPMBudgetAccountDetailFields.AMOUNT
        ));
    }

    private boolean existsFreezeDetail() {
        return !queryFreezeDetail().isEmpty();
    }

    private IObjectData findFreezeDetail() {
        List<IObjectData> details = queryFreezeDetail();
        if (CollectionUtils.isEmpty(details)) {
            return null;
        } else {
            return details.get(0);
        }
    }

    private IObjectData findFreezeDetailByBusinessId() {
        List<IObjectData> details = queryFreezeDetailByBusinessId();
        if (CollectionUtils.isEmpty(details)) {
            throw new MetaDataBusinessException("freeze detail can not be found, unfreeze budget failed.");
        }
        return details.get(0);
    }

    private List<IObjectData> findUnfreezeDetails(String freezeDetailId) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(-1);
        stq.setOffset(0);
        stq.setSearchSource("db");
        stq.setNeedReturnCountNum(false);

        Filter budgetAccountIdFilter = new Filter();
        budgetAccountIdFilter.setFieldName(TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID);
        budgetAccountIdFilter.setOperator(Operator.EQ);
        budgetAccountIdFilter.setFieldValues(Lists.newArrayList(this.getAccount().getId()));

        Filter mainTypeFilter = new Filter();
        mainTypeFilter.setFieldName(TPMBudgetAccountDetailFields.MAIN_TYPE);
        mainTypeFilter.setOperator(Operator.EQ);
        mainTypeFilter.setFieldValues(Lists.newArrayList(MainType.UNFREEZE.value()));

        Filter approvalTraceFilter = new Filter();
        approvalTraceFilter.setFieldName(TPMBudgetAccountDetailFields.APPROVAL_TRACE_ID);
        approvalTraceFilter.setOperator(Operator.EQ);
        approvalTraceFilter.setFieldValues(Lists.newArrayList(this.approvalTraceId));

        Filter businessTraceFilter = new Filter();
        businessTraceFilter.setFieldName(TPMBudgetAccountDetailFields.BIZ_TRACE_ID);
        businessTraceFilter.setOperator(Operator.EQ);
        businessTraceFilter.setFieldValues(Lists.newArrayList(this.businessTraceId));

        Filter detailStatusFilter = new Filter();
        detailStatusFilter.setFieldName(TPMBudgetAccountDetailFields.DETAIL_STATUS);
        detailStatusFilter.setOperator(Operator.EQ);
        detailStatusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountDetailFields.DetailStatus.INCLUDE));

        Filter freezeDetailIdFilter = new Filter();
        freezeDetailIdFilter.setFieldName(TPMBudgetAccountDetailFields.BUDGET_FREEZE_DETAIL_ID);
        freezeDetailIdFilter.setOperator(Operator.EQ);
        freezeDetailIdFilter.setFieldValues(Lists.newArrayList(freezeDetailId));

        stq.setFilters(Lists.newArrayList(budgetAccountIdFilter, mainTypeFilter, approvalTraceFilter, businessTraceFilter, detailStatusFilter, freezeDetailIdFilter));

        return QueryDataUtil.find(this.serviceFacade, this.sys.getTenantId(), ApiNames.TPM_BUDGET_ACCOUNT_DETAIL, stq, Lists.newArrayList(
                CommonFields.ID,
                CommonFields.TENANT_ID,
                CommonFields.OBJECT_DESCRIBE_API_NAME,
                TPMBudgetAccountDetailFields.AMOUNT
        ));
    }

    private IObjectData addUnfreezeDetail(BizType bizType, BigDecimal amount, IObjectData detail) {
        return budgetAccountDetailService.add(
                sys,
                this.getAccount().getId(),
                MainType.UNFREEZE,
                bizType,
                null,
                amount,
                this.what,
                this.businessTraceId,
                this.approvalTraceId,
                detail.getId()
        );
    }

    private List<IObjectData> queryRelatedAccounts() {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        List<IFilter> filters = new ArrayList<>();

        Filter typeFilter = new Filter();
        typeFilter.setFieldName(TPMBudgetAccountFields.BUDGET_TYPE_ID);
        typeFilter.setOperator(Operator.EQ);
        typeFilter.setFieldValues(Lists.newArrayList(this.account.get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class)));
        filters.add(typeFilter);

        Filter nodeFilter = new Filter();
        nodeFilter.setFieldName(TPMBudgetAccountFields.BUDGET_NODE_ID);
        nodeFilter.setOperator(Operator.EQ);
        nodeFilter.setFieldValues(Lists.newArrayList(this.account.get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class)));
        filters.add(nodeFilter);

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMBudgetAccountFields.BUDGET_STATUS);
        statusFilter.setOperator(Operator.EQ);
        statusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountFields.BUDGET_STATUS__ENABLE));
        filters.add(statusFilter);

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));
        filters.add(lifeStatusFilter);

        Filter departmentFilter = new Filter();
        departmentFilter.setFieldName(TPMBudgetAccountFields.BUDGET_DEPARTMENT);
        departmentFilter.setOperator(Operator.EQ);
        departmentFilter.setFieldValues(CommonUtils.cast(this.account.get(TPMBudgetAccountFields.BUDGET_DEPARTMENT), String.class));
        filters.add(departmentFilter);

        String periodFieldApiName = String.format("budget_period_%s", this.node.getTimeDimension());

        long period = this.account.get(periodFieldApiName, Long.class);
        Tuple2<Long, Long> span = fiscalTimeService.calculateTimeSpan(this.sys.getTenantId(), this.node.getControlTimeDimension(), period);

        Filter periodStartFilter = new Filter();
        periodStartFilter.setFieldName(periodFieldApiName);
        periodStartFilter.setOperator(Operator.GTE);
        periodStartFilter.setFieldValues(Lists.newArrayList(String.valueOf(span.getFirst())));
        filters.add(periodStartFilter);

        Filter periodEndFilter = new Filter();
        periodEndFilter.setFieldName(periodFieldApiName);
        periodEndFilter.setOperator(Operator.LT);
        periodEndFilter.setFieldValues(Lists.newArrayList(String.valueOf(span.getSecond())));
        filters.add(periodEndFilter);

        if (!CollectionUtils.isEmpty(this.node.getControlDimensions())) {
            this.node.getControlDimensions().forEach(budgetDimensionEntity -> {
                Filter dimensionFilter = new Filter();
                dimensionFilter.setFieldName(budgetDimensionEntity.getApiName());
                dimensionFilter.setOperator(Operator.EQ);
                dimensionFilter.setFieldValues(Lists.newArrayList(this.account.get(budgetDimensionEntity.getApiName(), String.class, "___EMPTY")));
                filters.add(dimensionFilter);
            });
        }

        query.setFilters(filters);

        return QueryDataUtil.find(serviceFacade, this.user.getTenantId(), ApiNames.TPM_BUDGET_ACCOUNT, query, Lists.newArrayList("_id", "name"));
    }

    private BigDecimal batchCalculateAvailableAmount(List<String> accountIds) {
        BigDecimal realRelatedAvailableAmount;
        Map<String, BigDecimal> occupiedMap = budgetOccupyService.batchStatistics(this.user.getTenantId(), accountIds);
        BigDecimal occupiedTotal = new BigDecimal("0");
        for (BigDecimal occupied : occupiedMap.values()) {
            occupiedTotal = occupiedTotal.add(occupied);
        }

        Map<String, BigDecimal> availableMap = budgetAccountDetailMapper.setTenantId(this.user.getTenantId()).batchGetBudgetAvailableAmount(user.getTenantId(), accountIds);
        BigDecimal availableTotal = new BigDecimal("0");
        for (BigDecimal amount : availableMap.values()) {
            availableTotal = availableTotal.add(amount);
        }

        realRelatedAvailableAmount = availableTotal.subtract(occupiedTotal);
        return realRelatedAvailableAmount;
    }

    private BigDecimal occupiedAmount() {
        return budgetOccupyService.statistics(this.user.getTenantId(), this.getAccount().getId());
    }

    private List<IObjectData> queryBrotherAccounts() {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        List<IFilter> filters = new ArrayList<>();

        Filter typeFilter = new Filter();
        typeFilter.setFieldName(TPMBudgetAccountFields.BUDGET_TYPE_ID);
        typeFilter.setOperator(Operator.EQ);
        typeFilter.setFieldValues(Lists.newArrayList(this.account.get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class)));
        filters.add(typeFilter);

        Filter nodeFilter = new Filter();
        nodeFilter.setFieldName(TPMBudgetAccountFields.BUDGET_NODE_ID);
        nodeFilter.setOperator(Operator.EQ);
        nodeFilter.setFieldValues(Lists.newArrayList(this.account.get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class)));
        filters.add(nodeFilter);

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMBudgetAccountFields.BUDGET_STATUS);
        statusFilter.setOperator(Operator.EQ);
        statusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountFields.BUDGET_STATUS__ENABLE));
        filters.add(statusFilter);

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));
        filters.add(lifeStatusFilter);

        Filter parentFilter = new Filter();
        parentFilter.setFieldName(TPMBudgetAccountFields.PARENT_ID);
        parentFilter.setOperator(Operator.EQ);
        parentFilter.setFieldValues(Lists.newArrayList(this.account.get(TPMBudgetAccountFields.PARENT_ID, String.class)));
        filters.add(parentFilter);

        query.setFilters(filters);

        return QueryDataUtil.find(serviceFacade, this.user.getTenantId(), ApiNames.TPM_BUDGET_ACCOUNT, query, Lists.newArrayList("_id", "name"));
    }

    public BigDecimal min(BigDecimal... amounts) {
        BigDecimal min = null;
        for (BigDecimal amount : amounts) {
            if (Objects.isNull(min) || amount.compareTo(min) < 0) {
                min = amount;
            }
        }
        return min;
    }
}
