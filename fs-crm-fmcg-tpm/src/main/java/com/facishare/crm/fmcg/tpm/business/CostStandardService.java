package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityItemCostStandardFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.ICostStandardService;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPM2Service;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/14 下午3:39
 */
@Component
public class CostStandardService implements ICostStandardService {

    private static final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);

    private static final ITPM2Service tpm2Service = SpringUtil.getContext().getBean(TPM2Service.class);

    @Override
    public IObjectData queryCostStandard(String tenantId, String storeId, String activityItemId) {
        SearchTemplateQuery costStandardQuery = new SearchTemplateQuery();
        costStandardQuery.setLimit(-1);
        costStandardQuery.setOffset(0);

        Filter activityItemFilter = new Filter();
        activityItemFilter.setFieldName(TPMActivityItemCostStandardFields.ACTIVITY_ITEM);
        activityItemFilter.setOperator(Operator.EQ);
        activityItemFilter.setFieldValues(Lists.newArrayList(activityItemId));

        if (tpm2Service.isTPM2Tenant(Integer.valueOf(tenantId))) {
            Filter isEnableFilter = new Filter();
            isEnableFilter.setFieldName(TPMActivityItemCostStandardFields.IS_ENABLE);
            isEnableFilter.setOperator(Operator.EQ);
            isEnableFilter.setFieldValues(Lists.newArrayList("true"));
            costStandardQuery.setFilters(Lists.newArrayList(activityItemFilter, isEnableFilter));
        } else {
            costStandardQuery.setFilters(Lists.newArrayList(activityItemFilter));
        }


        OrderBy orderBy = new OrderBy();
        orderBy.setIsAsc(false);
        orderBy.setFieldName(CommonFields.CREATE_TIME);
        costStandardQuery.setOrders(Lists.newArrayList(orderBy));

        List<IObjectData> costStandards = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_ITEM_COST_STANDARD, costStandardQuery);

        SearchTemplateQuery storeQuery = new SearchTemplateQuery();
        storeQuery.setLimit(1);
        storeQuery.setOffset(0);

        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(storeId));

        for (IObjectData standard : costStandards) {
            storeQuery.getFilters().clear();
            storeQuery.setFilters(Lists.newArrayList(idFilter));
            String jsonQuery = standard.get(TPMActivityItemCostStandardFields.STORE_RANGE, String.class, "");
            if (!Strings.isNullOrEmpty(jsonQuery)) {
                JSONObject rangeData = JSON.parseObject(jsonQuery);
                switch (rangeData.getString("type")) {
                    case "ALL":
                        return standard;
                    case "CONDITION":
                        List<Wheres> wheres = JSON.parseArray(rangeData.getString("value"), Wheres.class);
                        storeQuery.setWheres(wheres);
                        if (serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.ACCOUNT_OBJ, storeQuery).getTotalNumber() > 0)
                            return standard;
                        continue;
                    default:
                }
            }
        }
        return null;
    }
}
