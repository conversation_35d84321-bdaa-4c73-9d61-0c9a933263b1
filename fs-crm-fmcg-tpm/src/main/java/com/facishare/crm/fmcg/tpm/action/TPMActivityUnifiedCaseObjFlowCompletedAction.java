package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityUnifiedCaseFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IUnifiedActivityCommonLogicBusiness;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.PromotionPolicyPO;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.service.PromotionPolicyService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/2/20 17:28
 */
@Slf4j
public class TPMActivityUnifiedCaseObjFlowCompletedAction extends StandardFlowCompletedAction {

    private final IUnifiedActivityCommonLogicBusiness unifiedActivityCommonLogicBusiness = SpringUtil.getContext().getBean(IUnifiedActivityCommonLogicBusiness.class);
    private final PromotionPolicyService promotionPolicyService = SpringUtil.getContext().getBean(PromotionPolicyService.class);
    public final IActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);
    private String ACTIVITY_TYPE_TEMPLATE_ID = "";

    private IObjectData beforeDoActUnifiedCase;
    private IObjectData unifiedCase;

    @Override
    protected void before(Arg arg) {
        log.info("TPMActivityUnifiedCaseObj flow complete call back parameters : {}", arg);
        super.before(arg);
        initData();
    }

    private void initData() {
        this.beforeDoActUnifiedCase = serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), arg.getDataId(), arg.getDescribeApiName());
        String activityTypeId = this.beforeDoActUnifiedCase.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
        if (!com.google.common.base.Strings.isNullOrEmpty(activityTypeId)) {
            log.info("activity type id is {}", activityTypeId);
            ActivityTypeExt activityTypeExt = activityTypeManager.find(actionContext.getTenantId(), activityTypeId);
            ACTIVITY_TYPE_TEMPLATE_ID = activityTypeExt.get().getTemplateId();
        }

    }

    @Override
    protected Result doAct(Arg arg) {
        log.info("TPMActivityObj flow complete call back parameters : {}", arg);
        Result result = super.doAct(arg);

        unifiedCase = serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), arg.getDataId(), arg.getDescribeApiName());

        switch (arg.getTriggerType()) {
            case "1":
            case "2":
                this.crudActionCompleted();
                break;
            case "CloseTPMActivity_button_default":
                this.closeActionCompleted();
                break;
            default:
                break;
        }
        return result;
    }

    private void closeActionCompleted() {
        long now = System.currentTimeMillis();
        Long endDate = unifiedCase.get(TPMActivityUnifiedCaseFields.END_DATE, Long.class);

        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put(CommonFields.LIFE_STATUS, "normal");
        if (arg.isPass()) {
            updateMap.put(TPMActivityUnifiedCaseFields.CLOSE_STATUS, TPMActivityUnifiedCaseFields.CLOSE_STATUS__CLOSED);
            updateMap.put(TPMActivityUnifiedCaseFields.CLOSE_TIME, now);
            if (endDate >= now) {
                updateMap.put(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS, TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__CLOSED);
            }
        }
        serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), unifiedCase, updateMap);

    }

    private void crudActionCompleted() {
        String leftStatus = beforeDoActUnifiedCase.get(CommonFields.LIFE_STATUS, String.class);
        String closeStatus = beforeDoActUnifiedCase.get(TPMActivityUnifiedCaseFields.CLOSE_STATUS, String.class);

        Long startTime = unifiedCase.get(TPMActivityUnifiedCaseFields.START_DATE, Long.class);
        Long endTime = unifiedCase.get(TPMActivityUnifiedCaseFields.END_DATE, Long.class);
        String status;
        if (arg.isPass()) {
            if (TPMActivityUnifiedCaseFields.CLOSE_STATUS__CLOSED.equals(closeStatus)) {
                status = TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__CLOSED;
            } else {
                status = calculateActivityStatus(startTime, endTime);
            }
        } else {
            if (ObjectLifeStatus.INEFFECTIVE.getCode().equals(leftStatus)) {
                status = TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__INEFFECTIVE;
            } else if (TPMActivityUnifiedCaseFields.CLOSE_STATUS__CLOSED.equals(closeStatus)) {
                status = TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__CLOSED;
            } else {
                status = calculateActivityStatus(startTime, endTime);
            }
        }
        if (!Strings.isNullOrEmpty(status)) {
            Map<String, Object> updateMap = new HashMap<>();
            updateMap.put(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS, status);
            serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), unifiedCase, updateMap);
        }
        if (ApprovalFlowTriggerType.UPDATE.getTriggerTypeCode().equals(arg.getTriggerType())) {
            unifiedActivityCommonLogicBusiness.recalculateUnifiedAmountField(actionContext.getTenantId(), unifiedCase);
            // 更新关联的活动申请。
            updateActivityPromotionPolicy(unifiedCase);

        }

    }

    private void updateActivityPromotionPolicy(IObjectData objectData) {
        if (Objects.nonNull(ACTIVITY_TYPE_TEMPLATE_ID)
                && !ACTIVITY_TYPE_TEMPLATE_ID.startsWith("promotion") ||
                !Objects.equals(unifiedCase.get(CommonFields.LIFE_STATUS), CommonFields.LIFE_STATUS__NORMAL)) {
            return;
        }
        if (arg.isPass()) {
            batchUpdateActivityPromotionPolicy(objectData);
        } else {
            promotionPolicyService.revertPromotionPolicyRule(actionContext.getTenantId(),
                    actionContext.getUser().getUserId(),
                    objectData);
        }

    }

    private void batchUpdateActivityPromotionPolicy(IObjectData objectData) {

        PromotionPolicyPO promotionPolicyData = promotionPolicyService.getPromotionPolicyData(actionContext.getTenantId(), objectData);
        if (Objects.isNull(promotionPolicyData) || Objects.equals(promotionPolicyData.getProductGiftData(), promotionPolicyData.getProductGiftDataBackUp())) {
            return;
        }

        List<IObjectData> activityDataList = getActivityDataListByUnifiedCaseId(objectData);

        String modeType = unifiedCase.get(TPMActivityUnifiedCaseFields.MODE_TYPE, String.class, "");
        // 更新价格政策
        promotionPolicyService.batchUpdateSFAPromotionPolicy(actionContext.getTenantId(), actionContext.getUser().getUpstreamOwnerIdOrUserId(), modeType, activityDataList);
    }

    private List<IObjectData> getActivityDataListByUnifiedCaseId(IObjectData objectData) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(2000);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);

        Filter relationFilter = new Filter();
        relationFilter.setFieldName(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID);
        relationFilter.setOperator(Operator.EQ);
        relationFilter.setFieldValues(Lists.newArrayList(objectData.getId()));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        query.setFilters(Lists.newArrayList(relationFilter, lifeStatusFilter));

        return serviceFacade.findBySearchQueryIgnoreAll(
                User.systemUser(actionContext.getTenantId()),
                ApiNames.TPM_ACTIVITY_OBJ,
                query).getData();
    }


    @NotNull
    private String calculateActivityStatus(Long startTime, Long endTime) {
        String status;
        if (Objects.isNull(startTime) && Objects.isNull(endTime)) {
            status = TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__IN_PROGRESS;
        } else {
            long now = System.currentTimeMillis();
            if (startTime > now) {
                status = TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__SCHEDULE;
            } else if (now <= endTime) {
                status = TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__IN_PROGRESS;
            } else {
                status = TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__END;
            }
        }
        return status;
    }

}
