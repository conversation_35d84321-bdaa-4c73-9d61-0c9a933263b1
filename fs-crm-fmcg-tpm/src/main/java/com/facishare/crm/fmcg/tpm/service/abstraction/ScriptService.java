package com.facishare.crm.fmcg.tpm.service.abstraction;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.adapter.dto.pay.TransferDetail;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/8 上午11:11
 */
public interface ScriptService {

    void setDefaultUniqueId(List<String> tenantIds);

    void copyMongo(String fromTenantId, String toTenantId);

    void cleanTPMMongo(List<String> tenantIds, boolean isForceDelete);

    void cleanTPMMongo2(String tenantId, String db, int pageSize, int maxSize);

    String updateActivityAndWriteOffDefaultValue(List<String> tenantIds);

    Map<String, String> updateStoreRange(List<String> tenantIds, boolean forceUpdate);

    Map<String, String> addActivityStoreIfActivityHasDealer(List<String> tenantIds);


    String changeObjectFieldSupportRepeat(String tenantId, String describeApiName, String fieldName);

    String updateActivityCostObjActivityTypeField(List<String> tenantIds);

    String wxTransfer(String tenantId, JSONObject transferData);

    List<TransferDetail> queryTransferDetails(String tenantId, String batchId);

    String updateObject(String tenantId, String apiName, String objectId);

    Map<String, String> createI18nReferenceLabel(String tenantId, List<String> fieldApiName);


    List<Map> queryByTenant(String tenantId, String sql);

    void fillFreshStandard(String tenantId, Long createTime);

    Map<String, Boolean> productRangeTest(String tenantId, List<String> activityIds, String productId, String snId);

    void changeActivityRewardPersonId(String tenantId);

    List batchTriggerSelfDefineReward(String tenantId, List<String> snStatusIds);

    List<String> filterActivityByAccount(String tenantId, String userId, String storeId, List<String> activityTypes, List<String> departmentIds);

    String callBackByPhysicalReward(String tenantId, List<String> physicalRewardIds);
}
