package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.tpm.business.abstraction.IOperateInfoService;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.api.IdGenerator;
import com.github.mybatis.util.InjectSchemaUtil;
import de.lab4inf.math.util.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/7/19 上午11:56
 */
@Component
public class OperateInfoService implements IOperateInfoService {

    @Resource
    private SpecialTableMapper specialTableMapper;


    @Resource
    private DbRouterClient dbRouterClient;


    @Override
    public String log(String tenantId, String logType, String logData, String logDataType, String operatorId, String bizId, String targetApiName, String relatedDataId, int isApprovalData, long operateTime) {
        if(!Strings.isNullOrEmpty(logData)){
            logData = logData.replaceAll("'","''");
        }
        String sql = "insert into fmcg_operate_log(id,tenant_id,log_type,log_data,log_data_type,operator_id,biz_id,target_object_api_name,related_data_id,is_approval_data,operate_time) values('%s','%s','%s','%s','%s','%s','%s','%s','%s',%d,%d)";
        String id = IdGenerator.get();
        sql = getSchemaSqlByTenantId(tenantId,String.format(sql,id,tenantId,logType,logData,logDataType,operatorId,bizId,targetApiName,relatedDataId,isApprovalData,operateTime));
        int success = (specialTableMapper.setTenantId(tenantId)).insertBySql(sql);
        return id;
    }

    @Override
    public String log(String tenantId, String logType, String logData, String operateId,String targetApiName, String relatedDataId, boolean isApprovalData) {
        return log(tenantId,logType,logData,"json",operateId,"tpm",targetApiName,relatedDataId,isApprovalData?1:0,System.currentTimeMillis());
    }

    @Override
    public void updateLog(String id,String tenantId, String approvalInstanceId) {
        Map<String,Object> updates = new HashMap<>();
        updates.put("approval_instance_id",approvalInstanceId);
        updateLog(id,tenantId,updates);
    }

    @Override
    public int deleteLog(String tenantId, String id) {
        return (specialTableMapper.setTenantId(tenantId)).deleteBySql("delete from fmcg_operate_log where id = '"+id+"'");
    }

    public void updateLog(String id, String tenantId,Map<String,Object> updateField) {
        StringBuilder sql = new StringBuilder("update fmcg_operate_log set ");
        boolean isFirst = true;
        for (Map.Entry<String, Object> entry : updateField.entrySet()) {
            String k = entry.getKey();
            Object v = entry.getValue().toString().replaceAll("'","''");;
            if(!isFirst)
                sql.append(" and ");
            if (v instanceof String)
                sql.append(k).append(" = '").append(v).append("' ");
            else
                sql.append(k).append(" = ").append(v);
            isFirst = false;
        }
        sql.append(" where id = '").append(id).append("' ");
        (specialTableMapper.setTenantId(tenantId)).batchUpdateBySql(getSchemaSqlByTenantId(tenantId,sql.toString()));
    }

    public String getSchemaSqlByTenantId(String tenantId, String sql) {
        final RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, "CRM", "fs-metadata-service", "postgresql");
        if (Objects.equals(routerInfo.getStandalone(), Boolean.TRUE)) {
            return InjectSchemaUtil.injectSchema(sql, "postgresql", "sch_" + tenantId);
        }
        return sql;
    }

}
