package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.dao.pg.config.MetadataTransactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@MetadataTransactional
public interface IBudgetOccupyService {

    /**
     * occupy account money with trace id
     * @param tenantId        tenant id
     * @param accountId       budget account id
     * @param businessTraceId business trace id
     * @param approvalTraceId approval trace id
     * @param amount          occupy amount
     *
     * @return occupy object data
     */
    IObjectData occupy(
            String tenantId,
            String accountId,
            String businessTraceId, String approvalTraceId, BigDecimal amount);

    IObjectData occupy(
            String tenantId,
            String accountId,
            BigDecimal amount,
            String bizCode,
            String businessTraceId,
            String approvalTraceId,
            String operateMark,
            String relatedApiName,
            String relateObjectId);


    /**
     * calculate occupied money of current budget account
     * @param tenantId  tenant id
     * @param accountId current budget account id
     *
     * @return occupied amount
     */
    BigDecimal statistics(String tenantId, String accountId);

    Map<String, BigDecimal> batchStatistics(String tenantId, List<String> accountIds);

    /**
     * get occupy object data
     * @param tenantId tenant id
     * @param id       occupy object data id
     *
     * @return occupy object data
     */
    IObjectData get(String tenantId, String id);

    /**
     * query occupy object data by approval trace id
     * @param tenantId        tenant id
     * @param approvalTraceId approval trace id
     *
     * @return occupy object data list
     */
    List<IObjectData> query(String tenantId, String approvalTraceId);

    /**
     * release by occupy object data id
     * @param tenantId tenant id
     * @param ids      occupy object id list
     */
    void release(String tenantId, List<String> ids);

    /**
     * release by budget account id and trace
     * @param tenantId        tenant id
     * @param accountId       budget account id
     * @param businessTraceId business trace id
     * @param approvalTraceId approval trace id
     */
    void release(String tenantId, String accountId, String businessTraceId, String approvalTraceId);

}
