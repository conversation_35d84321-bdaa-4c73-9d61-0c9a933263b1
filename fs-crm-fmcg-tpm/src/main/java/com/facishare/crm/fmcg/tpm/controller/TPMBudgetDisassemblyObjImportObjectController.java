package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardImportObjectController;
import com.facishare.paas.appframework.metadata.importobject.ImportType;

/**
 * author: ligt
 */
public class TPMBudgetDisassemblyObjImportObjectController extends StandardImportObjectController {

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        after.getImportObject().setSupportType(ImportType.UNSUPPORT_UPDATE_IMPORT);
        after.getImportObject().setIsApprovalFlow(true);
        return after;
    }
}
