package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.tpm.api.enumeration.LogType;
import com.facishare.crm.fmcg.tpm.api.log.LogData;
import com.facishare.crm.fmcg.tpm.api.method.IdempotentArgBase;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetService;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.mq.model.ApprovalEventOBJ;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.TransactionProxy;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.GlobalConstant;
import com.facishare.crm.fmcg.tpm.utils.lock.LockKeyGenerator;
import com.facishare.crm.fmcg.tpm.utils.lock.RedisDistributedLock;
import com.facishare.idempotent.IdempotentRequestContext;
import com.facishare.idempotent.IdempotentStore;
import com.facishare.idempotent.RequestId;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.MetaDataActionServiceImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fmcg.framework.http.CrmWorkflowProxy;
import com.fmcg.framework.http.FmcgServiceProxy;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.approval.GetApprovalFlowInstanceV2;
import com.fmcg.framework.http.contract.fmcgservice.GetLicense;
import com.fmcg.framework.http.contract.fmcgservice.GetTenantConfig;
import com.fmcg.framework.http.contract.paas.log.GetNewLogInfoList;
import com.fmcg.framework.http.contract.workflow.DefinitionExist;
import com.fmcg.framework.http.contract.workflow.InstanceMatchRule;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.common.SqlEscaper;
import com.github.jedis.support.MergeJedisCmd;
import com.github.mybatis.util.InjectSchemaUtil;
import com.github.trace.TraceContext;
import com.google.common.base.Charsets;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.hash.Hashing;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2021/3/10 上午11:16
 */
//IgnoreI18nFile
@Slf4j
@Component
public class BudgetService implements IBudgetService {

    @Resource
    private FmcgServiceProxy fmcgServiceProxy;

    @Resource(name = "redisCmd")
    private MergeJedisCmd redisCmd;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private PaasDataProxy paasDataProxy;

    @Resource
    private SpecialTableMapper specialTableMapper;

    @Resource
    private DbRouterClient dbRouterClient;

    @Resource
    private OperateInfoService operateInfoService;

    @Resource
    private CrmWorkflowProxy crmWorkflowProxy;

    @Autowired
    private IdempotentStore idempotentStore;

    @Resource
    private TransactionProxy transactionProxy;

    private static final RedisDistributedLock redisLock = SpringUtil.getContext().getBean(RedisDistributedLock.class);
    private static final String BUDGET_KEY_ATTR = "BUDGET_KEY_ATTR:%s";
    private static final String BUDGET_VAL_ATTR = "BUDGET_VAL_ATTR:%s";
    private static final Logger LOGGER = LoggerFactory.getLogger(BudgetService.class);
    private static final String REDIS_KEY_BUDGET_START_MONTH_CACHE = "BUDGET_START_MONTH_CACHE:%s";
    private static final String REDIS_KEY_BUDGET_LICENSE_OPEN_CACHE = "BUDGET_LICENSE_OPEN_CACHE:%s";
    private static final long[] DECIMAL_BASE = new long[]{0, 10, 100, 1000, 10000, 100000, 1000000, 10000000, 100000000};

    public boolean isOpenBudge(int tenantId) {
        String redisKey = String.format(REDIS_KEY_BUDGET_LICENSE_OPEN_CACHE, tenantId);
        String value = redisCmd.get(redisKey);
        boolean result;
        if (StringUtils.isEmpty(redisCmd.get(redisKey))) {
            //update
            GetLicense.Arg getLicenseArg = new GetLicense.Arg();
            getLicenseArg.setAppCode("FMCG.TPM_BUDGET");
            GetLicense.Result getLicenseRst = fmcgServiceProxy.getLicense(tenantId, 1000, getLicenseArg);
            result = getLicenseRst.getLicense() != null;
            if (getLicenseRst.getLicense() == null) {
                getLicenseArg.setAppCode("FMCG.TPM_BUDGET.2");
                getLicenseRst = fmcgServiceProxy.getLicense(tenantId, 1000, getLicenseArg);
                result = getLicenseRst.getLicense() != null;
            }
            redisCmd.setex(redisKey, 60 * 30, String.valueOf(result));
        } else {
            result = "true".equals(value);
        }
        return result;
    }

    public int getBudgetStartMonth(int tenantId) {
        String redisKey = String.format(REDIS_KEY_BUDGET_START_MONTH_CACHE, tenantId);
        String value = redisCmd.get(redisKey);
        if (StringUtils.isEmpty(value)) {
            GetTenantConfig.Arg arg = new GetTenantConfig.Arg();
            arg.setKey("TPM_BUDGET_START_MONTH");
            GetTenantConfig.Result result = fmcgServiceProxy.getTenantConfig(tenantId, 1000, arg);
            value = result.getValue();
            redisCmd.setex(redisKey, 60 * 30, value);
        }
        return StringUtils.isEmpty(value) ? 1 : Integer.parseInt(value);
    }

    public Map<String, Double> calculateActivity(String tenantId, String activityId) {
        if (Strings.isNullOrEmpty(activityId)) {
            return null;
        }
        Map<String, Double> amountMap = getActivityAmountFields(tenantId, activityId);
        Double usedAmount = amountMap.get(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT);
        if (usedAmount == null) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_SERVICE_0));
        }
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(tenantId), activityId, ApiNames.TPM_ACTIVITY_OBJ);
        Map<String, Object> activityUpdateField = new HashMap<>();
        activityUpdateField.put(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, keepNDecimal(usedAmount, 3));
        serviceFacade.batchUpdateWithMap(User.systemUser(tenantId), Lists.newArrayList(activity), activityUpdateField);
        return amountMap;
    }

    public Map<String, Double> calculateBudget(String tenantId, String budgetId) {
        if (Strings.isNullOrEmpty(budgetId)) {
            return null;
        }
        if (TPMGrayUtils.skipDeletedBudgetWhenCalculating(tenantId)) {
            List<Map> data = (specialTableMapper.setTenantId(tenantId)).findBySql(String.format("select id,is_deleted from fmcg_tpm_activity_budget where id = '%s' and tenant_id = '%s'", SqlEscaper.pg_escape(budgetId), SqlEscaper.pg_escape(tenantId)));
            if (!CollectionUtils.isEmpty(data)) {
                Map budget = data.get(0);
                if (!"0".equals(budget.get("is_deleted").toString())) {
                    return null;
                }
            }
        }
        IObjectData budget = serviceFacade.findObjectData(User.systemUser(tenantId), budgetId, ApiNames.TPM_ACTIVITY_BUDGET);
        Map<String, Double> amountMap = getBudgetAmountFields(tenantId, budgetId);
        log.info("calculateBudget amountMap:{}", amountMap);
        Map<String, Object> budgetUpdateField = new HashMap<>();
        budgetUpdateField.put(TPMActivityBudgetFields.FROZEN_AMOUNT, keepNDecimal(amountMap.getOrDefault(TPMActivityBudgetFields.FROZEN_AMOUNT, 0.0), 3));
        budgetUpdateField.put(TPMActivityBudgetFields.USED_AMOUNT, keepNDecimal(amountMap.getOrDefault(TPMActivityBudgetFields.USED_AMOUNT, 0.0), 3));
        budgetUpdateField.put(TPMActivityBudgetFields.TRANSFER_IN_AMOUNT, keepNDecimal(amountMap.getOrDefault(TPMActivityBudgetFields.TRANSFER_IN_AMOUNT, 0.0), 3));
        budgetUpdateField.put(TPMActivityBudgetFields.TRANSFER_OUT_AMOUNT, keepNDecimal(amountMap.getOrDefault(TPMActivityBudgetFields.TRANSFER_OUT_AMOUNT, 0.0), 3));
        log.info("calculateBudget budgetUpdateField:{}", budgetUpdateField);
        serviceFacade.updateWithMap(User.systemUser(tenantId), budget, budgetUpdateField);
        return amountMap;
    }

    public void calculateBoth(String tenantId, String budgetId, String activityId) {
        calculateActivity(tenantId, activityId);
        calculateBudget(tenantId, budgetId);
    }

    public Map<String, Double> getActivityAmountFields(String tenantId, String activityId) {
        return getActivityAmountFieldsV2(tenantId, activityId);
    }

    public Map<String, Double> getActivityAmountFieldsV2(String tenantId, String activityId) {

        //sql query
        SpecialTableMapper mapper = specialTableMapper.setTenantId(tenantId);
        String sql = "select\n" +
                "\tsum(coalesce(confirmed_amount,0)) total_amount\n" +
                "from\n" +
                "\tfmcg_tpm_dealer_activity_cost\n" +
                "where\n" +
                "\ttenant_id = '#{tenant_id}' and is_deleted =0 and\n" +
                "\tactivity_id = '#{activity_id}'\n" +
                "\tand life_status = 'normal';";
        sql = sql.replaceAll("#\\{tenant_id}", SqlEscaper.pg_escape(tenantId)).replaceAll("#\\{activity_id}", SqlEscaper.pg_escape(activityId));
        List<Map> sqlMap = mapper.findBySql(getSchemaSqlByTenantId(tenantId, sql));
        double usedAmount = 0D;
        if (!CollectionUtils.isEmpty(sqlMap)) {
            Map tmpMap = sqlMap.get(0);
            usedAmount = tmpMap != null && tmpMap.get("total_amount") != null ? ((BigDecimal) (tmpMap.get("total_amount"))).doubleValue() : 0;
        }
        //
        Map<String, Double> amountMap = new HashMap<>();
        SearchTemplateQuery costQuery = new SearchTemplateQuery();
        int limit = 500;
        int offset = 0;
        costQuery.setLimit(limit);
        costQuery.setOffset(offset);
        costQuery.setSearchSource("db");
        IFilter activityFilter = new Filter();
        activityFilter.setFieldName(TPMDealerActivityCostFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));

        IFilter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.IN);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("in_change", "under_review"));

        costQuery.setFilters(Lists.newArrayList(lifeStatusFilter, activityFilter));
        OrderBy createTimeOrder = new OrderBy();
        createTimeOrder.setFieldName(CommonFields.CREATE_TIME);
        createTimeOrder.setIsAsc(true);
        costQuery.setOrders(Lists.newArrayList(createTimeOrder));
        List<IObjectData> costs = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_DEALER_ACTIVITY_COST, costQuery);

        double frozenAMount = 0;
        for (IObjectData cost : costs) {
            double used = StringUtil.isNullOrEmpty((String) cost.get(TPMDealerActivityCostFields.CONFIRMED_AMOUNT)) ? 0.0 : Double.parseDouble((String) cost.get(TPMDealerActivityCostFields.CONFIRMED_AMOUNT));
            if ("in_change".equals(cost.get(CommonFields.LIFE_STATUS))) {
                JSONObject snapshot = getApprovalInstanceSnapshot(tenantId, ApiNames.TPM_DEALER_ACTIVITY_COST, cost.getId());
                if (snapshot != null) {
                    double changeUsed = snapshot.getDouble(TPMDealerActivityCostFields.CONFIRMED_AMOUNT) == null ? 0 : snapshot.getDouble(TPMDealerActivityCostFields.CONFIRMED_AMOUNT);
                    //将单个审批中的待确认的数值也缓存下来
                    amountMap.put(String.format("%s:%s", TPMDealerActivityCostFields.CONFIRMED_AMOUNT, cost.getId()), changeUsed);
                    if (changeUsed >= used) {
                        frozenAMount += (used - changeUsed);
                    }
                    //amountMap.put(String.format("%s:%s:%s", TPMDealerActivityCostFields.CONFIRMED_AMOUNT, cost.getId(),snapshot.getString("_approvalId")), changeUsed);
                }
                usedAmount += used;
            } else if ("under_review".equals(cost.get(CommonFields.LIFE_STATUS))) {
                frozenAMount += used;
            }
        }
        amountMap.put(TPMActivityFields.ACTIVITY_FROZEN_AMOUNT, frozenAMount);
        amountMap.put(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, usedAmount);
        return amountMap;
    }

    public Map<String, Double> getBudgetAmountFields(String tenantId, String budgetId) {
        return getBudgetAmountFieldsV2(tenantId, budgetId);
    }

    @Override
    public Double getBudgetAvailableAmount(String tenantId, IObjectData budget, Map<String, Double> outAmountMap) {
        Map<String, Double> amountMap = getBudgetAmountFields(String.valueOf(tenantId), budget.getId());
        double totalAmount = Double.parseDouble((String) budget.get(TPMActivityBudgetFields.AMOUNT));
        double frozenAmount = amountMap.getOrDefault(TPMActivityBudgetFields.FROZEN_AMOUNT, 0.0D);
        double usedAmount = amountMap.getOrDefault(TPMActivityBudgetFields.USED_AMOUNT, 0.0D);
        double transferInAmount = amountMap.getOrDefault(TPMActivityBudgetFields.TRANSFER_IN_AMOUNT, 0.0D);
        double transferOutAmount = amountMap.getOrDefault(TPMActivityBudgetFields.TRANSFER_OUT_AMOUNT, 0.0D);
        if (outAmountMap != null) {
            outAmountMap.clear();
            outAmountMap.putAll(amountMap);
        }
        return CommonUtils.keepNDecimal(totalAmount + transferInAmount - frozenAmount - usedAmount - transferOutAmount, 3);
    }


    public Map<String, Double> getBudgetAmountFieldsV2(String tenantId, String budgetId) {

        String sql = "select  \n" +
                "\tsum(CASE WHEN closed_status ='closed' THEN 0 ELSE coalesce(activity_amount,0) - coalesce(activity_actual_amount,0) END)  as frozen_total_amount,\n" +
                "\tsum(coalesce(activity_actual_amount,0))  as actual_amount\n" +
                "from\n" +
                "\tfmcg_tpm_activity \n" +
                "where\n" +
                "\ttenant_id = '#{tenant_id}'\n" +
                "\tand is_deleted = 0\n" +
                "\tand budget_table = '#{budget_table}'\n" +
                "\tand life_status in ('under_review','normal');";

        if (TPMGrayUtils.excessDeductionForCost(tenantId)) {
            sql = "select  \n" +
                    "\tsum(CASE WHEN closed_status ='closed' or (coalesce(activity_amount,0) - coalesce(activity_actual_amount,0)) < 0  THEN 0   else  coalesce(activity_amount,0) - coalesce(activity_actual_amount,0) END)  as frozen_total_amount,\n" +
                    "\tsum(coalesce(activity_actual_amount,0))  as actual_amount\n" +
                    "from\n" +
                    "\tfmcg_tpm_activity \n" +
                    "where\n" +
                    "\ttenant_id = '#{tenant_id}'\n" +
                    "\tand is_deleted = 0\n" +
                    "\tand budget_table = '#{budget_table}'\n" +
                    "\tand life_status in ('under_review','normal')";
        }
        sql = sql.replaceAll("#\\{tenant_id}", tenantId).replaceAll("#\\{budget_table}", budgetId);
        List<Map> sqlMap = (specialTableMapper.setTenantId(tenantId)).findBySql(getSchemaSqlByTenantId(tenantId, sql));
        double frozenAmount = 0D;
        double usedAmount = 0D;
        double transferOutAmount = 0D;
        double transferInAmount = 0D;
        if (!CollectionUtils.isEmpty(sqlMap)) {
            Map tmpMap = sqlMap.get(0);
            frozenAmount = tmpMap != null && tmpMap.get("frozen_total_amount") != null ? ((BigDecimal) (tmpMap.get("frozen_total_amount"))).doubleValue() : 0;
            usedAmount = tmpMap != null && tmpMap.get("actual_amount") != null ? ((BigDecimal) (tmpMap.get("actual_amount"))).doubleValue() : 0;
        }

        //预算调整 计算
        String sql2 = "select\n" +
                "\tsum(case when life_status = 'under_review' then coalesce(amount, 0) else 0 end) as frozen_total_amount,\n" +
                "\tsum(case when life_status = 'normal' then coalesce(amount, 0) else 0 end) as transfer_out_amount\n" +
                "from\n" +
                "\tfmcg_tpm_activity_budget_adjust\n" +
                "where\n" +
                "\ttenant_id = '#{tenant_id}'\n" +
                "\tand is_deleted = 0\n" +
                "\tand from_budget_table_id = '#{budget_table}'\n" +
                "\tand life_status in ('under_review','normal');";
        sql2 = sql2.replaceAll("#\\{tenant_id}", SqlEscaper.pg_escape(tenantId)).replaceAll("#\\{budget_table}", SqlEscaper.pg_escape(budgetId));
        sqlMap = (specialTableMapper.setTenantId(tenantId)).findBySql(getSchemaSqlByTenantId(tenantId, sql2));
        if (!CollectionUtils.isEmpty(sqlMap)) {
            Map tmpMap = sqlMap.get(0);
            frozenAmount += tmpMap != null && tmpMap.get("frozen_total_amount") != null ? ((BigDecimal) (tmpMap.get("frozen_total_amount"))).doubleValue() : 0;
            transferOutAmount += tmpMap != null && tmpMap.get("transfer_out_amount") != null ? ((BigDecimal) (tmpMap.get("transfer_out_amount"))).doubleValue() : 0;
        }

        String sql3 = "select\n" +
                "\tsum(coalesce(amount, 0)) as transfer_in_amount\n" +
                "from\n" +
                "\tfmcg_tpm_activity_budget_adjust\n" +
                "where\n" +
                "\ttenant_id = '#{tenant_id}'\n" +
                "\tand is_deleted = 0\n" +
                "\tand to_budget_table_id = '#{budget_table}'\n" +
                "\tand life_status = 'normal';";
        sql3 = sql3.replaceAll("#\\{tenant_id}", SqlEscaper.pg_escape(tenantId)).replaceAll("#\\{budget_table}", SqlEscaper.pg_escape(budgetId));
        sqlMap = (specialTableMapper.setTenantId(tenantId)).findBySql(getSchemaSqlByTenantId(tenantId, sql3));
        if (!CollectionUtils.isEmpty(sqlMap)) {
            Map tmpMap = sqlMap.get(0);
            transferInAmount += tmpMap != null && tmpMap.get("transfer_in_amount") != null ? ((BigDecimal) (tmpMap.get("transfer_in_amount"))).doubleValue() : 0;
        }


        SearchTemplateQuery activityQuery = new SearchTemplateQuery();
        int limit = -1;
        int offset = 0;
        activityQuery.setOffset(offset);
        activityQuery.setLimit(limit);
        activityQuery.setSearchSource("db");

        IFilter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("in_change"));

        IFilter budgetTableFilter = new Filter();
        budgetTableFilter.setFieldName(TPMActivityFields.BUDGET_TABLE);
        budgetTableFilter.setOperator(Operator.EQ);
        budgetTableFilter.setFieldValues(Lists.newArrayList(budgetId));

        activityQuery.setFilters(Lists.newArrayList(lifeStatusFilter, budgetTableFilter));
        OrderBy createTimeOrder = new OrderBy();
        createTimeOrder.setFieldName(CommonFields.CREATE_TIME);
        createTimeOrder.setIsAsc(true);
        activityQuery.setOrders(Lists.newArrayList(createTimeOrder));

        List<IObjectData> activities = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_OBJ, activityQuery);

        for (IObjectData activity : activities) {
            double total = StringUtil.isNullOrEmpty((String) activity.get(TPMActivityFields.ACTIVITY_AMOUNT)) ? 0.0 : Double.parseDouble((String) activity.get(TPMActivityFields.ACTIVITY_AMOUNT));
            double actual = StringUtil.isNullOrEmpty((String) activity.get(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT)) ? 0.0 : Double.parseDouble((String) activity.get(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT));
            JSONObject snapshot = getApprovalInstanceSnapshot(tenantId, ApiNames.TPM_ACTIVITY_OBJ, activity.getId());
            if (snapshot != null) {
                double changeTotal = snapshot.getDouble(TPMActivityFields.ACTIVITY_AMOUNT) == null ? total : snapshot.getDouble(TPMActivityFields.ACTIVITY_AMOUNT);
                frozenAmount += Math.max(changeTotal, total);
            } else {
                log.info("can not find snap shot activity:{}", activity);
                if (System.currentTimeMillis() - activity.getLastModifiedTime() > 5000L) {
                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
                snapshot = getApprovalInstanceSnapshot(tenantId, ApiNames.TPM_ACTIVITY_OBJ, activity.getId());
                if (snapshot != null) {
                    double changeTotal = snapshot.getDouble(TPMActivityFields.ACTIVITY_AMOUNT) == null ? total : snapshot.getDouble(TPMActivityFields.ACTIVITY_AMOUNT);
                    frozenAmount += Math.max(changeTotal, total);
                } else {
                    log.info("second search snapshot fail use before value");
                    frozenAmount += total;
                }
            }
            usedAmount += actual;
        }
        Map<String, Double> rst = new HashMap<>();
        rst.put(TPMActivityBudgetFields.FROZEN_AMOUNT, frozenAmount);
        rst.put(TPMActivityBudgetFields.USED_AMOUNT, usedAmount);
        rst.put(TPMActivityBudgetFields.TRANSFER_OUT_AMOUNT, transferOutAmount);
        rst.put(TPMActivityBudgetFields.TRANSFER_IN_AMOUNT, transferInAmount);
        return rst;
    }

    @Override
    public IObjectData getApprovalInstance(String tenantId, String apiName, String objectId) {

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setSearchSource("db");
        query.setOffset(0);
        query.setLimit(1);
        IFilter statusFilter = new Filter();
        statusFilter.setFieldName("state");
        statusFilter.setOperator(Operator.IN);
        statusFilter.setFieldValues(Lists.newArrayList("error", "in_progress"));

        IFilter apiNameFilter = new Filter();
        apiNameFilter.setFieldName("object_api_name");
        apiNameFilter.setOperator(Operator.EQ);
        apiNameFilter.setFieldValues(Lists.newArrayList(apiName));

        IFilter dataIdFilter = new Filter();
        dataIdFilter.setFieldName("object_data_id");
        dataIdFilter.setOperator(Operator.EQ);
        dataIdFilter.setFieldValues(Lists.newArrayList(objectId));
        query.setFilters(Lists.newArrayList(statusFilter, apiNameFilter, dataIdFilter));
        OrderBy createTimeOrder = new OrderBy();
        createTimeOrder.setFieldName(CommonFields.CREATE_TIME);
        createTimeOrder.setIsAsc(false);
        query.setOrders(Lists.newArrayList(createTimeOrder));
        List<IObjectData> approvals = serviceFacade.findBySearchQuery(User.systemUser(tenantId), "ApprovalInstanceObj", query).getData();
        if (CollectionUtils.isEmpty(approvals)) {
            return null;
        }
        return approvals.get(0);
    }

    @Override
    public void updateBudgetAmount(String tenantId, IObjectData budget, Double transferInAmount, Double transferOutAmount) {
        Map<String, Object> map = new HashMap<>();
        if (transferInAmount != null)
            map.put(TPMActivityBudgetFields.TRANSFER_IN_AMOUNT, keepNDecimal(transferInAmount, 3));
        if (transferOutAmount != null)
            map.put(TPMActivityBudgetFields.TRANSFER_OUT_AMOUNT, keepNDecimal(transferOutAmount, 3));
        serviceFacade.updateWithMap(User.systemUser(tenantId), budget, map);
    }

    @Override
    public void updateBudgetAdjustAmount(String tenantId, IObjectData budgetAdjust, Double beforeTransferInAmount, Double afterTransferInAmount, Double beforeTransferOutAmount, Double afterTransferOutAmount) {
        Map<String, Object> updateMap = new HashMap<>();
        if (beforeTransferInAmount != null)
            updateMap.put(TPMActivityBudgetAdjustFields.BALANCE_BEFORE_TRANSFER_IN, keepNDecimal(beforeTransferInAmount, 3));
        if (afterTransferInAmount != null)
            updateMap.put(TPMActivityBudgetAdjustFields.BALANCE_AFTER_TRANSFER_IN, keepNDecimal(afterTransferInAmount, 3));
        if (beforeTransferOutAmount != null)
            updateMap.put(TPMActivityBudgetAdjustFields.BALANCE_BEFORE_TRANSFER_OUT, keepNDecimal(beforeTransferOutAmount, 3));
        if (afterTransferOutAmount != null)
            updateMap.put(TPMActivityBudgetAdjustFields.BALANCE_AFTER_TRANSFER_OUT, keepNDecimal(afterTransferOutAmount, 3));
        serviceFacade.updateWithMap(User.systemUser(tenantId), budgetAdjust, updateMap);
    }

    @Transactional
    @Override
    public void dealPassRefundForBudgetAdjust(String tenantId, String approvalId, ApprovalEventOBJ message, IObjectData adjustObj) {
        String userId = message.getEventData().getString("userId");
        IObjectData detail = null;
        boolean isTransformIn = adjustObj.get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID) == null;
        boolean isTransformOut = adjustObj.get(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID) == null;
        if (isTransformOut)
            return;
        double amount = 0;

        if (!isTransformIn) {
            if (!CollectionUtils.isEmpty(getUniqueBudgetDetail(tenantId, message.getId()))) {
                return;
            }
            JSONObject callbackData = message.getEventData().getJSONObject("callbackData");
            List<IObjectData> details = getBudgetDetail(tenantId, approvalId, callbackData);
            if (CollectionUtils.isEmpty(details))
                return;
            detail = details.get(0);
            amount = detail.get(TPMActivityBudgetDetailFields.AMOUNT, Double.class, 0.0);
        } else {
            amount = -adjustObj.get(TPMActivityBudgetAdjustFields.AMOUNT, Double.class, 0D);
        }

        String toBudgetId = (String) adjustObj.get(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID);
        String lockVal = UUID.randomUUID().toString();
        tryLockBudget(tenantId, toBudgetId, lockVal);
        try {
            IObjectData toBudget = serviceFacade.findObjectData(User.systemUser(tenantId), toBudgetId, ApiNames.TPM_ACTIVITY_BUDGET);
            LogData logData = LogData.builder().data(JSON.toJSONString(message)).build();
            logData.setAttribute("relatedDetail", detail);
            logData.setAttribute("toBudget", toBudget);
            logData.setAttribute("adjust", adjustObj);
            String logId = operateInfoService.log(tenantId, LogType.APPROVAL_PASS.value(), JSON.toJSONString(logData), userId, ApiNames.TPM_ACTIVITY_BUDGET_ADJUST_OBJ, adjustObj.getId(), false);

            Map<String, Double> amountMap = new HashMap<>();
            IObjectData lastDetail = getLastBudgetDetail(tenantId, toBudgetId);
            double availableAmount = 0;
            double beforeAmount = 0;
            double afterAmount = 0;
            if (lastDetail == null) {
                availableAmount = getBudgetAvailableAmount(tenantId, toBudget, amountMap);
                beforeAmount = availableAmount + amount;
                afterAmount = availableAmount;
            } else {
                availableAmount = lastDetail.get(TPMActivityBudgetDetailFields.AMOUNT_AFTER_OPERATION, Double.class);
                beforeAmount = availableAmount;
                afterAmount = availableAmount - amount;
            }
            String type = -amount > 0 ? "2" : "1";
            if (TPMGrayUtils.budgetTransferInSupportNegative(tenantId) && isTransformIn) {
                type = "2";
            }

            addBudgetDetail(tenantId, userId,
                    type,
                    toBudgetId,
                    String.format("预算调整：「%s」调整", toBudget.getName()),
                    -amount,
                    beforeAmount,
                    afterAmount,
                    System.currentTimeMillis(),
                    String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                    null,
                    message.getId(),
                    adjustObj.getId(),
                    IdempotentArgBase.builder().idempotentKey(message.getId()).build());

            calculateBudget(tenantId, toBudgetId);
            updateBudgetAdjustAmount(tenantId, adjustObj, beforeAmount, afterAmount, null, null);

        } finally {
            unLockBudget(tenantId, toBudgetId, lockVal);
        }
    }

    @Transactional
    @Override
    public void dealRejectRefundForBudgetAdjust(String status, String tenantId, String approvalId, ApprovalEventOBJ message, IObjectData adjustObj) {
        String userId = message.getEventData().getString("userId");
        JSONObject callbackData = message.getEventData().getJSONObject("callbackData");
        if (!CollectionUtils.isEmpty(getUniqueBudgetDetail(tenantId, message.getId())))
            return;
        List<IObjectData> details = getBudgetDetail(tenantId, approvalId, callbackData);
        if (CollectionUtils.isEmpty(details))
            return;
        IObjectData detail = details.get(0);
        String fromBudgetId = (String) adjustObj.get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID);
        IObjectData fromBudget = serviceFacade.findObjectData(User.systemUser(tenantId), fromBudgetId, ApiNames.TPM_ACTIVITY_BUDGET);
        LogData logData = LogData.builder().data(JSON.toJSONString(message)).build();
        logData.setAttribute("relatedDetail", detail);
        logData.setAttribute("fromBudget", fromBudget);
        logData.setAttribute("adjust", adjustObj);
        String logId = operateInfoService.log(tenantId, "reject".equals(status) ? LogType.APPROVAL_REJECT.value() : LogType.APPROVAL_CANCEL.value(), JSON.toJSONString(logData), userId, ApiNames.TPM_ACTIVITY_BUDGET_ADJUST_OBJ, adjustObj.getId(), false);

        Map<String, Double> amountMap = new HashMap<>();

        double amount = detail.get(TPMActivityBudgetDetailFields.AMOUNT, Double.class, 0.0);

        IObjectData lastDetail = getLastBudgetDetail(tenantId, fromBudgetId);
        double availableAmount = 0;
        double beforeAmount = 0;
        double afterAmount = 0;
        if (lastDetail == null) {
            availableAmount = getBudgetAvailableAmount(tenantId, fromBudget, amountMap);
            beforeAmount = availableAmount + amount;
            afterAmount = availableAmount;
        } else {
            availableAmount = lastDetail.get(TPMActivityBudgetDetailFields.AMOUNT_AFTER_OPERATION, Double.class);
            beforeAmount = availableAmount;
            afterAmount = availableAmount - amount;
        }


        addBudgetDetail(tenantId, userId,
                amount < 0 ? "2" : "1",
                fromBudgetId,
                String.format("预算调整审批-驳回：「%s」调整驳回", fromBudget.getName()),
                -amount,
                beforeAmount,
                afterAmount,
                System.currentTimeMillis(),
                String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                null,
                message.getId(),
                adjustObj.getId(),
                IdempotentArgBase.builder().idempotentKey(message.getId()).build());
    }

    @Transactional
    @Override
    public void dealRefund(String status, String tenantId, String approvalId, ApprovalEventOBJ message, IObjectData activity) {
        String userId = message.getEventData().getString("userId");
        if (!CollectionUtils.isEmpty(getUniqueBudgetDetail(tenantId, message.getId())))
            return;
        JSONObject callbackData = message.getEventData().getJSONObject("callbackData");
        List<IObjectData> details = getBudgetDetail(tenantId, approvalId, callbackData);
        if (CollectionUtils.isEmpty(details)) {
            IObjectData budget = serviceFacade.findObjectData(User.systemUser(tenantId), activity.get(TPMActivityFields.BUDGET_TABLE, String.class), ApiNames.TPM_ACTIVITY_BUDGET);
            LogData logData = LogData.builder().data(JSON.toJSONString(message)).build();
            logData.setAttribute("message", "无法找到对应的入账明细，无法针对性撤销");
            logData.setAttribute("budget", budget);
            operateInfoService.log(tenantId, "reject".equals(status) ? LogType.APPROVAL_REJECT.value() : LogType.APPROVAL_CANCEL.value(), JSON.toJSONString(logData), userId, ApiNames.TPM_ACTIVITY_OBJ, activity.getId(), false);
            log.info("can not find detail,tenantId:{},approval:{}", tenantId, approvalId);
            return;
        }
        IObjectData detail = details.get(0);
        String budgetId = (String) detail.get(TPMActivityBudgetDetailFields.BUDGET_TABLE_ID);
        IObjectData budget = serviceFacade.findObjectData(User.systemUser(tenantId), budgetId, ApiNames.TPM_ACTIVITY_BUDGET);
        LogData logData = LogData.builder().data(JSON.toJSONString(message)).build();
        logData.setAttribute("relatedDetail", detail);
        logData.setAttribute("budget", budget);
        String logId = operateInfoService.log(tenantId, "reject".equals(status) ? LogType.APPROVAL_REJECT.value() : LogType.APPROVAL_CANCEL.value(), JSON.toJSONString(logData), userId, ApiNames.TPM_ACTIVITY_OBJ, activity.getId(), false);

        Map<String, Double> amountMap = new HashMap<>();
        double availableAmount = getBudgetAvailableAmount(tenantId, budget, amountMap);


        double amount = detail.get(TPMActivityBudgetDetailFields.AMOUNT, Double.class, 0.0);
        addBudgetDetail(tenantId, userId,
                amount < 0 ? "2" : "1",
                budgetId,
                String.format("活动审批-驳回：「%s」%s", activity.getName(), "reject".equals(status) ? "驳回" : "取消"),
                -amount,
                availableAmount + amount,
                availableAmount,
                System.currentTimeMillis(),
                String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                detail.get(TPMActivityBudgetDetailFields.ACTIVITY_ID, String.class),
                message.getId(),
                IdempotentArgBase.builder().idempotentKey(message.getId()).build());
    }

    @Transactional
    @Override
    public void dealPassActivity(String status, String tenantId, String approvalId, ApprovalEventOBJ message, IObjectData activity) {
        String userId = message.getEventData().getString("userId");
        if (!CollectionUtils.isEmpty(getUniqueBudgetDetail(tenantId, message.getId())))
            return;
        JSONObject callbackData = message.getEventData().getJSONObject("callbackData");
        Double increaseAmount = 0D;
        if (callbackData.get(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY) != null) {
            JSONObject map = callbackData.getJSONObject(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY);
            increaseAmount = Double.valueOf(map.getOrDefault(GlobalConstant.BUDGET_APPROVAL_CALLBACK_INCREASE_AMOUNT_KEY, increaseAmount).toString());
        }
        if (increaseAmount == 0) {
            log.info("not a add amount action.callbackData:{}", callbackData);
            return;
        }
        String budgetId = (String) activity.get(TPMActivityFields.BUDGET_TABLE);
        IObjectData budget = serviceFacade.findObjectData(User.systemUser(tenantId), budgetId, ApiNames.TPM_ACTIVITY_BUDGET);
        LogData logData = LogData.builder().data(JSON.toJSONString(message)).build();
        logData.setAttribute("callbackData", callbackData);
        logData.setAttribute("budget", budget);
        String logId = operateInfoService.log(tenantId, LogType.APPROVAL_PASS.value(), JSON.toJSONString(logData), userId, ApiNames.TPM_ACTIVITY_OBJ, activity.getId(), false);

        Map<String, Double> amountMap = new HashMap<>();
        double availableAmount = 0;
        double beforeAmount = 0;
        double afterAmount = 0;

        IObjectData lastDetail = getLastBudgetDetail(tenantId, budgetId);
        if (lastDetail == null) {
            availableAmount = getBudgetAvailableAmount(tenantId, budget, amountMap);
            beforeAmount = availableAmount + increaseAmount;
            afterAmount = availableAmount;
        } else {
            availableAmount = lastDetail.get(TPMActivityBudgetDetailFields.AMOUNT_AFTER_OPERATION, Double.class);
            beforeAmount = availableAmount;
            afterAmount = availableAmount - increaseAmount;
        }

        addBudgetDetail(tenantId, userId,
                increaseAmount < 0 ? "2" : "1",
                budgetId,
                String.format("活动编辑：「%s」%s活动预算", activity.getName(), (increaseAmount < 0 ? "追加" : "削减")),
                -increaseAmount,
                beforeAmount,
                afterAmount,
                System.currentTimeMillis(),
                String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                activity.getId(),
                message.getId(),
                IdempotentArgBase.builder().idempotentKey(message.getId()).build());


    }

    @Override
    public boolean needApproval(String tenantId, String apiName, String triggerType) {
        DefinitionExist.Arg arg = new DefinitionExist.Arg();
        arg.setTriggerType(triggerType);
        arg.setApiName(apiName);
        DefinitionExist.Result result = crmWorkflowProxy.definitionExist(Integer.valueOf(tenantId), -10000, arg);
        if (result.getCode() == 0) {
            return result.isData();
        } else {
            log.info("getApproval exist err. arg:{},rst:{}", arg, result);
            throw new ValidateException(result.getMessage());
        }
    }

    @Override
    public boolean needApproval(String tenantId, String apiName, String dataId, String triggerType) {
        InstanceMatchRule.Arg arg = new InstanceMatchRule.Arg();
        arg.setApiName(apiName);
        arg.setTriggerType(triggerType);
        arg.setObjectId(dataId);
        InstanceMatchRule.Result result = crmWorkflowProxy.matchRule(Integer.valueOf(tenantId), -10000, arg);
        if (result.getCode() == 0) {
            return result.isData();
        } else {
            log.info("InstanceMatchRule  err. arg:{},rst:{}", arg, result);
            throw new ValidateException(result.getMessage());
        }
    }

    @Override
    public boolean needUpdateApproval(String tenantId, String apiName, String dataId, String triggerType, Map<String, Object> updateMap) {
        InstanceMatchRule.Arg arg = new InstanceMatchRule.Arg();
        arg.setApiName(apiName);
        arg.setTriggerType(triggerType);
        arg.setObjectId(dataId);
        Map<String, JSONObject> triggerData = new HashMap<>();
        arg.setTriggerData(triggerData);
        triggerData.put("data", new JSONObject(updateMap));
        InstanceMatchRule.Result result = crmWorkflowProxy.matchRule(Integer.valueOf(tenantId), -10000, arg);
        if (result.getCode() == 0) {
            return result.isData();
        } else {
            log.info("InstanceMatchRule  err. arg:{},rst:{}", arg, result);
            throw new ValidateException(result.getMessage());
        }
    }

    @Override
    public void rmSaveIdempotent(ActionContext actionContext) {
        String requestId = IdempotentRequestContext.getIdempotentKey();
        if (!Strings.isNullOrEmpty(requestId)) {
            idempotentStore.delete(requestId);
        }
        log.info("requestId:{}",requestId);
        String postId = actionContext.getPostId();
        if (Strings.isNullOrEmpty(postId))
            return;
        String key = Hashing.sha256().newHasher().putString("saveMasterAndDetailData", Charsets.UTF_8).putString(postId, Charsets.UTF_8).putString(actionContext.getTenantId(), Charsets.UTF_8).putString(actionContext.getObjectApiName() + "/" + actionContext.getActionCode(), Charsets.UTF_8).hash().toString();
        key = String.join(".", MetaDataActionServiceImpl.class.getSimpleName(), "saveMasterAndDetailData", key);
        if (!key.equals(requestId)) {
            idempotentStore.delete(key);
        }
    }

    @Override
    public void buildCallbackKey(ActionContext actionContext) {
        if (actionContext.getAttribute(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY) != null) {
            return;
        }
        actionContext.setAttribute(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY, IdGenerator.get());
    }

    @Override
    public IObjectData getLastBudgetDetail(String tenantId, String budgetId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");
        Filter filter = new Filter();
        filter.setFieldName(TPMActivityBudgetDetailFields.BUDGET_TABLE_ID);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(budgetId));
        OrderBy orderBy = new OrderBy();
        orderBy.setFieldName(CommonFields.CREATE_TIME);
        orderBy.setIsAsc(false);
        query.setOrders(Lists.newArrayList(orderBy));
        query.setFilters(Lists.newArrayList(filter));
        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_BUDGET_DETAIL_OBJ, query).getData();
        return CollectionUtils.isEmpty(data) ? null : data.get(0);
    }

    @Transactional
    @Override
    public void dealPassCost(String tenantId, String activityId, String budgetId, ApprovalEventOBJ message) {
        String dataId = message.getEventData().getString("dataId");
        if (!CollectionUtils.isEmpty(getUniqueBudgetDetail(tenantId, message.getId())))
            return;
        String userId = message.getEventData().getString("userId");
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(tenantId), activityId, ApiNames.TPM_ACTIVITY_OBJ);
        if (TPMGrayUtils.excessDeductionForCost(tenantId)) {
            Map<String, Double> amountMap = calculateActivity(tenantId, activity.getId());
            double activityAmount = Double.parseDouble(activity.get(TPMActivityFields.ACTIVITY_AMOUNT, String.class, "0"));
            double actualAmount = Double.parseDouble(activity.get(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, String.class, "0"));
            double calActivityAmount = amountMap.getOrDefault(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, 0D);
            if (CommonUtils.keepNDecimal(actualAmount - calActivityAmount, 3) != 0 && CommonUtils.keepNDecimal(activityAmount - calActivityAmount, 3) < 0) {
                double incrementAmount;
                if (CommonUtils.keepNDecimal(activityAmount - actualAmount, 3) >= 0) {
                    incrementAmount = calActivityAmount - activityAmount;
                } else {
                    incrementAmount = calActivityAmount - actualAmount;
                }

                IObjectData lastDetail = getLastBudgetDetail(tenantId, budgetId);
                calculateBudget(tenantId, budgetId);
                double beforeAmount = lastDetail.get(TPMActivityBudgetDetailFields.AMOUNT_AFTER_OPERATION, Double.class, 0D);
                double afterAmount = beforeAmount - incrementAmount;

                LogData logData = LogData.builder().data(JSON.toJSONString(message)).build();
                String logId = operateInfoService.log(tenantId, LogType.APPROVAL_PASS.value(), JSON.toJSONString(logData), userId, ApiNames.TPM_DEALER_ACTIVITY_COST, dataId, false);


                addBudgetDetail(tenantId, userId,
                        "1",
                        budgetId,
                        String.format("「%s」活动方案超额核销", activity.getName()),
                        -incrementAmount,
                        beforeAmount,
                        afterAmount,
                        System.currentTimeMillis(),
                        String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                        activity.getId(),
                        message.getId(),
                        IdempotentArgBase.builder().idempotentKey(message.getId()).build());
            }
        } else {
            calculateActivity(tenantId, activity.getId());
            calculateBudget(tenantId, budgetId);
        }
    }

    @Override
    public void openActivity(String tenantId, String activityId) {
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(tenantId), activityId, ApiNames.TPM_ACTIVITY_OBJ);
        String lockValue = UUID.randomUUID().toString();
        String budgetId = activity.get(TPMActivityFields.BUDGET_TABLE, String.class);
        String closeStatus = activity.get(TPMActivityFields.CLOSED_STATUS, String.class);
        String lifeStatus = activity.get(CommonFields.LIFE_STATUS, String.class);
        if (!CommonFields.LIFE_STATUS__NORMAL.equals(lifeStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_SERVICE_1));
        }
        if (TPMActivityFields.CLOSE_STATUS__UNCLOSED.equals(closeStatus)) {
            return;
        }
        if (Strings.isNullOrEmpty(budgetId))
            return;
        try {
            tryLockBudget(tenantId, budgetId, lockValue);
            transactionProxy.run(() -> {
                IObjectData realActivity = serviceFacade.findObjectData(User.systemUser(tenantId), activityId, ApiNames.TPM_ACTIVITY_OBJ);
                IObjectData budget = serviceFacade.findObjectData(User.systemUser(tenantId), budgetId, ApiNames.TPM_ACTIVITY_BUDGET);
                Map<String, Double> amountMap = new HashMap<>();
                Double availableAmount = getBudgetAvailableAmount(tenantId, budget, amountMap);
                BigDecimal activityAmount = BigDecimal.valueOf(realActivity.get(TPMActivityFields.ACTIVITY_AMOUNT) == null ? 0 : realActivity.get(TPMActivityFields.ACTIVITY_AMOUNT, Double.class));
                BigDecimal activityActualAmount = BigDecimal.valueOf(realActivity.get(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT) == null ? 0 : realActivity.get(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, Double.class));
                double amount = -activityAmount.add(activityActualAmount.negate()).doubleValue();
                if (amount > 0) {
                    log.info("金额异常");
                    return;
                }
                addBudgetDetail(tenantId, "-10000",
                        "1",
                        budgetId,
                        String.format("活动结案取消：「%s」结案取消", realActivity.get("name")),
                        amount,
                        availableAmount,
                        availableAmount + amount,
                        System.currentTimeMillis(),
                        String.format("LogId:%s-traceId:%s", null, TraceContext.get().getTraceId()),
                        activityId,
                        TraceContext.get().getTraceId(),
                        null);
                if (availableAmount + amount < 0) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_SERVICE_2));
                }

                Map<String, Object> updateMap = new HashMap<>();
                updateMap.put(TPMActivityFields.CLOSE_STATUS, TPMActivityFields.CLOSE_STATUS__UNCLOSED);
                serviceFacade.updateWithMap(User.systemUser(tenantId), realActivity, updateMap);
                calculateBudget(tenantId, budgetId);
            });
        } finally {
            unLockBudget(tenantId, budgetId, lockValue);
        }
    }

    @Transactional
    @Override
    public void invalidAdjust(String tenantId, List<IObjectData> adjusts) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter idFilter = new Filter();
        idFilter.setFieldName(TPMActivityBudgetDetailFields.BUDGET_ADJUST_ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(adjusts.stream().map(IObjectData::getId).collect(Collectors.toList()));
        query.setFilters(Lists.newArrayList(idFilter));

        List<IObjectData> dataList = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_BUDGET_DETAIL_OBJ, query);
        List<IObjectData> lockAdjust = adjusts.stream().filter(v -> "1".equals(v.get("lock_status", String.class))).collect(Collectors.toList());
        List<IObjectData> lockDetail = dataList.stream().filter(v -> "1".equals(v.get("lock_status", String.class))).collect(Collectors.toList());
       /* if (!CollectionUtils.isEmpty(lockAdjust))
            serviceFacade.bulkLockObjectData(lockAdjust, false, "default_lock_rule", User.systemUser(tenantId));
        if (!CollectionUtils.isEmpty(lockDetail))
            serviceFacade.bulkLockObjectData(lockDetail, false, "default_lock_rule", User.systemUser(tenantId));*/

        serviceFacade.bulkInvalid(dataList, User.systemUser(tenantId));
        serviceFacade.bulkInvalid(adjusts, User.systemUser(tenantId));
    }

    @Override
    public void resetDetailOperationAmount(String tenantId, String budgetId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter idFilter = new Filter();
        idFilter.setFieldName(TPMActivityBudgetDetailFields.BUDGET_TABLE_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(budgetId));

        query.setFilters(Lists.newArrayList(idFilter));
        List<IObjectData> dataList = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_BUDGET_DETAIL_OBJ, query);

        dataList = dataList.stream()
                .sorted(Comparator.comparingLong(datum -> datum.get(TPMActivityBudgetDetailFields.OPERATE_TIME, Long.class)))
                .collect(Collectors.toList());

        IObjectData budget = serviceFacade.findObjectData(User.systemUser(tenantId), budgetId, ApiNames.TPM_ACTIVITY_BUDGET);
        double afterAmount = budget.get(TPMActivityBudgetFields.AMOUNT, Double.class);

        for (IObjectData detail : dataList) {
            String type = detail.get(TPMActivityBudgetDetailFields.TYPE, String.class);
            if ("0".equals(type)) {
                continue;
            }

            Double detailAmount = detail.get(TPMActivityBudgetFields.AMOUNT, Double.class);
            detail.set(TPMActivityBudgetDetailFields.AMOUNT_BEFORE_OPERATION, CommonUtils.keepNDecimal(afterAmount, 3));
            detail.set(TPMActivityBudgetDetailFields.AMOUNT_AFTER_OPERATION, CommonUtils.keepNDecimal(afterAmount + detailAmount, 3));
            afterAmount = CommonUtils.keepNDecimal(afterAmount + detailAmount, 3);

        }
        List<String> updateFields = Lists.newArrayList(TPMActivityBudgetDetailFields.AMOUNT_AFTER_OPERATION, TPMActivityBudgetDetailFields.AMOUNT_BEFORE_OPERATION);
        for (List<IObjectData> details : Lists.partition(dataList, 100)) {
            serviceFacade.batchUpdateByFields(User.systemUser(tenantId), details, updateFields);
        }
    }

    @Transactional
    @Override
    public void invalidBudget(String tenantId, List<IObjectData> budgets) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter idFilter = new Filter();
        idFilter.setFieldName(TPMActivityBudgetDetailFields.BUDGET_TABLE_ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(budgets.stream().map(IObjectData::getId).collect(Collectors.toList()));
        query.setFilters(Lists.newArrayList(idFilter));

        List<IObjectData> dataList = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_BUDGET_DETAIL_OBJ, query);
        for (List<IObjectData> tmpList : Lists.partition(dataList, 50)) {
            serviceFacade.bulkInvalid(tmpList, User.systemUser(tenantId));
        }

        SearchTemplateQuery query2 = new SearchTemplateQuery();
        query2.setLimit(-1);
        query2.setOffset(0);
        query2.setSearchSource("db");

        Filter fromBudgetFilter = new Filter();
        fromBudgetFilter.setFieldName(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID);
        fromBudgetFilter.setOperator(Operator.IN);
        fromBudgetFilter.setFieldValues(budgets.stream().map(IObjectData::getId).collect(Collectors.toList()));

        Filter toBudgetFilter = new Filter();
        toBudgetFilter.setFieldName(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID);
        toBudgetFilter.setOperator(Operator.IN);
        toBudgetFilter.setFieldValues(budgets.stream().map(IObjectData::getId).collect(Collectors.toList()));

        Wheres where1 = new Wheres();
        where1.setFilters(Lists.newArrayList(fromBudgetFilter));
        Wheres where2 = new Wheres();
        where2.setFilters(Lists.newArrayList(toBudgetFilter));
        query2.setWheres(Lists.newArrayList(where1, where2));

        dataList = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_BUDGET_ADJUST_OBJ, query2);
        for (List<IObjectData> tmpList : Lists.partition(dataList, 50)) {
            serviceFacade.bulkInvalid(tmpList, User.systemUser(tenantId));
        }

        serviceFacade.bulkInvalid(budgets, User.systemUser(tenantId));
    }

    @Override
    public void updateActivityAmount(String tenantId, String activityId, BigDecimal changeToAmount) {
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(tenantId), activityId, ApiNames.TPM_ACTIVITY_OBJ);
        String lockValue = UUID.randomUUID().toString();
        String budgetId = activity.get(TPMActivityFields.BUDGET_TABLE, String.class);
        String closeStatus = activity.get(TPMActivityFields.CLOSED_STATUS, String.class);
        String lifeStatus = activity.get(CommonFields.LIFE_STATUS, String.class);
        if (!CommonFields.LIFE_STATUS__NORMAL.equals(lifeStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_SERVICE_3));
        }
        if (TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closeStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_SERVICE_4));
        }
        if (Strings.isNullOrEmpty(budgetId)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_SERVICE_5));
        }
        try {
            tryLockBudget(tenantId, budgetId, lockValue);
            transactionProxy.run(() -> {
                IObjectData realActivity = serviceFacade.findObjectData(User.systemUser(tenantId), activityId, ApiNames.TPM_ACTIVITY_OBJ);
                IObjectData budget = serviceFacade.findObjectData(User.systemUser(tenantId), budgetId, ApiNames.TPM_ACTIVITY_BUDGET);
                Map<String, Double> amountMap = new HashMap<>();
                Double availableAmount = getBudgetAvailableAmount(tenantId, budget, amountMap);
                BigDecimal activityAmount = BigDecimal.valueOf(realActivity.get(TPMActivityFields.ACTIVITY_AMOUNT) == null ? 0 : realActivity.get(TPMActivityFields.ACTIVITY_AMOUNT, Double.class));
                BigDecimal activityActualAmount = BigDecimal.valueOf(realActivity.get(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT) == null ? 0 : realActivity.get(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, Double.class));
                if (activityActualAmount.compareTo(changeToAmount) >= 0) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_SERVICE_6));
                }

                double amount = changeToAmount.subtract(activityAmount).doubleValue();

                addBudgetDetail(tenantId, "-10000",
                        amount > 0 ? "1" : "2",
                        budgetId,
                        String.format("活动编辑：「%s」更改活动金额", realActivity.get("name")),
                        -amount,
                        availableAmount,
                        availableAmount - amount,
                        System.currentTimeMillis(),
                        String.format("LogId:%s-traceId:%s", null, TraceContext.get().getTraceId()),
                        activityId,
                        TraceContext.get().getTraceId(),
                        null);

                if (availableAmount - amount < 0) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_SERVICE_7));
                }

                Map<String, Object> updateMap = new HashMap<>();
                updateMap.put(TPMActivityFields.ACTIVITY_AMOUNT, changeToAmount);
                serviceFacade.updateWithMap(User.systemUser(tenantId), realActivity, updateMap);
                calculateBudget(tenantId, budgetId);
            });
        } finally {
            unLockBudget(tenantId, budgetId, lockValue);
        }
    }

    List<IObjectData> getBudgetDetail(String tenantId, String approvalId, JSONObject callbackData) {
        String filterId = approvalId;
        if (callbackData.get(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY) != null) {
            JSONObject map = callbackData.getJSONObject(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY);
            filterId = (String) map.getOrDefault(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY, filterId);
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");
        Filter filter = new Filter();
        filter.setFieldName(TPMActivityBudgetDetailFields.APPROVAL_INSTANCE_ID);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(filterId));
        OrderBy orderBy = new OrderBy();
        orderBy.setFieldName("create_time");
        orderBy.setIsAsc(false);
        query.setOrders(Lists.newArrayList(orderBy));
        query.setFilters(Lists.newArrayList(filter));
        return serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_BUDGET_DETAIL_OBJ, query).getData();
    }

    List<IObjectData> getUniqueBudgetDetail(String tenantId, String sourceId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");
        Filter filter = new Filter();
        filter.setFieldName(TPMActivityBudgetDetailFields.SOURCE_ID);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(sourceId));
        OrderBy orderBy = new OrderBy();
        orderBy.setFieldName("create_time");
        orderBy.setIsAsc(false);
        query.setOrders(Lists.newArrayList(orderBy));
        query.setFilters(Lists.newArrayList(filter));
        return serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_BUDGET_DETAIL_OBJ, query).getData();
    }

/*    @Override
    public void updateBudgetAmount(ActionContext actionContext, String budgetId, double amount) {
        StandardIncrementUpdateAction.Arg arg = new StandardIncrementUpdateAction.Arg();
        ObjectDataDocument dataDocument = new ObjectDataDocument();
        dataDocument.put("_id",budgetId);
        dataDocument.put(TPMActivityBudgetFields.AMOUNT,amount);
        arg.setData(dataDocument);

        StandardIncrementUpdateAction.Result result = serviceFacade.triggerAction(new ActionContext(actionContext.getRequestContext(),ApiNames.TPM_ACTIVITY_BUDGET,"IncrementUpdate"),arg, StandardIncrementUpdateAction.Result.class);
        System.out.println(JSON.toJSONString(result));
    }*/


    public JSONObject getApprovalInstanceSnapshot(String tenantId, String apiName, String objectId) {

        IObjectData approval = getApprovalInstance(tenantId, apiName, objectId);
        if (approval == null) {
            log.info("can not find approval.tenantId:{},apiName:{},objectId:{}", tenantId, apiName, objectId);
            return null;
        }
        GetApprovalFlowInstanceV2.Result result = paasDataProxy.getApprovalFlowInstanceV2(Integer.parseInt(tenantId), -10000, apiName, objectId, approval.getId());
        if (result.getCode() != 0) {
            LOGGER.info("查询审批失败");
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_SERVICE_8));
        }
        if (result.getData().getSnapshot() != null)
            result.getData().getSnapshot().put("_approvalId", approval.getId());
        return result.getData().getSnapshot();
    }

    //@Idempotent(expireTime = 120)
    @Override
    public IObjectData addBudgetDetail(String tenantId, IObjectData detail, @RequestId(propertyName = "idempotentKey") IdempotentArgBase idempotent) {
        BuryService.asyncTpmLog(Integer.valueOf(tenantId), Integer.valueOf(detail.getOwner().get(0)), BuryModule.TPM.TPM_ACTIVITY_BUDGET_DETAIL, BuryOperation.CREATE, false);
        return serviceFacade.saveObjectData(User.systemUser(tenantId), detail);
    }

    //@Idempotent(expireTime = 120)
    @Override
    public IObjectData addBudgetDetail(String tenantId, String owner, String type, String budgetId, String remark, double amount, double beforeBalance, double afterBalance, long operateTime, String extraData, String activityId, String sourceId, String adjustId, @RequestId(propertyName = "idempotentKey") IdempotentArgBase idempotent) {
        BuryService.asyncTpmLog(Integer.valueOf(tenantId), Integer.valueOf(owner), BuryModule.TPM.TPM_ACTIVITY_BUDGET_DETAIL, BuryOperation.CREATE, false);
        IObjectData objectData = new ObjectData();
        objectData.setRecordType("default__c");
        objectData.set(CommonFields.LOCK_STATUS, CommonFields.LOCK_STATUS__LOCK);
        objectData.setTenantId(tenantId);
        objectData.setOwner(Lists.newArrayList(owner));
        objectData.setDescribeApiName(ApiNames.TPM_ACTIVITY_BUDGET_DETAIL_OBJ);
        objectData.set(TPMActivityBudgetDetailFields.ACTIVITY_ID, activityId);
        objectData.set(TPMActivityBudgetDetailFields.TYPE, type);
        objectData.set(TPMActivityBudgetDetailFields.BUDGET_TABLE_ID, budgetId);
        objectData.set(TPMActivityBudgetDetailFields.REMARK, remark);
        objectData.set(TPMActivityBudgetDetailFields.AMOUNT_BEFORE_OPERATION, keepNDecimal(beforeBalance, 3));
        objectData.set(TPMActivityBudgetDetailFields.AMOUNT_AFTER_OPERATION, keepNDecimal(afterBalance, 3));
        objectData.set(TPMActivityBudgetDetailFields.OPERATE_TIME, operateTime);
        objectData.set(TPMActivityBudgetDetailFields.AMOUNT, keepNDecimal(amount, 3));
        objectData.set(TPMActivityBudgetDetailFields.SOURCE_ID, sourceId);
        objectData.set(TPMActivityBudgetDetailFields.EXTRA_DATA, extraData);
        objectData.set(TPMActivityBudgetDetailFields.BUDGET_ADJUST_ID, adjustId);
        /*  User systemUser = User.systemUser(tenantId);
        IActionContext actionContext = ActionContextExt.of(systemUser, RequestContextManager.getContext())
                .allowUpdateInvalid(false)
                .getContext();
        actionContext.put(ActionContextKey.SKIP_OBJECT_REFERENCE_EXIST_VALID,true);*/
        return serviceFacade.saveObjectData(User.systemUser(tenantId), objectData);
    }

    // @Idempotent(expireTime = 120)
    @Override
    public IObjectData addBudgetDetail(String tenantId, String owner, String type, String budgetId, String remark, double amount, double beforeBalance, double afterBalance, long operateTime, String extraData, String activityId, String sourceId, @RequestId(propertyName = "idempotentKey") IdempotentArgBase idempotent) {
        return addBudgetDetail(tenantId, owner, type, budgetId, remark, amount, beforeBalance, afterBalance, operateTime, extraData, activityId, sourceId, null, idempotent);
    }

    @Override
    public void updateApprovalIdForDetail(String tenantId, String dataId, String approvalId) {
        IObjectData data = serviceFacade.findObjectData(User.systemUser(tenantId), dataId, ApiNames.TPM_ACTIVITY_BUDGET_DETAIL_OBJ);
        Map<String, Object> map = new HashMap<>();
        map.put(TPMActivityBudgetDetailFields.APPROVAL_INSTANCE_ID, approvalId);
        serviceFacade.updateWithMap(User.systemUser(tenantId), data, map);
    }

    public void tryLockBudget(ActionContext actionContext, String budgetId) {
        tryLockBudget(actionContext, actionContext.getPostId(), budgetId);
    }

    @Override
    public void tryLockBudget(ActionContext actionContext, String uniqueKey, String budgetId) {
        String redisKey = LockKeyGenerator.buildUpBudgetKey(actionContext.getTenantId(), budgetId);
        String redisVal = UUID.randomUUID().toString();
        actionContext.setAttribute(String.format(BUDGET_KEY_ATTR, uniqueKey), redisKey);
        actionContext.setAttribute(String.format(BUDGET_VAL_ATTR, uniqueKey), redisVal);
        if (!redisLock.tryLock(redisKey, redisVal)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_SERVICE_9));
        }
    }

    public void tryLockBudget(String tenantId, String budgetId, String val) {
        if (Strings.isNullOrEmpty(budgetId))
            return;
        String redisKey = LockKeyGenerator.buildUpBudgetKey(tenantId, budgetId);
        if (!redisLock.tryLock(redisKey, val)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_SERVICE_10));
        }
    }

    public void unLockBudget(String tenantId, String budgetId, String val) {
        if (Strings.isNullOrEmpty(budgetId))
            return;
        String redisKey = LockKeyGenerator.buildUpBudgetKey(tenantId, budgetId);
        redisLock.unlock(redisKey, val);
    }

    public void unLockBudget(ActionContext actionContext) {
        unLockBudget(actionContext, actionContext.getPostId());
    }

    @Override
    public void unLockBudget(ActionContext actionContext, String uniqueKey) {
        String attrKey = String.format(BUDGET_KEY_ATTR, uniqueKey);
        String attrVal = String.format(BUDGET_VAL_ATTR, uniqueKey);
        String redisKey = (String) CommonUtils.getOrDefault(actionContext.getAttribute(attrKey), "");
        String redisVal = (String) CommonUtils.getOrDefault(actionContext.getAttribute(attrVal), "");
        if (Strings.isNullOrEmpty(redisKey))
            return;
        redisLock.unlock(redisKey, redisVal);
        actionContext.setAttribute(attrKey, null);
        actionContext.setAttribute(attrVal, null);
    }

    public String getSchemaSqlByTenantId(String tenantId, String sql) {
        final RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, "CRM", "fs-metadata-service", "postgresql");
        if (Objects.equals(routerInfo.getStandalone(), Boolean.TRUE)) {
            return InjectSchemaUtil.injectSchema(sql, "postgresql", "sch_" + tenantId);
        }
        return sql;
    }

    public List<GetNewLogInfoList.ModifyRecord> getModifyLogList(String tenantId, String apiName, String dataId, String approvalId) {
        GetNewLogInfoList.Arg arg = new GetNewLogInfoList.Arg();
        arg.setPageSize(1000);
        arg.setPageNumber(1);
        arg.setObjectId(dataId);
        arg.setOtherBizIds(Lists.newArrayList(approvalId));
        arg.setApiName(apiName);
        GetNewLogInfoList.Result result = paasDataProxy.getNewLogInfoList(Integer.parseInt(tenantId), -10000, arg);
        if (result.getErrCode() != 0) {
            throw new ValidateException(result.getErrMessage());
        }
        return CollectionUtils.isEmpty(result.getResult().getModifyRecordList()) ? Lists.newArrayList() : result.getResult().getModifyRecordList();
    }

    public Map<String, Object> getLastModifyValue(String tenantId, String apiName, String dataId, String approvalId) {
        Map<String, Object> valueMap = new HashMap<>();
        getModifyLogList(tenantId, apiName, dataId, approvalId).forEach(modifyLog -> {
            if (!CollectionUtils.isEmpty(modifyLog.getObjectData())) {
                modifyLog.getObjectData().forEach(objectData -> {
                    if (!valueMap.containsKey(objectData.getFieldApiName())) {
                        valueMap.put(objectData.getFieldApiName(), objectData.getValue());
                    }
                });
            }
        });
        return valueMap;
    }


    private double keepNDecimal(double value, int n) {
        long base = 10;
        if (n >= DECIMAL_BASE.length) {
            for (int i = 0; i < n; i++) base *= 10;
        } else {
            base = DECIMAL_BASE[n];
        }
        return Math.round(value * base) * 1.0 / base;
    }
}
