package com.facishare.crm.fmcg.tpm.business.dto;

import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class StorageFlowInfoDTO {

    /** 单据类型 */
    private Integer billType;
    /** 单据类别 */
    private Integer billClass;
    /** 单据编号 */
    private String billNo;
    /** 创建时间（字符串格式） */
    private String createTime;
    /** 出入库时间（字符串格式） */
    private String outOrInTime;
    /** 出库仓库名称 */
    private String outStoreName;
    /** 入库仓库名称 */
    private String inStoreName;
    /** 销售组织名称 */
    private String sellOrgName;
    /** 销售组织ID */
    private String sellOrgId;
    /** 发货组织名称 */
    private String sendOrgName;
    /** 发货组织ID */
    private String sendOrgId;
    /** 单据名称 */
    private String billName;
    /** 出库实际数量 */
    private Integer outRealNum;
    /** 单据类型名称 */
    private String billTypeName;
    /** 标记创建时间（时间戳，毫秒） */
    private Long markCreateTime;
    /** 单据来源 */
    private Integer billSource;

}
