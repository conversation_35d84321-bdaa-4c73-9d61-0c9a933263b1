package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.crm.fmcg.tpm.business.dto.BudgetConsumeSession;
import com.facishare.paas.appframework.core.model.RequestContext;

/**
 * <AUTHOR>
 * @date 2021/6/21 上午10:46
 */
public interface IRedisLockService {

    String REDIS_SESSION_PREFIX = "FMCG:BUDGET:";

    String REDIS_BUDGET_KEY_TEMPLATE = "FMCG:TPM:BUDGET_ACCOUNT:LOCK:%s:%s";

    String REDIS_BUDGET_PARENT_KEY_TEMPLATE = "FMCG:TPM:BUDGET_ACCOUNT_PARENT:LOCK:%s:%s:%s";

    String REDIS_BUDGET_CONTROL_DIMENSION_KEY_TEMPLATE = "FMCG:TPM:BUDGET_ACCOUNT_CONTROL_DIMENSION:LOCK:%s:%s:%s:%s";

    Boolean tryLock(RequestContext requestContext, String key, String val);

    Boolean tryLock(RequestContext requestContext, String key, String val, long waitTime);

    void unLock(RequestContext requestContext);

    Boolean tryLock(BudgetConsumeSession session, String key, String val, long waitTime);

    void unLock(BudgetConsumeSession session);

    Boolean tryLock(String key, String val);

    Boolean tryLock(String key, String val, long waitTime);

    void unLock(String key, String val);

    Boolean uniqueValidate(BudgetConsumeSession session,String key,String val, long waitTime);
}
