package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson2.JSON;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.List;


public class RedPacketRecordObjIncrementUpdateAction extends StandardIncrementUpdateAction {

    public static List<String> ALLOW_EDIT_FIELD = Lists.newArrayList("_id","data_own_department", "reward_part_code", "transferee_name", "remarks", "transferor_tenant_name", "transferor_tenant_id", "transferor_tenant_id");

    static {
        ConfigFactory.getConfig("fs-fmcg-tpm-config", iConfig -> {
            String redPacketEditField = iConfig.get("red_packet_allow_edit_field");
            if (!Strings.isNullOrEmpty(redPacketEditField)) {
                ALLOW_EDIT_FIELD = JSON.parseArray(redPacketEditField).toJavaList(String.class);
            }
        });
    }

    @Override
    protected void before(Arg arg) {
        if (!actionContext.getRequestContext().isFromFunction()) {
            throw new ValidateException("action not allowed!");
        }
        validateEditableFields(arg.getData());
        super.before(arg);
    }

    private void validateEditableFields(ObjectDataDocument objectData) {
        objectData.keySet().forEach(key -> {
            if (!key.endsWith("__c") && !ALLOW_EDIT_FIELD.contains(key)) {
                throw new ValidateException("field not editable: " + key);
            }
        });
    }
}
