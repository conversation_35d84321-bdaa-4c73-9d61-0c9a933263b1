package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardImportObjectController;
import com.facishare.paas.appframework.metadata.importobject.ImportType;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * author: wuyx
 * description:
 * createTime: 2023/5/5 11:56
 */
public class TPMDealerActivityCostObjImportObjectController extends StandardImportObjectController {

    @Override
    protected Result after(Arg arg, Result result) {
        log.info("init TPMDealerActivityCostObjImportObjectController after");
        Result after = super.after(arg, result);
        after.getImportObject().setSupportType(ImportType.UNSUPPORT_INSERT_IMPORT);
        after.getImportObject().setIsApprovalFlow(true);
        return after;
    }

    @Override
    protected List<ObjectDescribeDocument> findDetailDescribes() {
        return Lists.newArrayList();
    }
}
