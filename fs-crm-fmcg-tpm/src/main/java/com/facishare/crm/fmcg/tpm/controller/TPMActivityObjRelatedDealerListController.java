package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IUnifiedActivityCommonLogicBusiness;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: wuyx
 * @description: TPM 客户范围
 * @createTime: 2021/12/29 11:04
 */
@SuppressWarnings("Duplicates")
public class TPMActivityObjRelatedDealerListController extends StandardRelatedListController {


    private static final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);
    private final IUnifiedActivityCommonLogicBusiness unifiedActivityCommonLogicBusiness = SpringUtil.getContext().getBean(IUnifiedActivityCommonLogicBusiness.class);


    /**
     * 重新设置上下文，将上下文改为客户对象
     *
     * @param arg 搜索客户入参
     */
    @Override
    protected void before(Arg arg) {
        this.controllerContext = new ControllerContext(
                controllerContext.getRequestContext(),
                ApiNames.ACCOUNT_OBJ,
                controllerContext.getMethodName());

        log.info("store filter arg : {}", arg);

        super.before(arg);
    }

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        List<IFilter> filters = query.getFilters();
        List<IFilter> objectDescribeNameList = filters.stream().filter(iFilter -> "object_describe_api_name".equals(iFilter.getFieldName())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(objectDescribeNameList)) {
            IFilter filter = new Filter();
            filter.setFieldName("object_describe_api_name");
            filter.setConnector("AND");
            filter.setOperator(Operator.EQ);
            filter.setFieldValues(Lists.newArrayList(arg.getObjectApiName()));
            query.setFilters(Lists.newArrayList(filter));
        }
        overrideAccountQuery(query);

        super.beforeQueryData(query);
    }

    /**
     * @param query 搜索 query
     * @return 搜索结果
     */
    private void overrideAccountQuery(SearchTemplateQuery query) {
        String tenantId = controllerContext.getTenantId();

        if (query.getFilters().stream().anyMatch(v -> CommonFields.RECORD_TYPE.equals(v.getFieldName()))) {
            query.getFilters().forEach(filter -> {
                if (CommonFields.RECORD_TYPE.equals(filter.getFieldName())) {
                    filter.setOperator(Operator.IN);
                    filter.setFieldValues(storeBusiness.findDealerRecordType(tenantId));
                }
            });
        } else {
            Filter recordTypeFilter = new Filter();
            recordTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
            recordTypeFilter.setOperator(Operator.IN);
            recordTypeFilter.setFieldValues(storeBusiness.findDealerRecordType(tenantId));
            query.getFilters().add(recordTypeFilter);
        }
        IObjectData data = arg.getObjectData().toObjectData();
        String unifiedActivityId = data.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class);
        if (!Strings.isNullOrEmpty(unifiedActivityId)) {
            IObjectData unifiedActivity = serviceFacade.findObjectDataIncludeDeleted(controllerContext.getUser(), unifiedActivityId, ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
            List<String> ids = new ArrayList<>(unifiedActivityCommonLogicBusiness.getDealerIdsOfUnifiedActivity(controllerContext.getTenantId(), unifiedActivity));

            if (CollectionUtils.isEmpty(ids)) {
                ids.add("no_dealer_id");
            }
            if (!ids.contains("-1")) {
                Filter dealerFilter = new Filter();
                dealerFilter.setFieldName(CommonFields.ID);
                dealerFilter.setOperator(Operator.IN);
                dealerFilter.setFieldValues(ids);
                query.getFilters().add(dealerFilter);
            }
        }
    }
}
