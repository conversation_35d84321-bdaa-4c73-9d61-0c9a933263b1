package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.action.StandardBulkRecoverAction;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2021/3/12 下午5:50
 */
public class TPMDealerActivityCostObjBulkRecoverAction extends StandardBulkRecoverAction {

    @Override
    protected void before(Arg arg) {
        //核销作废不允许恢复
        if (!CollectionUtils.isEmpty(arg.getIdList())){
            throw new ValidateException(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_OBJ_BULK_RECOVER_ACTION_0));
        }
        super.before(arg);
    }
}
