package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.paas.metadata.api.IObjectData;

/**
 * <AUTHOR>
 * @date 2022/1/14 下午3:58
 */
public interface IProofService {

    /**
     * @param storeId
     * @param activity
     * @param agreement
     * @param tipMode   1 单独提示  2. 组合提示
     */
    void validateProofTimeAndFrequent(String tenantId, String storeId, IObjectData activity, IObjectData agreement, int tipMode);
}
