package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardImportObjectController;
import com.fxiaoke.common.release.GrayRelease;

/**
 * author: wuyx
 * description:
 * createTime: 2022/8/10 16:46
 */
public class TPMActivityBudgetObjImportObjectController extends StandardImportObjectController {

    @Override
    protected Result after(Arg arg, Result result) {
        log.info("init TPMActivityBudgetObjImportObjectController after");

        Result after = super.after(arg, result);
        if (GrayRelease.isAllow("fmcg", "YQSL_BUDGET", controllerContext.getTenantId())) {
            after.getImportObject().setIsOpenWorkFlow(true);
        }
        return after;
    }
}
