package com.facishare.crm.fmcg.tpm.business.enums;

/**
 * Author: linmj
 * Date: 2024/3/27 16:48
 */
public enum ConsumerScanCodeStatusEnum {
    SUCCESS("0", "可领取"),
    LOCKED("1", "可领取，但是红包锁定"),
    NO_ACTIVITY("3", "活动不存在");

    private final String code;

    private final String description;

    ConsumerScanCodeStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String code() {
        return code;
    }
}
