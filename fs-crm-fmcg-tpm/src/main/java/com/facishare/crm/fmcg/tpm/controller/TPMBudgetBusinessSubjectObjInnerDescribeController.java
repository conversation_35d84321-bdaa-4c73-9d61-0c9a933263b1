package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.metadata.dto.DescribeResult;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/19 下午6:06
 */
public class TPMBudgetBusinessSubjectObjInnerDescribeController extends PreDefineController<TPMBudgetBusinessSubjectObjInnerDescribeController.Arg,TPMBudgetBusinessSubjectObjInnerDescribeController.Result> {


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected Result doService(Arg arg) {
        DescribeResult describe = serviceFacade.updateDescribe(controllerContext.getUser(),arg.getDescribeData(),null,true,false);
        log.info("describeRst:{}",describe);
        return new Result();
    }

    @Data
    @ToString
    public static class Arg implements Serializable{
        private String describeData;
    }


    @Data
    @ToString
    @NoArgsConstructor
    public static class Result implements Serializable{

    }
}
