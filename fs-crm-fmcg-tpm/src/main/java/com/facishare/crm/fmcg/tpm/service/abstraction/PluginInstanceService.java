package com.facishare.crm.fmcg.tpm.service.abstraction;

/**
 * <AUTHOR>
 * @date 2022/7/1 上午11:03
 */
public interface PluginInstanceService {

    Integer deletePluginUnit(String tenantId, String apiName, String pluginName);

    Boolean findPluginUnit(String tenantId, String apiName, String pluginName);

    void addPluginUnit(Integer tenantId, Integer user, String apiName, String pluginName);

    Integer deleteUseLessByPluginName(String tenantId, String pluginName);
}
