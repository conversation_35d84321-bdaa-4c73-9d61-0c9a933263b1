package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMDealerActivityCostFields;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fmcg.framework.http.FmcgServiceProxy;
import com.fmcg.framework.http.contract.fmcgservice.GetLicense;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/14 下午2:55
 */
public class TPMActivityBudgetObjUpdateActivityAmountController extends PreDefineController<TPMActivityBudgetObjUpdateActivityAmountController.Arg, TPMActivityBudgetObjUpdateActivityAmountController.Result> {

    private static final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);
    private static final FmcgServiceProxy fmcgServiceProxy = SpringUtil.getContext().getBean(FmcgServiceProxy.class);


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected Result doService(Arg arg) {
        if (isBudget2()) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_BUDGET_OBJ_UPDATE_ACTIVITY_AMOUNT_CONTROLLER_0));
        }
        validateApprovalCost(arg.getActivityId());
        budgetService.updateActivityAmount(controllerContext.getTenantId(), arg.getActivityId(), arg.getAmount());
        return new Result();
    }

    private boolean isBudget2() {
        GetLicense.Arg arg = new GetLicense.Arg();
        arg.setAppCode("FMCG.TPM_BUDGET_ACCOUNT");
        return fmcgServiceProxy.getLicense(Integer.parseInt(controllerContext.getTenantId()), -10000, arg).getLicense() != null;
    }

    private void validateApprovalCost(String activityId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setNeedReturnCountNum(false);
        query.setLimit(1);
        query.setOffset(0);
        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(TPMDealerActivityCostFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.IN);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("under_review", "in_change"));

        query.setFilters(Lists.newArrayList(activityIdFilter, lifeStatusFilter));

        if (!serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_DEALER_ACTIVITY_COST, query).getData().isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_BUDGET_OBJ_UPDATE_ACTIVITY_AMOUNT_CONTROLLER_1));
        }
    }

    @Data
    @ToString
    static class Arg implements Serializable {
        @JSONField(name = "activity_id")
        @JsonProperty("activity_id")
        @SerializedName("activity_id")
        private String activityId;

        @JSONField(name = "amount")
        @JsonProperty("amount")
        @SerializedName("amount")
        private BigDecimal amount;
    }


    @Data
    @ToString
    static class Result implements Serializable {

    }
}
