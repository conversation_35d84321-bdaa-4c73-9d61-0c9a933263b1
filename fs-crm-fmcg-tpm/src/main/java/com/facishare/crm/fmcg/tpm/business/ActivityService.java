package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.common.adapter.exception.RewardFmcgException;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.action.TPMActivityObjCloseTPMActivityAction;
import com.facishare.crm.fmcg.tpm.business.abstraction.IActivityService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRangeFieldBusiness;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypePO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.NodeType;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityNodeVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityTypeVO;
import com.facishare.crm.fmcg.tpm.web.tools.abstraction.ITenantDevService;
import com.facishare.organization.api.model.department.arg.GetDepartmentDtoArg;
import com.facishare.organization.api.model.department.result.GetDepartmentDtoResult;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * author: wuyx
 * description:
 * createTime: 2022/3/7 18:28
 */

@Slf4j
@Component
public class ActivityService implements IActivityService {

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private ActivityTypeDAO activityTypeDAO;

    @Resource
    private DescribeLogicService describeLogicService;

    @Resource
    private IRangeFieldBusiness rangeFieldBusiness;

    @Resource
    private StoreBusiness storeBusiness;

    @Resource
    private ITenantDevService tenantDevService;

    @Resource
    private DepartmentProviderService departmentProviderService;

    @Override
    public void preValidateAccount(User user, IObjectData activity) {
        ActionContext actionContext = new ActionContext(RequestContext.builder().tenantId(user.getTenantId()).user(user).build(), ApiNames.TPM_ACTIVITY_OBJ, "Add");
//        new TPMActivityObjAddAction().preValidate(activity, actionContext);
    }

    @Override
    public boolean validateActivityEnableEdit(String tenantId, String activityId) {
        log.info("init validateActivityEnableEdit tenantId={},activityId={}", tenantId, activityId);
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(tenantId), activityId, ApiNames.TPM_ACTIVITY_OBJ);
        if (Objects.isNull(activity)) {
            log.info("活动申请不存在！");
            return false;
        }

        String activityTypeId = ObjectDataExt.of(activity).getStringValue(TPMActivityFields.ACTIVITY_TYPE);

        ActivityTypePO po = activityTypeDAO.get(tenantId, activityTypeId);
        if (Objects.isNull(po)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_TEMPLATE_NOT_EXISTS_ERROR));
        }
        ActivityTypeVO vo = ActivityTypeVO.fromPO(po);

        for (ActivityNodeVO node : vo.getActivityNodeList()) {
            if (NodeType.PLAN.value().equals(node.getType())
                    || NodeType.PLAN_TEMPLATE.value().equals(node.getType())) {
                continue;
            }
            String apiName = node.getObjectApiName();
            IObjectDescribe describe = describeLogicService.findObject(tenantId, apiName);
            if (describe == null) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_OBJ_DESCRIBE_NOT_FOUND_ERROR));
            }

            for (IFieldDescribe fieldDescribe : describe.getFieldDescribes()) {
                if (fieldDescribe.getType().equals(IFieldType.OBJECT_REFERENCE)) {
                    String targetApiName = fieldDescribe.get(ObjectReferenceFieldDescribe.TARGET_API_NAME, String.class);
                    if (ApiNames.TPM_ACTIVITY_OBJ.equals(targetApiName)) {
                        List<IObjectData> obj = findObjByActivity(tenantId, activityId, apiName, fieldDescribe.getApiName());
                        if (!CollectionUtils.isEmpty(obj)) {

                          /*  if (TPMGrayUtils.isAllowEditActivityObjCustomField(tenantId) || tenantDevService.isMengNiuCloudTenant(Integer.parseInt(tenantId))) {
                                if (isNotExistsPreField) {
                                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_SERVICE_0));
                                }
                            } else {
                            }*/
                            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_DATA_ALREADY_EXISTS_ERROR));
                        }
                    }
                }
            }
        }

        return true;
    }

    @Override
    public boolean validateActivityProofEnableEdit(String tenantId, String proofId, String activityId) {
        log.info("init validateActivityEnableEdit tenantId={},activityId={}", tenantId, activityId);
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(tenantId), activityId, ApiNames.TPM_ACTIVITY_OBJ);
        if (Objects.isNull(activity)) {
            return false;
        }

        String activityTypeId = ObjectDataExt.of(activity).getStringValue(TPMActivityFields.ACTIVITY_TYPE);

        ActivityTypePO po = activityTypeDAO.get(tenantId, activityTypeId);
        if (Objects.isNull(po)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_TEMPLATE_NOT_EXISTS_ERROR));
        }
        ActivityTypeVO vo = ActivityTypeVO.fromPO(po);

        for (ActivityNodeVO node : vo.getActivityNodeList()) {
            if (NodeType.PLAN.value().equals(node.getType())
                    || NodeType.PLAN_TEMPLATE.value().equals(node.getType())
                    || NodeType.AGREEMENT.value().equals(node.getType())
                    || NodeType.PROOF.value().equals(node.getType())) {
                continue;
            }
            String apiName = node.getObjectApiName();
            IObjectDescribe describe = describeLogicService.findObject(tenantId, apiName);
            if (describe == null) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_OBJ_DESCRIBE_NOT_FOUND_ERROR));
            }

            for (IFieldDescribe fieldDescribe : describe.getFieldDescribes()) {
                if (fieldDescribe.getType().equals(IFieldType.OBJECT_REFERENCE)) {
                    String targetApiName = fieldDescribe.get(ObjectReferenceFieldDescribe.TARGET_API_NAME, String.class);
                    if (ApiNames.TPM_ACTIVITY_PROOF_OBJ.equals(targetApiName)) {
                        List<IObjectData> obj = findObjByActivity(tenantId, proofId, apiName, fieldDescribe.getApiName());
                        if (!CollectionUtils.isEmpty(obj)) {
                            if (ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ.equals(apiName)){
                                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_DATA_EXISTS_BY_AUDIT_ERROR));
                            }else if (ApiNames.TPM_DEALER_ACTIVITY_COST.equals(apiName)){
                                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_DATA_EXISTS_BY_COST_ERROR));
                            }else{
                                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_DATA_ALREADY_EXISTS_ERROR));
                            }
                        }
                    }
                }
            }
        }

        return true;
    }

    @Override
    public void ifAllowCreateDataDueToOnceWriteOff(String tenantId, IObjectData activity) {
        if (Objects.isNull(activity)) {
            log.info("ifAllowCreateDataDueToOnceWriteOff activity is null ");
            return;
        }

        String maxWriteOffCount = activity.get(TPMActivityFields.MAX_WRITE_OFF_COUNT, String.class);

        if (ActivityMaxWriteOffCountEnum.ONCE.value().equals(maxWriteOffCount)) {

            Filter activityFilter = new Filter();
            activityFilter.setFieldName(TPMDealerActivityCostFields.ACTIVITY_ID);
            activityFilter.setOperator(Operator.EQ);
            activityFilter.setFieldValues(Lists.newArrayList(activity.getId()));

            SearchTemplateQuery query = QueryDataUtil.minimumQuery(activityFilter);

            query.setLimit(1);

            List<IObjectData> costs = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_DEALER_ACTIVITY_COST, query, Lists.newArrayList("_id", CommonFields.RECORD_TYPE));

            if (!CollectionUtils.isEmpty(costs)) {
                IObjectData cost = costs.get(0);
                switch (cost.getRecordType()) {
                    case CommonFields.LIFE_STATUS__NORMAL:
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_SERVICE_1));
                    case CommonFields.LIFE_STATUS__INEFFECTIVE:
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_SERVICE_2));
                    default:
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_SERVICE_3));
                }
            }
        }
    }

    @Override
    public void triggerCloseTPMActivity(String tenantId, String userId, String activityId, boolean needForce, boolean needTriggerApproval) {

        ActionContext context = new ActionContext(RequestContext.builder().tenantId(tenantId).user(User.builder().tenantId(tenantId).userId(userId).build()).build(),
                ApiNames.TPM_ACTIVITY_OBJ, "CloseTPMActivity");
        context.setAttribute("triggerFlow", needTriggerApproval);
        TPMActivityObjCloseTPMActivityAction.Arg arg = new TPMActivityObjCloseTPMActivityAction.Arg();
        arg.setDataId(activityId);
        arg.setIsConfirmWriteOff(needForce);
        serviceFacade.triggerAction(context, arg, TPMActivityObjCloseTPMActivityAction.Arg.class);
    }

    @Override
    public List<IObjectData> findActivityByStore(String tenantId, List<String> departmentIds, IObjectData store, List<String> includeActivityIds, List<String> activityTypeIds) {
        if (CollectionUtils.isEmpty(activityTypeIds)) {
            return new ArrayList<>();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setFilters(Lists.newArrayList());

        int index = 1;
        StringBuilder pattern = new StringBuilder();

        Filter activityStatusFilter = new Filter();
        activityStatusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        activityStatusFilter.setOperator(Operator.EQ);
        activityStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS));
        query.getFilters().add(activityStatusFilter);
        pattern.append("  ").append(index++);

        if (!CollectionUtils.isEmpty(includeActivityIds)) {
            Filter idFilter = new Filter();
            idFilter.setFieldName(CommonFields.ID);
            idFilter.setOperator(Operator.IN);
            idFilter.setFieldValues(includeActivityIds);
            query.getFilters().add(idFilter);
            pattern.append(" and ").append(index++).append(" ");
        }

        if (!CollectionUtils.isEmpty(departmentIds)) {
            Filter departmentFilter = new Filter();
            departmentFilter.setFieldName(TPMActivityFields.MULTI_DEPARTMENT_RANGE);
            departmentFilter.setOperator(Operator.HASANYOF);
            departmentFilter.setFieldValues(departmentIds);
            query.getFilters().add(departmentFilter);

            pattern.append(" and ").append(index++).append(" ");
        }

        Filter storeFilter = new Filter();
        storeFilter.setFieldName(TPMActivityFields.DEALER_ID);
        storeFilter.setOperator(Operator.IS);
        storeFilter.setFieldValues(Lists.newArrayList());
        query.getFilters().add(storeFilter);

        String dealerId = storeBusiness.findDealerId(tenantId, store);

        if (!Strings.isNullOrEmpty(dealerId)) {
            Filter dealerIdFilter = new Filter();
            dealerIdFilter.setFieldName(TPMActivityFields.DEALER_ID);
            dealerIdFilter.setOperator(Operator.EQ);
            dealerIdFilter.setFieldValues(Lists.newArrayList(dealerId));
            query.getFilters().add(dealerIdFilter);
            pattern.append(" and  ( ").append(index++).append(" or ").append(index++).append(" ) ");
        } else {
            pattern.append(" and ").append(index++);
        }


        if (!CollectionUtils.isEmpty(activityTypeIds) && !activityTypeIds.contains("all")) {
            Filter activityTypeFilter = new Filter();
            activityTypeFilter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
            activityTypeFilter.setOperator(Operator.IN);
            activityTypeFilter.setFieldValues(activityTypeIds);
            query.getFilters().add(activityTypeFilter);
            pattern.append(" and  ").append(index);
        }

        query.setPattern(pattern.toString());
        List<IObjectData> activities = CommonUtils.queryAllDataInFields(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_OBJ, query, Lists.newArrayList(CommonFields.ID, CommonFields.NAME, CommonFields.CREATE_TIME, TPMActivityFields.DEALER_ID, TPMActivityFields.ACTIVITY_TYPE, TPMActivityFields.STORE_RANGE, TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, CommonFields.OBJECT_DESCRIBE_API_NAME, "reward_activity_tenant__c", TPMActivityFields.PRODUCT_RANGE, TPMActivityFields.PRODUCT_RANGE_FRESH_STANDARD, TPMActivityFields.ACTIVATION_START_TIME, TPMActivityFields.ACTIVATION_END_TIME, TPMActivityFields.ACTIVATION_END_TIME, TPMActivityFields.IS_ALLOW_OUTER_CODE_SCAN));

        Map<String, Boolean> existsMap = rangeFieldBusiness.judgeStoreInActivitiesStoreRange(tenantId, store.getId(), dealerId, activities, false, true);

        return activities.stream().filter(v -> existsMap.getOrDefault(v.getId(), false)).collect(Collectors.toList());
    }

    @Override
    public List<String> getDepartmentByStore(String tenantId, IObjectData store) {
        if (TPMGrayUtils.jumpDepartmentInRewardActivity(tenantId)) {
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(store.getDataOwnDepartment())) {
            throw new RewardFmcgException("10011", I18N.text(I18NKeys.REWARD2_ACTIVITY_SERVICE_0));
        }
        return queryDepartmentAndAncestorIds(tenantId, store.getDataOwnDepartment().get(0));
    }

    private List<String> queryDepartmentAndAncestorIds(String tenantId, String departmentId) {
        GetDepartmentDtoArg arg = new GetDepartmentDtoArg();
        arg.setEnterpriseId(Integer.parseInt(tenantId));
        arg.setDepartmentId(Integer.parseInt(departmentId));
        GetDepartmentDtoResult result = departmentProviderService.getDepartmentDto(arg);
        List<String> ids = new ArrayList<>();
        ids.add(departmentId);
        log.info("result:{},departmentId:{}", result, departmentId);
        result.getDepartment().getAncestors().forEach(v -> ids.add(String.valueOf(v)));
        return ids;
    }


    public List<IObjectData> findObjByActivity(String tenantId, String id, String objApiName, String referenceFieldApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);

        Filter activityTypeFilter = new Filter();
        activityTypeFilter.setFieldName(referenceFieldApiName);
        activityTypeFilter.setFieldValues(Lists.newArrayList(id));
        activityTypeFilter.setOperator(Operator.EQ);

        query.setFilters(Lists.newArrayList(activityTypeFilter));
        query.setOrders(Lists.newArrayList());

        List<IObjectData> activityObjs = serviceFacade.findBySearchQuery(User.systemUser(tenantId), objApiName, query).getData();
        if (activityObjs == null) {
            return Lists.newArrayList();
        }
        return activityObjs;
    }
}
