package com.facishare.crm.fmcg.tpm.business;

import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetBusinessSubjectFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetActivitySubjectService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.SqlEscaper;
import com.google.common.base.Strings;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/18 上午11:30
 */
@Service
public class BudgetActivitySubjectService implements IBudgetActivitySubjectService {

    private static final String QUERY_LEVEL_SQL = "with recursive  son_tree as\n" +
            "(\n" +
            " select name,id,parent_subject_id ,1 as rank from fmcg_tpm_budget_business_subject  where id = '%s'\n" +
            "  union all\n" +
            "  select a.name,a.id,a.parent_subject_id,b.rank + 1 from fmcg_tpm_budget_business_subject a join son_tree b on a.id=b.parent_subject_id where tenant_id ='%s' and is_deleted =0\n" +
            ") select max(rank) max_rank from son_tree;";

    @Resource
    private ServiceFacade serviceFacade;

    @Resource(name = "objectDataPgService")
    private IObjectDataService objectDataService;

    @Override
    public void resetSubjectLevel(User user, String parentId, Integer level, List<String> path) {
        if (Strings.isNullOrEmpty(parentId)) {
            return;
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        Filter parentFilter = new Filter();
        parentFilter.setFieldName(TPMBudgetBusinessSubjectFields.PARENT_SUBJECT_ID);
        parentFilter.setOperator(Operator.EQ);
        parentFilter.setFieldValues(Lists.newArrayList(parentId));
        query.setFilters(Lists.newArrayList(parentFilter));
        List<IObjectData> sons = CommonUtils.queryData(serviceFacade, user, ApiNames.TPM_BUDGET_BUSINESS_SUBJECT, query);
        for (IObjectData data : sons) {
            Map<String, Object> updateMap = new HashMap<>();
            updateMap.put(TPMBudgetBusinessSubjectFields.SUBJECT_LEVEL, level);
            serviceFacade.updateWithMap(user, data, updateMap);
            if (path.contains(data.getId())) {
                continue;
            }
            path.add(data.getId());
            resetSubjectLevel(user, data.getId(), level + 1, path);
            path.remove(data.getId());
        }
    }

    @Override
    public Integer getSubjectLevel(User user, String id) {
        String realSql = String.format(QUERY_LEVEL_SQL, SqlEscaper.pg_escape(id), user.getTenantId());
        int level = 0;
        try {
            List<Map> list = objectDataService.findBySql(user.getTenantId(), realSql);
            if (!CollectionUtils.isEmpty(list)) {
                Map map = list.get(0);
                if (!MapUtils.isEmpty(map)) {
                    level = Integer.parseInt(map.get("max_rank").toString());
                }
            }
        } catch (MetadataServiceException e) {
            throw new ValidateException(e.getMessage());
        }
        return level;
    }
}
