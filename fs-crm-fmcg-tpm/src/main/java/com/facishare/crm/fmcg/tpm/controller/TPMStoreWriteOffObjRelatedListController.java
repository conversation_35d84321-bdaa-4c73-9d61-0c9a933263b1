package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.SalesOrderObjFields;
import com.facishare.crm.fmcg.common.apiname.TPMStoreWriteOffFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPM2Service;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.NodeType;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;

import java.util.Objects;

public class TPMStoreWriteOffObjRelatedListController extends StandardRelatedListController {

    public static final ITPM2Service tpm2Service = SpringUtil.getContext().getBean(ITPM2Service.class);
    public static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);
    private boolean isTpm2Tenant = false;

    @Override
    protected void before(Arg arg) {
        this.isTpm2Tenant = tpm2Service.isTPM2Tenant(Integer.valueOf(controllerContext.getTenantId()));
        super.before(arg);
    }


    @Override
    protected Result doService(Arg arg) {
        return super.doService(arg);
    }

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        super.beforeQueryData(overrideTPMStoreWriteOffQuery(query));
    }

    private SearchTemplateQuery overrideTPMStoreWriteOffQuery(SearchTemplateQuery query) {
        if (!this.isTpm2Tenant) {
            return query;
        }

        ObjectDataDocument objectData = arg.getObjectData();
        if (Objects.isNull(objectData)) {
            return query;
        }

        String objectApiName = (String) objectData.get("object_describe_api_name");
        if (!ApiNames.SALES_ORDER_OBJ.equals(objectApiName)) {
            return query;
        }

        String activityId = (String) objectData.get(SalesOrderObjFields.ACTIVITY_ID);
        if (Strings.isNullOrEmpty(activityId)) {
            throw new ValidateException(I18N.text(I18NKeys.STORE_WRITE_OFF_OBJ_RELATED_LIST_CONTROLLER_0));
        }

        ActivityTypeExt activityTypeExt = activityTypeManager.findByActivityId(controllerContext.getTenantId(), activityId);
        if (Objects.isNull(activityTypeExt)) {
            throw new ValidateException(I18N.text(I18NKeys.STORE_WRITE_OFF_OBJ_RELATED_LIST_CONTROLLER_1));
        }

        ActivityNodeEntity node = activityTypeExt.node(NodeType.STORE_WRITE_OFF);
        if (Objects.isNull(node)) {
            throw new ValidateException(I18N.text(I18NKeys.STORE_WRITE_OFF_OBJ_RELATED_LIST_CONTROLLER_2));
        }

        // 活动方案下的门店费用核销
        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMStoreWriteOffFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));
        query.getFilters().add(activityFilter);

        return query;
    }
}
