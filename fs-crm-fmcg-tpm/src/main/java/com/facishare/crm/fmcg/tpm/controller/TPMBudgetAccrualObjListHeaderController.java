package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.business.ListHeaderBusiness;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.google.common.collect.Sets;

import java.util.Set;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 4:39 PM
 */
public class TPMBudgetAccrualObjListHeaderController extends StandardListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {
        Set<String> hiddenFieldSet = Sets.newHashSet("Add", "Edit", "Import");
        if (TPMGrayUtils.isAllowAccrualCreateByHand(controllerContext.getTenantId())) {
            hiddenFieldSet.remove("Add");
        }
        ListHeaderBusiness.listHeaderFilter(controllerContext.getTenantId(), ApiNames.TPM_BUDGET_ACCRUAL_DETAIL_OBJ, hiddenFieldSet, result);
        return super.after(arg, result);
    }
}
