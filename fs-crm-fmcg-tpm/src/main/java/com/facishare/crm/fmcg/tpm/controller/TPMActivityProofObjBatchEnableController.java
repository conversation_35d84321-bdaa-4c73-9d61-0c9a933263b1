package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Maps;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.api.proof.BatchEnableV2;
import com.facishare.crm.fmcg.common.apiname.ActivityCustomerTypeEnum;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IEnableCacheService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRangeFieldBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IUnifiedActivityCommonLogicBusiness;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.DataPrivilegeService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * author: wuyx
 * description: 缓存优化后接口
 * createTime: 2022/7/8 11:04
 */
@SuppressWarnings("Duplicates,unused")
public class TPMActivityProofObjBatchEnableController extends PreDefineController<BatchEnableV2.Arg, BatchEnableV2.Result> {

    private final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);
    private final OrganizationService organizationService = SpringUtil.getContext().getBean(OrganizationService.class);
    private final MergeJedisCmd redisCmd = SpringUtil.getContext().getBean("redisCmd", MergeJedisCmd.class);
    private final IUnifiedActivityCommonLogicBusiness unifiedActivityCommonLogicBusiness = SpringUtil.getContext().getBean(IUnifiedActivityCommonLogicBusiness.class);

    private IRangeFieldBusiness rangeFieldBusiness = SpringUtil.getContext().getBean(IRangeFieldBusiness.class);

    private final DataPrivilegeService dataPrivilegeService = SpringUtil.getContext().getBean(DataPrivilegeService.class);
    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);

    private boolean hasAllDealerAllRangeActivity = false;
    public static final int UNPROCESSED_FLAG = -1;
    public static final int ENABLE_FLAG = 1;
    public static final int NOT_ENABLE_FLAG = 0;

    private ISearchTemplate activitySearchTemplate;

    private IObjectDescribe activityDescribe;

    private Map<String, Set<String>> unifiedActivityId2DealerIdsMap = new HashMap<>();

    private User systemUser;

    private boolean isAdminRequest = false;

    private final IEnableCacheService enableCacheService = SpringUtil.getContext().getBean(IEnableCacheService.class);


    @Override
    protected BatchEnableV2.Result doService(BatchEnableV2.Arg arg) {
        this.systemUser = User.systemUser(controllerContext.getTenantId());
        this.isAdminRequest = serviceFacade.isAdmin(controllerContext.getUser());
        this.activityDescribe = serviceFacade.findDescribeAndLayout(this.systemUser, ApiNames.TPM_ACTIVITY_OBJ, false, null).getObjectDescribe();
        this.activitySearchTemplate = serviceFacade.findSearchTemplateByIdAndType(this.systemUser, "", ApiNames.TPM_ACTIVITY_OBJ, "All");

        Map<String, Integer> data = arg.getStoreIds().stream().collect(Collectors.toMap(k -> k, v -> UNPROCESSED_FLAG));

        boolean isAllowSkillSkip = false;
        // 兼容外部用户赋值给userId
        if (!controllerContext.getUser().isOutUser()) {
            String userIdStr = controllerContext.getUser().getUserId();
            if (!Strings.isNullOrEmpty(userIdStr)) {
                long userId = Long.parseLong(userIdStr);
                isAllowSkillSkip = userId > 300000000;
            }
        }

        if (TPMGrayUtils.skipEnableCheck(controllerContext.getTenantId()) || isAllowSkillSkip) {
            data.replaceAll((k, v) -> 1);
        } else {
            if (arg.isUseCache() && !TPMGrayUtils.skipEnableCacheCheck(controllerContext.getTenantId())) {
                doCacheCheck(data);
            }
        }
        stopWatch.lap("start doEnableCheck");
        doEnableCheck(data);
        return convertToResult(data);
    }

    private void doCacheCheck(Map<String, Integer> data) {
        for (Map.Entry<String, Integer> entry : data.entrySet()) {
            String store = entry.getKey();
            Integer value = entry.getValue();
            int cache = enableCacheService.getCache(controllerContext.getTenantId(), controllerContext.getUser().getUserIdOrOutUserIdIfOutUser(), store);
            if (cache != -1) {
                entry.setValue(cache);
            }
        }
    }

    private void doEnableCheck(Map<String, Integer> data) {
        List<String> storeIds = data.entrySet().stream().filter(f -> f.getValue().equals(UNPROCESSED_FLAG)).map(Map.Entry::getKey).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storeIds)) {
            return;
        }

        List<Integer> departmentIds;
        if (!controllerContext.getUser().isOutUser()) {
            departmentIds = organizationService.getDepartmentIds(controllerContext.getUser().getTenantIdInt(), Integer.parseInt(controllerContext.getUser().getUserIdOrOutUserIdIfOutUser()));
            if (CollectionUtils.isEmpty(departmentIds)) {
                for (String storeId : storeIds) {
                    data.put(storeId, NOT_ENABLE_FLAG);
                }
                return;
            }
        } else {
            departmentIds = Lists.newArrayList();
        }
        stopWatch.lap("query department");

        List<IObjectData> stores = serviceFacade.findObjectDataByIdsIgnoreAll(controllerContext.getTenantId(), storeIds, ApiNames.ACCOUNT_OBJ);

        Map<String, String> dealerIdMap = Maps.newHashMap();
        List<String> dealerRecordType = storeBusiness.findDealerRecordType(controllerContext.getTenantId());
        for (IObjectData store : stores) {
            if (dealerRecordType.contains(store.getRecordType())) {
                dealerIdMap.put(store.getId(), store.getId());
            } else {
                dealerIdMap.put(store.getId(), storeBusiness.findDealerId(controllerContext.getTenantId(), store));
            }
        }
        stopWatch.lap("get store");

        List<IObjectData> activities = queryNoDealerActivity(departmentIds);

        stopWatch.lap("queryNoDealerActivity");

        validationAllRangeActivity(activities);
        stopWatch.lap("validationAllRangeActivity");

        if (this.hasAllDealerAllRangeActivity) {
            data.replaceAll((k, v) -> ENABLE_FLAG);
            return;
        }

        final List<String> dealerIds = dealerIdMap.values().stream().filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, List<IObjectData>> dealerActivityMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(dealerIds)) {
            dealerActivityMap = queryDealerActivity(departmentIds, dealerIds)
                    .stream()
                    .collect(Collectors.groupingBy(g -> g.get(TPMActivityFields.DEALER_ID, String.class)));
        }
        stopWatch.lap("init dealerActivityMap");

        Set<String> errors = new CopyOnWriteArraySet<>();
        try {
            ParallelUtils.ParallelTask checkTask = ParallelUtils.createParallelTask();
            Map<String, List<IObjectData>> finalDealerActivityMap = dealerActivityMap;
            Lists.partition(stores, 8).forEach(parts -> checkTask.submit(() -> {
                for (IObjectData store : parts) {
                    boolean enable;

                    enable = doEnableCheck(
                            store.getId(),
                            dealerIdMap.get(store.getId()),
                            activities,
                            finalDealerActivityMap,
                            departmentIds
                    );
                    data.put(store.getId(), enable ? 1 : 0);
                }
            }));
            checkTask.await(4000, TimeUnit.MILLISECONDS);
        } catch (ValidateException e) {
            errors.add(e.getMessage());
        } catch (Exception e) {
            log.error("enable check timeout", e);
        }

        stopWatch.lap("existsDealerActivityOfStore");
        if (CollectionUtils.isNotEmpty(errors)) {
            throw new ValidateException(String.join(",", errors));
        }
    }

    private void validationAllRangeActivity(List<IObjectData> activities) {
        List<IObjectData> hasUnifiedActivities = activities.stream().filter(activity -> {
            String unifiedActivityId = activity.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class);
            return !Strings.isNullOrEmpty(unifiedActivityId);
        }).collect(Collectors.toList());

        List<String> hasUnifiedActivityIds = hasUnifiedActivities.stream().map(IObjectData::getId).collect(Collectors.toList());

        List<IObjectData> noUnifiedActivitiesActivities = activities.stream().filter(iObjectData -> !hasUnifiedActivityIds.contains(iObjectData.getId())).collect(Collectors.toList());
        this.hasAllDealerAllRangeActivity = noUnifiedActivitiesActivities.stream().anyMatch(a -> {
            String json = (String) a.get(TPMActivityFields.STORE_RANGE);
            String customerType = a.get(TPMActivityFields.CUSTOMER_TYPE, String.class, ActivityCustomerTypeEnum.DEALER_STORE.value());
            if (!ActivityCustomerTypeEnum.DEALER_STORE.value().equals(customerType)) {
                return false;
            }
            if (!Strings.isNullOrEmpty(json)) {
                JSONObject range = JSON.parseObject(json);
                String type = range.getString("type");
                return "ALL".equals(type);
            }
            return false;
        });

        Map<String, List<IObjectData>> unifiedActivityIdMap = hasUnifiedActivities.stream().collect(Collectors.groupingBy(activity -> activity.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class)));
        List<IObjectData> unifiedActivities = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), hasUnifiedActivityIds, ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);

        if (!this.hasAllDealerAllRangeActivity) {
            this.hasAllDealerAllRangeActivity = unifiedActivities.stream().anyMatch(unifiedActivity -> {
                String json = unifiedActivity.get(TPMActivityUnifiedCaseFields.STORE_RANGE, String.class);
                if (!Strings.isNullOrEmpty(json)) {
                    JSONObject range = JSON.parseObject(json);
                    String type = range.getString("type");
                    if ("ALL".equals(type)) {
                        List<IObjectData> activityOfUnifiedActivity = unifiedActivityIdMap.get(unifiedActivity.getId());
                        for (IObjectData a : activityOfUnifiedActivity) {
                            String storeRange = (String) a.get(TPMActivityFields.STORE_RANGE);
                            if (!Strings.isNullOrEmpty(storeRange)) {
                                JSONObject activityRange = JSON.parseObject(storeRange);
                                String activityType = activityRange.getString("type");
                                return "ALL".equals(activityType);
                            }
                        }
                    }
                }
                return false;
            });
        }
    }

    private BatchEnableV2.Result convertToResult(Map<String, Integer> data) {
        return new BatchEnableV2.Result(
                data.entrySet().stream().collect(
                        Collectors.toMap(Map.Entry::getKey, v -> v.getValue() != NOT_ENABLE_FLAG)
                )
        );
    }


    private List<String> getProofActivityType() {
        return activityTypeManager.queryProofActivityTypeIds(controllerContext.getTenantId());
    }


    private List<IObjectData> queryNoDealerActivity(List<Integer> departmentIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter activityStatusFilter = new Filter();
        activityStatusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        activityStatusFilter.setOperator(Operator.EQ);
        activityStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS));

        Filter dealerActivityFilter = new Filter();
        dealerActivityFilter.setFieldName(TPMActivityFields.DEALER_ID);
        dealerActivityFilter.setOperator(Operator.IS);
        dealerActivityFilter.setFieldValues(Lists.newArrayList());

        //只有含有举证节点的活动需要Enable
        Filter activityTypeFilter = new Filter();
        activityTypeFilter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
        activityTypeFilter.setOperator(Operator.IN);
        activityTypeFilter.setFieldValues(getProofActivityType());

        Filter closeStatusFilter = new Filter();
        closeStatusFilter.setFieldName(TPMActivityFields.CLOSE_STATUS);
        closeStatusFilter.setOperator(Operator.EQ);
        closeStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.CLOSE_STATUS__UNCLOSED));

        query.setFilters(Lists.newArrayList(activityStatusFilter, activityTypeFilter, dealerActivityFilter, closeStatusFilter));

        if (!controllerContext.getUser().isOutUser() && !TPMGrayUtils.denyDepartmentFilterOnActivity(controllerContext.getTenantId())) {

            Filter departmentRangeFilter = new Filter();
            departmentRangeFilter.setFieldName(TPMActivityFields.MULTI_DEPARTMENT_RANGE);
            departmentRangeFilter.setOperator(Operator.HASANYOF);
            departmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));

            query.getFilters().add(departmentRangeFilter);
        }

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));
        query.getFilters().add(lifeStatusFilter);

        SearchQueryUtil.handleDataPrivilege(dataPrivilegeService, controllerContext.getUser(), query, this.activityDescribe, this.activitySearchTemplate, this.isAdminRequest);
        return QueryDataUtil.find(
                serviceFacade,
                controllerContext.getUser(),
                controllerContext.getRequestContext(),
                ApiNames.TPM_ACTIVITY_OBJ,
                query,
                Lists.newArrayList(CommonFields.ID, TPMActivityFields.STORE_RANGE, TPMActivityFields.DEALER_ID, TPMActivityFields.CUSTOMER_TYPE, TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, TPMActivityFields.MULTI_DEPARTMENT_RANGE));
    }


    private List<IObjectData> queryDealerActivity(List<Integer> departmentIds, List<String> dealerIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter activityStatus = new Filter();
        activityStatus.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        activityStatus.setOperator(Operator.EQ);
        activityStatus.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS));

        Filter dealerIdEqualFilter = new Filter();
        dealerIdEqualFilter.setFieldName(TPMActivityFields.DEALER_ID);
        dealerIdEqualFilter.setOperator(Operator.IN);
        dealerIdEqualFilter.setFieldValues(Lists.newArrayList(dealerIds));

        //只有含有举证节点的活动需要Enable
        Filter activityTypeFilter = new Filter();
        activityTypeFilter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
        activityTypeFilter.setOperator(Operator.IN);
        activityTypeFilter.setFieldValues(getProofActivityType());

        Filter closeStatusFilter = new Filter();
        closeStatusFilter.setFieldName(TPMActivityFields.CLOSE_STATUS);
        closeStatusFilter.setOperator(Operator.EQ);
        closeStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.CLOSE_STATUS__UNCLOSED));

        query.setFilters(Lists.newArrayList(activityStatus, dealerIdEqualFilter, activityTypeFilter, closeStatusFilter));

        if (!controllerContext.getUser().isOutUser() && !TPMGrayUtils.denyDepartmentFilterOnActivity(controllerContext.getTenantId())) {

            Filter departmentRangeFilter = new Filter();
            departmentRangeFilter.setFieldName(TPMActivityFields.MULTI_DEPARTMENT_RANGE);
            departmentRangeFilter.setOperator(Operator.HASANYOF);
            departmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));

            query.getFilters().add(departmentRangeFilter);
        }

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));
        query.getFilters().add(lifeStatusFilter);

        SearchQueryUtil.handleDataPrivilege(dataPrivilegeService, controllerContext.getUser(), query, this.activityDescribe, this.activitySearchTemplate, this.isAdminRequest);
        return QueryDataUtil.find(
                serviceFacade,
                controllerContext.getUser(),
                controllerContext.getRequestContext(),
                ApiNames.TPM_ACTIVITY_OBJ,
                query,
                Lists.newArrayList(CommonFields.ID, TPMActivityFields.STORE_RANGE, TPMActivityFields.DEALER_ID, TPMActivityFields.CUSTOMER_TYPE, TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, TPMActivityFields.MULTI_DEPARTMENT_RANGE));
    }

    private boolean doEnableCheck(
            String storeId,
            String dealerId,
            List<IObjectData> activities,
            Map<String, List<IObjectData>> dealerActivityMap,
            List<Integer> departmentIds) {


        List<IObjectData> dealList = new ArrayList<>(activities);
        if (!Strings.isNullOrEmpty(dealerId)) {
            dealList.addAll(dealerActivityMap.getOrDefault(dealerId, Lists.newArrayList()));
        }

        return checkEnableV2(controllerContext.getTenantId(), storeId, dealList, dealerId);
    }

    private boolean checkEnableV2(String tenantId, String storeId, List<IObjectData> activities, String dealerId) {
        Map<String, Boolean> enableResult = rangeFieldBusiness.judgeStoreInActivitiesStoreRange(tenantId, storeId, dealerId, activities, true, true);
        //stopWatch.lap("judgeStoreInActivitiesStoreRange");
        return enableResult.containsValue(true);
    }


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }

    @Override
    protected void before(BatchEnableV2.Arg arg) {
        super.before(arg);

        if (CollectionUtils.isEmpty(arg.getStoreIds())) {
            throw new ValidateException("data id list is empty.");
        }

        arg.setStoreIds(arg.getStoreIds().stream().distinct().collect(Collectors.toList()));
    }

    @Override
    protected BatchEnableV2.Result after(BatchEnableV2.Arg arg, BatchEnableV2.Result result) {
        if (this.hasAllDealerAllRangeActivity) {
            enableCacheService.setALLCache(controllerContext.getTenantId(), controllerContext.getUser().getUserIdOrOutUserIdIfOutUser());
        } else {
            for (Map.Entry<String, Boolean> entry : result.getEnableData().entrySet()) {
                enableCacheService.setStoreCache(controllerContext.getTenantId(), controllerContext.getUser().getUserIdOrOutUserIdIfOutUser(), entry.getKey(), entry.getValue());
            }
        }
        return super.after(arg, result);
    }
}