package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.apiname.PromotionActivityTemplateIdEnum;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDescribeCacheService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRangeFieldBusiness;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityNodeTemplateDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("Duplicates,unused")
public class TPMActivityObjRelatedListV2Controller extends StandardRelatedListController {

    private static final ActivityNodeTemplateDAO activityNodeTemplateDAO = SpringUtil.getContext().getBean(ActivityNodeTemplateDAO.class);
    private static final ActivityTypeDAO activityTypeDAO = SpringUtil.getContext().getBean(ActivityTypeDAO.class);

    private static final OrganizationService organizationService = SpringUtil.getContext().getBean(OrganizationService.class);
    private static final IDescribeCacheService I_DESCRIBE_CACHE_SERVICE = SpringUtil.getContext().getBean(IDescribeCacheService.class);

    private static final IRangeFieldBusiness rangeFieldBusiness = SpringUtil.getContext().getBean(IRangeFieldBusiness.class);
    private static final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);

    private Map<String, String> recordTypeMap = new HashMap<>();

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        if (TPMGrayUtils.TPMUseEs(controllerContext.getTenantId())) {
            query.setSearchSource("es");
        }
        super.beforeQueryData(overrideActivityQuery(query));
    }

    @Override
    protected Result after(Arg arg, Result result) {
        overrideActivityData(result);
        return super.after(arg, result);
    }

    private void overrideActivityData(Result result) {
        for (ObjectDataDocument activity : result.getDataList()) {

            // fill current object record type
            String activityTypeId = (String) activity.get(TPMActivityFields.ACTIVITY_TYPE);
            activity.put("__current_object_record_type", recordTypeMap.getOrDefault(activityTypeId, ""));

            // override begin date and end date
            Long beginDate = (Long) activity.get(TPMActivityFields.BEGIN_DATE);
            Long endDate = (Long) activity.get(TPMActivityFields.END_DATE);
            if (beginDate <= TimeUtils.MIN_DATE) {
                activity.put(TPMActivityFields.BEGIN_DATE, null);
            }
            if (endDate >= TimeUtils.MAX_DATE) {
                activity.put(TPMActivityFields.END_DATE, null);
            }
        }
    }

    private SearchTemplateQuery overrideActivityQuery(SearchTemplateQuery query) {
        log.info("original query : {}", JSON.toJSONString(query));

        if (Objects.isNull(arg.getObjectData())) {
            return query;
        }

        String objectApiName = (String) arg.getObjectData().get("object_describe_api_name");
        if (Strings.isNullOrEmpty(objectApiName) || objectApiName.endsWith("__c") || ApiNames.TPM_ACTIVITY_OBJ.equals(objectApiName)) {
            return query;
        }

        switch (objectApiName) {
            case ApiNames.SALES_ORDER_OBJ:
                return overrideActivityQueryForSalesOrder(query);
            case ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ:
                return overrideActivityQueryForAgreement(query);
            default:
                return overrideActivityQueryForOther(objectApiName, query);
        }
    }

    private SearchTemplateQuery overrideActivityQueryForSalesOrder(SearchTemplateQuery query) {

        Filter closeStatusFilter = new Filter();
        closeStatusFilter.setFieldName(TPMActivityFields.CLOSE_STATUS);
        closeStatusFilter.setOperator(Operator.NEQ);
        closeStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.CLOSE_STATUS__CLOSED));
        query.getFilters().add(closeStatusFilter);

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(TPMActivityFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));
        query.getFilters().add(lifeStatusFilter);

        Filter activityStatusFilter = new Filter();
        activityStatusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        activityStatusFilter.setOperator(Operator.IN);
        activityStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS, TPMActivityFields.ACTIVITY_STATUS__END));
        query.getFilters().add(activityStatusFilter);

        ActivityNodeTemplatePO template = activityNodeTemplateDAO.findByApiName(controllerContext.getTenantId(), ApiNames.SALES_ORDER_OBJ);
        if (Objects.isNull(template)) {
            return query;
        }

        String referenceActivityFieldApiName = template.getReferenceFieldApiName();
        IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), ApiNames.SALES_ORDER_OBJ);
        IFieldDescribe fieldDescribe = describe.getFieldDescribe(referenceActivityFieldApiName);

        if (Objects.isNull(fieldDescribe)) {
            return query;
        }

        if (!(fieldDescribe instanceof ObjectReferenceFieldDescribe)) {
            return query;
        }

        ObjectReferenceFieldDescribe objectReferenceField = (ObjectReferenceFieldDescribe) fieldDescribe;
        String fieldTargetRelatedListName = objectReferenceField.getTargetRelatedListName();

        if (!Strings.isNullOrEmpty(arg.getRelatedListName()) && !arg.getRelatedListName().equals(fieldTargetRelatedListName)) {
            return query;
        }

        List<ActivityTypePO> activityTypes = activityTypeDAO.queryByNodeTemplateId(controllerContext.getTenantId(), template.getId().toString());
        List<ActivityTypePO> promotionActivityTypes = activityTypeDAO.queryByTypeTemplateId(controllerContext.getTenantId(), PromotionActivityTemplateIdEnum.templateIds());

        activityTypes.addAll(promotionActivityTypes);

        List<String> activityTypeIds = activityTypes.stream().map(m -> m.getId().toString()).distinct().collect(Collectors.toList());

        for (ActivityTypePO activityType : activityTypes) {
            this.recordTypeMap.put(activityType.getId().toString(), findRecordType(activityType, ApiNames.SALES_ORDER_OBJ));
        }

        String accountId = (String) arg.getObjectData().get(SalesOrderObjFields.ACCOUNT_ID);
        List<String> activityIds = queryActivityIdsByAccountId(accountId, activityTypeIds);

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(CommonFields.ID);
        activityIdFilter.setOperator(CollectionUtils.isEmpty(activityIds) ? Operator.IS : Operator.IN);
        activityIdFilter.setFieldValues(activityIds);
        query.getFilters().add(activityIdFilter);

        log.info("final query : {}", JSON.toJSONString(query));

        return query;
    }

    private SearchTemplateQuery overrideActivityQueryForAgreement(SearchTemplateQuery query) {
        Filter closeStatusFilter = new Filter();
        closeStatusFilter.setFieldName(TPMActivityFields.CLOSE_STATUS);
        closeStatusFilter.setOperator(Operator.NEQ);
        closeStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.CLOSE_STATUS__CLOSED));
        query.getFilters().add(closeStatusFilter);

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(TPMActivityFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));
        query.getFilters().add(lifeStatusFilter);

        ActivityNodeTemplatePO template = activityNodeTemplateDAO.findByApiName(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        if (Objects.isNull(template)) {
            return query;
        }

        String referenceActivityFieldApiName = template.getReferenceFieldApiName();
        IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        IFieldDescribe fieldDescribe = describe.getFieldDescribe(referenceActivityFieldApiName);

        if (!Objects.isNull(fieldDescribe) && fieldDescribe instanceof ObjectReferenceFieldDescribe) {
            ObjectReferenceFieldDescribe objectReferenceField = (ObjectReferenceFieldDescribe) fieldDescribe;
            String fieldTargetRelatedListName = objectReferenceField.getTargetRelatedListName();

            if (Strings.isNullOrEmpty(arg.getRelatedListName()) || arg.getRelatedListName().equals(fieldTargetRelatedListName)) {
                List<ActivityTypePO> activityTypes = activityTypeDAO.queryByNodeTemplateId(controllerContext.getTenantId(), template.getId().toString());
                List<String> activityTypeIds = activityTypes.stream().map(m -> m.getId().toString()).collect(Collectors.toList());
                recordTypeMap = activityTypes.stream().collect(Collectors.toMap(k -> k.getId().toString(), v -> findRecordType(v, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ)));
                List<IFilter> newFilters = Lists.newArrayList();

                //协议节点
                Integer count = 0;
                StringBuilder pattern = new StringBuilder();
                List<IFilter> sourceFilters = query.getFilters();
                for (int i = 0; i < sourceFilters.size(); i++) {
                    if (i != 0) {
                        IFilter iFilter = sourceFilters.get(i);
                        if ("AND".equals(iFilter.getConnector())) {
                            pattern.append(" and ");
                        } else if ("OR".equals(iFilter.getConnector())) {
                            pattern.append(" or ");
                        } else {
                            pattern.append(" and ");
                        }
                    }
                    count += 1;
                    pattern.append(count);
                }

                count = activityAgreementFilter(count, pattern, activityTypes, activityTypeIds, newFilters);
                query.setPattern(pattern.toString());

                log.info("end agreement count : {} , pattern : {}", count, pattern);

                List<Integer> departmentIds = organizationService.getDepartmentIds(controllerContext.getUser().getTenantIdInt(), Integer.parseInt(controllerContext.getUser().getUpstreamOwnerIdOrUserId()));
                departmentIds = CollectionUtils.isNotEmpty(departmentIds) ? departmentIds : Lists.newArrayList(-1);

                Filter departmentFilter = new Filter();
                departmentFilter.setFieldName(TPMActivityFields.MULTI_DEPARTMENT_RANGE);
                departmentFilter.setOperator(Operator.HASANYOF);
                departmentFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));
                newFilters.add(departmentFilter);

                if (CollectionUtils.isEmpty(query.getFilters())) {
                    query.setFilters(newFilters);
                } else {
                    query.getFilters().addAll(newFilters);
                }

                List<LinkedHashMap> wheres = objectReferenceField.getWheres();
                for (int i = 0; i < wheres.size(); i++) {
                    JSONObject jsonWhere = JSON.parseObject(JSON.toJSONString(wheres.get(i)));
                    List<IFilter> filters = toFilters(jsonWhere.getJSONArray("filters"));
                    if (CollectionUtils.isEmpty(filters)) {
                        continue;
                    }
                    if (i == 0) {
                        pattern.append(" ").append(" and").append(" (");
                    } else {
                        pattern.append(" ").append(jsonWhere.getString("connector").toLowerCase()).append(" (");
                    }
                    for (int j = 0; j < filters.size(); j++) {
                        IFilter iFilter = filters.get(j);
                        count += 1;
                        pattern.append(" ");
                        if (j != 0) {
                            pattern.append(iFilter.getConnector().toLowerCase()).append(" ");
                        }
                        pattern.append(count);
                        query.getFilters().add(iFilter);
                    }
                    pattern.append(" )");
                }
                query.setPattern(pattern.toString());

            }
        }

        log.info("final query : {}", JSON.toJSONString(query));
        return query;
    }

    private SearchTemplateQuery overrideActivityQueryForOther(String objectApiName, SearchTemplateQuery query) {
        Filter closeStatusFilter = new Filter();
        closeStatusFilter.setFieldName(TPMActivityFields.CLOSE_STATUS);
        closeStatusFilter.setOperator(Operator.NEQ);
        closeStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.CLOSE_STATUS__CLOSED));
        query.getFilters().add(closeStatusFilter);

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(TPMActivityFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));
        query.getFilters().add(lifeStatusFilter);

        Filter activityStatusFilter = new Filter();
        if (ApiNames.TPM_DEALER_ACTIVITY_COST.equals(objectApiName)) {
            activityStatusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
            activityStatusFilter.setOperator(Operator.IN);
            activityStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS, TPMActivityFields.ACTIVITY_STATUS__END));
        } else {
            activityStatusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
            activityStatusFilter.setOperator(Operator.EQ);
            activityStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS));
        }
        query.getFilters().add(activityStatusFilter);

        ActivityNodeTemplatePO template = activityNodeTemplateDAO.findByApiName(controllerContext.getTenantId(), ApiNames.SALES_ORDER_OBJ);
        if (Objects.isNull(template)) {
            return query;
        }

        String referenceActivityFieldApiName = template.getReferenceFieldApiName();
        IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), ApiNames.SALES_ORDER_OBJ);
        IFieldDescribe fieldDescribe = describe.getFieldDescribe(referenceActivityFieldApiName);

        if (Objects.isNull(fieldDescribe)) {
            return query;
        }

        if (!(fieldDescribe instanceof ObjectReferenceFieldDescribe)) {
            return query;
        }

        ObjectReferenceFieldDescribe objectReferenceField = (ObjectReferenceFieldDescribe) fieldDescribe;
        String fieldTargetRelatedListName = objectReferenceField.getTargetRelatedListName();


        if (!Strings.isNullOrEmpty(arg.getRelatedListName()) && !arg.getRelatedListName().equals(fieldTargetRelatedListName)) {
            return query;
        }

        List<ActivityTypePO> activityTypes = activityTypeDAO.queryByNodeTemplateId(controllerContext.getTenantId(), template.getId().toString());
        List<String> activityTypeIds = activityTypes.stream().map(m -> m.getId().toString()).collect(Collectors.toList());
        this.recordTypeMap = activityTypes.stream().collect(Collectors.toMap(k -> k.getId().toString(), v -> findRecordType(v, objectApiName)));

        Filter activityTypeFilter = new Filter();
        activityTypeFilter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
        activityTypeFilter.setOperator(Operator.IN);
        activityTypeFilter.setFieldValues(CollectionUtils.isEmpty(activityTypeIds) ? Lists.newArrayList("none") : activityTypeIds);
        query.getFilters().add(activityTypeFilter);

        if (ApiNames.TPM_ACTIVITY_PROOF_OBJ.equals(objectApiName)) {
            List<Integer> departmentIds = organizationService.getDepartmentIds(controllerContext.getUser().getTenantIdInt(), Integer.parseInt(controllerContext.getUser().getUpstreamOwnerIdOrUserId()));
            departmentIds = CollectionUtils.isNotEmpty(departmentIds) ? departmentIds : Lists.newArrayList(-1);

            Filter departmentFilter = new Filter();
            departmentFilter.setFieldName(TPMActivityFields.MULTI_DEPARTMENT_RANGE);
            departmentFilter.setOperator(Operator.HASANYOF);
            departmentFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));
            query.getFilters().add(departmentFilter);
        }

        log.info("final query : {}", JSON.toJSONString(query));

        return query;
    }

    private List<String> queryPromotionActivityType() {
        List<ActivityTypePO> activityTypes = activityTypeDAO.queryByTypeTemplateId(controllerContext.getTenantId(), PromotionActivityTemplateIdEnum.templateIds());

        if (CollectionUtils.isEmpty(activityTypes)) {
            return Lists.newArrayList();
        }

        return activityTypes.stream().map(m -> m.getId().toString()).collect(Collectors.toList());
    }

    private Integer activityAgreementFilter(Integer count, StringBuilder pattern, List<ActivityTypePO> activityTypes, List<String> activityTypeIds, List<IFilter> newFilters) {

        log.info("agreement activityTypeIds : {}", JSON.toJSONString(activityTypeIds));

        List<ActivityTypePO> configIsEmptyTypes = activityTypes.stream().filter(activityTypePO -> {
            ActivityTypeExt ext = ActivityTypeExt.of(activityTypePO);
            ActivityAgreementConfigEntity activityAgreementConfig = ext.agreementNode().getActivityAgreementConfig();
            if (Objects.isNull(activityAgreementConfig)) {
                return true;
            } else {
                return (Objects.isNull(activityAgreementConfig.getBeginTimeSetting()) || activityAgreementConfig.getBeginTimeSetting().getValue() == 0) && (Objects.isNull(activityAgreementConfig.getEndTimeSetting()) || activityAgreementConfig.getEndTimeSetting().getValue() == 0);
            }
        }).collect(Collectors.toList());

        List<String> configIsEmptyTypeIds = configIsEmptyTypes.stream().map(m -> m.getId().toString()).collect(Collectors.toList());

        log.info("agreement configIsEmptyTypeIds : {}", JSON.toJSONString(configIsEmptyTypeIds));

        StringBuilder configIsEmptyPattern = new StringBuilder();
        if (CollectionUtils.isNotEmpty(configIsEmptyTypeIds)) {
            Filter activityStatusFilter = new Filter();
            activityStatusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
            activityStatusFilter.setOperator(Operator.EQ);
            activityStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS));
            newFilters.add(activityStatusFilter);

            Filter activityTypeFilter = new Filter();
            activityTypeFilter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
            activityTypeFilter.setOperator(Operator.IN);
            activityTypeFilter.setFieldValues(CollectionUtils.isEmpty(configIsEmptyTypeIds) ? Lists.newArrayList("none") : configIsEmptyTypeIds);
            newFilters.add(activityTypeFilter);

            configIsEmptyPattern.append(count += 1).append(" and ").append(count += 1);
        }

        List<ActivityTypePO> configIsNotEmptyTypes = activityTypes.stream().filter(activityTypePO -> !configIsEmptyTypeIds.contains(activityTypePO.getId().toString())).collect(Collectors.toList());

        log.info("agreement configIsNotEmptyTypes : {}", JSON.toJSONString(configIsNotEmptyTypes));

        StringBuilder configIsNotEmptyPattern = new StringBuilder();
        long now = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(configIsNotEmptyTypes)) {
            for (int i = 0; i < configIsNotEmptyTypes.size(); i++) {
                ActivityTypePO activityTypePO = configIsNotEmptyTypes.get(i);
                ActivityTypeExt ext = ActivityTypeExt.of(activityTypePO);

                if (configIsNotEmptyTypes.size() != 1) {
                    if (i == 0) {
                        configIsNotEmptyPattern.append(" (");
                    } else {
                        configIsNotEmptyPattern.append(" or (");
                    }
                }

                Filter beginDateFilter = new Filter();
                beginDateFilter.setFieldName(calculateFieldNameByTimeSetting(ext.agreementConfig().getBeginTimeSetting(), TPMActivityFields.BEGIN_DATE));
                beginDateFilter.setOperator(Operator.LT);
                beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(calculateStartTimeByTimeSetting(ext.agreementConfig().getBeginTimeSetting(), now))));
                newFilters.add(beginDateFilter);

                Filter endDateFilter = new Filter();
                endDateFilter.setFieldName(calculateFieldNameByTimeSetting(ext.agreementConfig().getEndTimeSetting(), TPMActivityFields.END_DATE));
                endDateFilter.setOperator(Operator.GT);
                endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(calculateEndTimeByTimeSetting(ext.agreementConfig().getEndTimeSetting(), now))));
                newFilters.add(endDateFilter);

                Filter activityTypeFilter = new Filter();
                activityTypeFilter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
                activityTypeFilter.setOperator(Operator.EQ);
                activityTypeFilter.setFieldValues(Lists.newArrayList(activityTypePO.getId().toString()));
                newFilters.add(activityTypeFilter);

                configIsNotEmptyPattern.append(count += 1).append(" and ").append(count += 1).append(" and ").append(count += 1);
                if (configIsNotEmptyTypes.size() != 1) {
                    configIsNotEmptyPattern.append(" )");
                }
            }
        }

        if (CollectionUtils.isNotEmpty(activityTypes)) {
            pattern.append(" and");
            if (CollectionUtils.isNotEmpty(configIsEmptyTypeIds) && CollectionUtils.isNotEmpty(configIsNotEmptyTypes)) {
                pattern.append(" ( (").append(configIsEmptyPattern).append(" ) or (").append(configIsNotEmptyPattern).append(" ) )");
            } else if (CollectionUtils.isNotEmpty(configIsEmptyTypeIds)) {
                pattern.append(configIsEmptyPattern);
            } else if (CollectionUtils.isNotEmpty(configIsNotEmptyTypes)) {
                pattern.append(" (").append(configIsNotEmptyPattern).append(" )");
            }
        }
        log.info("agreement count : {} , pattern : {},configIsEmptyPattern :{},configIsNotEmptyPattern:{}", count, pattern, configIsEmptyPattern, configIsNotEmptyPattern);
        log.info("agreement newFilters : {}", JSON.toJSONString(newFilters));
        return count;
    }

    private String calculateFieldNameByTimeSetting(ActivityAgreementTimeSettingEntity setting, String defaultValue) {
        if (Objects.isNull(setting) || Strings.isNullOrEmpty(setting.getType())) {
            return defaultValue;
        }

        if (setting.getType().contains("begin")) {
            return TPMActivityFields.BEGIN_DATE;
        }

        if (setting.getType().contains("end")) {
            return TPMActivityFields.END_DATE;
        }

        return defaultValue;
    }

    private long calculateStartTimeByTimeSetting(ActivityAgreementTimeSettingEntity setting, long now) {
        if (Objects.isNull(setting) || Strings.isNullOrEmpty(setting.getType())) {
            return now;
        }

/*        if (setting.getType().contains("end")) {
            now = now + (24 * 60 * 60 * 1000);
        }*/

        return calculateTimeByTimeSetting(setting, now);
    }

    private long calculateEndTimeByTimeSetting(ActivityAgreementTimeSettingEntity setting, long now) {
        if (Objects.isNull(setting) || Strings.isNullOrEmpty(setting.getType())) {
            return now;
        }

        return calculateTimeByTimeSetting(setting, now);
    }

    private long calculateTimeByTimeSetting(ActivityAgreementTimeSettingEntity setting, long now) {
        if (setting.getType().contains("before")) {
            return now + setting.getValue();
        }

        if (setting.getType().contains("after")) {
            return now - setting.getValue();
        }

        return now;
    }

    private List<String> queryActivityIdsByAccountId(String accountId, List<String> activityTypeIds) {
        if (Strings.isNullOrEmpty(accountId)) {
            return Lists.newArrayList();
        }

        IObjectData storeData = serviceFacade.findObjectDataIgnoreAll(User.systemUser(controllerContext.getTenantId()), accountId, ApiNames.ACCOUNT_OBJ);
        if (storeData == null) {
            throw new ValidateException("account not found.");
        }

        String dealerId = storeBusiness.findDealerId(controllerContext.getTenantId(), storeData);
        if (dealerId == null && storeBusiness.findDealerRecordType(controllerContext.getTenantId()).contains(storeData.getRecordType())) {
            dealerId = storeData.getId();
        }
        List<Integer> departmentIds = new ArrayList<>();
        if (!controllerContext.getUser().isOutUser()) {
            departmentIds = organizationService.getDepartmentIds(controllerContext.getUser().getTenantIdInt(), Integer.parseInt(controllerContext.getUser().getUpstreamOwnerIdOrUserId()));
            if (CollectionUtils.isEmpty(departmentIds)) {
                return Lists.newArrayList();
            }
            log.info("departments : {}", departmentIds);
        }

        List<String> ids = Lists.newArrayList();
        List<IObjectData> activities = queryActivity(departmentIds, dealerId, activityTypeIds);
        if (!CollectionUtils.isEmpty(activities)) {
            log.info("related list activity size {}", activities.size());

            Map<String, Boolean> validateResult = rangeFieldBusiness.judgeStoreInActivitiesStoreRange(controllerContext.getTenantId(), storeData.getId(), dealerId, activities, false, true);

            // 只返回有为 true的数据
            return validateResult.entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList());
            //return new ArrayList<>(validateResult.keySet());

            //todo 需要sfa客开支持
            //ids = filterIdsBySalesOrderProduct(tenantId, salesOrderProducts, ids, activities);
        }
        return ids;
    }

    private List<String> filterIdsBySalesOrderProduct(String tenantId, List<ObjectDataDocument> salesOrderProducts, List<String> ids, List<IObjectData> activities) {
        if (CollectionUtils.isNotEmpty(salesOrderProducts)) {
            List<String> finalIds = ids;
            List<IObjectData> goodsCashingActivities = activities.stream().filter(activity -> {
                String dealerCashingType = activity.get(TPMActivityFields.DEALER_CASHING_TYPE, String.class);
                String storeCashingType = activity.get(TPMActivityFields.STORE_CASHING_TYPE, String.class);
                return (TPMActivityCashingProductFields.GOODS.equals(dealerCashingType) || TPMActivityCashingProductFields.GOODS.equals(storeCashingType)) && finalIds.contains(activity.getId());

            }).collect(Collectors.toList());

            List<String> salesProductIds = salesOrderProducts.stream().map(data -> data.toObjectData().get(SalesOrderProductFields.PRODUCT_ID, String.class)).collect(Collectors.toList());
            List<String> activityIds = goodsCashingActivities.stream().map(IObjectData::getId).collect(Collectors.toList());
            ids = queryHasActivityCashingProductActivityIds(tenantId, activityIds, salesProductIds);
        }
        return ids;
    }

    private List<String> queryHasActivityCashingProductActivityIds(String tenantId, List<String> activityIds, List<String> salesProductIds) {
        List<String> ids = Lists.newArrayList();
        if (CollectionUtils.isEmpty(activityIds) || CollectionUtils.isEmpty(salesProductIds)) {
            return ids;
        }
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityCashingProductFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.IN);
        activityFilter.setFieldValues(Lists.newArrayList(activityIds));

        Filter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(TPMActivityCashingProductFields.PRODUCT_ID);
        storeIdFilter.setOperator(Operator.IN);
        storeIdFilter.setFieldValues(Lists.newArrayList(salesProductIds));

        query.setFilters(Lists.newArrayList(activityFilter, storeIdFilter));

        List<IObjectData> dataList = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_CASHING_PRODUCT_OBJ, query);
        if (CollectionUtils.isEmpty(dataList)) {
            return ids;
        }
        return dataList.stream().map(data -> data.get(TPMActivityCashingProductFields.ACTIVITY_ID, String.class)).distinct().collect(Collectors.toList());
    }

    private List<IObjectData> queryActivity(List<Integer> departmentIds, String dealerId, List<String> activityTypeList) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setFilters(Lists.newArrayList());
        query.setSearchSource("db");

        int number = 2;
        StringBuilder pattern = new StringBuilder(" 1 and 2 ");

        Filter activityStatusFilter = new Filter();
        activityStatusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        activityStatusFilter.setOperator(Operator.IN);
        activityStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS, TPMActivityFields.ACTIVITY_STATUS__END));
        query.getFilters().add(activityStatusFilter);

        Filter closeStatusFilter = new Filter();
        closeStatusFilter.setFieldName(TPMActivityFields.CLOSE_STATUS);
        closeStatusFilter.setOperator(Operator.NEQ);
        closeStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.CLOSE_STATUS__CLOSED));
        query.getFilters().add(closeStatusFilter);


        if (!CollectionUtils.isEmpty(activityTypeList)) {
            if (activityTypeList.size() == 1) {
                Filter activityType = new Filter();
                activityType.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
                activityType.setOperator(Operator.EQ);
                activityType.setFieldValues(activityTypeList);
                query.getFilters().add(activityType);
            } else {
                Filter activityType = new Filter();
                activityType.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
                activityType.setOperator(Operator.IN);
                activityType.setFieldValues(activityTypeList);
                query.getFilters().add(activityType);
            }
            pattern.append(" and ").append(++number).append(" ");
        }

        if (!Strings.isNullOrEmpty(dealerId)) {

            Filter dealerIdEqualFilter = new Filter();
            dealerIdEqualFilter.setFieldName(TPMActivityFields.DEALER_ID);
            dealerIdEqualFilter.setOperator(Operator.EQ);
            dealerIdEqualFilter.setFieldValues(Lists.newArrayList(dealerId));

            Filter dealerIdEmptyFilter = new Filter();
            dealerIdEmptyFilter.setFieldName(TPMActivityFields.DEALER_ID);
            dealerIdEmptyFilter.setOperator(Operator.IS);
            dealerIdEmptyFilter.setFieldValues(Lists.newArrayList());

            query.getFilters().addAll(Lists.newArrayList(dealerIdEmptyFilter, dealerIdEqualFilter));
            pattern.append(" and (").append(++number).append(" or ").append(++number).append(" ) ");
        } else {

            Filter dealerIdEmptyFilter = new Filter();
            dealerIdEmptyFilter.setFieldName(TPMActivityFields.DEALER_ID);
            dealerIdEmptyFilter.setOperator(Operator.IS);
            dealerIdEmptyFilter.setFieldValues(Lists.newArrayList());

            query.getFilters().add(dealerIdEmptyFilter);

            pattern.append(" and ").append(++number).append(" ");
        }

        if (!controllerContext.getUser().isOutUser() && !TPMGrayUtils.denyDepartmentFilterOnActivity(controllerContext.getTenantId())) {

            Filter departmentRangeFilter = new Filter();
            departmentRangeFilter.setFieldName(TPMActivityFields.DEPARTMENT_RANGE);
            departmentRangeFilter.setOperator(Operator.IN);
            departmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));

            Filter multiDepartmentRangeFilter = new Filter();
            multiDepartmentRangeFilter.setFieldName(TPMActivityFields.MULTI_DEPARTMENT_RANGE);
            multiDepartmentRangeFilter.setOperator(Operator.HASANYOF);
            multiDepartmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));

            if (I_DESCRIBE_CACHE_SERVICE.isExistField(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, TPMActivityFields.DEPARTMENT_RANGE)) {
                pattern.append(" and (").append(++number).append(" or ").append(++number).append(" ) ");
                query.getFilters().addAll(Lists.newArrayList(departmentRangeFilter, multiDepartmentRangeFilter));
            } else {
                pattern.append(" and ").append(++number).append(" ");
                query.getFilters().add(multiDepartmentRangeFilter);
            }
        }
        query.setPattern(pattern.toString());

        List<String> fields = Lists.newArrayList(CommonFields.ID, TPMActivityFields.STORE_RANGE, TPMActivityFields.DEALER_ID, TPMActivityFields.CUSTOMER_TYPE);
        return CommonUtils.queryData(serviceFacade, User.systemUser(controllerContext.getTenantId()), controllerContext.getRequestContext(), ApiNames.TPM_ACTIVITY_OBJ, query, fields);
    }

    private List<IFilter> toFilters(JSONArray arr) {
        List<IFilter> filters = Lists.newArrayList();
        for (int i = 0; i < arr.size(); i++) {
            JSONObject jsonFilter = arr.getJSONObject(i);
            filters.add(toFilter(jsonFilter));
        }
        return filters;
    }

    private IFilter toFilter(JSONObject jsonFilter) {
        IFilter filter = new Filter();
        filter.setValueType(jsonFilter.getInteger("value_type"));
        filter.setOperator(Operator.valueOf(jsonFilter.getString("operator")));
        filter.setFieldName(jsonFilter.getString("field_name"));
        filter.setFieldValues(jsonFilter.getJSONArray("field_values").toJavaList(String.class));
        return filter;
    }

    private String findRecordType(ActivityTypePO activityType, String objectApiName) {
        ActivityTypeExt ext = ActivityTypeExt.of(activityType);
        switch (objectApiName) {
            case ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ:
                return ext.agreementNode().getObjectRecordType();
            case ApiNames.TPM_ACTIVITY_PROOF_OBJ:
                return ext.proofNode().getObjectRecordType();
            case ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ:
                return ext.auditNode().getObjectRecordType();
            case ApiNames.TPM_DEALER_ACTIVITY_COST:
                return ext.writeOffNode().getObjectRecordType();
            case ApiNames.SALES_ORDER_OBJ:
                return ext.costAssignNode().getObjectRecordType();
            default:
                return "default__c";
        }
    }
}
