package com.facishare.crm.fmcg.tpm.button.provider;


import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.button.abs.AbstractTPMSpecialButtonProvider;
import com.facishare.crm.fmcg.tpm.utils.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/8 下午3:04
 */
@Component
public class TPMBudgetAccountObjObjSpecialButtonProvider extends AbstractTPMSpecialButtonProvider {

    private static final Logger LOG = LoggerFactory.getLogger(TPMBudgetAccountObjObjSpecialButtonProvider.class);

    @Override
    public String getApiName() {
        return ApiNames.TPM_BUDGET_ACCOUNT;
    }

    @Override
    public List<IButton> getSpecialButtons() {

        List<IButton> buttons = super.getSpecialButtons();
        buttons.add(ButtonUtils.buildButton(ObjectAction.ENABLE_BUDGET));
        return buttons;
    }
}
