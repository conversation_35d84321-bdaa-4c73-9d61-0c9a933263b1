package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.ActivityCustomerTypeEnum;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.api.proof.UnappliedCustomerFilter;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDescribeCacheService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRangeFieldBusiness;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/2/20 4:12 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityProofObjUnappliedCustomerFilterController extends PreDefineController<UnappliedCustomerFilter.Arg, UnappliedCustomerFilter.Result> {

    private static final OrganizationService organizationService = SpringUtil.getContext().getBean(OrganizationService.class);
    private static final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);
    private static final IDescribeCacheService I_DESCRIBE_CACHE_SERVICE = SpringUtil.getContext().getBean(IDescribeCacheService.class);

    private static final IRangeFieldBusiness rangeFieldBusiness = SpringUtil.getContext().getBean(IRangeFieldBusiness.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }

    @Override
    protected UnappliedCustomerFilter.Result doService(UnappliedCustomerFilter.Arg arg) {
        if (CollectionUtils.isEmpty(arg.getCustomerIdList())) {
            throw new ValidateException("Stop this madness remote call. HaiZi!");
        }

        List<Integer> departmentIds = organizationService.getDepartmentIds(controllerContext.getUser().getTenantIdInt(), Integer.parseInt(controllerContext.getUser().getUpstreamOwnerIdOrUserId()));
        log.info("department id list : {}", departmentIds);

        List<IObjectData> activityList = queryActivityByDepartment(departmentIds, arg.getActivityId(), arg.getDealerId());
        log.info("activity size : {}", activityList.size());
        UnappliedCustomerFilter.Result data = new UnappliedCustomerFilter.Result();
        List<IObjectData> stores = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), arg.getCustomerIdList(), ApiNames.ACCOUNT_OBJ);

        Set<String> errors = new CopyOnWriteArraySet<>();
        try {
            ParallelUtils.ParallelTask checkTask = ParallelUtils.createParallelTask();
            Lists.partition(stores, 15).forEach(parts -> checkTask.submit(() -> {
                for (IObjectData store : parts) {
                    String dealerId = storeBusiness.findDealerId(controllerContext.getTenantId(), store);
                    Map<String, Boolean> res = rangeFieldBusiness.judgeStoreInActivitiesStoreRange(controllerContext.getTenantId(), store.getId(), dealerId, activityList, true, true);
                    if (res.containsValue(true)) {
                        data.getData().put(store.getId(), UnappliedCustomerFilter.UnappliedCustomerVO.builder().customerData(ObjectDataDocument.of(store)).build());
                    }
                }
            }));
            checkTask.await(8000, TimeUnit.MILLISECONDS);
        } catch (ValidateException e) {
            errors.add(e.getMessage());
        } catch (Exception e) {
            log.error("enable check timeout", e);
        }

        if (CollectionUtils.isNotEmpty(errors)) {
            throw new ValidateException(String.join(",", errors));
        }


        log.info("data size : {}", data.getData().size());

        return data;
    }

    private List<IObjectData> queryActivityByDepartment(List<Integer> departmentIds, String activityId, String dealerId) {
        long now = System.currentTimeMillis();

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        int countNumber = 7;

        StringBuilder pattern = new StringBuilder("1 and 2 and 3 and 4 and (5 or 6) ");

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LT);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityFields.END_DATE);
        endDateFilter.setOperator(Operator.GT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("normal"));

        Filter activityStatusFilter = new Filter();
        activityStatusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        activityStatusFilter.setOperator(Operator.EQ);
        activityStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS));

        Filter customerTypeFilter = new Filter();
        customerTypeFilter.setFieldName(TPMActivityFields.CUSTOMER_TYPE);
        customerTypeFilter.setOperator(Operator.NEQ);
        customerTypeFilter.setFieldValues(Lists.newArrayList(ActivityCustomerTypeEnum.BRAND.value()));

        Filter customerTypeNullFilter = new Filter();
        customerTypeNullFilter.setFieldName(TPMActivityFields.CUSTOMER_TYPE);
        customerTypeNullFilter.setOperator(Operator.IS);
        customerTypeNullFilter.setFieldValues(Lists.newArrayList());


        query.setFilters(Lists.newArrayList(beginDateFilter, endDateFilter, lifeStatusFilter, activityStatusFilter, customerTypeFilter, customerTypeNullFilter));

        if (!Strings.isNullOrEmpty(activityId)) {
            Filter activityIdFilter = new Filter();
            activityIdFilter.setFieldName("_id");
            activityIdFilter.setOperator(Operator.EQ);
            activityIdFilter.setFieldValues(Lists.newArrayList(activityId));
            query.getFilters().add(activityIdFilter);
            pattern.append(" and ").append(countNumber++).append(" ");
        }

        if (!Strings.isNullOrEmpty(dealerId)) {
            Filter dealerIdFilter = new Filter();
            dealerIdFilter.setFieldName(TPMActivityFields.DEALER_ID);
            dealerIdFilter.setOperator(Operator.EQ);
            dealerIdFilter.setFieldValues(Lists.newArrayList(dealerId));
            query.getFilters().add(dealerIdFilter);
            pattern.append(" and (").append(countNumber++).append(" ");

            Filter noDealerIdFilter = new Filter();
            noDealerIdFilter.setFieldName(TPMActivityFields.DEALER_ID);
            noDealerIdFilter.setOperator(Operator.IS);
            noDealerIdFilter.setFieldValues(Lists.newArrayList());
            query.getFilters().add(noDealerIdFilter);
            pattern.append(" or ").append(countNumber++).append(") ");
        }

        if (!controllerContext.getUser().isOutUser() && !TPMGrayUtils.denyDepartmentFilterOnActivity(controllerContext.getTenantId())) {

            Filter departmentRangeFilter = new Filter();
            departmentRangeFilter.setFieldName(TPMActivityFields.DEPARTMENT_RANGE);
            departmentRangeFilter.setOperator(Operator.IN);
            departmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));

            Filter multiDepartmentRangeFilter = new Filter();
            multiDepartmentRangeFilter.setFieldName(TPMActivityFields.MULTI_DEPARTMENT_RANGE);
            multiDepartmentRangeFilter.setOperator(Operator.HASANYOF);
            multiDepartmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));

            if (I_DESCRIBE_CACHE_SERVICE.isExistField(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, TPMActivityFields.DEPARTMENT_RANGE)) {
                query.getFilters().add(departmentRangeFilter);
                query.getFilters().add(multiDepartmentRangeFilter);
                pattern.append("and (").append(countNumber++).append(" or ").append(countNumber++).append(" ) ");
            } else {
                query.getFilters().add(multiDepartmentRangeFilter);
                pattern.append("and ").append(countNumber++).append(" ");
            }
        }
        query.setPattern(pattern.toString());
        return CommonUtils.queryData(serviceFacade, User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query);
    }
}