package com.facishare.crm.fmcg.tpm.controller;

import com.google.common.collect.Lists;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * author: wuyx
 * description:
 * createTime: 2022/8/24 11:30
 */
public class TPMBudgetStatisticTableObjWebDetailController extends StandardWebDetailController {

    @Override
    protected Result after(Arg arg, Result result) {
        buttonFilter(arg, result);
        return super.after(arg, result);
    }

    private void buttonFilter(Arg arg, Result result) {

        List<String> removeList = Lists.newArrayList("Abolish", "Edit");
        if (!"mobile".equals(arg.getLayoutAgentType())) {
            List components = (ArrayList) (result.getLayout().get("components"));
            removeButton(components, removeList);
        } else {
            ArrayList buttons = (ArrayList) result.getLayout().get("buttons");
            buttons.removeIf(button -> {
                Map btn = (Map) (button);
                return removeList.contains(btn.get("action"));
            });
        }

    }

    private void removeButton(List components, List<String> removeList) {
        for (Object component : components) {
            Map com = (Map) component;
            if ("head_info".equals(com.get("api_name"))) {
                ArrayList buttons = (ArrayList) com.get("buttons");
                buttons.removeIf(button -> {
                    Map btn = (Map) (button);
                    return removeList.contains(btn.get("action"));
                });
            }
        }
    }
}
