package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

/**
 * author: wuyx
 * description:
 * createTime: 2022/8/10 18:26
 */
public class TPMActivityProofAuditObjListController extends StandardListController {

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        if (TPMGrayUtils.TPMUseEs(controllerContext.getTenantId())) {
            query.setSearchSource("es");
        }
        super.beforeQueryData(query);
    }
}
