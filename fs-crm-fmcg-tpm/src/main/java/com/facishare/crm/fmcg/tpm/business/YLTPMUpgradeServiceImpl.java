package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.business.abstraction.YLTPMUpgradeService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
//IgnoreI18nFile
@Service
@Slf4j
public class YLTPMUpgradeServiceImpl implements YLTPMUpgradeService {

    @Resource
    private ServiceFacade serviceFacade;
    //todo 部门修改,1316 驻马店处  1560驻马店市区组
    //1381南宁处 南宁青秀组1937 南宁崇左组1942 南宁宾武组1938 南宁江南组 1936 南宁西超组1940

    private static final List<String> departmentIds = Lists.newArrayList("1316", "1560", "1381", "1937", "1942", "1938", "1936", "1940");

    private static final Map<String, String> departmentManager = Maps.newHashMap();
    private static final Map<String, String> departmentUpdateManager = Maps.newHashMap();

    static {
        departmentManager.put("1316", "14939");
        departmentManager.put("1560", "6721");
        departmentManager.put("1381", "19185");
        departmentManager.put("1937", "2224");
        departmentManager.put("1942", "6661");
        departmentManager.put("1938", "11358");
        departmentManager.put("1936", "6659");
        departmentManager.put("1940", "30001");

        departmentUpdateManager.put("2352", "2196");
        departmentUpdateManager.put("2835", "12185");
        departmentUpdateManager.put("2326", "2182");
        departmentUpdateManager.put("3590", "2181");
        departmentUpdateManager.put("3593", "2824");
        departmentUpdateManager.put("3591", "2847");
        departmentUpdateManager.put("3589", "2856");
        departmentUpdateManager.put("3592", "2834");
    }

    private void handlerDataAndDepartment(IObjectData objectData, String targetTenantId) {

        String department = objectData.getDataOwnDepartment().get(0);
        String departmentUpdate = getDepartmentUpdate(department);
        String owner = departmentUpdateManager.get(departmentUpdate);
        if (StringUtils.isEmpty(owner)) {
            owner = "1000";
        }

        objectData.set("tenant_id", targetTenantId);
        if ("TPMActivityAgreementObj".equals(objectData.getDescribeApiName())) {
            String nameUpdate = objectData.getName() + "--" + UUID.randomUUID().toString().substring(0, 4);
            if (nameUpdate.length() > 100) {
                objectData.set("name", objectData.getName() + "-co");
            } else {
                objectData.set("name", nameUpdate);
            }

        } else {
            objectData.set("name", objectData.getName() + "--" + UUID.randomUUID());
        }

        objectData.set(CommonFields.DATA_OWN_DEPARTMENT, Lists.newArrayList(departmentUpdate));
        objectData.set(CommonFields.OWNER, Lists.newArrayList(owner));
        objectData.set("enterpriserelation_id", null);
        objectData.set("field_yzdZf__c", null);
        objectData.set("dealer_activity", null);
        objectData.set("dealer_activity_cost_id", null);
        String replaceValueNullFieldNamesForYlTpmUpgrade = ConfigFactory.getConfig("fs-fmcg-framework-config").get("replace_value_null_field_names_for_yl_tpm_upgrade");
        if (!StringUtils.isEmpty(replaceValueNullFieldNamesForYlTpmUpgrade)) {
            List<String> fieldNames = JSON.parseArray(replaceValueNullFieldNamesForYlTpmUpgrade, String.class);
            if (CollectionUtils.isNotEmpty(fieldNames)) {
                for (String fieldName : fieldNames) {
                    objectData.set(fieldName, null);
                }
            }
        }

    }


    private String getDepartmentUpdate(String department) {


        String departmentUpdate = "999999";
        if (StringUtils.isEmpty(department)) {
            return departmentUpdate;
        }
        switch (department) {
            case "1316": {
                departmentUpdate = "2352";
                break;
            }
            case "1560": {
                departmentUpdate = "2835";
                break;
            }
            case "1381": {
                departmentUpdate = "2326";
                break;
            }
            case "1937": {
                departmentUpdate = "3590";
                break;
            }
            case "1942": {
                departmentUpdate = "3593";
                break;
            }
            case "1938": {
                departmentUpdate = "3591";
                break;
            }
            case "1936": {
                departmentUpdate = "3589";
                break;
            }
            case "1940": {
                departmentUpdate = "3592";
                break;
            }
            default: {
                departmentUpdate = "999999";
                break;
            }
        }
        return departmentUpdate;
    }

    @Override
    public void copy(String sourceTenantId, String targetTenantId, String flag) {
        switch (flag) {
            case "1": {
                copyDepartment(sourceTenantId, targetTenantId);
                copyPerson(sourceTenantId, targetTenantId);

                break;
            }
            case "2": {
                copyChannelType(sourceTenantId, targetTenantId);

                copyChannel(sourceTenantId, targetTenantId);

                copyKASystemType(sourceTenantId, targetTenantId);

                copyKASystem(sourceTenantId, targetTenantId);

                break;
            }
            case "3": {
                copyProductCategory(sourceTenantId, targetTenantId);
                copyProduct(sourceTenantId, targetTenantId);
                copyTPMProductCategory(sourceTenantId, targetTenantId);

                copyActivityItem(sourceTenantId, targetTenantId);


                break;
            }
            case "4": {
                copyDealer(sourceTenantId, targetTenantId);
                copyDistributor(sourceTenantId, targetTenantId);
                copyAccount(sourceTenantId, targetTenantId);


                break;
            }

            case "5": {
                List<String> ids = copyAreaManagement(sourceTenantId, targetTenantId);
                copyCoveredStores(sourceTenantId, targetTenantId, ids);
                break;
            }
            case "6": {

                copyObjDataWithDepartment(sourceTenantId, targetTenantId, "data_own_department", "DealerSupplyObj");
                copyObjDataWithDepartment(sourceTenantId, targetTenantId, "data_own_department", "DistributorSupplyObj");
                copySupplyStoreObj(sourceTenantId, targetTenantId);
                copyObjDataWithDepartment(sourceTenantId, targetTenantId, "data_own_department", "SpecialSupplyObj");
                break;
            }
            case "7": {
                List<String> activityPlanIds = copyObjDataWithDepartment(sourceTenantId, targetTenantId, "data_own_department", "tpm_activity_plan__c");
                copySubObj(sourceTenantId, targetTenantId, "tpm_activity_plan_id__c", "tpm_activity_plan_channel__c", activityPlanIds);
                copySubObj(sourceTenantId, targetTenantId, "tpm_activity_plan_id__c", "tpm_activity_plan_store__c", activityPlanIds);
                copySubObj(sourceTenantId, targetTenantId, "tpm_activity_plan_id__c", "tpm_activity_plan_product__c", activityPlanIds);
                copySubObj(sourceTenantId, targetTenantId, "tpm_activity_plan_id__c", "tpm_activity_plan_item__c", activityPlanIds);
                copySubObj(sourceTenantId, targetTenantId, "tpm_activity_plan_id__c", "tpm_activity_plan_time_span__c", activityPlanIds);
                copySubObj(sourceTenantId, targetTenantId, "tpm_activity_plan_id__c", "tpm_activity_plan_business_group__c", activityPlanIds);
                copySubObj(sourceTenantId, targetTenantId, "tpm_activity_plan_id__c", "tpm_activity_plan_dealer__c", activityPlanIds);
                copySubObj(sourceTenantId, targetTenantId, "field_A232R__c", "Display_scheme__c", activityPlanIds);
                copySubObj(sourceTenantId, targetTenantId, "tpm_activity_plan_id__c", "tpm_activity_plan_gift__c", activityPlanIds);
                copySubObj(sourceTenantId, targetTenantId, "tpm_activity_plan_id__c", "tpm_activity_plan_kasystem__c", activityPlanIds);
                break;
            }
            case "8": {
                List<String> activityTimSpanPlanIds = copyObjDataWithDepartment(sourceTenantId, targetTenantId, "data_own_department", "tpm_activity_time_span_plan__c");
                copySubObj(sourceTenantId, targetTenantId, "tpm_activity_time_span_plan_id__c", "tpm_activity_time_span_plan_channel__c", activityTimSpanPlanIds);
                copySubObj(sourceTenantId, targetTenantId, "tpm_activity_time_span_plan_id__c", "tpm_activity_time_span_plan_store__c", activityTimSpanPlanIds);
                copySubObj(sourceTenantId, targetTenantId, "tpm_activity_time_span_plan_id__c", "tpm_activity_time_span_plan_product__c", activityTimSpanPlanIds);
                copySubObj(sourceTenantId, targetTenantId, "tpm_activity_time_span_plan_id__c", "tpm_activity_time_span_plan_item__c", activityTimSpanPlanIds);
                copySubObj(sourceTenantId, targetTenantId, "tpm_activity_time_span_plan_id__c", "tpm_activity_span_plan_kasystem__c", activityTimSpanPlanIds);
                copySubObj(sourceTenantId, targetTenantId, "field_dnn2v__c", "Keyword_range__c", activityTimSpanPlanIds);
                break;
            }
            case "9": {

                List<String> businessBudget = copyObjDataWithDepartment(sourceTenantId, targetTenantId, "data_own_department", "tpm_business_center_budget__c");
                copySubObj(sourceTenantId, targetTenantId, "tpm_business_center_budget_id__c", "tpm_area_budget__c", businessBudget);
                //chongpao
                break;
            }
            case "10": {
                List<String> activity = copyObjDataWithDepartment(sourceTenantId, targetTenantId, "data_own_department", "TPMActivityObj");
                copySubObj(sourceTenantId, targetTenantId, "activity_id", "TPMActivityStoreObj", activity);
                copySubObj(sourceTenantId, targetTenantId, "activity_id", "TPMActivityDetailObj", activity);
                break;
            }
            case "11": {
                //chongpao
                List<String> agreement = copyObjDataWithDepartment(sourceTenantId, targetTenantId, "data_own_department", "TPMActivityAgreementObj");
                copySubObj(sourceTenantId, targetTenantId, "activity_agreement_id", "TPMActivityAgreementDetailObj", agreement);
                copySubObj(sourceTenantId, targetTenantId, "activity_agreement_id__c", "tpm_activity_agreement_product__c", agreement);

                break;
            }

            case "12": {

                List<String> proof = copyObjDataWithDepartment(sourceTenantId, targetTenantId, "data_own_department", "TPMActivityProofObj");
                copySubObj(sourceTenantId, targetTenantId, "activity_proof_id", "TPMActivityProofDetailObj", proof);
                break;
            }
        }


    }

    private void copySubObj(String sourceTenantId, String targetTenantId, String fieldName, String apiName, List<String> ids) {
        List<List<String>> partition = Lists.partition(ids, 50);
        for (List<String> strings : partition) {
            SearchTemplateQuery query2 = new SearchTemplateQuery();
            query2.setLimit(-1);
            query2.setOffset(0);
            query2.setSearchSource("es");

            Filter filter = new Filter();
            filter.setFieldName(CommonFields.CREATE_TIME);
            filter.setOperator(Operator.GTE);
            filter.setFieldValues(Lists.newArrayList("1696089600000"));

            Filter filter2 = new Filter();
            filter2.setFieldName(fieldName);
            filter2.setOperator(Operator.IN);
            filter2.setFieldValues(strings);

            query2.setFilters(Lists.newArrayList(filter, filter2));
            List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), apiName, query2);
            for (IObjectData datum : data) {
                handlerDataAndDepartment(datum, targetTenantId);
            }
            if ("tpm_activity_agreement_product__c".equals(apiName)) {
                List<String> timeSpanIds = data.stream().filter(v -> !StringUtils.isEmpty(v.get("product_id__c", String.class)))
                        .map(v -> v.get("product_id__c", String.class)).collect(Collectors.toList());
                List<String> existIds = queryIdsByIds(targetTenantId, timeSpanIds, "ProductObj");
                data.removeIf(v -> !existIds.contains(v.get("product_id__c", String.class)));
            }
            filterData(targetTenantId, data, apiName);
            serviceFacade.bulkSaveObjectData(data, User.systemUser(targetTenantId), false);
        }


    }

    private void copySupplyStoreObj(String sourceTenantId, String targetTenantId) {
        for (int i = 0; i < 8000; i += 1000) {
            SearchTemplateQuery query2 = new SearchTemplateQuery();
            query2.setLimit(1000);
            query2.setOffset(i);
            query2.setSearchSource("es");

            Filter filter2 = new Filter();
            filter2.setFieldName("data_own_department");
            filter2.setOperator(Operator.IN);
            filter2.setFieldValues(departmentIds);

            query2.setFilters(Lists.newArrayList(filter2));
            List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "SupplyStoreObj", query2);
            if (CollectionUtils.isEmpty(data)) {
                break;
            }
            for (IObjectData datum : data) {
                handlerDataAndDepartment(datum, targetTenantId);
            }
            List<String> timeSpanIds = data.stream().filter(v -> !StringUtils.isEmpty(v.get("supply_id", String.class)))
                    .map(v -> v.get("supply_id", String.class)).collect(Collectors.toList());
            List<String> existIds = queryIdsByIds(targetTenantId, timeSpanIds, "AccountObj");
            data.removeIf(v -> !existIds.contains(v.get("supply_id", String.class)));

            List<String> timeSpanIds2 = data.stream().filter(v -> !StringUtils.isEmpty(v.get("store", String.class)))
                    .map(v -> v.get("store", String.class)).collect(Collectors.toList());
            List<String> existIds2 = queryIdsByIds(targetTenantId, timeSpanIds2, "AccountObj");
            data.removeIf(v -> !existIds2.contains(v.get("store", String.class)));

            List<String> timeSpanIds3 = data.stream().filter(v -> !StringUtils.isEmpty(v.get("supply_dealer", String.class)))
                    .map(v -> v.get("supply_dealer", String.class)).collect(Collectors.toList());
            List<String> existIds3 = queryIdsByIds(targetTenantId, timeSpanIds3, "DealerSupplyObj");
            data.removeIf(v -> !existIds3.contains(v.get("supply_dealer", String.class)));

            List<String> timeSpanIds4 = data.stream().filter(v -> !StringUtils.isEmpty(v.get("field_2UOLp__c", String.class)))
                    .map(v -> v.get("field_2UOLp__c", String.class)).collect(Collectors.toList());
            List<String> existIds4 = queryIdsByIds(targetTenantId, timeSpanIds4, "AreaManageObj");
            data.removeIf(v -> !existIds4.contains(v.get("field_2UOLp__c", String.class)));

            filterData(targetTenantId, data, "SupplyStoreObj");
            serviceFacade.bulkSaveObjectData(data, User.systemUser(targetTenantId), false);
        }


    }

    private List<String> copyObjDataWithDepartment(String sourceTenantId, String targetTenantId, String fieldName, String apiName) {
        //不需要时间的对象
        List<String> noNeedTimeFilter = Lists.newArrayList("DealerSupplyObj", "DistributorSupplyObj", "SupplyStoreObj", "SpecialSupplyObj");
        SearchTemplateQuery query2 = new SearchTemplateQuery();
        query2.setLimit(-1);
        query2.setOffset(0);
        query2.setSearchSource("es");


        Filter filter = new Filter();
        filter.setFieldName(CommonFields.CREATE_TIME);
        filter.setOperator(Operator.GTE);
        filter.setFieldValues(Lists.newArrayList("1696089600000"));

        Filter filter2 = new Filter();
        filter2.setFieldName("data_own_department");
        filter2.setOperator(Operator.IN);
        filter2.setFieldValues(departmentIds);


        if (noNeedTimeFilter.contains(apiName)) {
            query2.setFilters(Lists.newArrayList(filter2));
        } else {
            query2.setFilters(Lists.newArrayList(filter, filter2));
        }

        List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), apiName, query2);
        for (IObjectData datum : data) {
            handlerDataAndDepartment(datum, targetTenantId);
        }
        if ("TPMActivityAgreementObj".equals(apiName)) {
            List<String> timeSpanIds = data.stream().filter(v -> !StringUtils.isEmpty(v.get("activity_plan_time_span_id__c", String.class)))
                    .map(v -> v.get("activity_plan_time_span_id__c", String.class)).collect(Collectors.toList());
            List<String> existIds = queryIdsByIds(targetTenantId, timeSpanIds, "tpm_activity_plan_time_span__c");
            data.removeIf(v -> !existIds.contains(v.get("activity_plan_time_span_id__c", String.class)));
        }
        if ("DealerSupplyObj".equals(apiName)) {
            List<String> timeSpanIds = data.stream().filter(v -> !StringUtils.isEmpty(v.get("delear", String.class)))
                    .map(v -> v.get("delear", String.class)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(timeSpanIds)) {
                List<String> existIds = queryIdsByIds(targetTenantId, timeSpanIds, "AccountObj");
                data.removeIf(v -> !existIds.contains(v.get("delear", String.class)));
            }
        }
        if ("TPMActivityProofObj".equals(apiName)) {
            List<String> timeSpanIds = data.stream().filter(v -> !StringUtils.isEmpty(v.get("activity_id", String.class)))
                    .map(v -> v.get("activity_id", String.class)).collect(Collectors.toList());
            List<String> existIds = queryIdsByIds(targetTenantId, timeSpanIds, "TPMActivityObj");
            data.removeIf(v -> !existIds.contains(v.get("activity_id", String.class)));

            List<String> timeSpanId2 = data.stream().filter(v -> !StringUtils.isEmpty(v.get("dealer_id", String.class)))
                    .map(v -> v.get("dealer_id", String.class)).collect(Collectors.toList());
            List<String> existIds2 = queryIdsByIds(targetTenantId, timeSpanId2, "AccountObj");
            data.removeIf(v -> !existIds2.contains(v.get("dealer_id", String.class)));

            List<String> timeSpanId3 = data.stream().filter(v -> !StringUtils.isEmpty(v.get("activity_agreement_id", String.class)))
                    .map(v -> v.get("activity_agreement_id", String.class)).collect(Collectors.toList());
            List<String> existIds3 = queryIdsByIds(targetTenantId, timeSpanId3, "TPMActivityAgreementObj");
            data.removeIf(v -> !existIds3.contains(v.get("activity_agreement_id", String.class)));

            List<String> timeSpanId4 = data.stream().filter(v -> !StringUtils.isEmpty(v.get("store_id", String.class)))
                    .map(v -> v.get("store_id", String.class)).collect(Collectors.toList());
            List<String> existIds4 = queryIdsByIds(targetTenantId, timeSpanId4, "AccountObj");
            data.removeIf(v -> !existIds4.contains(v.get("store_id", String.class)));

        }
        if ("DistributorSupplyObj".equals(apiName)) {
            List<String> timeSpanIds = data.stream().filter(v -> !StringUtils.isEmpty(v.get("up_level_dealer", String.class)))
                    .map(v -> v.get("up_level_dealer", String.class)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(timeSpanIds)) {
                List<String> existIds = queryIdsByIds(targetTenantId, timeSpanIds, "DealerSupplyObj");
                data.removeIf(v -> !existIds.contains(v.get("up_level_dealer", String.class)));
            }

            List<String> timeSpanIds2 = data.stream().filter(v -> !StringUtils.isEmpty(v.get("up_dealer", String.class)))
                    .map(v -> v.get("up_dealer", String.class)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(timeSpanIds2)) {
                List<String> existIds2 = queryIdsByIds(targetTenantId, timeSpanIds2, "AccountObj");
                data.removeIf(v -> !existIds2.contains(v.get("up_dealer", String.class)));
            }


            List<String> timeSpanIds3 = data.stream().filter(v -> !StringUtils.isEmpty(v.get("store", String.class)))
                    .map(v -> v.get("store", String.class)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(timeSpanIds3)) {
                List<String> existIds3 = queryIdsByIds(targetTenantId, timeSpanIds3, "AccountObj");
                data.removeIf(v -> !existIds3.contains(v.get("store", String.class)));
            }


            List<String> timeSpanIds4 = data.stream().filter(v -> !StringUtils.isEmpty(v.get("field_2UOLp__c", String.class)))
                    .map(v -> v.get("field_2UOLp__c", String.class)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(timeSpanIds4)) {
                List<String> existIds4 = queryIdsByIds(targetTenantId, timeSpanIds4, "AreaManageObj");
                data.removeIf(v -> !existIds4.contains(v.get("field_2UOLp__c", String.class)));
            }

        }

        if ("SpecialSupplyObj".equals(apiName)) {
            List<String> timeSpanIds = data.stream().filter(v -> !StringUtils.isEmpty(v.get("dealer_supply", String.class)))
                    .map(v -> v.get("dealer_supply", String.class)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(timeSpanIds)) {
                List<String> existIds = queryIdsByIds(targetTenantId, timeSpanIds, "DealerSupplyObj");
                data.removeIf(v -> !existIds.contains(v.get("dealer_supply", String.class)));
            }


            List<String> timeSpanIds2 = data.stream().filter(v -> !StringUtils.isEmpty(v.get("supplier", String.class)))
                    .map(v -> v.get("supplier", String.class)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(timeSpanIds2)) {
                List<String> existIds2 = queryIdsByIds(targetTenantId, timeSpanIds2, "AccountObj");
                data.removeIf(v -> !existIds2.contains(v.get("supplier", String.class)));

            }

            List<String> timeSpanIds3 = data.stream().filter(v -> !StringUtils.isEmpty(v.get("product", String.class)))
                    .map(v -> v.get("product", String.class)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(timeSpanIds3)) {
                List<String> existIds3 = queryIdsByIds(targetTenantId, timeSpanIds3, "ProductObj");
                data.removeIf(v -> !existIds3.contains(v.get("product", String.class)));

            }

            List<String> timeSpanIds4 = data.stream().filter(v -> !StringUtils.isEmpty(v.get("supply_object_name", String.class)))
                    .map(v -> v.get("supply_object_name", String.class)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(timeSpanIds4)) {
                List<String> existIds4 = queryIdsByIds(targetTenantId, timeSpanIds4, "AccountObj");
                data.removeIf(v -> !existIds4.contains(v.get("supply_object_name", String.class)));
            }


            List<String> timeSpanIds5 = data.stream().filter(v -> !StringUtils.isEmpty(v.get("field_2UOLp__c", String.class)))
                    .map(v -> v.get("field_2UOLp__c", String.class)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(timeSpanIds5)) {
                List<String> existIds5 = queryIdsByIds(targetTenantId, timeSpanIds5, "AreaManageObj");
                data.removeIf(v -> !existIds5.contains(v.get("field_2UOLp__c", String.class)));
            }


        }
        String apiNamesStr = ConfigFactory.getConfig("fs-fmcg-framework-config").get("skip_filter_api_names_for_yl_tpm_upgrade");
        if (StringUtils.isEmpty(apiNamesStr)) {
            apiNamesStr = "[\"tpm_activity_plan__c\", \"tpm_activity_time_span_plan__c\",\"tpm_business_center_budget__c\",\"TPMActivityAgreementObj\"]";
        }
        List<String> apiNames = JSON.parseArray(apiNamesStr, String.class);
        if (CollectionUtils.isNotEmpty(apiNames)) {
            if (apiNames.contains(apiName)) {
                return data.stream().map(DBRecord::getId).collect(Collectors.toList());
            }
        }
        filterData(targetTenantId, data, apiName);
        List<IObjectData> objectData = serviceFacade.bulkSaveObjectData(data, User.systemUser(targetTenantId), false);
        return objectData.stream().map(DBRecord::getId).collect(Collectors.toList());
    }

    private List<String> queryIdsByIds(String tenantId, List<String> ids, String apiName) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(ids);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);
        List<IObjectData> objectData = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                apiName,
                stq,
                Lists.newArrayList(
                        CommonFields.ID
                )
        );
        return objectData.stream().map(DBRecord::getId).collect(Collectors.toList());
    }

    private void copyCoveredStores(String sourceTenantId, String targetTenantId, List<String> ids) {
        List<List<String>> partition = Lists.partition(ids, 5);
        for (List<String> strings : partition) {
            SearchTemplateQuery query2 = new SearchTemplateQuery();
            query2.setLimit(-1);
            query2.setOffset(0);
            query2.setSearchSource("es");

            Filter filter2 = new Filter();
            filter2.setFieldName("belong_area");
            filter2.setOperator(Operator.IN);
            filter2.setFieldValues(strings);

            query2.setFilters(Lists.newArrayList(filter2));
            List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "CoveredStoresObj", query2);
            for (IObjectData datum : data) {
                handlerDataAndDepartment(datum, targetTenantId);
            }

            List<String> timeSpanIds = data.stream().filter(v -> !StringUtils.isEmpty(v.get("store", String.class)))
                    .map(v -> v.get("store", String.class)).collect(Collectors.toList());
            List<String> existIds = queryIdsByIds(targetTenantId, timeSpanIds, "AccountObj");
            data.removeIf(v -> !existIds.contains(v.get("store", String.class)));

            filterData(targetTenantId, data, "CoveredStoresObj");
            serviceFacade.bulkSaveObjectData(data, User.systemUser(targetTenantId), false);
        }


    }

    private List<String> copyAreaManagement(String sourceTenantId, String targetTenantId) {
        SearchTemplateQuery query2 = new SearchTemplateQuery();
        query2.setLimit(-1);
        query2.setOffset(0);
        query2.setSearchSource("es");

        Filter filter2 = new Filter();
        filter2.setFieldName("service_center__c");
        filter2.setOperator(Operator.IN);
        filter2.setFieldValues(departmentIds);

        query2.setFilters(Lists.newArrayList(filter2));
        List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "AreaManageObj", query2);
        for (IObjectData datum : data) {
            handlerDataAndDepartment(datum, targetTenantId);
        }
        return data.stream().map(DBRecord::getId).collect(Collectors.toList());
//        filterData(targetTenantId, data, "AreaManageObj");
//        List<IObjectData> objectData = serviceFacade.bulkSaveObjectData(data, User.systemUser(targetTenantId), false);
//        return objectData.stream().map(DBRecord::getId).collect(Collectors.toList());
    }

    @Override
    public void copyAccount(String sourceTenantId, String targetTenantId, String limit, String offset, String flag) {
        if ("1".equals(flag)) {
            int limit2 = 200;
            int offset2 = 0;
            int max = 0;
            while (true) {
                max++;
                if (max > 1000) {
                    break;
                }
                SearchTemplateQuery query2 = new SearchTemplateQuery();
                query2.setLimit(limit2);
                query2.setOffset(offset2);
                query2.setSearchSource("db");
                Filter filter = new Filter();
                filter.setFieldName("record_type");
                filter.setOperator(Operator.EQ);
                filter.setFieldValues(Lists.newArrayList("default__c"));
                Filter filter2 = new Filter();
                filter2.setFieldName("Service__c");
                filter2.setOperator(Operator.IN);
                //todo 部门修改
                filter2.setFieldValues(departmentIds);

                query2.setFilters(Lists.newArrayList(filter, filter2));
                List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "AccountObj", query2);
                log.info("data size:{}", data.size());
                if (CollectionUtils.isEmpty(data)) {
                    break;
                }
                for (IObjectData datum : data) {
                    handlerDataAndDepartment(datum, targetTenantId);
                }
                filterData(targetTenantId, data, "AccountObj");

                Map<String, IObjectData> collect = data.stream().collect(Collectors.toMap(IObjectData::getId, v -> v, (oldData, newData) -> newData));
                List<IObjectData> collect2 = new ArrayList<>(collect.values());
                serviceFacade.bulkSaveObjectData(collect2.stream().filter(v -> StringUtils.isEmpty(v.get("id", String.class))).collect(Collectors.toList()), User.systemUser(targetTenantId), false);
                offset2 += limit2;
            }
        } else {
            SearchTemplateQuery query2 = new SearchTemplateQuery();
            query2.setLimit(Integer.parseInt(limit));
            query2.setOffset(Integer.parseInt(offset));
            query2.setSearchSource("db");
            Filter filter = new Filter();
            filter.setFieldName("record_type");
            filter.setOperator(Operator.EQ);
            filter.setFieldValues(Lists.newArrayList("default__c"));
            Filter filter2 = new Filter();
            filter2.setFieldName("Service__c");
            filter2.setOperator(Operator.IN);
            //todo 部门修改
            filter2.setFieldValues(departmentIds);

            query2.setFilters(Lists.newArrayList(filter, filter2));
            List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "AccountObj", query2);
            log.info("data size:{}", data.size());
            if (CollectionUtils.isEmpty(data)) {
                return;
            }
            for (IObjectData datum : data) {
                handlerDataAndDepartment(datum, targetTenantId);
            }
            filterData(targetTenantId, data, "AccountObj");

            Map<String, IObjectData> collect = data.stream().collect(Collectors.toMap(IObjectData::getId, v -> v, (oldData, newData) -> newData));
            List<IObjectData> collect2 = new ArrayList<>(collect.values());
            log.info("collect2 size:{}", collect2.size());
            Map<String, IObjectData> collect3 = collect2.stream().collect(Collectors.toMap(k -> k.get("id", String.class), v -> v, (oldData, newData) -> newData));
            log.info("collect3 size:{}", new ArrayList<>(collect3.values()).size());
            try {
                boolean logFlag = true;
                for (Map.Entry<String, IObjectData> stringIObjectDataEntry : collect3.entrySet()) {
                    String id = stringIObjectDataEntry.getValue().get("id", String.class);
                    if (!StringUtils.isEmpty(id)) {
                        log.info("id:{}", id);
                        if (logFlag) {
                            log.info(JSON.toJSONString(stringIObjectDataEntry.getValue()));
                            logFlag = false;
                        }
                    }
                }

            } catch (Exception ignore) {
            }
            serviceFacade.bulkSaveObjectData(new ArrayList<>(collect3.values()), User.systemUser(targetTenantId), false);
        }


    }

    //todo 根据条件过滤
    private void copyAccount(String sourceTenantId, String targetTenantId) {
        int limit = 200;
        int offset = 0;
        int max = 0;
        while (true) {
            max++;
            if (max > 1000) {
                break;
            }
            SearchTemplateQuery query2 = new SearchTemplateQuery();
            query2.setLimit(limit);
            query2.setOffset(offset);
            query2.setSearchSource("db");
            Filter filter = new Filter();
            filter.setFieldName("record_type");
            filter.setOperator(Operator.EQ);
            filter.setFieldValues(Lists.newArrayList("default__c"));
            Filter filter2 = new Filter();
            filter2.setFieldName("Service__c");
            filter2.setOperator(Operator.IN);
            //todo 部门修改
            filter2.setFieldValues(departmentIds);

            query2.setFilters(Lists.newArrayList(filter, filter2));
            List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "AccountObj", query2);
            log.info("data size:{}", data.size());
            if (CollectionUtils.isEmpty(data)) {
                break;
            }
            for (IObjectData datum : data) {
                handlerDataAndDepartment(datum, targetTenantId);
            }
            filterData(targetTenantId, data, "AccountObj");

            Map<String, IObjectData> collect = data.stream().collect(Collectors.toMap(IObjectData::getId, v -> v, (oldData, newData) -> newData));
            List<IObjectData> collect2 = new ArrayList<>(collect.values());
            log.info("collect2 size:{}", collect2.size());
            Map<String, IObjectData> collect3 = collect2.stream().collect(Collectors.toMap(k -> k.get("id", String.class), v -> v, (oldData, newData) -> newData));
            log.info("collect3 size:{}", new ArrayList<>(collect3.values()).size());
            try {
                boolean logFlag = true;
                for (Map.Entry<String, IObjectData> stringIObjectDataEntry : collect3.entrySet()) {
                    String id = stringIObjectDataEntry.getValue().get("id", String.class);
                    if (!StringUtils.isEmpty(id)) {
                        log.info("id:{}", id);
                        if (logFlag) {
                            log.info(JSON.toJSONString(stringIObjectDataEntry.getValue()));
                            logFlag = false;
                        }
                    }
                }

            } catch (Exception ignore) {
            }
            serviceFacade.bulkSaveObjectData(new ArrayList<>(collect3.values()), User.systemUser(targetTenantId), false);
            offset += limit;
        }


    }

    private void copyDistributor(String sourceTenantId, String targetTenantId) {

        SearchTemplateQuery query2 = new SearchTemplateQuery();
        query2.setLimit(-1);
        query2.setOffset(0);
        query2.setSearchSource("es");
        Filter filter = new Filter();
        filter.setFieldName("record_type");
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList("distributor__c"));
        Filter filter2 = new Filter();
        filter2.setFieldName("Service__c");
        filter2.setOperator(Operator.IN);
        //todo 部门修改
        filter2.setFieldValues(departmentIds);

        query2.setFilters(Lists.newArrayList(filter, filter2));
        List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "AccountObj", query2);
        for (IObjectData datum : data) {
            handlerDataAndDepartment(datum, targetTenantId);
        }
        filterData(targetTenantId, data, "AccountObj");
        serviceFacade.bulkSaveObjectData(data, User.systemUser(targetTenantId), false);

    }

    private void copyDealer(String sourceTenantId, String targetTenantId) {
        SearchTemplateQuery query2 = new SearchTemplateQuery();
        query2.setLimit(-1);
        query2.setOffset(0);
        query2.setSearchSource("es");

        Filter filter = new Filter();
        filter.setFieldName("record_type");
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList("dealer__c"));

        Filter filter2 = new Filter();
        filter2.setFieldName("Service__c");
        filter2.setOperator(Operator.IN);
        //todo 部门修改
        filter2.setFieldValues(departmentIds);

        query2.setFilters(Lists.newArrayList(filter, filter2));
        List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "AccountObj", query2);
        for (IObjectData datum : data) {
            handlerDataAndDepartment(datum, targetTenantId);
        }
        filterData(targetTenantId, data, "AccountObj");
        serviceFacade.bulkSaveObjectData(data, User.systemUser(targetTenantId), false);

    }

    private void copyPerson(String sourceTenantId, String targetTenantId) {
        SearchTemplateQuery query2 = new SearchTemplateQuery();
        query2.setLimit(-1);
        query2.setOffset(0);
        query2.setSearchSource("es");

        Filter filter = new Filter();
        filter.setFieldName("main_department");
        filter.setOperator(Operator.IN);
        //todo 部门修改
        filter.setFieldValues(departmentIds);


        query2.setFilters(Lists.newArrayList(filter));

        List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "DepartmentObj", query2);
        for (IObjectData datum : data) {
            handlerDataAndDepartment(datum, targetTenantId);
        }
        serviceFacade.bulkSaveObjectData(data, User.systemUser(targetTenantId), false);
    }


    private void copyDepartment(String sourceTenantId, String targetTenantId) {
        SearchTemplateQuery query2 = new SearchTemplateQuery();
        query2.setLimit(1);
        query2.setOffset(0);
        query2.setSearchSource("es");
        Filter filter = new Filter();
        filter.setFieldName("name");
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList("福建营业部"));
        query2.setFilters(Lists.newArrayList(filter));
        List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "DepartmentObj", query2);
        for (IObjectData datum : data) {
            handlerDataAndDepartment(datum, targetTenantId);
        }
        serviceFacade.bulkSaveObjectData(data, User.systemUser(targetTenantId), false);

        if (CollectionUtils.isEmpty(data)) {
            return;
        }

        String id = data.get(0).getId();
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("es");
        List<IObjectData> data2 = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "DepartmentObj", query);
        for (IObjectData datum : data2) {
            handlerDataAndDepartment(datum, targetTenantId);
            datum.set("parent_id", id);
        }
        serviceFacade.bulkSaveObjectData(data2, User.systemUser(targetTenantId), false);

    }

    private void copyActivityItem(String sourceTenantId, String targetTenantId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(2000);
        query.setOffset(0);
        query.setSearchSource("es");

        List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "TPMActivityItemObj", query);

        for (IObjectData datum : data) {
            handlerDataAndDepartment(datum, targetTenantId);
        }
        serviceFacade.bulkSaveObjectData(data, User.systemUser(targetTenantId), false);
    }

    private void copyTPMProductCategory(String sourceTenantId, String targetTenantId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("es");

        List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "tpm_product_category__c", query);

        for (IObjectData datum : data) {
            handlerDataAndDepartment(datum, targetTenantId);
        }
        serviceFacade.bulkSaveObjectData(data, User.systemUser(targetTenantId), false);
    }

    private void copyProduct(String sourceTenantId, String targetTenantId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("es");

        List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "ProductObj", query);

        for (IObjectData datum : data) {
            handlerDataAndDepartment(datum, targetTenantId);
        }
        serviceFacade.bulkSaveObjectData(data, User.systemUser(targetTenantId), false);
    }

    private void copyProductCategory(String sourceTenantId, String targetTenantId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("es");

        List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "ProductCategoryObj", query);

        for (IObjectData datum : data) {
            handlerDataAndDepartment(datum, targetTenantId);
        }
        serviceFacade.bulkSaveObjectData(data, User.systemUser(targetTenantId), false);


        List<String> ids = data.stream().map(DBRecord::getId).collect(Collectors.toList());
        SearchTemplateQuery query2 = new SearchTemplateQuery();
        query2.setLimit(-1);
        query2.setOffset(0);
        query2.setSearchSource("es");
        Filter filter = new Filter();
        filter.setFieldName("pid");
        filter.setOperator(Operator.IN);
        filter.setFieldValues(ids);
        query2.setFilters(Lists.newArrayList(filter));
        List<IObjectData> data2 = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "ProductCategoryObj", query2);

        List<String> ids2 = data2.stream().map(DBRecord::getId).collect(Collectors.toList());
        SearchTemplateQuery query3 = new SearchTemplateQuery();
        query3.setLimit(-1);
        query3.setOffset(0);
        query3.setSearchSource("es");
        Filter filter2 = new Filter();
        filter2.setFieldName("pid");
        filter2.setOperator(Operator.IN);
        filter2.setFieldValues(ids2);
        query3.setFilters(Lists.newArrayList(filter2));
        List<IObjectData> data3 = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "ProductCategoryObj", query3);


        List<String> ids3 = data3.stream().map(DBRecord::getId).collect(Collectors.toList());
        SearchTemplateQuery query4 = new SearchTemplateQuery();
        query4.setLimit(-1);
        query4.setOffset(0);
        query4.setSearchSource("es");
        Filter filter3 = new Filter();
        filter3.setFieldName("pid");
        filter3.setOperator(Operator.IN);
        filter3.setFieldValues(ids3);
        query4.setFilters(Lists.newArrayList(filter3));
        List<IObjectData> data4 = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "ProductCategoryObj", query4);


        List<String> ids4 = data4.stream().map(DBRecord::getId).collect(Collectors.toList());
        SearchTemplateQuery query5 = new SearchTemplateQuery();
        query5.setLimit(-1);
        query5.setOffset(0);
        query5.setSearchSource("es");
        Filter filter4 = new Filter();
        filter4.setFieldName("pid");
        filter4.setOperator(Operator.IN);
        filter4.setFieldValues(ids4);
        query5.setFilters(Lists.newArrayList(filter4));
        List<IObjectData> data5 = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "ProductCategoryObj", query5);

        List<String> ids6 = data5.stream().map(DBRecord::getId).collect(Collectors.toList());
        SearchTemplateQuery query6 = new SearchTemplateQuery();
        query6.setLimit(-1);
        query6.setOffset(0);
        query6.setSearchSource("es");
        Filter filter5 = new Filter();
        filter5.setFieldName("pid");
        filter5.setOperator(Operator.IN);
        filter5.setFieldValues(ids6);
        query6.setFilters(Lists.newArrayList(filter5));
        List<IObjectData> data6 = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "ProductCategoryObj", query6);

    }

    private void copyKASystem(String sourceTenantId, String targetTenantId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("es");

        List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "tpm_activity_plan_kasystem__c", query);

        for (IObjectData datum : data) {
            handlerDataAndDepartment(datum, targetTenantId);
        }
        serviceFacade.bulkSaveObjectData(data, User.systemUser(targetTenantId), false);

    }

    private void copyKASystemType(String sourceTenantId, String targetTenantId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("es");

        List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "ka_systemtype__c", query);

        for (IObjectData datum : data) {
            handlerDataAndDepartment(datum, targetTenantId);
        }
        filterData(targetTenantId, data, "ka_systemtype__c");
        serviceFacade.bulkSaveObjectData(data, User.systemUser(targetTenantId), false);

    }

    private void copyChannel(String sourceTenantId, String targetTenantId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("es");

        List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "channel__c", query);

        for (IObjectData datum : data) {
            handlerDataAndDepartment(datum, targetTenantId);
        }
        filterData(targetTenantId, data, "channel__c");
        serviceFacade.bulkSaveObjectData(data, User.systemUser(targetTenantId), false);

    }


    private void copyChannelType(String sourceTenantId, String targetTenantId) {

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("es");

        Filter filter = new Filter();
        filter.setFieldName("rank__c");
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList("rootlevel"));

        query.setFilters(Lists.newArrayList(filter));

        List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "channel_type__c", query);

        for (IObjectData datum : data) {
            handlerDataAndDepartment(datum, targetTenantId);
        }

        filterData(targetTenantId, data, "channel_type__c");
        serviceFacade.bulkSaveObjectData(data, User.systemUser(targetTenantId), false);


        SearchTemplateQuery query2 = new SearchTemplateQuery();

        query2.setLimit(-1);
        query2.setOffset(0);
        query2.setSearchSource("es");

        Filter filter2 = new Filter();
        filter2.setFieldName("rank__c");
        filter2.setOperator(Operator.EQ);
        filter2.setFieldValues(Lists.newArrayList("firststage"));

        query2.setFilters(Lists.newArrayList(filter2));

        List<IObjectData> data2 = CommonUtils.queryData(serviceFacade, User.systemUser(sourceTenantId), "channel_type__c", query2);

        for (IObjectData datum : data2) {
            handlerDataAndDepartment(datum, targetTenantId);
        }
        filterData(targetTenantId, data2, "channel_type__c");
        serviceFacade.bulkSaveObjectData(data, User.systemUser(targetTenantId), false);

    }

    private void filterData(String targetTenantId, List<IObjectData> data, String apiName) {
        List<String> ids = data.stream().map(DBRecord::getId).collect(Collectors.toList());
        List<IObjectData> result = serviceFacade.findObjectDataByIds(targetTenantId, ids, apiName);
        List<String> filter = result.stream().map(DBRecord::getId).collect(Collectors.toList());
        data.removeIf(id -> filter.contains(id.getId()));
    }
}
