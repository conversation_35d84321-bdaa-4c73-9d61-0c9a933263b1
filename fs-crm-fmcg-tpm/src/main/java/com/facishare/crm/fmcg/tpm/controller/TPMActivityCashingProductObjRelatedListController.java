package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.NodeType;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@SuppressWarnings("Duplicates")
public class TPMActivityCashingProductObjRelatedListController extends StandardRelatedListController {

    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);
    private static final String PRODUCT_ID = "product_id";
    private static final String PRODUCT_STATUS = "product_status";
    private static final String PRODUCT_STATUS_LISTED = "1";
    private static final String UNIT = "unit";
    private static final String IMMEDIATE_UNIT_KEY = "__immediate_unit";
    private static final String ACTIVITY_CASHING_PRODUCT_ID = "__activity_cashing_product_id";
    private static final String ACTIVITY_CASHING_PRODUCT_ID__R = "__activity_cashing_product_id__r";
    private IObjectData activity;
    private String activityCashingProductRangeType;
    private String activityUnifiedCashingProductRangeType;
    private String activityUnifiedId = null;
    private boolean enableActivityPlan;

    /**
     * 重置上下文对象
     * 活动申请的产品范围为ALL、方案为FIXED  直接查方案的产品
     * 活动申请的产品范围为ALL、方案为ALL   直接查产品对象
     * 活动申请的产品范围为FIXED、方案为ALL  直接查申请的产品
     * 活动申请的产品范围为FIXED、方案为FIXED  直接查申请的产品
     *
     * @param arg
     */
    @Override
    protected void before(Arg arg) {

        if (apiNameValidateFail()) {
            super.before(arg);
            return;
        }
        ObjectDataDocument masterData = arg.getMasterData();

        String activityId = (String) masterData.get(TPMActivityAgreementFields.ACTIVITY_ID);
        if (Strings.isNullOrEmpty(activityId)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_CASHING_PRODUCT_OBJ_RELATED_LIST_CONTROLLER_0));
        }
        activity = serviceFacade.findObjectDataIgnoreAll(User.systemUser(controllerContext.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);
        if (Objects.isNull(activity)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_CASHING_PRODUCT_OBJ_RELATED_LIST_CONTROLLER_1));
        }
        activityUnifiedId = activity.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class);
        assignmentActivityCashingProductRangeType();

        enableActivityPlan = isEnableActivityPlan(activity.get(TPMActivityFields.ACTIVITY_TYPE, String.class));

        assignmentActivityUnifiedCashingProductRangeType();

        resettingControllerContext();
        super.before(arg);
    }

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        if (TPMGrayUtils.TPMUseEs(controllerContext.getTenantId())) {
            query.setSearchSource("es");
        }
        super.beforeQueryData(overrideActivityAgreementQuery(query));
    }

    @Override
    protected Result after(Arg arg, Result result) {
        return overrideResult(super.after(arg, result));
    }

    private Result overrideResult(Result result) {
        if (apiNameValidateFail()) {
            return result;
        }
        List<String> productIds = result.getDataList().stream().map(data -> (String) data.get(TPMActivityCashingProductScopeFields.PRODUCT_ID)).collect(Collectors.toList());

        List<IObjectData> products = serviceFacade.findObjectDataByIdsIgnoreRelevantTeam(controllerContext.getTenantId(), Lists.newArrayList(productIds), ApiNames.PRODUCT_OBJ);
        IObjectDescribe productDescribe = serviceFacade.findObject(controllerContext.getTenantId(), ApiNames.PRODUCT_OBJ);
        serviceFacade.fillObjectDataWithRefObject(productDescribe, products, User.systemUser(controllerContext.getTenantId()), null, false);
        Map<String, IObjectData> productMap = products.stream().collect(Collectors.toMap(IObjectData::getId, v -> v, (oldData, newData) -> newData));
        List<ObjectDataDocument> resultList = Lists.newArrayList();

        for (ObjectDataDocument dataDocument : result.getDataList()) {
            ObjectDataDocument product;
            if (Objects.equals(controllerContext.getObjectApiName(), ApiNames.PRODUCT_OBJ)) {
                product = dataDocument;
            } else {
                String productId = (String) dataDocument.get(PRODUCT_ID);
                IObjectData productObjData = productMap.get(productId);
                if (productObjData == null) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_CASHING_PRODUCT_OBJ_RELATED_LIST_CONTROLLER_2));
                }
                product = new ObjectDataDocument();
                product.putAll(ObjectDataDocument.of(productObjData));
                if (Objects.equals(ApiNames.TPM_ACTIVITY_CASHING_PRODUCT_OBJ, controllerContext.getObjectApiName())) {
                    product.put(ACTIVITY_CASHING_PRODUCT_ID, dataDocument.getId());
                    product.put(ACTIVITY_CASHING_PRODUCT_ID__R, product.get(CommonFields.NAME));
                    product.put(TPMActivityAgreementCashingProductFields.DISPLAY_NAME, product.get(CommonFields.NAME));
                }
            }
            if (product != null) {
                String unit = (String) product.get(UNIT);
                product.put(IMMEDIATE_UNIT_KEY, StringUtils.isEmpty(unit) ? "" : unit);
            }
            resultList.add(ObjectDataDocument.of(product));
        }

        result.setDataList(resultList);
        return result;
    }

    private SearchTemplateQuery overrideActivityAgreementQuery(SearchTemplateQuery query) {

        if (apiNameValidateFail()) {
            return query;
        }
        ObjectDataDocument masterData = arg.getMasterData();

        String activityId = (String) masterData.get(TPMActivityAgreementFields.ACTIVITY_ID);
        String activityUnifiedCaseId = activity.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class);
        if (!enableActivityPlan) {

            switch (activityCashingProductRangeType) {
                case "FIXED": {
                    // 活动申请下的兑付产品
                    Filter activityFilter = new Filter();
                    activityFilter.setFieldName(TPMActivityAgreementFields.ACTIVITY_ID);
                    activityFilter.setOperator(Operator.EQ);
                    activityFilter.setFieldValues(Lists.newArrayList(activityId));
                    query.getFilters().add(activityFilter);
                    break;
                }
                case "ALL":
                default: {
                    break;
                }
            }
        } else if (StringUtils.isEmpty(activityUnifiedId)) {

            switch (activityCashingProductRangeType) {
                case "FIXED": {
                    Filter activityFilter = new Filter();
                    activityFilter.setFieldName(TPMActivityAgreementFields.ACTIVITY_ID);
                    activityFilter.setOperator(Operator.EQ);
                    activityFilter.setFieldValues(Lists.newArrayList(activityId));
                    query.getFilters().add(activityFilter);
                    break;
                }
                case "ALL":
                default: {
                    break;
                }
            }

        } else {
            switch (activityCashingProductRangeType) {
                case "FIXED": {
                    Filter activityFilter = new Filter();
                    activityFilter.setFieldName(TPMActivityAgreementFields.ACTIVITY_ID);
                    activityFilter.setOperator(Operator.EQ);
                    activityFilter.setFieldValues(Lists.newArrayList(activityId));
                    query.getFilters().add(activityFilter);
                    break;
                }
                case "ALL": {
                    switch (activityUnifiedCashingProductRangeType) {
                        case "FIXED": {
                            // 活动方案下的兑付产品
                            Filter activityFilter = new Filter();
                            activityFilter.setFieldName(TPMActivityCashingProductScopeFields.ACTIVITY_UNIFIED_CASE_ID);
                            activityFilter.setOperator(Operator.EQ);
                            activityFilter.setFieldValues(Lists.newArrayList(activityUnifiedCaseId));
                            query.getFilters().add(activityFilter);
                            break;
                        }
                        case "ALL":
                        default: {
                            break;
                        }
                    }
                    break;
                }
                default: {
                    break;
                }
            }
        }
        if (Objects.equals(controllerContext.getObjectApiName(), ApiNames.PRODUCT_OBJ)) {
            Filter productStatusFiler = new Filter();
            productStatusFiler.setFieldName(PRODUCT_STATUS);
            productStatusFiler.setOperator(Operator.EQ);
            productStatusFiler.setFieldValues(Lists.newArrayList(PRODUCT_STATUS_LISTED));
            query.getFilters().add(productStatusFiler);
        }
        return query;
    }

    private void resettingControllerContext() {
        boolean flag = enableActivityPlan ? Objects.equals("ALL", activityUnifiedCashingProductRangeType) && Objects.equals("ALL", activityCashingProductRangeType) : Objects.equals("ALL", activityCashingProductRangeType);
        boolean flag2 = enableActivityPlan && StringUtils.isEmpty(activityUnifiedId) && Objects.equals("ALL", activityCashingProductRangeType);
        if (flag || flag2) {
            this.controllerContext = new ControllerContext(
                    controllerContext.getRequestContext(),
                    ApiNames.PRODUCT_OBJ,
                    controllerContext.getMethodName());
        }
        if (Objects.equals("FIXED", activityUnifiedCashingProductRangeType) && Objects.equals("ALL", activityCashingProductRangeType)) {
            this.controllerContext = new ControllerContext(
                    controllerContext.getRequestContext(),
                    ApiNames.TPM_ACTIVITY_CASHING_PRODUCT_SCOPE_OBJ,
                    controllerContext.getMethodName());
        }
    }

    private void assignmentActivityCashingProductRangeType() {
        JSONObject activityCashingProductRange = getCashingProductRange(activity);
        activityCashingProductRangeType = activityCashingProductRange.getString("type").toUpperCase();
    }

    private void assignmentActivityUnifiedCashingProductRangeType() {
        if (enableActivityPlan) {
            if (StringUtils.isEmpty(activityUnifiedId)) {
                activityUnifiedCashingProductRangeType = "OTHER";
                return;
            }
            IObjectData activityUnified = serviceFacade.findObjectDataIgnoreAll(User.systemUser(controllerContext.getTenantId()), activityUnifiedId, ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
            JSONObject activityUnifiedCashingProductRange = getCashingProductRange(activityUnified);
            activityUnifiedCashingProductRangeType = activityUnifiedCashingProductRange.getString("type").toUpperCase();
        }
    }

    private JSONObject getCashingProductRange(IObjectData objectData) {
        String cashingProductRangeStr = objectData.get(TPMActivityFields.CASHING_PRODUCT_RANGE, String.class);
        //cashingProductRangeStr是空代表的此条数据是老数据
        if (StringUtils.isEmpty(cashingProductRangeStr)) {
            cashingProductRangeStr = "{\"type\":\"FIXED\",\"value\":\"FIXED\"}";
        }
        return JSON.parseObject(cashingProductRangeStr);
    }

    private boolean isEnableActivityPlan(String activityTypeId) {

        boolean enableActivityPlan = false;
        if (!Strings.isNullOrEmpty(activityTypeId)) {
            ActivityTypeExt activityType = activityTypeManager.find(controllerContext.getTenantId(), activityTypeId);
            if (!Objects.isNull(activityType.node(NodeType.PLAN_TEMPLATE))) {
                enableActivityPlan = true;
            }
        }
        return enableActivityPlan;
    }

    private boolean apiNameValidateFail() {
        ObjectDataDocument objectData = arg.getObjectData();
        if (Objects.isNull(objectData)) {
            return true;
        }

        String objectApiName = (String) objectData.get("object_describe_api_name");
        return !ApiNames.TPM_ACTIVITY_AGREEMENT_CASHING_PRODUCT_OBJ.equals(objectApiName) && !ApiNames.TPM_STORE_WRITE_OFF_CASHING_PRODUCT_OBJ.equals(objectApiName)
                && !ApiNames.TPM_DEALER_ACTIVITY_CASHING_PRODUCT_OBJ.equals(objectApiName);
    }
}
