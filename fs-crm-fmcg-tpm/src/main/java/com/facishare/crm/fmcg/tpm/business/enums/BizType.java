package com.facishare.crm.fmcg.tpm.business.enums;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import org.apache.logging.log4j.util.Strings;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum BizType {

    INIT_MONEY("init_money", "期初新建"),
    TRANSFER_APPEND("transfer_append", "调整-追加"),
    ACCRUAL("accrual", "计提"),
    CARRY_OVER_IN("carry_over_in", "结转-转入"),
    CARRY_OVER_OUT("carry_over_out", "结转-转出"),
    TRANSFER_DEDUCTION("transfer_deduction", "调整-扣减"),
    TRANSFER_DEDUCTION_RECOVERY("transfer_deduction_recovery", "调整-扣减回溯-转入"),
    TRANSFER_IN("transfer_in", "调整-调拨-转入"),
    TRANSFER_OUT("transfer_out", "调整-调拨-转出"),
    CONSUME("consume", "消费"),
    TAKE_APART_IN("take_apart_in", "拆解-转入"),
    TAKE_APART_OUT("take_apart_out", "拆解-转出"),
    RELEASE("release", "释放"),
    APPROVAL_BACK("approval_back", "审批退回"),
    RETURN("return", "差额返还"),
    INVALID_BACK("invalid_back", "作废回退"),
    SELF_DEFINE("self_define", "自定义变更"),;

    private static final Map<String, BizType> INNER_MAP = Stream.of(values()).collect(Collectors.toMap(BizType::value, v -> v, (before, after) -> before));

    BizType(String value, String label) {
        this.label = label;
        this.value = value;
    }

    private final String label;
    private final String value;

    public String value() {
        return this.value;
    }

    public String label() {
        return this.label;
    }


    public String getI18Label() {
        String prefix = "fmcg.budget.bizType.%s";
        String i18Key = String.format(prefix, this.value);
        String i18Text = I18N.text(i18Key);
        if (Strings.isNotEmpty(i18Text)) {
            return i18Text;
        }
        return this.label;
    }

    public static BizType of(String value) {
        if (!INNER_MAP.containsKey(value)) {
            throw new ValidateException("no this value");
        }
        return INNER_MAP.get(value);
    }
}
