package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/8 上午10:59
 */
public class TPMActivityBudgetObjBudgetTransferAction extends AbstractStandardAction<TPMActivityBudgetObjBudgetTransferAction.Arg, TPMActivityBudgetObjBudgetTransferAction.Result> {

    private IObjectData objectData;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList("BudgetTransfer");
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getDataId());
    }

    @Override
    protected Result doAct(Arg arg) {
        return Result.of(objectData);
    }

    protected IObjectData getPreObjectData() {
        return objectData;
    }

    protected IObjectData getPostObjectData() {
        return objectData;
    }

    protected String getButtonApiName() {
        return "BudgetTransfer_button_default";
    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.notEmpty(dataList)) {
            objectData = dataList.get(0);
        }
    }

    @Data
    public static class Arg{

        @SerializedName("objectDataId")
        @JSONField(name = "objectDataId")
        @JsonProperty("objectDataId")
        private String dataId;
    }


    @Data
    public static class Result{
        private ObjectDataDocument objectData;

        public static Result of(IObjectData objectData) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            return result;
        }
    }
}
