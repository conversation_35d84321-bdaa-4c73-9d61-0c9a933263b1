package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/6/15 下午4:45
 */
public class TPMBudgetAccountObjTestBizController extends PreDefineController<TPMBudgetAccountObjTestBizController.Arg, TPMBudgetAccountObjTestBizController.Result> {


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected Result doService(Arg arg) {
        log.info("arg :{}",arg);
        arg.getObjectData().put("convert_message","aaa");
        return new Result(arg.getObjectData(),arg.getDetails());
    }

    @Data
    @ToString
    static class Arg implements Serializable {
        @SerializedName("describeApiName")
        private String describeApiName;
        @SerializedName("objectDataId")
        private String objectDataId;
        private List<String> dataIds;
        @SerializedName("buttonApiName")
        private String buttonApiName;
        @SerializedName("args")
        private Map<String, Object> args;
        @SerializedName("objectData")
        private ObjectDataDocument objectData;
        @SerializedName("details")
        Map<String, List<ObjectDataDocument>> details;
        @SerializedName("bizKey")
        String bizKey;
        private String actionStage;
        private Map<String, Object> actionParams;

    }

    @Data
    @ToString
    static class Result implements Serializable {
        private ObjectDataDocument objectData;
        private Map<String, List<ObjectDataDocument>> details;
        private String targetDescribeApiName;
        private boolean hasReturnValue;
        private Object returnValue;
        private String returnType;
        private boolean block;

        public Result(ObjectDataDocument objectData, Map<String, List<ObjectDataDocument>> details) {
            this.objectData = objectData;
            this.details = details;
            if (this.details == null) {
                this.details = Maps.newHashMap();
            }
            if (this.objectData == null) {
                this.objectData = new ObjectDataDocument();
            }
        }
    }
}
