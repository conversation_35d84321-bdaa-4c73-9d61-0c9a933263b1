package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.TPMAllowEditFieldsService;
import com.facishare.crm.fmcg.tpm.business.TPM2Service;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRangeFieldBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPM2Service;
import com.facishare.crm.fmcg.tpm.business.abstraction.IUnifiedActivityCommonLogicBusiness;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.PromotionPolicyPO;
import com.facishare.crm.fmcg.tpm.service.PackTransactionProxyImpl;
import com.facishare.crm.fmcg.tpm.service.abstraction.PackTransactionProxy;
import com.facishare.crm.fmcg.tpm.service.abstraction.TransactionService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.crm.fmcg.tpm.web.contract.PromotionPolicyEdit;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.service.PromotionPolicyService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/13 17:25
 */
@SuppressWarnings("Duplicates")
public class TPMActivityUnifiedCaseObjEditAction extends StandardEditAction implements TransactionService<StandardEditAction.Arg, StandardEditAction.Result> {

    private static final Logger LOGGER = LoggerFactory.getLogger(TPMActivityUnifiedCaseObjAddAction.class);

    private final ITPM2Service tpm2Service = SpringUtil.getContext().getBean(TPM2Service.class);

    private final IUnifiedActivityCommonLogicBusiness unifiedActivityCommonLogicBusiness = SpringUtil.getContext().getBean(IUnifiedActivityCommonLogicBusiness.class);

    private final IRangeFieldBusiness rangeFieldBusiness = SpringUtil.getContext().getBean(IRangeFieldBusiness.class);
    private final PromotionPolicyService promotionPolicyService = SpringUtil.getContext().getBean(PromotionPolicyService.class);
    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);
    public final IActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);
    public final TPMAllowEditFieldsService tpmAllowEditFieldsService = SpringUtil.getContext().getBean(TPMAllowEditFieldsService.class);
    private String ACTIVITY_TYPE_TEMPLATE_ID = "";
    private String PRODUCT_GIFT_DATA = "";
    /*是否为可编辑的自定义字段*/
    private boolean enableEditCustomField = false;
    /*是否为可编辑的预置字段*/
    private boolean enableEditPreField = false;


    @Override
    protected void before(Arg arg) {
        LOGGER.info("start TPMActivityUnifiedCaseObj edit");
        fillArgData(arg);
        super.before(arg);
        initData();
        setDefaultValue();
        validateEnableEditCustomField(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ, this.updatedFieldMap, this.detailChangeMap);
        if (!enableEditCustomField) {
            //校验活动周期是否合理
            validateDateRange(arg);
            //结案状态
            validateCloseStatus();
            //校验费用的合理性
            validateCostCorrect(arg);
            validateActivityStatus(arg);
            //兑付产品范围
            validateCashingProduct(arg);
            // 是否已存在活动申请
            if (queryActivityRelation(arg)) {
                if (!enableEditPreField) {
                    IObjectData activityCase = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getObjectData().getId(), ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
                    LOGGER.info("activityCase is {}", activityCase);
                    //方案已被申请，方案金额调整不支持低于原定的方案金额
                    validateActivityAmount(arg, activityCase);
                    // 校验活动周期不可缩短
                    validateActivityDateScope(arg, activityCase);
                    // 校验参与部门不可减少
                    validateDepartmentRange(arg, activityCase);
                    // 参与经销商指定不可减少,兑付产品不可减少,品项不可减少
                    validateDealerAndCashingRangeAndProductRange(arg, activityCase);
                }
            }
            validateEnableEditPricePolicy(arg);
        }
    }

    private void initData() {
        String activityTypeId = this.arg.getObjectData().toObjectData().get(TPMActivityFields.ACTIVITY_TYPE, String.class);
        if (!Strings.isNullOrEmpty(activityTypeId)) {
            LOGGER.info("activity type id is {}", activityTypeId);
            ActivityTypeExt activityTypeExt = activityTypeManager.find(actionContext.getTenantId(), activityTypeId);
            ACTIVITY_TYPE_TEMPLATE_ID = activityTypeExt.get().getTemplateId();
        }
    }

    private void validateEnableEditPricePolicy(Arg arg) {
        if (Objects.nonNull(ACTIVITY_TYPE_TEMPLATE_ID) && !ACTIVITY_TYPE_TEMPLATE_ID.startsWith("promotion")) {
            return;
        }

        //判断web端请求 WEB.chrome
        String clientInfo = actionContext.getRequestContext().getClientInfo();
        if (!clientInfo.startsWith("WEB")) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_EDIT_ACTION_0));
        }

        IObjectData activityUnifiedCase = arg.getObjectData().toObjectData();
        String modeType = activityUnifiedCase.get(TPMActivityUnifiedCaseFields.MODE_TYPE, String.class, "");
        if (Strings.isNullOrEmpty(modeType)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_EDIT_ACTION_1));
        }

        String sourceObjectApiName = activityUnifiedCase.get(TPMActivityUnifiedCaseFields.SOURCE_OBJECT_API_NAME, String.class, "");
        if (Strings.isNullOrEmpty(sourceObjectApiName)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_EDIT_ACTION_2));
        }
        PromotionPolicyEdit.Result result = promotionPolicyService.doEnableEditPromotionPolicy(actionContext.getTenantId(), arg.getObjectData().toObjectData());
        if (Boolean.FALSE.equals(result.getEnable())) {
            throw new ValidateException(result.getMsg() == null ? I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_EDIT_ACTION_3) : result.getMsg());
        }
        PRODUCT_GIFT_DATA = (String) CommonUtils.getOrDefault(arg.getObjectData().get("product_gift_data_json"), "");

    }

    private void setDefaultValue() {
        if (Strings.isNullOrEmpty((String) arg.getObjectData().get(TPMActivityUnifiedCaseFields.STORE_RANGE))) {
            arg.getObjectData().put(TPMActivityUnifiedCaseFields.STORE_RANGE, "{\"type\":\"ALL\",\"value\":\"ALL\"}");
        }

        if (this.updatedFieldMap.containsKey(TPMActivityUnifiedCaseFields.STORE_RANGE)) {
            this.objectData.set(TPMActivityUnifiedCaseFields.STORE_RANGE, rangeFieldBusiness.newUnifiedDealerRangeConditionCode(actionContext.getTenantId(), this.objectData.get(TPMActivityUnifiedCaseFields.STORE_RANGE, String.class)));
        }

        IObjectData objectData = arg.getObjectData().toObjectData();
        List<String> departmentList = CommonUtils.castIgnore(objectData.get(TPMActivityUnifiedCaseFields.ACTIVITY_DEPARTMENT_RANGE), String.class);
        // 如果部门为空 或者 等于 待分配，表示为选择部门，默认全公司。
        if (CollectionUtils.isEmpty(departmentList) || departmentList.contains("999998")) {
            objectData.set(TPMActivityUnifiedCaseFields.ACTIVITY_DEPARTMENT_RANGE, Lists.newArrayList("999999"));
            if (this.updatedFieldMap.containsKey(TPMActivityUnifiedCaseFields.ACTIVITY_DEPARTMENT_RANGE)) {
                this.updatedFieldMap.put(TPMActivityUnifiedCaseFields.ACTIVITY_DEPARTMENT_RANGE, Lists.newArrayList("999999"));
            }
        }

    }

    private void validateCloseStatus() {
        //已经结案不可编辑
        if (Objects.nonNull(this.dbMasterData)) {
            if (TPMActivityUnifiedCaseFields.CLOSE_STATUS__CLOSED.equals(this.dbMasterData.get(TPMActivityUnifiedCaseFields.CLOSE_STATUS))) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_EDIT_ACTION_4));
            }
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        return packTransactionProxy.packAct(this, arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result afterResult = super.after(arg, result);

        recalculateAmount(afterResult.getObjectData().toObjectData());
        resetActivityStatus(result);
        saveOrUpdatePromotionPolicy(afterResult.getObjectData().toObjectData(), PRODUCT_GIFT_DATA);
        return afterResult;
    }

    private void recalculateAmount(IObjectData data) {
        if (!this.isApprovalFlowStartSuccess(arg.getObjectData().getId())) {
            unifiedActivityCommonLogicBusiness.recalculateUnifiedAmountField(actionContext.getTenantId(), data);
        }
    }

    private void validateDealerAndCashingRangeAndProductRange(Arg arg, IObjectData activityCase) {
        if (activityCase == null) {
            return;
        }

        Map<String, List<IObjectData>> originalDetails = getDetailsMap(activityCase, User.systemUser(actionContext.getTenantId()));
        String oldStoreRange = activityCase.get(TPMActivityUnifiedCaseFields.STORE_RANGE, String.class);
        String storeRange = arg.getObjectData().toObjectData().get(TPMActivityUnifiedCaseFields.STORE_RANGE, String.class);

        Map<String, List<ObjectDataDocument>> details = arg.getDetails();
        validateDealerScope(details, originalDetails, oldStoreRange, storeRange);
        validateCashingScope(details, originalDetails);
        validateProductRange(details, originalDetails);
    }

    private void validateCashingScope(Map<String, List<ObjectDataDocument>> details,
                                      Map<String, List<IObjectData>> originalDetails) {
        //兑付产品不可减少
        List<IObjectData> oldCashingObject = originalDetails.get(ApiNames.TPM_ACTIVITY_CASHING_PRODUCT_SCOPE_OBJ);
        if (CollectionUtils.isNotEmpty(oldCashingObject)) {
            List<String> oldCashingIds = oldCashingObject.stream().map(v -> v.get("product_id", String.class)).collect(Collectors.toList());
            List<ObjectDataDocument> cashingObject = details.getOrDefault(ApiNames.TPM_ACTIVITY_CASHING_PRODUCT_SCOPE_OBJ, new ArrayList<>());
            List<String> cashingIds = cashingObject.stream().map(v -> (String) v.get("product_id")).collect(Collectors.toList());
            if (!new HashSet<>(cashingIds).containsAll(oldCashingIds)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_EDIT_ACTION_5));
            }
        }
    }

    private void validateProductRange(Map<String, List<ObjectDataDocument>> details,
                                      Map<String, List<IObjectData>> originalDetails) {
        //方案品项不可减少
        List<IObjectData> oldProductRangeObjects = originalDetails.get(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_PRODUCT_RANGE_OBJ);
        List<ObjectDataDocument> currentProductRangeObjects = details.getOrDefault(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_PRODUCT_RANGE_OBJ, new ArrayList<>());
        if (CollectionUtils.isNotEmpty(oldProductRangeObjects)) {
            List<String> oldProductIds = oldProductRangeObjects.stream().map(v -> v.get("product_id", String.class)).collect(Collectors.toList());
            List<String> currentProductIds = currentProductRangeObjects.stream().map(v -> (String) v.get("product_id")).collect(Collectors.toList());

            if (!new HashSet<>(currentProductIds).containsAll(oldProductIds)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_EDIT_ACTION_6));
            }
        }
    }

    private void validateCashingProduct(Arg arg) {
        if (!TPMGrayUtils.activityCashingProductIsRequired(actionContext.getTenantId())) {
            return;
        }
        String writeOffCash = (String) arg.getObjectData().get(TPMActivityUnifiedCaseFields.WRITE_OFF_CASH);
        if (Objects.equals(writeOffCash, TPMActivityUnifiedCaseFields.WRITE_OFF_CASH__GOODS)) {
            List<ObjectDataDocument> details = arg.getDetails().get(ApiNames.TPM_ACTIVITY_CASHING_PRODUCT_SCOPE_OBJ);
            if (CollectionUtils.isEmpty(details)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_EDIT_ACTION_7));
            }
        }
    }

    private void validateDealerScope(Map<String, List<ObjectDataDocument>> details,
                                     Map<String, List<IObjectData>> originalDetails,
                                     String oldStoreRange, String storeRange) {

        JSONObject oldObject = JSON.parseObject(oldStoreRange);
        String oldType = oldObject.getString("type");

        JSONObject object = JSON.parseObject(storeRange);
        String type = object.getString("type");
        // 经销商范围不可切换取值范围类型
        if (!Objects.equals(oldType, type)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_EDIT_ACTION_8));
        }

        if (type.contains("ALL")) {
            return;
        }
        if (type.contains("CONDITION")) {
            // 把store_range 中的 code 过滤掉。
            oldObject.remove("code");
            object.remove("code");

            if (!Objects.equals(oldObject, object)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_EDIT_ACTION_9));
            }
        }
        if (type.contains("FIXED")) {
            // 参与经销商指定不可减少
            List<IObjectData> oldDocuments = originalDetails.get(ApiNames.TPM_ACTIVITY_DEALER_SCOPE_OBJ);
            if (CollectionUtils.isNotEmpty(oldDocuments)) {
                List<String> oldDealerIds = oldDocuments.stream().map(v -> v.get("dealer_id", String.class)).collect(Collectors.toList());
                List<ObjectDataDocument> documents = details.getOrDefault(ApiNames.TPM_ACTIVITY_DEALER_SCOPE_OBJ, new ArrayList<>());
                List<String> dealerIds = documents.stream().map(v -> (String) v.get("dealer_id")).collect(Collectors.toList());
                if (!dealerIds.containsAll(oldDealerIds)) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_EDIT_ACTION_10));
                }
            }
        }
    }

    private Map<String, List<IObjectData>> getDetailsMap(IObjectData masterData, User user) {

        List<IObjectDescribe> detailDescribe = serviceFacade.findDetailDescribes(user.getTenantId(), masterData.getDescribeApiName());
        return serviceFacade.findDetailObjectDataList(detailDescribe, masterData, user);
    }

    private void validateDepartmentRange(Arg arg, IObjectData activityCase) {
        if (activityCase == null) {
            return;
        }

        List<String> oldDeparts = CommonUtils.cast(activityCase.get(TPMActivityUnifiedCaseFields.ACTIVITY_DEPARTMENT_RANGE), String.class);
        List<String> departs = CommonUtils.cast(arg.getObjectData().get(TPMActivityUnifiedCaseFields.ACTIVITY_DEPARTMENT_RANGE), String.class);
        LOGGER.info("oldDeparts is {}, departs is {}", oldDeparts, departs);
        if (!departs.containsAll(oldDeparts)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_EDIT_ACTION_11));
        }

    }


    private void validateActivityAmount(Arg arg, IObjectData activityCase) {
        if (activityCase == null) {
            return;
        }
        //原始数据
        BigDecimal oldAmount = activityCase.get(TPMActivityUnifiedCaseFields.ACTIVITY_AMOUNT, BigDecimal.class);
        //数据
        BigDecimal amount = arg.getObjectData().toObjectData().get(TPMActivityUnifiedCaseFields.ACTIVITY_AMOUNT, BigDecimal.class);
        if (amount.compareTo(oldAmount) < 0) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_EDIT_ACTION_12));
        }
    }

    private void validateActivityDateScope(Arg arg, IObjectData activityCase) {

        if (activityCase == null) {
            return;
        }

        //原始数据
        long oldEnd = activityCase.get(TPMActivityUnifiedCaseFields.END_DATE, Long.class);
        //数据
        long end = arg.getObjectData().toObjectData().get(TPMActivityUnifiedCaseFields.END_DATE, Long.class);
        LOGGER.info("oldEnd is {}, end is {}", oldEnd, end);
        if (oldEnd > end) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_EDIT_ACTION_13));
        }
    }

    private void validateCostCorrect(Arg arg) {
        if (arg.getObjectData().get(TPMActivityUnifiedCaseFields.ACTIVITY_AMOUNT) == null) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_EDIT_ACTION_14));
        }
        BigDecimal amount = arg.getObjectData().toObjectData().get(TPMActivityUnifiedCaseFields.ACTIVITY_AMOUNT, BigDecimal.class);
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_EDIT_ACTION_15));
        }
    }

    private void resetActivityStatus(Result result) {
        if (tpm2Service.isTPM2Tenant(Integer.valueOf(actionContext.getTenantId()))) {
            String lifeStatus = (String) result.getObjectData().get(CommonFields.LIFE_STATUS);
            if (!ObjectLifeStatus.NORMAL.getCode().equals(lifeStatus)) {
                Map<String, Object> updateMap = new HashMap<>();
                updateMap.put(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS, TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__APPROVAL);
                serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), result.getObjectData().toObjectData(), updateMap);
            }
        }
    }

    private void fillArgData(Arg arg) {
        //日期为空，表示默认值。
        ObjectDataDocument objectData = arg.getObjectData();
        if (objectData.containsKey(TPMActivityUnifiedCaseFields.START_DATE)) {
            objectData.putIfAbsent(TPMActivityUnifiedCaseFields.START_DATE, TimeUtils.MIN_DATE);
        }
        if (objectData.containsKey(TPMActivityUnifiedCaseFields.END_DATE)) {
            objectData.putIfAbsent(TPMActivityUnifiedCaseFields.END_DATE, TimeUtils.convertToDayEndIfTimeWasDayBegin(TimeUtils.MAX_DATE));
        }
    }

    private void validateActivityStatus(Arg arg) {
        // 校验时间是否存在
        long begin = (long) arg.getObjectData().get(TPMActivityUnifiedCaseFields.START_DATE);
        long end = (long) arg.getObjectData().get(TPMActivityUnifiedCaseFields.END_DATE);
        long now = System.currentTimeMillis();
        String status;
        if (now < begin) {
            status = TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__SCHEDULE;
        } else if (now < end) {
            status = TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__IN_PROGRESS;
        } else {
            status = TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__END;
        }
        arg.getObjectData().put(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS, status);
    }

    private void validateDateRange(Arg arg) {
        arg.getObjectData().putIfAbsent(TPMActivityUnifiedCaseFields.START_DATE, TimeUtils.MIN_DATE);
        arg.getObjectData().putIfAbsent(TPMActivityUnifiedCaseFields.END_DATE, TimeUtils.MAX_DATE);
        if (this.updatedFieldMap.containsKey(TPMActivityUnifiedCaseFields.START_DATE)) {
            this.updatedFieldMap.putIfAbsent(TPMActivityUnifiedCaseFields.START_DATE, TimeUtils.MIN_DATE);
        }
        if (this.updatedFieldMap.containsKey(TPMActivityUnifiedCaseFields.END_DATE)) {
            this.updatedFieldMap.putIfAbsent(TPMActivityUnifiedCaseFields.END_DATE, TimeUtils.MAX_DATE);
        }
        if (Objects.equals(arg.getObjectData().get(TPMActivityUnifiedCaseFields.START_DATE), 0)) {
            arg.getObjectData().put(TPMActivityUnifiedCaseFields.START_DATE, TimeUtils.MIN_DATE);
        }
        //设置时间
        long begin = arg.getObjectData().toObjectData().get(TPMActivityUnifiedCaseFields.START_DATE, Long.class);
        long end = TimeUtils.convertToDayEndIfTimeWasDayBegin(arg.getObjectData().toObjectData().get(TPMActivityUnifiedCaseFields.END_DATE, Long.class));
        arg.getObjectData().put(TPMActivityUnifiedCaseFields.END_DATE, end);

        if (end <= begin) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_EDIT_ACTION_16));
        }

        LOGGER.info("end date : {}", end);
    }


    private boolean queryActivityRelation(Arg arg) {

        // 查询关联的活动申请

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);

        Filter idFilter = new Filter();
        idFilter.setFieldName(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(arg.getObjectData().getId()));

        Filter deletedFilter = new Filter();
        deletedFilter.setFieldName(CommonFields.IS_DELETED);
        deletedFilter.setOperator(Operator.EQ);
        deletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(idFilter, deletedFilter));

        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query).getData();
        LOGGER.info("data is empty is {}", data.isEmpty());
        return !data.isEmpty();
    }

    private List<IObjectData> getActivityDataListByUnifiedCaseId(IObjectData objectData) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(2000);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);

        Filter relationFilter = new Filter();
        relationFilter.setFieldName(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID);
        relationFilter.setOperator(Operator.EQ);
        relationFilter.setFieldValues(Lists.newArrayList(objectData.getId()));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        query.setFilters(Lists.newArrayList(relationFilter, lifeStatusFilter));

        return serviceFacade.findBySearchQueryIgnoreAll(
                User.systemUser(actionContext.getTenantId()),
                ApiNames.TPM_ACTIVITY_OBJ,
                query).getData();
    }

    @Override
    public Result doActTransaction(Arg arg) {
        return super.doAct(arg);
    }

    private void saveOrUpdatePromotionPolicy(IObjectData objectData, String newProductGiftData) {
        if (Objects.nonNull(ACTIVITY_TYPE_TEMPLATE_ID) && ACTIVITY_TYPE_TEMPLATE_ID.startsWith("promotion")) {
            // 方案的 字段变更，不会同步政策，依活动申请为主。
            promotionPolicyService.saveOrUpdate(actionContext.getTenantId(),
                    actionContext.getUser().getUpstreamOwnerIdOrUserId(),
                    objectData,
                    newProductGiftData,
                    false);

            // 状态为正常。
            if (Objects.equals(objectData.get(CommonFields.LIFE_STATUS, String.class), CommonFields.LIFE_STATUS__NORMAL)) {
                updateActivityPromotionPolicy(objectData);
            }
        }
    }

    private void updateActivityPromotionPolicy(IObjectData objectData) {

        PromotionPolicyPO promotionPolicyData = promotionPolicyService.getPromotionPolicyData(actionContext.getTenantId(), objectData);
        if (Objects.isNull(promotionPolicyData) || Objects.equals(promotionPolicyData.getProductGiftData(), promotionPolicyData.getProductGiftDataBackUp())) {
            return;
        }

        // 更新关联的活动申请。
        List<IObjectData> activityDataList = getActivityDataListByUnifiedCaseId(objectData);
        String modeType = this.objectData.get(TPMActivityUnifiedCaseFields.MODE_TYPE, String.class, "");
        // 更新价格政策
        promotionPolicyService.batchUpdateSFAPromotionPolicy(actionContext.getTenantId(), actionContext.getUser().getUserId(), modeType, activityDataList);
    }

    private void validateEnableEditCustomField(String objApiName, Map<String, Object> updatedFieldMap, Map<String, Map<String, Object>> detailChangeMap) {

        Map<String, List<String>> needUpdateFieldOBJFieldsMap = Maps.newHashMap();
        if (!updatedFieldMap.isEmpty()) {
            needUpdateFieldOBJFieldsMap.put(objApiName, Lists.newArrayList(updatedFieldMap.keySet()));
        }
        if (!detailChangeMap.isEmpty()) {
            detailChangeMap.forEach((apiName, operateObjectMap) -> {
                if (operateObjectMap.containsKey("Edit")) {
                    Map<String, Object> editMap = (Map<String, Object>) operateObjectMap.get("Edit");
                    if (!editMap.isEmpty()) {
                        List<String> detailUpdateFields = Lists.newArrayList();
                        editMap.values().forEach(o -> {
                            Map<String, Object> detailFilesMap = (Map<String, Object>) (o);
                            detailUpdateFields.addAll(detailFilesMap.keySet());
                        });

                        if (CollectionUtils.isNotEmpty(detailUpdateFields)) {
                            needUpdateFieldOBJFieldsMap.put(apiName, detailUpdateFields.stream().distinct().collect(Collectors.toList()));
                        }
                    }
                }
            });
        }
        if (!needUpdateFieldOBJFieldsMap.isEmpty()) {
            LOGGER.info("needUpdateFieldOBJFieldsMap is {}", needUpdateFieldOBJFieldsMap);
            needUpdateFieldOBJFieldsMap.forEach((apiName, needUpdateFieldOBJFields) -> {
                List<String> preFields = needUpdateFieldOBJFields.stream().filter(s ->
                        !s.contains("CALLBACK") && !s.contains("KEY") && !s.endsWith("__c")).collect(Collectors.toList());
                enableEditCustomField = preFields.isEmpty();
                List<String> enableEditPreFields = preFields.stream().filter(s -> !tpmAllowEditFieldsService.getTPMAllowedEditFields().contains(s)).collect(Collectors.toList());
                enableEditPreField = enableEditPreFields.isEmpty();
            });
        }
    }
}
