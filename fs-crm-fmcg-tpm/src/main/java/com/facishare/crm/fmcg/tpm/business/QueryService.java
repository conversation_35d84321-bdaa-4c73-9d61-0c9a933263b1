package com.facishare.crm.fmcg.tpm.business;

import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.tpm.business.abstraction.IQueryService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/23 下午6:42
 */
@Service
public class QueryService implements IQueryService {

    @Resource
    private ServiceFacade serviceFacade;

    @Override
    public List<IObjectData> queryDataListByRelatedField(String tenantId, String describeApiName, String relateFieldApiName, String relatedId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter relatedFilter = new Filter();
        relatedFilter.setFieldName(relateFieldApiName);
        relatedFilter.setOperator(Operator.EQ);
        relatedFilter.setFieldValues(Lists.newArrayList(relatedId));

        query.setFilters(Lists.newArrayList(relatedFilter));
        return CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), describeApiName, query);
    }
}
