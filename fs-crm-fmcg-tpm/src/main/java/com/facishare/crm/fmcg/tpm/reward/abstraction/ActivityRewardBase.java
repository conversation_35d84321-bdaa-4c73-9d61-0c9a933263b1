package com.facishare.crm.fmcg.tpm.reward.abstraction;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.paas.I18N;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.common.adapter.EnterpriseConnectionService;
import com.facishare.crm.fmcg.common.adapter.exception.RewardFmcgException;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.constant.ScanCodeActionConstants;
import com.facishare.crm.fmcg.common.utils.IdentityIdGenerator;
import com.facishare.crm.fmcg.common.utils.RandomUtil;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.api.MengNiuTenantInformation;
import com.facishare.crm.fmcg.tpm.api.SimpleDTO;
import com.facishare.crm.fmcg.tpm.business.FmcgSerialNumberService;
import com.facishare.crm.fmcg.tpm.business.RewardExceptionRecordService;
import com.facishare.crm.fmcg.tpm.business.dto.GetRewardDetailDTO;
import com.facishare.crm.fmcg.tpm.business.dto.PayerInfoDTO;
import com.facishare.crm.fmcg.tpm.business.dto.ReceiverInfoDTO;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityRewardRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.BizCodeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.RewardTransferAccountDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.retry.setter.RedPacketStatusSetter;
import com.facishare.crm.fmcg.tpm.retry.setter.RewardRuleRedPacketSetter;
import com.facishare.crm.fmcg.tpm.service.TransactionProxy;
import com.facishare.crm.fmcg.tpm.service.abstraction.IRoleService;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.web.service.TenantHierarchyService;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.api.IdGenerator;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * Author: linmj
 * Date: 2023/10/7 16:36
 */
//IgnoreI18nFile
@Slf4j
public abstract class ActivityRewardBase {
    @Resource
    protected BizCodeDAO bizCodeDAO;

    @Resource
    protected ServiceFacade serviceFacade;

    @Resource
    protected FmcgSerialNumberService fmcgSerialNumberService;

    @Resource
    protected EnterpriseConnectionService enterpriseConnectionService;

    @Resource
    protected TransactionProxy transactionProxy;

    @Resource
    protected EnterpriseEditionService enterpriseEditionService;

    @Resource
    protected TenantHierarchyService tenantHierarchyService;

    @Resource(name = "tpmOrganizationService")
    protected OrganizationService organizationService;

    @Resource(name = "tpmRoleService")
    protected IRoleService roleService;

    @Resource
    protected EIEAConverter eieaConverter;

    @Resource
    protected RewardExceptionRecordService rewardExceptionRecordService;

    @Resource
    protected RewardRuleRedPacketSetter rewardRuleRedPacketSetter;

    @Resource
    protected RedPacketStatusSetter redPacketStatusSetter;

    @Resource
    protected ActivityRewardRuleDAO activityRewardRuleDAO;

    @Resource
    protected RewardTransferAccountDAO rewardTransferAccountDAO;


    @Resource
    protected RedissonClient redissonCmd;

    protected static final long LOCK_WAIT = 2000;

    protected static final long LOCK_LEASE = 120000;

    public static final String PHYSICAL_RED_PACKET_TRIGGER_ID = "PHYSICAL_RED_PACKET_REWARD";


    public GetRewardDetailDTO getRewardDetail(String tenantId, String activityId, String businessId, RewardDetailEntity rewardDetail, IObjectData serialNumberStatus) {
        GetRewardDetailDTO getRewardDetailDTO = new GetRewardDetailDTO();
        String snCodeId = serialNumberStatus.get(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID, String.class);
        String actionId = serialNumberStatus.get(FMCGSerialNumberStatusFields.ACTION_ID, String.class);
        IObjectData snCodeObj = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), snCodeId, ApiNames.FMCG_SERIAL_NUMBER_OBJ);
        String productId = snCodeObj.get(FMCGSerialNumberFields.PRODUCT_ID, String.class);
        String scanActionId = fmcgSerialNumberService.getActionIdByActionUniqueId(tenantId, ScanCodeActionConstants.CONSUMER_SCAN);
        String getRedPacketAction = fmcgSerialNumberService.getActionIdByActionUniqueId(tenantId, ScanCodeActionConstants.CONSUMER_GET_RED_PACKAGE);
        if (actionId != null && (actionId.equals(scanActionId) || actionId.equals(getRedPacketAction))) {
            log.info("消费者动作 无需重复判断");
            return null;
        }
        ReceiverInfoDTO receiverInfoDTO = getReceiverInfo(tenantId, rewardDetail, serialNumberStatus);
        if (Strings.isNullOrEmpty(receiverInfoDTO.getTenantId())) {
            log.info("接收用户无法找到。tenantId:{}", receiverInfoDTO);
            return null;
        }
        if (unMatchRewardRole(rewardDetail, receiverInfoDTO)) {
            log.info("角色不匹配：tenantId:{},role:{},roleCode:{}", tenantId, receiverInfoDTO.getRewardRole(), receiverInfoDTO.getRewardRoleCode());
            return null;
        }
        IObjectData exceptionRewardDetail = getExceptionRewardDetail(tenantId, rewardDetail, serialNumberStatus, businessId, receiverInfoDTO, activityId, productId);
        if (exceptionRewardDetail != null) {
            log.info("异常不给予激励,异常记录:{}", exceptionRewardDetail);
            getRewardDetailDTO.setActivityRewardDetails(Lists.newArrayList(exceptionRewardDetail));
            return getRewardDetailDTO;
        }

        String scanActionStoreId = serialNumberStatus.get(FMCGSerialNumberStatusFields.ACCOUNT_ID, String.class);
        RewardMethodEnum rewardMethod = RewardMethodEnum.get(rewardDetail.getRewardStrategy().getRewardMethod());

        if (rewardMethod == RewardMethodEnum.RED_PACKET) {
            PayerInfoDTO payerInfoDTO = getPayerInfo(tenantId, rewardDetail, serialNumberStatus);
            String remarks = rewardDetail.getRewardStrategy().getRewardRemark();
            IObjectData relatedObjectByRedPacket = getRelatedObjectBySnStatus(tenantId, serialNumberStatus);
            IObjectData redPacketRecord = formRedPacket(tenantId, businessId, payerInfoDTO, receiverInfoDTO, relatedObjectByRedPacket, activityId, rewardDetail.getDetailId(), serialNumberStatus.getId(), actionId, remarks, formRecordIdentity(activityId, rewardDetail.getDetailId(), snCodeId));
            IObjectData redPacketRecordDetail = formRedPacketDetail(tenantId, productId, receiverInfoDTO.getAmount(), snCodeId, businessId);
            IObjectData activityRewardDetail = formRewardDetail(tenantId, businessId, TPMActivityRewardDetailFields.RewardType.RED_PACKET, receiverInfoDTO.getAmount(), activityId, receiverInfoDTO.getRewardRole(), receiverInfoDTO.getRewardPerson(), receiverInfoDTO.getUnionId(), snCodeId, productId, redPacketRecordDetail.getTenantId(), redPacketRecordDetail.getDescribeApiName(), redPacketRecordDetail.getId(), receiverInfoDTO.getRewardPersonId(), null, TPMActivityRewardDetailFields.Status.UNDO, null, scanActionStoreId, receiverInfoDTO.getRewardRoleCode(), rewardDetail.getDetailId());

            getRewardDetailDTO.setActivityRewardDetails(Lists.newArrayList(activityRewardDetail));
            getRewardDetailDTO.setRedPacket(redPacketRecord);
            getRewardDetailDTO.setRedPacketDetails(Lists.newArrayList(redPacketRecordDetail));

        } else if (rewardMethod == RewardMethodEnum.REBATE) {
            //todo:rebate
            //from reward detail
            //from rebate
            IObjectData rebateObj = formRebate(tenantId, receiverInfoDTO, activityId);
            IObjectData activityRewardDetail = formRewardDetail(tenantId, businessId, TPMActivityRewardDetailFields.RewardType.REBATE, receiverInfoDTO.getAmount(), activityId, receiverInfoDTO.getRewardRole(), receiverInfoDTO.getRewardPerson(), receiverInfoDTO.getUnionId(), snCodeId, productId, rebateObj.getTenantId(), rebateObj.getDescribeApiName(), rebateObj.getId(), receiverInfoDTO.getRewardPersonId(), null, TPMActivityRewardDetailFields.Status.UNDO, null, scanActionStoreId, receiverInfoDTO.getRewardRoleCode(), rewardDetail.getDetailId());

            getRewardDetailDTO.setActivityRewardDetails(Lists.newArrayList(activityRewardDetail));
            getRewardDetailDTO.setRebate(rebateObj);
        } else {
            log.info("reward detail:{}", rewardDetail);
            throw new ValidateException("未支持的奖励方式，请检查奖励配置。");
        }


        return getRewardDetailDTO;
    }

    private boolean unMatchRewardRole(RewardDetailEntity rewardDetail, ReceiverInfoDTO receiverInfoDTO) {
        if (CollectionUtils.isNotEmpty(rewardDetail.getRewardNode().getRewardRole())) {
            return !rewardDetail.getRewardNode().getRewardRole().contains(receiverInfoDTO.getRewardRoleCode());
        }
        return false;
    }

    private IObjectData getExceptionRewardDetail(String tenantId, RewardDetailEntity rewardDetail, IObjectData serialNumberStatus, String businessId, ReceiverInfoDTO receiverInfoDTO, String activityId, String productId) {
        RewardStrategyEntity rewardStrategy = rewardDetail.getRewardStrategy();
        if (rewardStrategy.getExceptionStrategy() == null || ExceptionStrategyEnum.REWARD.code().equals(rewardStrategy.getExceptionStrategy())) {
            return null;
        }
        String snId = serialNumberStatus.get(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID, String.class);
        String statusExceptionType = fmcgSerialNumberService.getLastStatusExceptionTypes(tenantId, snId, rewardStrategy.getExceptionActionId());

        String actionStoreId = serialNumberStatus.get(FMCGSerialNumberStatusFields.ACCOUNT_ID, String.class);
        if (rewardStrategy.getExceptionTypes().contains(statusExceptionType)) {

            String rewardType = transferRewardType(rewardStrategy.getRewardMethod());
            return formRewardDetail(tenantId, businessId, rewardType, receiverInfoDTO.getAmount(), activityId, receiverInfoDTO.getRewardRole(), receiverInfoDTO.getRewardPerson()
                    , receiverInfoDTO.getUnionId(), snId, productId, serialNumberStatus.getTenantId(), serialNumberStatus.getDescribeApiName(), serialNumberStatus.getId(), receiverInfoDTO.getRewardPersonId(), null, TPMActivityRewardDetailFields.Status.NO_NEED, "因码状态异常未发放", actionStoreId, receiverInfoDTO.getRewardRoleCode(), rewardDetail.getDetailId());
        }

        return null;
    }

    private String transferRewardType(String rewardMethod) {
        RewardMethodEnum method = RewardMethodEnum.get(rewardMethod);
        switch (method) {
            case RED_PACKET:
                return TPMActivityRewardDetailFields.RewardType.RED_PACKET;
            case NONE:
                return TPMActivityRewardDetailFields.RewardType.NONE;
            case PHYSICAL_ITEM:
                return TPMActivityRewardDetailFields.RewardType.PHYSICAL_ITEM;
            case REDUCED_PAYMENT:
                return TPMActivityRewardDetailFields.RewardType.DISCOUNT_PAY;
            default:
                return TPMActivityRewardDetailFields.RewardType.RED_PACKET;
        }
    }


    private IObjectData formRebate(String tenantId, ReceiverInfoDTO receiverInfoDTO, String activityId) {
        IObjectData rebate = new ObjectData();
        rebate.setTenantId(receiverInfoDTO.getTenantId());
        rebate.setId(IdGenerator.get());
        rebate.setDescribeApiName(ApiNames.REBATE_OBJ);
        rebate.setRecordType(CommonFields.RECORD_TYPE__DEFAULT);
        rebate.setOwner(receiverInfoDTO.getStore().getOwner());

        long now = System.currentTimeMillis();

        rebate.set(RebateFields.TOPIC, getActivityName(tenantId, activityId) + "返利单");
        rebate.set(RebateFields.ACCOUNT_ID, receiverInfoDTO.getStore().getId());
        rebate.set(RebateFields.ACTIVE_STATUS, "enable");
        rebate.set(RebateFields.REBATE_TYPE, RewardMethodTypeEnum.RETURN_BY_MONEY.code().equals(receiverInfoDTO.getRewardMethodType()) ? RebateFields.RebateType.MONEY : RebateFields.RebateType.PRODUCT);
        rebate.set(RebateFields.USE_TYPE, receiverInfoDTO.getRebateUseType());
        rebate.set(RebateFields.SUM_AMOUNT, receiverInfoDTO.getAmount());

        rebate.set("start_date", now);
        rebate.set("end_date", receiverInfoDTO.getExpiredTime());
        rebate.set("remark", receiverInfoDTO.getRemarks());

        return rebate;
    }

    private String getActivityName(String tenantId, String activityId) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                SearchQueryUtil.filter(CommonFields.ID, Operator.EQ, Lists.newArrayList(activityId))
        ));

        List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_OBJ, query, Lists.newArrayList(CommonFields.ID, CommonFields.NAME));
        if (CollectionUtils.isEmpty(data)) {
            log.info("can not query activity, tenantId:{},id:{}", tenantId, activityId);
            throw new ValidateException("找不到对应激励活动。");
        }
        return data.get(0).getName();
    }

    private IObjectData getRelatedObjectBySnStatus(String tenantId, IObjectData serialNumberStatus) {
        String dataTenantId = serialNumberStatus.get(FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, String.class);
        String objectId = serialNumberStatus.get(FMCGSerialNumberStatusFields.BUSINESS_OBJECT_ID, String.class);
        String apiName = serialNumberStatus.get(FMCGSerialNumberStatusFields.BUSINESS_OBJECT, String.class);
        if (apiName.equals(ApiNames.ACTIVITY_BARCODE_ACTIVATION_RECORD_OBJ) || apiName.equals("unlock_outer_code_record__c")) {
            dataTenantId = tenantId;
        }
        if (serialNumberStatus.containsField(FMCGSerialNumberStatusFields.VIRTUAL_FLAG)) {
            IObjectData obj = new ObjectData();
            obj.setDescribeApiName(apiName);
            obj.setTenantId(dataTenantId);
            obj.setId(objectId);
            obj.setName(serialNumberStatus.get(FMCGSerialNumberStatusFields.BUSINESS_OBJECT_NAME, String.class));
            return obj;
        }
        return serviceFacade.findObjectDataIncludeDeletedIgnoreFormula(User.systemUser(dataTenantId), objectId, apiName);
    }

    private String formRecordIdentity(String activityId, String detailId, String snId) {
        return String.format("ACTIVITY_REWARD:%s-%s-%s", activityId, snId, detailId);
    }


    protected PayerInfoDTO getPayerInfo(String tenantId, RewardDetailEntity rewardDetail, IObjectData serialNumberStatus) {
        PayerInfoDTO payerInfoDTO = new PayerInfoDTO();
        RewardAccountEntity payAccountConfig = rewardDetail.getRewardPayment().getPayAccount();
        String rewardDimensionEnum = rewardDetail.getRewardNode().getRewardDimension();
        if (PayAccountTypeEnum.MAIN_ACCOUNT.code().equals(payAccountConfig.getAccountType())) {
            payerInfoDTO.setTenantId(tenantId);
            payerInfoDTO.setTenantName(getTenantName(tenantId));
            if (Strings.isNullOrEmpty(payAccountConfig.getFixedAccountId())) {
                payerInfoDTO.setAccount("default");
            } else {
                RewardTransferAccountPO fixedAccount = rewardTransferAccountDAO.getByCode(tenantId, payAccountConfig.getFixedAccountId());
                if (fixedAccount == null) {
                    log.info("未找到指定支出云账户，cloudAccountId:{}", payAccountConfig.getFixedAccountId());
                    throw new ValidateException("未找到指定支出云账户");
                }
                setAccountPlatform(payerInfoDTO, fixedAccount);
                payerInfoDTO.setAccount(JSON.toJSONString(fixedAccount.toDTO()));
            }
        } else if (serialNumberStatus != null && PayAccountTypeEnum.DOWNSTREAM_ACCOUNT.code().equals(payAccountConfig.getAccountType())) {
            String codeId = serialNumberStatus.get(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID, String.class);
            List<IObjectData> statuses = fmcgSerialNumberService.queryAllSerialNumberStatusBySerialNumberId(tenantId, codeId);
            Set<String> relatedTenantIds = statuses.stream().map(v -> v.get(FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, String.class)).collect(Collectors.toSet());
            int targetLevel = RewardDimensionEnum.get(payAccountConfig.getAccountDimension()).order();
            for (IObjectData status : statuses) {
                String currentTenantId = status.get(FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, String.class);
                if (Strings.isNullOrEmpty(currentTenantId)) {
                    log.info("current tenant id is null,status:{}", status);
                    continue;
                }
                int level = enterpriseConnectionService.getLevel(tenantId, currentTenantId, relatedTenantIds);
                if (level == targetLevel) {
                    payerInfoDTO.setTenantId(currentTenantId);
                    payerInfoDTO.setTenantName(getTenantName(currentTenantId));
                    payerInfoDTO.setAccount("default");
                    break;
                }
            }
        } else if (rewardDimensionEnum.equals(RewardDimensionEnum.CONSUMER.code()) && PayAccountTypeEnum.DOWNSTREAM_ACCOUNT.code().equals(payAccountConfig.getAccountType())) {
            throw new RewardFmcgException("90001", I18N.text(I18NKeys.REWARD2_ACTIVITY_REWARD_BASE_0));
        } else {
            log.info("department does not support. account info:{}", JSON.toJSONString(payAccountConfig));
        }
        if (Strings.isNullOrEmpty(payerInfoDTO.getType())) {
            if (rewardDimensionEnum.equals(RewardDimensionEnum.CONSUMER.code())) {
                payerInfoDTO.setType(RedPacketRecordObjFields.TransferorAccountType.WECHAT_MERCHANT);
            } else {
                payerInfoDTO.setType(RedPacketRecordObjFields.TransferorAccountType.CLOUD);
            }
        }
        return payerInfoDTO;
    }

    private void setAccountPlatform(PayerInfoDTO payerInfoDTO, RewardTransferAccountPO account) {
        if (Strings.isNullOrEmpty(account.getPlatform())) {
            if (RewardAccountPlatFormEnum.CLOUD_ACCOUNT.code().equals(account.getPlatform())) {
                payerInfoDTO.setType(RedPacketRecordObjFields.TransferorAccountType.CLOUD);
            } else if (RewardAccountPlatFormEnum.WX_MERCHANT_ACCOUNT.code().equals(account.getPlatform())) {
                payerInfoDTO.setType(RedPacketRecordObjFields.TransferorAccountType.WECHAT_MERCHANT);
            }
        }
    }

    protected ReceiverInfoDTO getReceiverInfo(String tenantId, RewardDetailEntity rewardDetail, IObjectData serialNumberStatus) {
        log.info("rewardDetail:{},serialNumberStatusId:{}", JSON.toJSONString(rewardDetail), serialNumberStatus.getId());
        RewardTargetEnum rewardTarget = RewardTargetEnum.get(rewardDetail.getRewardNode().getRewardTarget());
        ReceiverInfoDTO receiverInfoDTO = new ReceiverInfoDTO();
        String currentTenantId = serialNumberStatus.get(FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, String.class);
        receiverInfoDTO.setTenantId(currentTenantId);
        receiverInfoDTO.setTenantName(getTenantName(receiverInfoDTO.getTenantId()));
        receiverInfoDTO.setType(RedPacketRecordObjFields.TransfereeAccountType.WECHAT);
        String currentAccountId = serialNumberStatus.get(FMCGSerialNumberStatusFields.ACCOUNT_ID, String.class);

        //奖励给门店且这条是门店流转时候
        if (rewardDetail.getRewardNode().getRewardDimension().equals(RewardDimensionEnum.STORE.code()) && !Strings.isNullOrEmpty(currentAccountId)
                || rewardDetail.getRewardNode().getRewardTarget().equals(RewardTargetEnum.STORE_OWNER.code())) {
            receiverInfoDTO.setStore(serviceFacade.findObjectDataIgnoreAll(User.systemUser(currentTenantId), currentAccountId, ApiNames.ACCOUNT_OBJ));
        } else if (currentTenantId.equals(tenantId)) {
            receiverInfoDTO.setStore(null);
        } else {
            receiverInfoDTO.setStore(serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), enterpriseConnectionService.getStoreId(tenantId, currentTenantId), ApiNames.ACCOUNT_OBJ));
        }
        fillReceiverUserInfo(receiverInfoDTO, rewardTarget, tenantId, currentTenantId, currentAccountId, serialNumberStatus);

        //deal amount
        RewardStrategyEntity rewardStrategyEntity = rewardDetail.getRewardStrategy();
        receiverInfoDTO.setDistributionType(rewardStrategyEntity.getDistributeMethod());
        receiverInfoDTO.setRewardMethod(rewardStrategyEntity.getRewardMethod());
        receiverInfoDTO.setRewardMethodType(rewardStrategyEntity.getRewardMethodType());
        fillReceiverAmount(tenantId, receiverInfoDTO, rewardDetail);
        return receiverInfoDTO;
    }

    private void fillReceiverUserInfo(ReceiverInfoDTO receiverInfoDTO, RewardTargetEnum rewardTarget, String tenantId, String currentTenantId, String currentAccountId, IObjectData serialNumberStatus) {
        if (rewardTarget == RewardTargetEnum.SCAN_EXECUTOR) {
            String userId = serialNumberStatus.get(FMCGSerialNumberStatusFields.PERSONNEL_ID, String.class);
            String userApiName = serialNumberStatus.get(FMCGSerialNumberStatusFields.PERSONNEL_API_NAME, String.class, ApiNames.PERSONNEL_OBJ);
            IObjectData user = serviceFacade.findObjectDataIgnoreAll(User.systemUser(currentTenantId), userId, userApiName);
            fillOuterTenantId(user);
            fillReceiverRoleInfo(user, receiverInfoDTO);
            fillReceiverIdentityInfo(tenantId, user, receiverInfoDTO);
        } else if (rewardTarget == RewardTargetEnum.ENTERPRISE_BOSS) {
            Integer userId = organizationService.getDepartment(Integer.parseInt(currentTenantId), DepartmentDto.COMPANY_DEPARTMENT_ID).getPrincipalId();
            if (userId != null) {
                IObjectData user = serviceFacade.findObjectDataIgnoreAll(User.systemUser(currentTenantId), String.valueOf(userId), ApiNames.PERSONNEL_OBJ);
                fillReceiverRoleInfo(user, receiverInfoDTO);
                fillReceiverIdentityInfo(tenantId, user, receiverInfoDTO);
            } else {
                log.info("部门负责人为空！tenantId:{}", currentTenantId);
            }
        } else if (rewardTarget == RewardTargetEnum.STORE_BOSS) {
            //todo:店老板
            IObjectData user = getStoreMaster(currentTenantId, receiverInfoDTO.getStore().getId());
            fillReceiverRoleInfo(user, receiverInfoDTO);
            fillReceiverIdentityInfo(tenantId, user, receiverInfoDTO);
        } else if (rewardTarget == RewardTargetEnum.STORE_OWNER) {
            //需要使用签收门店
            IObjectData user = getStoreOwner(currentTenantId, currentAccountId);
            if (user != null) {
                fillReceiverRoleInfo(user, receiverInfoDTO);
                fillReceiverIdentityInfo(tenantId, user, receiverInfoDTO);
            } else {
                log.info("门店负责人为空！tenantId:{}，storeId:{}", currentTenantId, receiverInfoDTO.getStore().getId());
            }
        }
    }

    private IObjectData getStoreOwner(String currentTenantId, String id) {
        IObjectData currentStore = serviceFacade.findObjectData(User.systemUser(currentTenantId), id, ApiNames.ACCOUNT_OBJ);
        if (CollectionUtils.isNotEmpty(currentStore.getOwner())) {
            if ("-10000".equals(currentStore.getOwner().get(0))) {
                log.info("门店负责人为系统！tenantId:{}，storeId:{}，ownerId:{}", currentTenantId, currentStore.getId(), currentStore.getOwner());
                return null;
            }
            return serviceFacade.findObjectData(User.systemUser(currentTenantId), currentStore.getOwner().get(0), ApiNames.PERSONNEL_OBJ);
        }
        return null;
    }

    private void fillOuterTenantId(IObjectData user) {
        if (ApiNames.CONTACT_OBJ.equals(user.getDescribeApiName())) {
            if (Strings.isNullOrEmpty(user.getOutTenantId())) {
                log.info("补偿下外部负责人。");
                enterpriseConnectionService.getPublicEmployeeIdByContactObj(user);
            }
        }
    }

    protected void fillReceiverAmount(String tenantId, ReceiverInfoDTO receiverInfoDTO, RewardDetailEntity rewardDetail) {
        RewardStrategyEntity rewardStrategyEntity = rewardDetail.getRewardStrategy();
        if (RewardMethodEnum.RED_PACKET.code().equals(rewardStrategyEntity.getRewardMethod())) {
            if (Strings.isNullOrEmpty(rewardStrategyEntity.getRewardMethodType()) || RewardMethodTypeEnum.SOLID_RED_PACKET.code().equals(rewardStrategyEntity.getRewardMethodType())) {
                receiverInfoDTO.setAmount(new BigDecimal(rewardStrategyEntity.getRewardQuantity()).setScale(2, RoundingMode.DOWN));
                validateSolidPacket(tenantId, rewardDetail.getDetailId(), rewardStrategyEntity);
            } else if (RewardMethodTypeEnum.RANDOM_RED_PACKET.code().equals(rewardStrategyEntity.getRewardMethodType())) {
                RandomRewardLevelEntity level;
                rewardStrategyEntity.setUseRandomTopLevel(Boolean.TRUE.equals(receiverInfoDTO.getRandomTopLevel()));
                level = dealRandomRedPacket(tenantId, rewardDetail.getDetailId(), rewardStrategyEntity);
                if (level != null) {
                    receiverInfoDTO.setAmount(BigDecimal.valueOf(level.getRewardAmount()));
                    receiverInfoDTO.setRandomPacketLevel(String.valueOf(level.getLevel()));
                } else {
                    throw new ValidateException("获取随机红包异常。");//IgnoreI18n
                }
            }
            if (RewardDistributeMethodEnum.WITHDRAW.code().equals(rewardStrategyEntity.getDistributeMethod())) {
                if (rewardStrategyEntity.getExpiredDays() != null) {
                    receiverInfoDTO.setExpiredTime(getTimeAfterNDays(System.currentTimeMillis(), rewardStrategyEntity.getExpiredDays()));
                }
            }
        } else if (RewardMethodEnum.REBATE.code().equals(rewardStrategyEntity.getRewardMethod())) {
            receiverInfoDTO.setAmount(new BigDecimal(rewardStrategyEntity.getRewardQuantity()).setScale(2, RoundingMode.DOWN));
            receiverInfoDTO.setExpiredTime(getTimeAfterNDays(System.currentTimeMillis(), rewardStrategyEntity.getExpiredDays()));
            receiverInfoDTO.setRebateUseType(rewardStrategyEntity.getRebateUseType());
        }
        receiverInfoDTO.setRemarks(rewardStrategyEntity.getRewardRemark());
    }

    private long getTimeAfterNDays(long currentTime, int days) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(currentTime), ZoneOffset.ofHours(8));
        return localDateTime.plusDays(days + 1).withHour(0).withMinute(0).withSecond(0).withNano(0).toInstant(ZoneOffset.ofHours(8)).toEpochMilli() - 1;
    }

    private void validateSolidPacket(String tenantId, String detailId, RewardStrategyEntity rewardStrategyEntity) {
        if (Objects.nonNull(rewardStrategyEntity.getRewardCount())) {
            Map<String, Integer> usedCountMap = countSentRedPacket(tenantId, detailId, RewardMethodTypeEnum.SOLID_RED_PACKET.code());
            int usedCount = usedCountMap.getOrDefault("default", 0);
            if (usedCount >= rewardStrategyEntity.getRewardCount()) {
                throw new RewardFmcgException("90002", I18N.text(I18NKeys.REWARD2_ACTIVITY_REWARD_BASE_1));
            }
        }
    }

    //固定红包
    protected Map<String, Integer> countSentRedPacket(String tenantId, String rewardDetailId, String redPacketType) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 2000, Lists.newArrayList(
                SearchQueryUtil.filter(RedPacketRecordObjFields.REWARD_DETAIL_ID, Operator.EQ, Lists.newArrayList(rewardDetailId)),
                SearchQueryUtil.filter(RedPacketRecordObjFields.RED_PACKET_TYPE, Operator.EQ, Lists.newArrayList(redPacketType))
        ));
        Map<String, Integer> levelCountMap = new HashMap<>();
        List<IObjectData> result = serviceFacade.aggregateFindBySearchQuery(tenantId, query, ApiNames.RED_PACKET_RECORD_OBJ, RedPacketRecordObjFields.RED_PACKET_LEVEL, "count", "");
        if (CollectionUtils.isNotEmpty(result)) {
            result.forEach(data -> {
                String level = data.get(RedPacketRecordObjFields.RED_PACKET_LEVEL, String.class, "default");
                int count = data.get("groupbycount", Integer.class, 0);
                levelCountMap.put(level, levelCountMap.getOrDefault(level, 0) + count);
            });
        }
        return levelCountMap;
    }

    protected RandomRewardLevelEntity dealRandomRedPacket(String tenantId, String detailId, RewardStrategyEntity rewardStrategyEntity) {
        Map<String, Integer> usedCountMap = countSentRedPacket(tenantId, detailId, RewardMethodTypeEnum.RANDOM_RED_PACKET.code());

        // 按概率随机模式
        if (rewardStrategyEntity.getRandomRewardMode() != null &&
                rewardStrategyEntity.getRandomRewardMode().equals(RandomRewardModeEnum.PROBABILITY.code())) {
            return dealRandomProbabilityRedPacketAndReturnLevel(rewardStrategyEntity);
        }

        // 默认按数量随机模式
        if (rewardStrategyEntity.isUseRandomTopLevel()) {
            RandomRewardLevelEntity topLevel = rewardStrategyEntity.getRandomRewardLevels().stream().max(Comparator.comparing(RandomRewardLevelEntity::getLevel)).orElse(null);
            if (!Objects.isNull(topLevel) && topLevel.getRewardCount() != null && topLevel.getRewardCount() != 0) {
                int rewardedCount = usedCountMap.getOrDefault(String.valueOf(topLevel.getLevel()), 0);
                if (topLevel.getRewardCount() > rewardedCount) {
                    log.info("force random top level reward success");
                    return topLevel;
                }
            }
        }

        int total = 0;
        for (RandomRewardLevelEntity rewardLevel : rewardStrategyEntity.getRandomRewardLevels()) {
            if (rewardLevel.getRewardCount() == null || rewardLevel.getRewardCount() == 0) {
                continue;
            }
            int usedMaxNumber = usedCountMap.getOrDefault(String.valueOf(rewardLevel.getLevel()), 0);
            usedMaxNumber = rewardLevel.getRewardCount() > usedMaxNumber ? usedMaxNumber : rewardLevel.getRewardCount();
            total += rewardLevel.getRewardCount() - usedMaxNumber;
        }
        int target = RandomUtil.nextInt(total) + 1;
        if (total <= 0) {
            throw new RewardFmcgException("90002", "红包已经用尽，欢迎下次参加。");
        }
        int accumulativeTotal = 0;
        for (RandomRewardLevelEntity rewardLevel : rewardStrategyEntity.getRandomRewardLevels()) {
            if (rewardLevel.getRewardCount() == null || rewardLevel.getRewardCount() == 0) {
                continue;
            }
            int usedMaxNumber = usedCountMap.getOrDefault(String.valueOf(rewardLevel.getLevel()), 0);
            usedMaxNumber = rewardLevel.getRewardCount() > usedMaxNumber ? usedMaxNumber : rewardLevel.getRewardCount();
            accumulativeTotal += rewardLevel.getRewardCount() - usedMaxNumber;
            if (accumulativeTotal >= target) {
                return rewardLevel;
            }
        }
        return null;
    }


    // 新增方法：按概率随机并返回对应的等级实体
    protected RandomRewardLevelEntity dealRandomProbabilityRedPacketAndReturnLevel(RewardStrategyEntity rewardStrategyEntity) {
        double value = RandomUtil.nextInt(10000) / 100.0;
        double sumProbability = 0;
        for (RandomRewardLevelEntity rewardLevel : rewardStrategyEntity.getRandomRewardLevels()) {
            if (rewardLevel.getRewardProbability() == 0) {
                continue;
            }
            sumProbability += rewardLevel.getRewardProbability();
            if (value < sumProbability) {
                return rewardLevel;
            }
        }
        return null;
    }

    /**
     * 获取是角色是店老板的联系人
     *
     * @param tenantId
     * @param storeId
     * @return
     */
    public IObjectData getStoreMaster(String tenantId, String storeId) {
        List<IObjectData> contacts = getContactsByStoreId(tenantId, storeId);
        Map<String, IObjectData> outUserId2Contacts = new HashMap<>();
        log.info("contacts:{}", JSON.toJSONString(contacts.stream().map(DBRecord::getId).collect(Collectors.toList())));
        List<String> outerUserIds = contacts.stream().map(v -> {
            String id = enterpriseConnectionService.getPublicEmployeeIdByContactObj(v);
            if (!Strings.isNullOrEmpty(id)) {
                outUserId2Contacts.put(id, v);
                return id;
            } else {
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
        log.info("outerUserIds:{}", JSON.toJSONString(outerUserIds));
        List<IObjectData> publicEmployees = serviceFacade.findObjectDataByIdsExcludeInvalidIgnoreAll(tenantId, outerUserIds, ApiNames.PUBLIC_EMPLOYEE_OBJ);
        IObjectData storeMaster = null;
        if (CollectionUtils.isNotEmpty(publicEmployees)) {
            for (IObjectData employee : publicEmployees) {
                if (Boolean.TRUE.equals(employee.get(PublicEmployeeFields.RELATION_OWNER))) {
                    storeMaster = outUserId2Contacts.get(employee.getId());
                    if (Strings.isNullOrEmpty(storeMaster.getOutTenantId())) {
                        log.info("contact has no outerTenantId,contactId:{},outerTenantId:{}", storeMaster.getId(), employee.get(PublicEmployeeFields.OUTER_TENANT_ID, String.class));
                        storeMaster.setOutTenantId(employee.get(PublicEmployeeFields.OUTER_TENANT_ID, String.class));
                    }
                    if (CollectionUtils.isEmpty(storeMaster.getOutOwner())) {
                        log.info("contact has no outerOwner,contactId:{},employeeId:{}", storeMaster.getId(), employee.getId());
                        storeMaster.setOutOwner(Lists.newArrayList(employee.getId()));
                    }
                    break;
                }
            }
        }
        if (storeMaster == null) {
            log.info("没有找到门店老板，tenantId:{},storeId:{}", tenantId, storeId);
        }
        return storeMaster;
    }

    private String getStoreMasterCode(String tenantId,
                                      String outerUserId) {
        Boolean isMaster = false;
        try {
            IObjectData employeeObj = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), outerUserId, ApiNames.PUBLIC_EMPLOYEE_OBJ);
            if (employeeObj != null) {
                isMaster = employeeObj.get(PublicEmployeeFields.RELATION_OWNER, Boolean.class);
            }
        } catch (Exception ex) {
            log.info("find employeeObj error", ex);
        }
        return Boolean.TRUE.equals(isMaster) ? "1" : "0";
    }

    protected List<IObjectData> getContactsByStoreId(String tenantId, String storeId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);

        Filter storeFilter = new Filter();
        storeFilter.setFieldName(ContactFields.ACCOUNT_ID);
        storeFilter.setOperator(Operator.EQ);
        storeFilter.setFieldValues(Lists.newArrayList(storeId));


        query.setFilters(Lists.newArrayList(storeFilter));

        return CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.CONTACT_OBJ, query);
    }

    public void fillReceiverRoleInfo(IObjectData user, ReceiverInfoDTO receiverInfoDTO) {
        if (user == null) {
            log.info("接收用户无法找到。");
            return;
        }
        log.info("user:{}", user);
        receiverInfoDTO.setRewardPerson(user.getName());
        //填充主角色
        if (ApiNames.CONTACT_OBJ.equals(user.getDescribeApiName())) {
            String outerTenantId = user.getOutTenantId();
            String outOwnerId = enterpriseConnectionService.getPublicEmployeeIdByContactObj(user);
            user.set(CommonFields.SELF_DEFINE_OUTER_USER_ID, outOwnerId);
            setOuterRole(user.getTenantId(), outerTenantId, outOwnerId, receiverInfoDTO);
        } else if (ApiNames.PUBLIC_EMPLOYEE_OBJ.equals(user.getDescribeApiName())) {
            String outerTenantId = user.get(PublicEmployeeFields.OUTER_TENANT_ID, String.class);
            String outOwnerId = user.getId();
            setOuterRole(user.getTenantId(), outerTenantId, outOwnerId, receiverInfoDTO);
        } else {
            SimpleDTO role = roleService.getMainRoleByEmployeeId(user.getTenantId(), user.getId());
            if (role != null) {
                receiverInfoDTO.setRewardRole(role.getName());
                receiverInfoDTO.setRewardRoleCode(role.getValue() + "_crm");
            } else {
                log.info("can not find main role.user:{}", user);
            }
        }
    }

    private void setOuterRole(String tenantId, String outerTenantId, String outOwnerId, ReceiverInfoDTO receiverInfoDTO) {
        Map<String, List<SimpleDTO>> roleMap = roleService.queryOuterRoleByOuterInfo(tenantId, outerTenantId, Lists.newArrayList(outOwnerId));
        List<SimpleDTO> roleList = roleMap.getOrDefault(outOwnerId, Lists.newArrayList());
        roleList.stream().filter(role -> role.getType().equals("default")).forEach(role -> {
            receiverInfoDTO.setRewardRole(role.getName());
            receiverInfoDTO.setStoreBossTag(getStoreMasterCode(tenantId, outOwnerId));
            receiverInfoDTO.setRewardRoleCode(role.getValue() + "_outer");
        });
    }

    public void fillReceiverIdentityInfo(String upperTenantId, IObjectData user, ReceiverInfoDTO receiverInfoDTO) {
        if (user == null) {
            log.info("接收用户无法找到。tenantId:{}", upperTenantId);
            return;
        }
        if (ApiNames.PUBLIC_EMPLOYEE_OBJ.equals(user.getDescribeApiName())) {
            String contactId = user.get(PublicEmployeeFields.CONTRACT_ID, String.class);
            IObjectData contact = serviceFacade.findObjectDataIgnoreAll(User.systemUser(user.getTenantId()), contactId, ApiNames.CONTACT_OBJ);
            if (Strings.isNullOrEmpty(contact.getOutTenantId())) {
                contact.setOutTenantId(user.get(PublicEmployeeFields.OUTER_TENANT_ID, String.class));
                contact.set(CommonFields.SELF_DEFINE_OUTER_USER_ID, user.getId());
                contact.set(CommonFields.OUT_OWNER, Lists.newArrayList(user.getId()));
            }
            user = contact;
        }
        if (ApiNames.CONTACT_OBJ.equals(user.getDescribeApiName())) {
            String appId = user.get(ContactFields.MENGNIU_WECHAT_APP_ID, String.class);
            if (Strings.isNullOrEmpty(appId)) {
                appId = getDefaultAppId(upperTenantId);
            }
            receiverInfoDTO.setWxAppId(appId);
            receiverInfoDTO.setName(user.get(ContactFields.NAME, String.class));
            receiverInfoDTO.setPhone(user.get(ContactFields.MOBILE1, String.class));
            receiverInfoDTO.setIdCard(user.get(ContactFields.MENGNIU_ID_CARD_NUMBER, String.class));
            receiverInfoDTO.setWxOpenId(user.get(ContactFields.MENGNIU_WECHAT_OPEN_ID, String.class));
            receiverInfoDTO.setUnionId(user.get(ContactFields.MENGNIU_WX_UNION_ID, String.class));
            if (!Strings.isNullOrEmpty(user.getOutTenantId())) {
                receiverInfoDTO.setRewardPersonId(user.getOutTenantId() + '.' + enterpriseConnectionService.getPublicEmployeeIdByContactObj(user));
            }
        } else if (ApiNames.PERSONNEL_OBJ.equals(user.getDescribeApiName())) {
            String appId = user.get(PersonnelFields.MENGNIU_WECHAT_APP_ID, String.class);
            if (Strings.isNullOrEmpty(appId)) {
                appId = getDefaultAppId(upperTenantId);
            }
            receiverInfoDTO.setWxAppId(appId);
            receiverInfoDTO.setName(user.get(PersonnelFields.FULL_NAME, String.class));
            receiverInfoDTO.setPhone(user.get(PersonnelFields.PHONE, String.class));
            receiverInfoDTO.setIdCard(user.get(PersonnelFields.MENGNIU_IDCARD, String.class));
            receiverInfoDTO.setWxOpenId(user.get(PersonnelFields.MENGNIU_WECHAT_OPEN_ID, String.class));
            receiverInfoDTO.setUnionId(user.get(ContactFields.MENGNIU_WX_UNION_ID, String.class));
            receiverInfoDTO.setRewardPersonId(user.getTenantId() + '.' + user.getId());
        }
    }

    private String getDefaultAppId(String upperTenantId) {
        MengNiuTenantInformation tenant = tenantHierarchyService.load(upperTenantId);
        if (tenant == null) {
            return null;
        }
        return tenantHierarchyService.findManufacturerByTenantId(tenant.getManufacturer().getTenantId()).getRedPacketAccountWeChatAppId();
    }

    public String getTenantName(String tenantId) {
        GetSimpleEnterpriseArg arg = new GetSimpleEnterpriseArg();
        arg.setEnterpriseId(Integer.parseInt(tenantId));
        return enterpriseEditionService.getSimpleEnterprise(arg).getSimpleEnterprise().getEnterpriseName();
    }


    protected IObjectData formRewardDetail(String tenantId, String businessId, String rewardType, BigDecimal rewardAmount, String activityId, String rewardRole, String rewardPerson, String unionId, String snCodeId, String productId, String relatedTenantId, String relatedApiName, String relatedDataId, String rewardPersonId, String prizeId, String status, String distributionInstructions, String accountId, String rewardRoleCode, String rewardDetailId) {
        IObjectData rewardDetail = new ObjectData();
        rewardDetail.setTenantId(tenantId);
        rewardDetail.setId(IdGenerator.get());
        rewardDetail.setOwner(Lists.newArrayList("-10000"));
        rewardDetail.setDescribeApiName(ApiNames.TPM_ACTIVITY_REWARD_DETAIL_OBJ);
        rewardDetail.setRecordType(CommonFields.RECORD_TYPE__DEFAULT);
        rewardDetail.set(TPMActivityRewardDetailFields.BUSINESS_ID, businessId);
        rewardDetail.set(TPMActivityRewardDetailFields.REWARD_PERSON_ID, rewardPersonId);
        rewardDetail.set(TPMActivityRewardDetailFields.REWARD_VALUE, rewardAmount);
        rewardDetail.set(TPMActivityRewardDetailFields.REWARD_TYPE, rewardType);
        rewardDetail.set(TPMActivityRewardDetailFields.ACTIVITY_ID, activityId);
        rewardDetail.set(TPMActivityRewardDetailFields.REWARD_PART, rewardRole);
        rewardDetail.set(TPMActivityRewardDetailFields.REWARDED_PERSON, rewardPerson);
        rewardDetail.set(TPMActivityRewardDetailFields.SERIAL_NUMBER_ID, snCodeId);
        rewardDetail.set(TPMActivityRewardDetailFields.PRODUCT_ID, productId);
        rewardDetail.set(TPMActivityRewardDetailFields.PRIZE_NAME, prizeId);
        rewardDetail.set(TPMActivityRewardDetailFields.RELATED_OBJECT_API_NAME, relatedApiName);
        rewardDetail.set(TPMActivityRewardDetailFields.RELATED_OBJECT_DATA_ID, relatedDataId);
        rewardDetail.set(TPMActivityRewardDetailFields.RELATED_OBJECT_DATA_TENANT_ID, relatedTenantId);
        rewardDetail.set(TPMActivityRewardDetailFields.WX_UNION_ID, unionId);
        rewardDetail.set(TPMActivityRewardDetailFields.ACCOUNT_ID__C, accountId);
        rewardDetail.set(TPMActivityRewardDetailFields.ACCOUNT_ID, accountId);
        rewardDetail.set(TPMActivityRewardDetailFields.REWARD_PART_CODE, rewardRoleCode);
        rewardDetail.set(TPMActivityRewardDetailFields.REWARD_DETAIL_ID, rewardDetailId);

        rewardDetail.set(TPMActivityRewardDetailFields.REWARD_TIME, System.currentTimeMillis());
        rewardDetail.set(TPMActivityRewardDetailFields.STATUS, status);
        rewardDetail.set(TPMActivityRewardDetailFields.WX_UNION_ID, unionId);
        rewardDetail.set(TPMActivityRewardDetailFields.DISTRIBUTION_INSTRUCTIONS, distributionInstructions);
        return rewardDetail;
    }


    public IObjectData formRedPacket(String tenantId, String businessId, PayerInfoDTO payerInfo, ReceiverInfoDTO receiverInfo, IObjectData relatedObject, String activityId, String rewardDetailId, String snStatusId, String actionId, String remarks, String recordIdentity) {
        IObjectData master = new ObjectData();
        master.setTenantId(tenantId);
        master.setId(IdGenerator.get());
        master.setDescribeApiName(ApiNames.RED_PACKET_RECORD_OBJ);
        master.setRecordType(CommonFields.RECORD_TYPE__DEFAULT);
        master.setOwner(Lists.newArrayList("-10000"));
        master.set(RedPacketRecordObjFields.BUSINESS_ID, businessId);
        master.set(RedPacketRecordObjFields.ACTIVITY_ID, activityId);
        master.set(RedPacketRecordObjFields.REWARD_DETAIL_ID, rewardDetailId);
        master.set(RedPacketRecordObjFields.SERIAL_NUMBER_STATUS_ID, snStatusId);
        master.set(RedPacketRecordObjFields.REWARD_AMOUNT, receiverInfo.getAmount());
        master.set(RedPacketRecordObjFields.REWARD_PART, receiverInfo.getRewardRole());
        master.set(RedPacketRecordObjFields.REWARD_PART_CODE, receiverInfo.getStoreBossTag());
        master.set(RedPacketRecordObjFields.REWARDED_PERSON, receiverInfo.getRewardPerson());
        master.set(RedPacketRecordObjFields.REWARD_TIME, System.currentTimeMillis());
        master.set(RedPacketRecordObjFields.RELATED_OBJECT_TENANT_ID, relatedObject.getTenantId());
        master.set(RedPacketRecordObjFields.RELATED_OBJECT_API_NAME, relatedObject.getDescribeApiName());
        master.set(RedPacketRecordObjFields.RELATED_OBJECT_DATA_ID, relatedObject.getId());
        master.set(RedPacketRecordObjFields.RELATED_OBJECT_NAME, relatedObject.getName());
        master.set(RedPacketRecordObjFields.EVENT_TYPE, RedPacketRecordObjFields.EventType.REWARD);
        if (PHYSICAL_RED_PACKET_TRIGGER_ID.equals(actionId)) {
            master.set(RedPacketRecordObjFields.TRIGGER_EVENT, actionId);
        } else {
            master.set(RedPacketRecordObjFields.TRIGGER_EVENT, fmcgSerialNumberService.getActionUniqueIdByActionId(tenantId, actionId));
        }
        if (receiverInfo.getStore() != null) {
            master.set(RedPacketRecordObjFields.ACCOUNT_ID, receiverInfo.getStore().getId());
            master.set(RedPacketRecordObjFields.ACCOUNT_NAME, receiverInfo.getStore().getName());
            master.set(RedPacketRecordObjFields.ACCOUNT_TENANT_ID, receiverInfo.getStore().getTenantId());
        }
        master.set(RedPacketRecordObjFields.TRANSFEROR_PHONE, payerInfo.getPhone());
        master.set(RedPacketRecordObjFields.TRANSFEROR_ACCOUNT_TYPE, payerInfo.getType());
        master.set(RedPacketRecordObjFields.TRANSFEROR_NAME, payerInfo.getName());
        master.set(RedPacketRecordObjFields.TRANSFEROR_ID, payerInfo.getIdCard());
        master.set(RedPacketRecordObjFields.TRANSFEROR_WECHAT_OPEN_ID, payerInfo.getWxOpenId());
        master.set(RedPacketRecordObjFields.TRANSFEROR_WECHAT_APP_ID, payerInfo.getWxAppId());
        master.set(RedPacketRecordObjFields.TRANSFEROR_ACCOUNT, payerInfo.getAccount());
        master.set(RedPacketRecordObjFields.TRANSFEROR_TENANT_ID, payerInfo.getTenantId());
        master.set(RedPacketRecordObjFields.TRANSFEROR_TENANT_NAME, payerInfo.getTenantName());
        master.set(RedPacketRecordObjFields.TRANSFEREE_ID, receiverInfo.getIdCard());
        master.set(RedPacketRecordObjFields.TRANSFEREE_NAME, receiverInfo.getName());
        master.set(RedPacketRecordObjFields.TRANSFEREE_PHONE, receiverInfo.getPhone());
        master.set(RedPacketRecordObjFields.TRANSFEREE_ACCOUNT_TYPE, receiverInfo.getType());
        master.set(RedPacketRecordObjFields.TRANSFEREE_TENANT_NAME, receiverInfo.getTenantName());
        master.set(RedPacketRecordObjFields.TRANSFEREE_TENANT_ID, receiverInfo.getTenantId());
        master.set(RedPacketRecordObjFields.TRANSFEREE_WECHAT_OPEN_ID, receiverInfo.getWxOpenId());
        master.set(RedPacketRecordObjFields.TRANSFEREE_WECHAT_APP_ID, receiverInfo.getWxAppId());
        master.set(RedPacketRecordObjFields.TRANSFEREE_WX_UNION_ID, receiverInfo.getUnionId());
        master.set(RedPacketRecordObjFields.TRADING_ID, IdentityIdGenerator.formPaymentIdentityId());
        master.set(RedPacketRecordObjFields.PAYMENT_STATUS, RedPacketRecordObjFields.PaymentStatus.INIT);
        master.set(RedPacketRecordObjFields.REWARD_TIME, System.currentTimeMillis());
        master.set(RedPacketRecordObjFields.REWARDED_PERSON_ID, receiverInfo.getRewardPersonId());
        master.set(RedPacketRecordObjFields.RED_PACKET_TYPE, receiverInfo.getRewardMethodType());
        master.set(RedPacketRecordObjFields.RED_PACKET_LEVEL, receiverInfo.getRandomPacketLevel());
        master.set(RedPacketRecordObjFields.REMARKS, remarks);
        if (!Strings.isNullOrEmpty(receiverInfo.getDistributionType()) && receiverInfo.getDistributionType().equals(RewardDistributeMethodEnum.WITHDRAW.code())) {
            master.set(RedPacketRecordObjFields.WITHDRAWAL_STATUS, RedPacketRecordObjFields.WithdrawalStatus.AWAIT);
            master.set(RedPacketRecordObjFields.DISTRIBUTE_WAY, RedPacketRecordObjFields.DistributeWay.ARTIFICIAL);
        } else {
            master.set(RedPacketRecordObjFields.DISTRIBUTE_WAY, RedPacketRecordObjFields.DistributeWay.AUTO);
            master.set(RedPacketRecordObjFields.WITHDRAWAL_STATUS, RedPacketRecordObjFields.WithdrawalStatus.NO_NEED);
        }
        master.set(RedPacketRecordObjFields.RED_PACKET_STATUS, RedPacketRecordObjFields.RedPacketStatus.EFFECTUATE);
        master.set(RedPacketRecordObjFields.EXPIRATION_TIME, receiverInfo.getExpiredTime());
        master.set(RedPacketRecordObjFields.RECORD_IDENTITY, recordIdentity);

        return master;
    }

    public IObjectData formRedPacketDetail(String tenantId, String productId, BigDecimal amount, String snId, String businessId) {
        IObjectData detail = new ObjectData();
        detail.setTenantId(tenantId);
        detail.setId(IdGenerator.get());
        detail.setOwner(Lists.newArrayList("-10000"));
        detail.setDescribeApiName(ApiNames.RED_PACKET_RECORD_DETAIL_OBJ);
        detail.setRecordType(CommonFields.RECORD_TYPE__DEFAULT);
        detail.set(RedPacketRecordDetailObjFields.PRODUCT_ID, productId);
        detail.set(RedPacketRecordDetailObjFields.RED_PACKET_AMOUNT, amount);
        detail.set(RedPacketRecordDetailObjFields.SERIAL_NUMBER_ID, snId);
        detail.set(RedPacketRecordDetailObjFields.BUSINESS_ID, businessId);
        return detail;
    }

    protected void saveRewardData(String tenantId, String bizId, List<IObjectData> rewardDetails, Map<IObjectData, List<IObjectData>> redPacketDetails, IObjectData physicalItem, Map<String, List<IObjectData>> othersObjMap, String bizType) {
        //todo:非事务的对象怎么处理othersObjMap *********
        if (MapUtils.isNotEmpty(othersObjMap)) {
            othersObjMap.forEach((key, value) -> {
                if (CollectionUtils.isNotEmpty(value)) {
                    value.stream().collect(groupingBy(IObjectData::getTenantId)).forEach((apiName, list) -> {
                        try {
                            if (Strings.isNullOrEmpty(list.get(0).getTenantId())) {
                                throw new RewardFmcgException("90101", "保存数据时租户id为空");
                            }
                            serviceFacade.bulkSaveObjectData(list, User.systemUser(list.get(0).getTenantId()));
                        } catch (Exception e) {
                            log.error("保存对象失败：{}.list:{}", apiName, list, e);
                            rollbackOthersMap(othersObjMap);
                            throw e;
                        }
                    });
                }
            });
        }
        transactionProxy.run(() -> {
            User user = User.systemUser(tenantId);
            if (physicalItem != null) {
                serviceFacade.saveObjectData(user, physicalItem);
            }
            if (CollectionUtils.isNotEmpty(rewardDetails)) {
                serviceFacade.bulkSaveObjectData(rewardDetails, user);
            }
            Map<String, IObjectDescribe> describeMap = new HashMap<>();
            describeMap.put(ApiNames.RED_PACKET_RECORD_DETAIL_OBJ, serviceFacade.findObject(tenantId, ApiNames.RED_PACKET_RECORD_DETAIL_OBJ));
            redPacketDetails.forEach((master, details) -> {
                Map<String, List<IObjectData>> detailsMap = new HashMap<>();
                detailsMap.put(ApiNames.RED_PACKET_RECORD_DETAIL_OBJ, details);
                SaveMasterAndDetailData.Arg saveArg = SaveMasterAndDetailData.Arg.builder().masterObjectData(master).detailObjectData(detailsMap).objectDescribes(describeMap).build();
                serviceFacade.saveMasterAndDetailData(user, saveArg);
            });
            bizCodeDAO.setStatus(tenantId, bizId, BizCodeStatusEnum.USED);

        }, () -> rollbackOthersMap(othersObjMap));


        for (IObjectData redPacket : redPacketDetails.keySet()) {
            String distributeWay = redPacket.get(RedPacketRecordObjFields.DISTRIBUTE_WAY, String.class);
            if (RedPacketRecordObjFields.DistributeWay.AUTO.equals(distributeWay)) {
                rewardRuleRedPacketSetter.setUpdateStatusTask(redPacket.getTenantId(), redPacket.getId());
            } else if (RedPacketRecordObjFields.DistributeWay.ARTIFICIAL.equals(distributeWay)) {
                redPacketStatusSetter.setUpdateStatusTask(redPacket.getTenantId(), redPacket.getId(), redPacket.get(RedPacketRecordObjFields.EXPIRATION_TIME, Long.class, 0L));
            }
        }
        if (!Strings.isNullOrEmpty(bizType)) {
            rewardExceptionRecordService.writeRecord(tenantId, bizId, bizType, "", "2");
        }


    }

    //todo:查询存在的话删除obj
    private void rollbackOthersMap(Map<String, List<IObjectData>> othersObjMap) {
        if (MapUtils.isEmpty(othersObjMap)) {
            return;
        }
        othersObjMap.forEach((key, value) -> {
            if (CollectionUtils.isNotEmpty(value)) {
                value.stream().collect(groupingBy(IObjectData::getTenantId)).forEach((apiName, list) -> {
                    try {
                        serviceFacade.bulkDeleteDirect(list, User.systemUser(list.get(0).getTenantId()));
                    } catch (Exception e) {
                        log.error("回滚对象失败：{}.list:{}", apiName, list, e);
                    }
                });
            }
        });
    }

    protected GetRewardDetailDTO getRewardDetail(String tenantId, String activityId, String businessId, int level, RewardDetailEntity rewardDetail, IObjectData serialNumberStatus) {
        String actionId = serialNumberStatus.get(FMCGSerialNumberStatusFields.ACTION_ID, String.class);
        String channelType = serialNumberStatus.get(FMCGSerialNumberStatusFields.CHANNEL_TYPE, String.class);
        RewardDimensionEnum rewardDimensionEnum = RewardDimensionEnum.get(rewardDetail.getRewardNode().getRewardDimension());
        GetRewardDetailDTO getRewardDetailDTO = null;
        if (rewardDetail.getRewardNode().getRewardType().equals(RewardTypeEnum.BY_BIZ_ACTION.code())) {
            if (actionId.equals(rewardDetail.getRewardNode().getRewardAction())) {
                if (rewardDimensionEnum.type().equals(RewardDimensionEnum.Type.STORE.code()) && channelType.equals(FMCGSerialNumberStatusFields.ChannelType.STORE)) {
                    //todo:门店奖励
                    getRewardDetailDTO = getRewardDetail(tenantId, activityId, businessId, rewardDetail, serialNumberStatus);
                } else if (!rewardDimensionEnum.type().equals(RewardDimensionEnum.Type.CONSUMER.code()) && level == rewardDimensionEnum.order()) {
                    //todo：企业激励
                    getRewardDetailDTO = getRewardDetail(tenantId, activityId, businessId, rewardDetail, serialNumberStatus);
                } else {
                    log.info("激励层级不满足。code:{},rewardDetail:{}", serialNumberStatus, rewardDetail);
                }
            }
        } else {
            Set<String> storeActionIdSet = getStoreActionIdSet(tenantId);
            if ((rewardDimensionEnum.type().equals(RewardDimensionEnum.Type.DOWNSTREAM.code()) || rewardDimensionEnum.type().equals(RewardDimensionEnum.Type.MASTER.code()))) {
                String rewardTarget = rewardDetail.getRewardNode().getRewardTarget();
                //符合层级 且 是门店负责人或企业老板
                if (rewardDimensionEnum.order() == level && (RewardTargetEnum.ENTERPRISE_BOSS.code().equals(rewardTarget)
                        || storeActionIdSet.contains(actionId) && RewardTargetEnum.STORE_OWNER.code().equals(rewardTarget))) {
                    getRewardDetailDTO = getRewardDetail(tenantId, activityId, businessId, rewardDetail, serialNumberStatus);
                }
            } else if (rewardDimensionEnum.type().equals(RewardDimensionEnum.Type.STORE.code())) {
                if (storeActionIdSet.contains(actionId)) {
                    getRewardDetailDTO = getRewardDetail(tenantId, activityId, businessId, rewardDetail, serialNumberStatus);
                }
            }
        }
        return getRewardDetailDTO;
    }

    private Set<String> getStoreActionIdSet(String tenantId) {
        Set<String> set = new HashSet<>();
        set.add(fmcgSerialNumberService.getActionIdByActionUniqueId(tenantId, ScanCodeActionConstants.STORE_SIGN));
        set.add(fmcgSerialNumberService.getActionIdByActionUniqueId(tenantId, ScanCodeActionConstants.STORE_STOCK_CHECK));
        return set;
    }


    protected List<GetRewardDetailDTO> getRewardDetails(String tenantId, String activityId, String code, String businessId, String signStoreId) {
        List<GetRewardDetailDTO> rewardDetailDTOS = new ArrayList<>();
        Map<Object, Boolean> rewardDetailHasDoneMap = new HashMap<>();
        Set<String> rewardedActionSet = new HashSet<>();
        ActivityRewardRulePO rewardRulePO = activityRewardRuleDAO.getByRelatedObject(tenantId, ApiNames.TPM_ACTIVITY_OBJ, activityId);
        List<IObjectData> serialNumberStatuses = fmcgSerialNumberService.queryAllSerialNumberStatusBySerialNumberId(tenantId, code);
        Set<String> relatedTenantIds = serialNumberStatuses.stream().map(v -> v.get(FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, String.class)).collect(Collectors.toSet());
        Set<String> rewardTagSet = new HashSet<>();
        if (lackOfRequiredNode(tenantId, serialNumberStatuses, rewardRulePO.getRewardDetails())) {
            log.info("不满足必含条件，businessId:{}", businessId);
            return Lists.newArrayList();
        }
        for (IObjectData serialNumberStatus : serialNumberStatuses) {
            String currentTenantId = serialNumberStatus.get(FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, String.class);
            if (Strings.isNullOrEmpty(currentTenantId)) {
                log.info("码状态 当前企业数据为空:{},status:{}", currentTenantId, serialNumberStatus);
                continue;
            }
            String actionId = serialNumberStatus.get(FMCGSerialNumberStatusFields.ACTION_ID, String.class);
            Integer level = enterpriseConnectionService.getLevel(tenantId, currentTenantId, relatedTenantIds);
            log.info("currentTenantId:{},actionId:{},level:{}", currentTenantId, actionId, level);
            //todo:门店相邻的得特殊处理 必须是激励过的
            for (int index = rewardRulePO.getRewardDetails().size() - 1; index >= 0; index--) {
                RewardDetailEntity rewardDetail = rewardRulePO.getRewardDetails().get(index);
                if (Boolean.TRUE.equals(rewardDetailHasDoneMap.get(rewardDetail))) {
                    log.info("has deal one . detail:{},status:{}", rewardDetail.getDetailId(), serialNumberStatus.getId());
                    continue;
                }
                if (rewardDetail.getRewardNode().getRewardDimension().equals(RewardDimensionEnum.CONSUMER.code())) {
                    //消费者节点单独处理
                    continue;
                }
                if (rewardRulePO.getRuleType().equals(ScanCodeActionConstants.STORE_SCAN_CODE_REWARD_TEMPLATE_ID) && index == rewardRulePO.getRewardDetails().size() - 1) {
                    //门店扫码最后一个节点不在这里处理
                    continue;
                }
                if (rewardDetail.getRewardStrategy().getRewardMethod().equals(RewardMethodEnum.REDUCED_PAYMENT.code())) {
                    //减价支付跳过 消费者节点已经处理
                    continue;
                }
                if (RewardMethodEnum.NONE.code().equals(rewardDetail.getRewardStrategy().getRewardMethod())) {
                    continue;
                }
                if (containsSpecialRepeatAction(tenantId, activityId, actionId, rewardedActionSet, rewardDetail)) {
                    continue;
                }
                if (rewardTagHasRewarded(tenantId, rewardDetail, rewardTagSet)) {
                    continue;
                }
                GetRewardDetailDTO getRewardDetailDTO = getRewardDetail(tenantId, activityId, businessId, level, rewardDetail, serialNumberStatus);
                if (getRewardDetailDTO != null && CollectionUtils.isNotEmpty(getRewardDetailDTO.getActivityRewardDetails())) {
                    rewardDetailDTOS.add(getRewardDetailDTO);
                    rewardDetailHasDoneMap.put(rewardDetail, true);
                    if (!RewardTypeEnum.BY_DESIGNEE.code().equals(rewardDetail.getRewardNode().getRewardType())) {
                        rewardedActionSet.add(actionId);
                    }
                    if (!Strings.isNullOrEmpty(rewardDetail.getRewardNode().getRewardTag())) {
                        rewardTagSet.add(rewardDetail.getRewardNode().getRewardTag());
                    }
                }
            }
        }
        fillSignStore(rewardDetailDTOS, signStoreId);
        return rewardDetailDTOS;
    }

    private boolean rewardTagHasRewarded(String tenantId, RewardDetailEntity rewardDetail, Set<String> rewardTagSet) {
        if (rewardTagSet.contains(rewardDetail.getRewardNode().getRewardTag())) {
            log.info("has reward tag reward. node:{}", JSON.toJSONString(rewardDetail.getRewardNode()));
            return true;
        }
        return false;
    }

    private boolean containsSpecialRepeatAction(String tenantId, String activityId, String actionId, Set<String> rewardedActionSet, RewardDetailEntity rewardDetail) {
        if (!TPMGrayUtils.allowSpecialRepeatAction(activityId)) {
            return false;
        }
        if (RewardTypeEnum.BY_DESIGNEE.code().equals(rewardDetail.getRewardNode().getRewardType())) {
            log.info("固定人员的跳过，只支持按动作的。");
            return false;
        }
        String salesOutActionId = fmcgSerialNumberService.getActionIdByActionUniqueId(tenantId, ScanCodeActionConstants.SALES_OUT_OF_WAREHOUSE);
        String stockCheckActionId = fmcgSerialNumberService.getActionIdByActionUniqueId(tenantId, ScanCodeActionConstants.STORE_STOCK_CHECK);
        if ((actionId.equals(stockCheckActionId) || actionId.equals(salesOutActionId))
                && (rewardedActionSet.contains(stockCheckActionId) || rewardedActionSet.contains(salesOutActionId))) {
            log.info("触发只发一次奖励，当前action:{},rewardedActionSet:{}", actionId, rewardedActionSet);
            return true;
        }
        return false;
    }

    private void fillSignStore(List<GetRewardDetailDTO> rewardDetailDTOS, String signStoreId) {
        if (Strings.isNullOrEmpty(signStoreId)) {
            return;
        }
        rewardDetailDTOS.forEach(detail -> {
            if (CollectionUtils.isNotEmpty(detail.getActivityRewardDetails())) {
                detail.getActivityRewardDetails().forEach(activityRewardDetail -> activityRewardDetail.set(TPMActivityRewardDetailFields.ACCOUNT_ID__C, signStoreId));
            }
        });
    }

    protected boolean lackOfRequiredNode(String topTenantId, List<IObjectData> serialNumberStatuses, List<RewardDetailEntity> rewardDetails) {
        Map<String, List<IObjectData>> actionId2StatusMap = serialNumberStatuses.stream().collect(groupingBy(v -> v.get(FMCGSerialNumberStatusFields.ACTION_ID, String.class)));
        Set<String> relatedTenantIds = serialNumberStatuses.stream().map(v -> v.get(FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, String.class)).collect(Collectors.toSet());

        Set<RewardDetailEntity> requiredNodes = rewardDetails.stream().filter(v -> Boolean.TRUE.equals(v.getRewardNode().getRequired())).collect(Collectors.toSet());
        for (RewardDetailEntity detail : requiredNodes) {

            List<IObjectData> statusList = actionId2StatusMap.get(detail.getRewardNode().getRewardAction());
            if (CollectionUtils.isNotEmpty(statusList)) {
                if (statusList.stream().noneMatch(status -> {
                    String dimension = detail.getRewardNode().getRewardDimension();
                    String actionId = status.get(FMCGSerialNumberStatusFields.ACTION_ID, String.class);
                    String statusDimension = getDimension(topTenantId, status, relatedTenantIds);
                    return dimension.equals(statusDimension) && (actionId.equals(detail.getRewardNode().getRewardAction())
                            || Strings.isNullOrEmpty(detail.getRewardNode().getRewardAction()) && detail.getRewardNode().getRewardType().equals(RewardTypeEnum.BY_DESIGNEE.code()));
                })) {
                    log.info("必含条件不满足，statusIds:{},nodes:{}", JSON.toJSONString(serialNumberStatuses.stream().map(DBRecord::getId).collect(Collectors.toList())), JSON.toJSONString(requiredNodes.stream().map(RewardDetailEntity::getRewardNode).collect(Collectors.toList())));
                    return true;
                }
            } else {
                log.info("必含条件不满足，statusIds:{},nodes:{}", JSON.toJSONString(serialNumberStatuses.stream().map(DBRecord::getId).collect(Collectors.toList())), JSON.toJSONString(requiredNodes.stream().map(RewardDetailEntity::getRewardNode).collect(Collectors.toList())));
                return true;
            }
        }
        return false;
    }

    protected String getDimension(String tenantId, IObjectData serialNumberStatus, Set<String> relatedTenantIds) {
        String currentTenantId = serialNumberStatus.get(FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, String.class);
        String channelType = serialNumberStatus.get(FMCGSerialNumberStatusFields.CHANNEL_TYPE, String.class, "");
        if (channelType.equals(FMCGSerialNumberStatusFields.ChannelType.STORE) || channelType.equals(FMCGSerialNumberStatusFields.ChannelType.STORE_HOUSE)) {
            return RewardDimensionEnum.STORE.code();
        } else if (channelType.equals(FMCGSerialNumberStatusFields.ChannelType.CONSUMER)) {
            return RewardDimensionEnum.CONSUMER.code();
        } else {
            return String.valueOf(enterpriseConnectionService.getLevel(tenantId, currentTenantId, relatedTenantIds));
        }
    }

    protected void validateActivityAmount(String tenantId, String activityId, Collection<IObjectData> redPackets) {
        BigDecimal amount = BigDecimal.ZERO;
        for (IObjectData packet : redPackets) {
            amount = amount.add(packet.get(RedPacketRecordObjFields.REWARD_AMOUNT, BigDecimal.class, BigDecimal.ZERO));
        }
        IObjectData activity = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), activityId, ApiNames.TPM_ACTIVITY_OBJ);
        if (!TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS.equals(activity.get(TPMActivityFields.ACTIVITY_STATUS, String.class))) {
            throw new RewardFmcgException("90003", I18N.text(I18NKeys.REWARD2_ACTIVITY_REWARD_BASE_3));
        }

        validateActivityAmountByFilters(tenantId, activity, amount, TPMActivityFields.ACTIVITY_AMOUNT, Lists.newArrayList(
                SearchQueryUtil.filter(RedPacketRecordObjFields.ACTIVITY_ID, Operator.EQ, Lists.newArrayList(activity.getId())),
                SearchQueryUtil.filter(RedPacketRecordObjFields.TRIGGER_EVENT, Operator.NEQ, Lists.newArrayList(RedPacketRecordObjFields.TriggerEvent.STORE_REDEEM))
        ));

        long now = System.currentTimeMillis();
        validateActivityAmountByFilters(tenantId, activity, amount, TPMActivityFields.ACTIVITY_MONTH_AMOUNT, Lists.newArrayList(
                SearchQueryUtil.filter(RedPacketRecordObjFields.ACTIVITY_ID, Operator.EQ, Lists.newArrayList(activity.getId())),
                SearchQueryUtil.filter(RedPacketRecordObjFields.TRIGGER_EVENT, Operator.NEQ, Lists.newArrayList(RedPacketRecordObjFields.TriggerEvent.STORE_REDEEM)),
                SearchQueryUtil.filter(CommonFields.CREATE_TIME, Operator.BETWEEN, Lists.newArrayList(String.valueOf(TimeUtils.getMonthStart(now)), String.valueOf(TimeUtils.getMonthEnd(now))))
        ));
    }

    private void validateActivityAmountByFilters(String tenantId, IObjectData activity, BigDecimal validateAmount, String activityAmountField, List<IFilter> filters) {

        BigDecimal totalLimit = activity.get(activityAmountField, BigDecimal.class);

        if (TPMActivityFields.ACTIVITY_AMOUNT.equals(activityAmountField) && totalLimit == null) {
            totalLimit = BigDecimal.ZERO;
        }
        if (totalLimit == null) {
            log.info("没填写跳过，activityAmountField:{}", activityAmountField);
            return;
        }
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 2000, filters);
        query.setSearchSource("db");
        List<IObjectData> result = serviceFacade.aggregateFindBySearchQuery(tenantId, query, ApiNames.RED_PACKET_RECORD_OBJ, RedPacketRecordObjFields.ACTIVITY_ID, "sum", RedPacketRecordObjFields.REWARD_AMOUNT);
        if (CollectionUtils.isNotEmpty(result)) {
            BigDecimal sum = result.get(0).get("sum_reward_amount", BigDecimal.class, BigDecimal.ZERO);
            totalLimit = totalLimit.subtract(sum);
        }
        if (totalLimit.compareTo(validateAmount) < 0) {
            log.info("total:{},amount:{}", totalLimit, validateAmount);
            // 请求修改错误码，改了切屌
            throw new RewardFmcgException("90004", I18N.text(I18NKeys.REWARD2_ACTIVITY_REWARD_BASE_4));
        }
    }


    protected List<IObjectData> needCreateDataList(User user, List<IObjectData> dataList) {
        List<String> ids = dataList.stream().map(IObjectData::getId).collect(Collectors.toList());
        List<IObjectData> dbData = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), ids, dataList.get(0).getDescribeApiName());
        Set<String> dbIds = dbData.stream().map(IObjectData::getId).collect(Collectors.toSet());
        return dataList.stream().filter(data -> !dbIds.contains(data.getId())).collect(Collectors.toList());
    }

    protected boolean validateSerialNumberHasDeal(String tenantId, String snId, String businessPrefix) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.BUSINESS_ID, Operator.LIKE, Lists.newArrayList(businessPrefix)),
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.SERIAL_NUMBER_ID, Operator.EQ, Lists.newArrayList(snId)),
                SearchQueryUtil.filter(CommonFields.IS_DELETED, Operator.EQ, Lists.newArrayList("0"))
        ));
        int count = serviceFacade.countObjectDataFromDB(tenantId, ApiNames.TPM_ACTIVITY_REWARD_DETAIL_OBJ, query);
        return count > 0;
    }

    protected RLock tryLock(String key) {

        RLock lock = redissonCmd.getLock(key);

        log.info("try lock key : {}", key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            return lock;
        }
        try {
            if (lock.tryLock(LOCK_WAIT, LOCK_LEASE, TimeUnit.MILLISECONDS)) {
                return lock;
            } else {
                throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_ACTIVITY_REWARD_BASE_0));
            }
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_ACTIVITY_REWARD_BASE_1));
        }
    }

    protected void unlock(RLock lock) {
        if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
}
