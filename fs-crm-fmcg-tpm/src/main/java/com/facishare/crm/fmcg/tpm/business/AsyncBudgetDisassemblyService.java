package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.business.abstraction.*;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.tpm.business.enums.BudgetDetailOperateMark;
import com.facishare.crm.fmcg.tpm.business.enums.DisassemblyActionCode;
import com.facishare.crm.fmcg.tpm.business.enums.MainType;
import com.facishare.crm.fmcg.tpm.controller.TPMBudgetDisassemblyObjDescribeLayoutController;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetDimensionEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.service.abstraction.ITransactionProxy;
import com.facishare.crm.fmcg.tpm.session.SendMessageService;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.utils.SendMessageArgFormatUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.model.TextCardMessageBody;
import com.fxiaoke.model.TextCardMessageHead;
import com.fxiaoke.model.message.SendTextCardMessageArg;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import groovy.lang.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@SuppressWarnings("Duplicates")
public class AsyncBudgetDisassemblyService implements IAsyncBudgetDisassemblyService {
    private static final String DISASSEMBLY_LOCK_KEY = "FMCG:TPM:DISASSEMBLY:LOCK:%s:%s";
    private static final int LOCK_WAIT = 30;
    private static final int LOCK_LEASE = 420;
    @Resource
    private IBudgetAccountService budgetAccountService;
    @Resource
    private IBudgetCalculateService budgetCalculateService;
    @Resource
    private IBudgetStatisticTableService budgetStatisticTableService;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private BudgetTypeDAO budgetTypeDAO;
    @Resource
    private IBudgetDisassemblyService budgetDisassemblyService;
    @Resource
    private ITransactionProxy transactionProxy;
    @Resource
    private RedissonClient redissonCmd;
    @Resource
    private SendMessageService sendMessageService;
    private static final Map<EXECUTE, Map<String, Status>> EXECUTE_MAP = Maps.newHashMap();
    private static final Map<EXECUTE, Tuple2<String, String>> EXECUTE_FAILED_STATUS_MAP = Maps.newHashMap();
    private static final Map<EXECUTE, Tuple2<String, String>> EXECUTE_SUCCESS_STATUS_MAP = Maps.newHashMap();
    public static final Map<String, String> STATUS_TRANSLATE_MAP = Maps.newHashMap();
    private static final List<String> ASYNC_STATISTIC_ACTION_CODE = Lists.newArrayList();


    static {
        setStatisticDisassemblyActionCode();

        setExecuteMap();

        setExecuteFailedStatusMap();

        setExecuteSuccessStatusMap();

        initStatusTranslateValue();
    }

    public enum Status {
        ALLOW, NOT_ALLOW, COMPLETED, ERROR
    }

    public enum EXECUTE {
        DISASSEMBLY, UNFROZEN
    }


    @Override
    public void unFrozen(User user, String dataId) {
        invoke(EXECUTE.UNFROZEN, user, dataId, DisassemblyActionCode.UN_FROZEN_RETRY.value());
    }

    @Override
    public void doDisassembly(User user, String dataId, String actionCode) {
        invoke(EXECUTE.DISASSEMBLY, user, dataId, actionCode);
    }

    private void invoke(EXECUTE execute, User user, String dataId, String actionCode) {
        if (!tryLock(user.getTenantId(), dataId)) {
            log.info("invoke get lock fail,dataId:{}", dataId);
            return;
        }
        log.info("invoke start,dataId:{}", dataId);
        IObjectData what = loadMaster(user.getTenantId(), dataId);
        IBudgetOperator sourceAccountOperator = budgetDisassemblyService.initSourceAccountOperator(user, dataId);
        try {
            if (finished(execute, what)) {
                log.info("invoke already finished,dataId:{}", dataId);
                return;
            }
            validateSourceAccountOperator(user, dataId, sourceAccountOperator);
            List<IObjectData> accounts = transactionProxy.call(() -> {
                List<IObjectData> needStatisticAccounts = doExecute(execute, user, sourceAccountOperator, what, actionCode);
                doSuccess(user, dataId, execute);
                return needStatisticAccounts;
            });

            if (CollectionUtils.isNotEmpty(accounts)) {
                budgetStatisticTableService.asyncDoStatistic(user.getTenantId(), accounts);
            }
        } catch (ValidateException validateException) {
            doFail(user, dataId, execute, String.format("%s,%s", validateException.getMessage(), I18N.text(I18NKeys.BUDGET_DO_DISASSEMBLY_ERROR)));
            log.error("invoke validateException,", validateException);
        } catch (MetaDataBusinessException metaDataBusinessException) {
            doFail(user, dataId, execute, String.format("%s,%s", metaDataBusinessException.getMessage(), I18N.text(I18NKeys.BUDGET_DO_DISASSEMBLY_ERROR)));
            log.error("invoke metaDataBusinessException,", metaDataBusinessException);
        } catch (Exception ex) {
            doFail(user, dataId, execute, I18N.text(I18NKeys.BUDGET_DO_DISASSEMBLY_UNKNOWN_ERROR));
            log.error("invoke error,", ex);
        } finally {
            if (Objects.nonNull(sourceAccountOperator)) {
                sourceAccountOperator.unlock();
            }
            unlock(user.getTenantId(), dataId);
        }
    }

    private boolean tryLock(String tenantId, String dataId) {
        String key = formatLockKey(tenantId, dataId);
        RLock lock = redissonCmd.getLock(key);
        log.info("[disassembly_lock] lock: {}", key);
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            return true;
        }
        try {
            return lock.tryLock(LOCK_WAIT, LOCK_LEASE, TimeUnit.SECONDS);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_ASYNC_BUDGET_DISASSEMBLY_SERVICE_0), key));
        }
    }

    private void unlock(String tenantId, String dataId) {
        String key = formatLockKey(tenantId, dataId);
        RLock lock = redissonCmd.getLock(key);
        log.info("[disassembly_lock] unlock: {}", key);
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    private String formatLockKey(String tenantId, String dataId) {
        return String.format(DISASSEMBLY_LOCK_KEY, tenantId, dataId);
    }

    @Override
    public void markMasterDisassemblyInfo(String tenantId, IObjectData objectData, String status) {
        this.markMasterDisassemblyInfo(tenantId, objectData, status, null, null);
    }

    private void doSuccess(User user, String dataId, EXECUTE execute) {
        String tenantId = user.getTenantId();
        IObjectData master = loadMaster(tenantId, dataId);
        markMasterDisassemblyInfo(tenantId, master, EXECUTE_SUCCESS_STATUS_MAP.get(execute).getFirst(), System.currentTimeMillis());
        doLog(tenantId, master, I18N.text(EXECUTE_SUCCESS_STATUS_MAP.get(execute).getSecond()));
        doSend(user, master, execute, EXECUTE_SUCCESS_STATUS_MAP, null);
    }

    private void doFail(User user, String dataId, EXECUTE execute, String errorMessage) {
        String tenantId = user.getTenantId();
        IObjectData master = loadMaster(tenantId, dataId);
        markMasterDisassemblyInfo(tenantId, master, EXECUTE_FAILED_STATUS_MAP.get(execute).getFirst(), errorMessage);
        doLog(tenantId, master, String.format(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_FAIL), errorMessage));
        doSend(user, master, execute, EXECUTE_FAILED_STATUS_MAP, errorMessage);
    }


    public void markMasterDisassemblyInfo(String tenantId, IObjectData master, String status, String errorMsg) {
        markMasterDisassemblyInfo(tenantId, master, status, errorMsg, System.currentTimeMillis());
    }

    private void markMasterDisassemblyInfo(String tenantId, IObjectData master, String status, long completedTime) {
        markMasterDisassemblyInfo(tenantId, master, status, null, completedTime);
    }

    private void markMasterDisassemblyInfo(String tenantId, IObjectData objectData, String status, String errorMsg, Long completedTime) {
        Map<String, Object> updater = Maps.newHashMap();
        updater.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS, status);
        updater.put(TPMBudgetDisassemblyFields.DISASSEMBLY_FAILED_MESSAGE, errorMsg);
        if (Objects.nonNull(completedTime)) {
            updater.put(TPMBudgetDisassemblyFields.DISASSEMBLY_COMPLETED_TIME, completedTime);
        }
        serviceFacade.updateWithMap(User.systemUser(tenantId), objectData, updater);
    }

    private void doLog(String tenantId, IObjectData master, String message) {
        IObjectDescribe describe = serviceFacade.findObject(tenantId, ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ);
        serviceFacade.logWithCustomMessage(User.systemUser(tenantId), EventType.MODIFY, ActionType.MODIFY, describe, master, message);
    }

    private void doSend(User user, IObjectData master, EXECUTE execute, Map<EXECUTE, Tuple2<String, String>> executeStatusMap, String errorMessage) {
        String name = master.get(CommonFields.NAME, String.class);
        TextCardMessageHead head = SendMessageArgFormatUtil.buildMessageHead(I18N.text(I18NKeys.BUDGET_ASYNC_DISASSEMBLY_NOTIFY));
        Map<String, String> formMessage = Maps.newHashMap();
        formMessage.put(I18N.text(I18NKeys.BUDGET_ASYNC_DISASSEMBLY_COMPLETED_TIME), SendMessageArgFormatUtil.formatDateStr(System.currentTimeMillis()));
        formMessage.put(I18N.text(I18NKeys.BUDGET_ASYNC_DISASSEMBLY_COMPLETED_STATUS), I18N.text(STATUS_TRANSLATE_MAP.get(executeStatusMap.get(execute).getFirst())));
        String contentHeadMessage;
        if (!StringUtils.isEmpty(errorMessage)) {
            contentHeadMessage = String.format(I18N.text(I18NKeys.BUDGET_ASYNC_DISASSEMBLY_COMPLETED_BUT_ERROR), name, errorMessage);
        } else {
            contentHeadMessage = String.format(I18N.text(I18NKeys.BUDGET_ASYNC_DISASSEMBLY_COMPLETED), name);
        }
        TextCardMessageBody body = SendMessageArgFormatUtil.buildMessageBody(contentHeadMessage, formMessage);
        SendTextCardMessageArg arg = SendMessageArgFormatUtil.buildTextCardArgForAppNotify(user, ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ, master.getId(), head, body);
        sendMessageService.sendTextCardMessage(arg);
    }


    private List<IObjectData> doExecute(EXECUTE execute, User user, IBudgetOperator sourceAccountOperator, IObjectData what, String actionCode) {
        List<IObjectData> accounts = Lists.newArrayList();
        switch (execute) {
            case UNFROZEN: {
                flowCallBackUnFrozen(sourceAccountOperator);
                break;
            }
            case DISASSEMBLY: {
                disassemblyUnFrozen(sourceAccountOperator);
                accounts = doDisassemblyNewDetails(user, what, actionCode);
                doDisassemblyExistDetails(user, what);
                break;
            }
            default: {
                log.info("[doAction] were not found effective execute:{}", execute);
                break;
            }
        }
        return accounts;
    }

    private void validateSourceAccountOperator(User user, String dataId, IBudgetOperator sourceAccountOperator) {
        if (sourceAccountOperator == null) {
            log.info("sourceAccountOperator is null,dataId:{},tenantId:{}", dataId, user.getTenantId());
            throw new ValidateException(I18N.text(I18NKeys.ASYNC_BUDGET_DISASSEMBLY_SERVICE_0));
        } else {
            IObjectData master = serviceFacade.findObjectDataIgnoreAll(User.systemUser(user.getTenantId()), dataId, ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ);
            if (master == null) {
                log.info("master is null,dataId:{}", dataId);
                throw new ValidateException(I18N.text(I18NKeys.ASYNC_BUDGET_DISASSEMBLY_SERVICE_1));
            }
            String traceId = master.getId().toUpperCase(Locale.ROOT);
            IObjectData freezeDetail = findFreezeDetail(user, master.get(TPMBudgetDisassemblyFields.SOURCE_BUDGET_ACCOUNT_ID, String.class), traceId);
            //已经有了冻结记录，无需再次校验金额是否足够
            if (freezeDetail != null) {
                return;
            }
            BigDecimal disassemblyAmount = master.get(TPMBudgetDisassemblyFields.DISASSEMBLY_AMOUNT, BigDecimal.class);
            sourceAccountOperator.validateOperableAmount(disassemblyAmount);
        }
    }

    private IObjectData findFreezeDetail(User user, String accountId, String traceId) {
        List<IObjectData> details = queryFreezeDetail(user, accountId, traceId);
        if (CollectionUtils.isEmpty(details)) {
            return null;
        }
        return details.get(0);
    }

    private List<IObjectData> queryFreezeDetail(User user, String accountId, String traceId) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(1);
        stq.setOffset(0);
        stq.setSearchSource("db");
        stq.setNeedReturnCountNum(false);

        Filter budgetAccountIdFilter = new Filter();
        budgetAccountIdFilter.setFieldName(TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID);
        budgetAccountIdFilter.setOperator(Operator.EQ);
        budgetAccountIdFilter.setFieldValues(Lists.newArrayList(accountId));

        Filter mainTypeFilter = new Filter();
        mainTypeFilter.setFieldName(TPMBudgetAccountDetailFields.MAIN_TYPE);
        mainTypeFilter.setOperator(Operator.EQ);
        mainTypeFilter.setFieldValues(Lists.newArrayList(MainType.FREEZE.value()));

        Filter approvalTraceFilter = new Filter();
        approvalTraceFilter.setFieldName(TPMBudgetAccountDetailFields.APPROVAL_TRACE_ID);
        approvalTraceFilter.setOperator(Operator.EQ);
        approvalTraceFilter.setFieldValues(com.google.common.collect.Lists.newArrayList(traceId));

        Filter businessTraceFilter = new Filter();
        businessTraceFilter.setFieldName(TPMBudgetAccountDetailFields.BIZ_TRACE_ID);
        businessTraceFilter.setOperator(Operator.EQ);
        businessTraceFilter.setFieldValues(com.google.common.collect.Lists.newArrayList(traceId));

        Filter operateMarkEmptyFilter = new Filter();
        operateMarkEmptyFilter.setFieldName(TPMBudgetAccountDetailFields.OPERATE_MARK_DETAIL);
        operateMarkEmptyFilter.setOperator(Operator.IS);
        operateMarkEmptyFilter.setFieldValues(Lists.newArrayList());

        Filter operateMarkNeqFilter = new Filter();
        operateMarkNeqFilter.setFieldName(TPMBudgetAccountDetailFields.OPERATE_MARK_DETAIL);
        operateMarkNeqFilter.setOperator(Operator.NEQ);
        operateMarkNeqFilter.setFieldValues(Lists.newArrayList(BudgetDetailOperateMark.COMPLETED_UNFREEZE.value()));

        stq.setFilters(Lists.newArrayList(budgetAccountIdFilter, mainTypeFilter, approvalTraceFilter, businessTraceFilter, operateMarkEmptyFilter, operateMarkNeqFilter));
        stq.setPattern("1 and 2 and 3 and 4 and ( 5 or 6 )");

        return QueryDataUtil.find(this.serviceFacade, user.getTenantId(), ApiNames.TPM_BUDGET_ACCOUNT_DETAIL, stq, Lists.newArrayList(
                CommonFields.ID,
                CommonFields.TENANT_ID,
                CommonFields.OBJECT_DESCRIBE_API_NAME,
                TPMBudgetAccountDetailFields.AMOUNT
        ));
    }

    private List<IObjectData> doDisassemblyNewDetails(User user, IObjectData what, String actionCode) {

        List<IObjectData> newAccounts = Lists.newArrayList();
        List<IObjectData> accounts = Lists.newArrayList();
        String traceId = what.getId().toUpperCase(Locale.ROOT);

        BudgetTypeNodeEntity targetNode = loadTargetNode(user.getTenantId(), what.getId());
        List<IObjectData> newDetails = loadNewDetails(user.getTenantId(), targetNode, what.getId());
        IObjectData sourceAccount = loadSourceAccount(user.getTenantId(), what);

        for (IObjectData newDetail : newDetails) {
            newDetail.setOwner(Lists.newArrayList(user.getUserId()));
            IObjectData newAccount = convertToNewAccount(user.getTenantId(), newDetail, sourceAccount, targetNode);
            newAccounts.add(newAccount);
        }

        if (CollectionUtils.isNotEmpty(newAccounts)) {
            for (IObjectData data : newAccounts) {
                String totalAmount = data.get(TPMBudgetAccountFields.TOTAL_AMOUNT, String.class);
                IObjectData account = budgetAccountService.createBudgetAccount(User.systemUser(user.getTenantId()), data, false, true, true, true);
                accounts.add(account);
                IBudgetOperator operator = BudgetOperatorFactory.initOperator(BizType.TAKE_APART_IN, user, data.getId(), traceId, traceId, what);
                operator.income(new BigDecimal(totalAmount));
                budgetCalculateService.recalculateBudgetAmount(user, data.getId());
                BuryService.asyncBudgetLog(user.getTenantId(), user.getUserIdInt(), BuryModule.Budget.BUDGET_DISASSEMBLY_NEW_ACCOUNT, BuryOperation.CREATE);
                //todo 测试，需要删掉代码
                if (TPMGrayUtils.carryForwardShowcase(user.getTenantId())) {
                    if (Objects.equals(actionCode, DisassemblyActionCode.ADD.value()) || Objects.equals(actionCode, DisassemblyActionCode.EDIT.value())) {
                        throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_ASYNC_BUDGET_DISASSEMBLY_SERVICE_1));
                    }
                }
            }
            log.info("doDisassembly newAccounts end...");
            if (ASYNC_STATISTIC_ACTION_CODE.contains(actionCode)) {
                return accounts;
            }
        }
        return Lists.newArrayList();
    }

    private void doDisassemblyExistDetails(User user, IObjectData what) {

        String traceId = what.getId().toUpperCase(Locale.ROOT);
        List<IObjectData> existsDetails = loadExistDetails(user.getTenantId(), what.getId());
        if (CollectionUtils.isNotEmpty(existsDetails)) {

            for (IObjectData detail : existsDetails) {
                String accountId = detail.get(TPMBudgetDisassemblyExistsDetailsFields.BUDGET_ACCOUNT_ID, String.class);
                IBudgetOperator operator = BudgetOperatorFactory.initOperator(BizType.TAKE_APART_IN, user, accountId, traceId, traceId, what);

                operator.income(new BigDecimal(detail.get(TPMBudgetDisassemblyExistsDetailsFields.AMOUNT, String.class)));
                operator.recalculate();
                BuryService.asyncBudgetLog(user.getTenantId(), user.getUserIdInt(), BuryModule.Budget.BUDGET_DISASSEMBLY_EDIT_ACCOUNT, BuryOperation.UPDATE);
            }
            log.info("doDisassembly existsDetails end...");
        }
    }

    private void disassemblyUnFrozen(IBudgetOperator sourceAccountOperator) {
        BigDecimal amount = sourceAccountOperator.unfreeze(BizType.RELEASE);
        sourceAccountOperator.expenditure(amount);
        sourceAccountOperator.recalculate();
        log.info("doDisassembly unfreeze end...");

    }

    private void flowCallBackUnFrozen(IBudgetOperator sourceAccountOperator) {
        sourceAccountOperator.unfreeze(BizType.APPROVAL_BACK);
        sourceAccountOperator.recalculate();
        log.info("flowCallBackUnFrozen unfreeze end...");
    }

    private boolean finished(EXECUTE execute, IObjectData what) {
        if (what == null) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_ASYNC_BUDGET_DISASSEMBLY_SERVICE_2));
        }
        String status = what.get(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS, String.class);
        if (Objects.isNull(status)) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_ASYNC_BUDGET_DISASSEMBLY_SERVICE_3));
        }
        Status allowContinue = EXECUTE_MAP.get(execute).get(status);

        switch (allowContinue) {
            case ALLOW: {
                return false;
            }
            case COMPLETED: {
                return true;
            }
            case NOT_ALLOW: {
                throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_ASYNC_BUDGET_DISASSEMBLY_SERVICE_4), status, execute));
            }
            default: {
                throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_ASYNC_BUDGET_DISASSEMBLY_SERVICE_5), execute, status));
            }
        }
    }

    private IObjectData convertToNewAccount(String tenantId, IObjectData newDetail, IObjectData sourceAccount, BudgetTypeNodeEntity targetNode) {
        return budgetDisassemblyService.buildDataForCreateBudgetAccount(tenantId, newDetail, sourceAccount, targetNode);
    }

    private IObjectData loadSourceAccount(String tenantId, IObjectData what) {
        String sourceAccountId = (String) what.get(TPMBudgetDisassemblyFields.SOURCE_BUDGET_ACCOUNT_ID);
        return serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), sourceAccountId, ApiNames.TPM_BUDGET_ACCOUNT);
    }

    private BudgetTypeNodeEntity loadTargetNode(String tenantId, String dataId) {
        IObjectData master = loadMaster(tenantId, dataId);
        // 加载预算类型信息
        String typeId = master.get(TPMBudgetDisassemblyFields.BUDGET_TYPE_ID, String.class);
        if (Strings.isNullOrEmpty(typeId)) {
            throw new ValidateException(I18N.text(I18NKeys.ASYNC_BUDGET_DISASSEMBLY_SERVICE_2));
        }
        BudgetTypePO budgetType = budgetTypeDAO.get(tenantId, typeId);

        // 加载拆解到的节点信息
        String targetNodeId = master.get(TPMBudgetDisassemblyFields.TARGET_BUDGET_NODE_ID, String.class);
        return budgetType.getNodes().stream().filter(f -> f.getNodeId().equals(targetNodeId)).findFirst().orElse(null);

    }

    private IObjectData loadMaster(String tenantId, String dataId) {
        return serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), dataId, ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ);
    }

    private List<IObjectData> loadNewDetails(String tenantId, BudgetTypeNodeEntity targetNode, String dataId) {
        List<String> fields = Lists.newArrayList(
                CommonFields.ID,
                CommonFields.OBJECT_DESCRIBE_API_NAME,
                CommonFields.TENANT_ID,
                CommonFields.NAME,
                CommonFields.OWNER,
                TPMBudgetAccountFields.BUDGET_DEPARTMENT,
                TPMBudgetDisassemblyFields.BUDGET_TYPE_ID,
                TPMBudgetDisassemblyNewDetailsFields.BUDGET_NODE_ID,
                TPMBudgetAccountFields.BUDGET_SUBJECT_ID,
                TPMBudgetAccountFields.PRODUCT_CATEGORY_ID,
                TPMBudgetAccountFields.PRODUCT_ID,
                TPMBudgetAccountFields.DEALER_ID,
                TPMBudgetDisassemblyNewDetailsFields.AMOUNT);
        fields.addAll(TPMBudgetDisassemblyObjDescribeLayoutController.TIME_DIMENSION_FIELD_API_NAME);

        for (BudgetDimensionEntity dimension : targetNode.getDimensions()) {
            if (!TPMBudgetDisassemblyObjDescribeLayoutController.DIMENSION_FIELD_API_NAME.contains(dimension.getApiName())) {
                fields.add(dimension.getApiName());
            }
        }

        return loadDetails(tenantId, dataId, fields, ApiNames.TPM_BUDGET_DISASSEMBLY_NEW_DETAIL_OBJ);
    }

    private List<IObjectData> loadExistDetails(String tenantId, String dataId) {
        List<String> fields = Lists.newArrayList(TPMBudgetDisassemblyExistsDetailsFields.BUDGET_ACCOUNT_ID, TPMBudgetDisassemblyExistsDetailsFields.AMOUNT, CommonFields.NAME);


        return loadDetails(tenantId, dataId, fields, ApiNames.TPM_BUDGET_DISASSEMBLY_EXISTS_DETAIL_OBJ);
    }


    private List<IObjectData> loadDetails(String tenantId, String dataId, List<String> fields, String apiName) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(-1);
        stq.setOffset(0);
        stq.setNeedReturnCountNum(false);
        stq.setNeedReturnQuote(false);
        stq.setSearchSource("db");

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(TPMBudgetDisassemblyNewDetailsFields.BUDGET_DISASSEMBLY_ID);
        masterFilter.setOperator(Operator.EQ);
        masterFilter.setFieldValues(com.google.common.collect.Lists.newArrayList(dataId));

        stq.setFilters(com.google.common.collect.Lists.newArrayList(masterFilter));

        return QueryDataUtil.find(serviceFacade, tenantId, apiName, stq, fields);
    }

    private static void setStatisticDisassemblyActionCode() {
        ASYNC_STATISTIC_ACTION_CODE.add(DisassemblyActionCode.ADD.value());
        ASYNC_STATISTIC_ACTION_CODE.add(DisassemblyActionCode.EDIT.value());
        ASYNC_STATISTIC_ACTION_CODE.add(DisassemblyActionCode.FLOW_COMPLETED.value());
        ASYNC_STATISTIC_ACTION_CODE.add(DisassemblyActionCode.DISASSEMBLY_RETRY.value());

    }

    private static void setExecuteMap() {
        Map<String, Status> unFrozenMap = new HashMap<>();
        //允许解冻状态
        unFrozenMap.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__FROZEN, Status.ALLOW);
        unFrozenMap.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__UNFROZEN_FAILED, Status.ALLOW);
        //不允许解冻状态
        unFrozenMap.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__SCHEDULE, Status.NOT_ALLOW);
        //完成状态
        unFrozenMap.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__UNFROZEN, Status.COMPLETED);
        unFrozenMap.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__SUCCESS, Status.COMPLETED);
        //失败状态
        unFrozenMap.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__FROZEN_FAILED, Status.ERROR);
        unFrozenMap.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__FAILED, Status.ERROR);
        unFrozenMap.put(null, Status.ERROR);
        EXECUTE_MAP.put(EXECUTE.UNFROZEN, unFrozenMap);


        Map<String, Status> doDisassemblyMap = new HashMap<>();
        //允许拆解状态
        doDisassemblyMap.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__FROZEN, Status.ALLOW);
        doDisassemblyMap.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__FAILED, Status.ALLOW);
        //不允许拆解状态
        doDisassemblyMap.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__SCHEDULE, Status.NOT_ALLOW);
        doDisassemblyMap.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__UNFROZEN, Status.NOT_ALLOW);
        //完成状态
        doDisassemblyMap.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__SUCCESS, Status.COMPLETED);
        //失败状态
        doDisassemblyMap.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__FROZEN_FAILED, Status.ERROR);
        doDisassemblyMap.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__UNFROZEN_FAILED, Status.ERROR);
        doDisassemblyMap.put(null, Status.ERROR);
        EXECUTE_MAP.put(EXECUTE.DISASSEMBLY, doDisassemblyMap);

    }

    private static void setExecuteFailedStatusMap() {
        EXECUTE_FAILED_STATUS_MAP.put(EXECUTE.UNFROZEN, new Tuple2<>(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__UNFROZEN_FAILED, I18NKeys.BUDGET_ASYNC_DISASSEMBLY_UNFROZEN_FAIL_BUT_RETRY));
        EXECUTE_FAILED_STATUS_MAP.put(EXECUTE.DISASSEMBLY, new Tuple2<>(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__FAILED, I18NKeys.BUDGET_ASYNC_DISASSEMBLY_FAIL_BUT_RETRY));

    }

    private static void setExecuteSuccessStatusMap() {
        EXECUTE_SUCCESS_STATUS_MAP.put(EXECUTE.UNFROZEN, new Tuple2<>(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__UNFROZEN, I18NKeys.BUDGET_ASYNC_DISASSEMBLY_UNFROZEN_SUCCESS));
        EXECUTE_SUCCESS_STATUS_MAP.put(EXECUTE.DISASSEMBLY, new Tuple2<>(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__SUCCESS, I18NKeys.BUDGET_ASYNC_DISASSEMBLY_SUCCESS));


    }

    private static void initStatusTranslateValue() {
        STATUS_TRANSLATE_MAP.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__FROZEN, I18NKeys.BUDGET_ASYNC_DISASSEMBLY_STATUS_FROZEN);
        STATUS_TRANSLATE_MAP.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__FAILED, I18NKeys.BUDGET_ASYNC_DISASSEMBLY_STATUS_FAIL);
        STATUS_TRANSLATE_MAP.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__SCHEDULE, I18NKeys.BUDGET_ASYNC_DISASSEMBLY_STATUS_NOT_STARTED);
        STATUS_TRANSLATE_MAP.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__UNFROZEN, I18NKeys.BUDGET_ASYNC_DISASSEMBLY_STATUS_UNFROZEN);
        STATUS_TRANSLATE_MAP.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__SUCCESS, I18NKeys.BUDGET_ASYNC_DISASSEMBLY_STATUS_SUCCESS);
        STATUS_TRANSLATE_MAP.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__FROZEN_FAILED, I18NKeys.BUDGET_ASYNC_DISASSEMBLY_STATUS_FROZEN_FAIL);
        STATUS_TRANSLATE_MAP.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__UNFROZEN_FAILED, I18NKeys.BUDGET_ASYNC_DISASSEMBLY_STATUS_UNFROZEN_FAIL);

    }
}
