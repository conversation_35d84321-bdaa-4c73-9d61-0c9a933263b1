package com.facishare.crm.fmcg.tpm.action;

import com.google.common.collect.Lists;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAsyncBulkAction;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/6 下午3:16
 */
public class TPMBudgetAccountObjAsyncBulkEnableBudgetAction extends AbstractStandardAsyncBulkAction<TPMBudgetAccountObjAsyncBulkEnableBudgetAction.Arg, TPMBudgetAccountObjEnableBudgetAction.Arg> {


    @Override
    protected String getDataIdByParam(TPMBudgetAccountObjEnableBudgetAction.Arg arg) {
        return arg.getDataId();
    }

    @Override
    protected List<TPMBudgetAccountObjEnableBudgetAction.Arg> getButtonParams() {
        return arg.getDataIds().stream()
                .map(id -> {
                    TPMBudgetAccountObjEnableBudgetAction.Arg arg = new TPMBudgetAccountObjEnableBudgetAction.Arg();
                    arg.setDataId(id);
                    return arg;
                })
                .collect(Collectors.toList());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.ENABLE_BUDGET.getButtonApiName();
    }

    @Override
    protected String getActionCode() {
        return ObjectAction.ENABLE_BUDGET.getActionCode();

    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.ENABLE_BUDGET.getActionCode());
    }

    @Data
    public static class Arg {
        private List<String> dataIds;
    }
}
