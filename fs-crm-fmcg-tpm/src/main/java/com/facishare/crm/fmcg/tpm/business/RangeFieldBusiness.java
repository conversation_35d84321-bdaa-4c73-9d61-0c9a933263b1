package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.fmcg.common.adapter.exception.RewardFmcgException;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.apiname.ActivityCustomerTypeEnum;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.business.abstraction.IFmcgSerialNumberService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRangeFieldBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IUnifiedActivityCommonLogicBusiness;
import com.facishare.crm.fmcg.tpm.business.enums.UseRangeEnum;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.utils.OperatorRelationEnum;
import com.facishare.crm.fmcg.tpm.web.condition.ConditionAdapter;
import com.facishare.crm.fmcg.tpm.web.condition.model.ConditionDto;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Author: linmj
 * Date: 2023/4/18 15:31
 */
@Slf4j
@Service
public class RangeFieldBusiness implements IRangeFieldBusiness {

    @Resource
    private ConditionAdapter conditionAdapter;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private StoreBusiness storeBusiness;

    @Resource
    private IUnifiedActivityCommonLogicBusiness unifiedActivityCommonLogicBusiness;

    //todo:customer Type fit
    @Override
    public String formDealerRangeRuleCode(String tenantId, String ruleJsonString) {
        List<IFilter> filters = JSON.parseObject(ruleJsonString, new TypeReference<List<IFilter>>() {
        });
        filters.forEach(filter -> {
            Filter recordTypeFilter = new Filter();
            recordTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
            recordTypeFilter.setOperator(Operator.IN);
            recordTypeFilter.setFieldValues(storeBusiness.findDealerRecordType(tenantId));
            recordTypeFilter.setConnector("AND");
            filter.getFilters().add(recordTypeFilter);
        });
        Wheres where = new Wheres();
        String conditionPattern = formConditionPattern(filters, where);
        List<ConditionDto> conditionDtoList = simplifyFilters(filters, where);
        log.info("conditionPattern:{},conditions:{}", conditionPattern, conditionDtoList);
        return conditionAdapter.publish(Integer.valueOf(tenantId), -10000, ApiNames.ACCOUNT_OBJ, conditionPattern, conditionDtoList);
    }

    @Override
    public Map<String, Boolean> judgeStoreInActivitiesStoreRange(String tenantId, String storeId, String dealerId, List<IObjectData> enableActivities, Boolean fastJumpOut, Boolean validateUnifiedActivity) {
        if (CollectionUtils.isEmpty(enableActivities)) {
            return new HashMap<>();
        }
        if (validateUnifiedActivity) {
            enableActivities = unifiedActivityCommonLogicBusiness.getValidActivitiesByDealerId(tenantId, enableActivities, dealerId).getActivities();
        }
        Map<String, Map<String, JSONObject>> typeMap = getTypeMap(enableActivities, TPMActivityFields.STORE_RANGE);
        Map<String, IObjectData> activityMap = enableActivities.stream().collect(Collectors.toMap(DBRecord::getId, v -> v, (a, b) -> a));
        Map<String, Boolean> result = new HashMap<>();
        StopWatch stopWatch = StopWatch.create("judgeStoreInActivitiesStoreRange");
        try {
            List<String> types = new ArrayList<>(typeMap.keySet());
            types.sort(Comparator.comparingInt(String::length));
            for (String type : types) {
                stopWatch.lap("start_check_type:" + type);
                Map<String, JSONObject> jsonMap = typeMap.get(type);
                if (fastJumpOut && result.containsValue(true)) {
                    return result;
                }

                switch (UseRangeEnum.valueOf(type.toUpperCase())) {
                    case ALL:
                        // 经销商匹配直接相等
                        jsonMap.forEach((activityId, data) -> {
                            IObjectData activity = activityMap.get(activityId);
                            String customerType = activity.get(TPMActivityFields.CUSTOMER_TYPE, String.class, ActivityCustomerTypeEnum.DEALER_STORE.value());
                            String activityDealerId = activity.get(TPMActivityFields.DEALER_ID, String.class);
                            if (ActivityCustomerTypeEnum.BRAND.value().equals(customerType)) {
                                result.put(activityId, true);
                            } else if (ActivityCustomerTypeEnum.DEALER_STORE.value().equals(customerType)) {
                                result.put(activityId, Strings.isNullOrEmpty(activityDealerId) || activityDealerId.equals(dealerId));
                            } else if (ActivityCustomerTypeEnum.DEALER.value().equals(customerType)) {
                                result.put(activityId, storeId.equals(dealerId) && (Strings.isNullOrEmpty(activityDealerId) || storeId.equals(activityDealerId)));
                            } else {
                                result.put(activityId, !storeId.equals(dealerId) && !storeId.equals(activityDealerId));
                            }
                        });
                        stopWatch.lap(" finished_check_type:" + type);
                        break;
                    case FIXED:

                        for (List<String> activityIds : Lists.partition(new ArrayList<>(jsonMap.keySet()), 2000)) {
                            //过滤已经确定有效的
                            List<String> validIds = findActivityIdFromActivityFixedStore(tenantId, storeId, activityIds);
                            validIds.forEach(id -> result.put(id, true));
                            stopWatch.lap(" separate_check_type:" + type);
                            if (fastJumpOut && CollectionUtils.isNotEmpty(validIds)) {
                                return result;
                            }
                            stopWatch.lap(" finished_check_type:" + type);
                        }
                        break;
                    case CONDITION:
                        Map<String, String> code2ActivityId = getCode2DataIdMap(jsonMap);
                        Map<String, Boolean> ruleMatchResultMap = conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(tenantId), -10000, ApiNames.ACCOUNT_OBJ, storeId, code2ActivityId.keySet());
                        ruleMatchResultMap.forEach((code, matched) -> result.put(code2ActivityId.get(code), matched));

                        stopWatch.lap(" finished_check_type:" + type);

                        break;
                    default:
                        log.info("nothing matched. type:{}", type);
                }
            }
        } finally {
            stopWatch.log();
        }
        return result;
    }

    private boolean dealerJudgement(String storeId, String dealerId, boolean fastJumpOut, Map<String, Boolean> result, Map<String, JSONObject> jsonMap, Map<String, IObjectData> activityMap) {
        for (String s : jsonMap.keySet()) {
            IObjectData activity = activityMap.get(s);
            String customerType = activity.get(TPMActivityFields.CUSTOMER_TYPE, String.class, ActivityCustomerTypeEnum.DEALER_STORE.value());
            String activityDealerId = activity.get(TPMActivityFields.DEALER_ID, String.class);
            if (ActivityCustomerTypeEnum.DEALER_STORE.value().equals(customerType) || ActivityCustomerTypeEnum.DEALER.value().equals(customerType)) {
                boolean noDealerActivityOrInDealerActivity = storeId.equals(dealerId) && (Strings.isNullOrEmpty(activityDealerId) || dealerId.equals(activityDealerId));
                result.put(s, noDealerActivityOrInDealerActivity);
                if (fastJumpOut && noDealerActivityOrInDealerActivity) {
                    return true;
                }
            }
        }
        return false;
    }


    @Override
    public Map<String, Boolean> judgeDealerInActivitiesDealerRange(String tenantId, String dealerId, List<IObjectData> unifiedActivities) {
        if (CollectionUtils.isEmpty(unifiedActivities) || Strings.isNullOrEmpty(dealerId)) {
            return new HashMap<>();
        }
        Map<String, Map<String, JSONObject>> typeMap = getTypeMap(unifiedActivities, TPMActivityUnifiedCaseFields.STORE_RANGE);
        Map<String, Boolean> result = new HashMap<>();
        StopWatch stopWatch = StopWatch.create("judgeDealerInActivitiesDealerRange");
        try {
            for (String type : typeMap.keySet()) {
                Map<String, JSONObject> jsonMap = typeMap.get(type);
                stopWatch.lap("start_check_type:" + type);
                switch (UseRangeEnum.valueOf(type.toUpperCase())) {
                    case ALL:
                        jsonMap.forEach((activityId, data) -> result.put(activityId, !Strings.isNullOrEmpty(dealerId)));
                        stopWatch.lap(" finished_check_type:" + type);
                        break;
                    case FIXED:
                        jsonMap.keySet().forEach(id -> result.put(id, false));
                        if (Strings.isNullOrEmpty(dealerId)) {
                            break;
                        }
                        for (List<String> unifiedIds : Lists.partition(new ArrayList<>(jsonMap.keySet()), 500)) {
                            findUnifiedActivityIdFromUnifiedActivityFixedStore(tenantId, dealerId, unifiedIds).forEach(id -> result.put(id, true));
                            stopWatch.lap(" separate_check_type:" + type);
                        }
                        stopWatch.lap(" finished_check_type:" + type);
                        break;
                    case CONDITION:
                        if (Strings.isNullOrEmpty(dealerId)) {
                            jsonMap.keySet().forEach(id -> result.put(id, false));
                            break;
                        }
                        Map<String, String> code2ActivityId = getCode2DataIdMap(jsonMap);
                        Map<String, Boolean> ruleMatchResultMap = conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(tenantId), -10000, ApiNames.ACCOUNT_OBJ, dealerId, code2ActivityId.keySet());
                        ruleMatchResultMap.forEach((code, matched) -> result.put(code2ActivityId.get(code), matched));
                        stopWatch.lap(" finished_check_type:" + type);
                        break;
                    default:
                        log.info("nothing matched. type:{}", type);
                }
            }
        } finally {
            stopWatch.log();
        }
        return result;
    }

    @Override
    public String newUnifiedDealerRangeConditionCode(String tenantId, String storeRangeString) {
        JSONObject rangeObj = JSON.parseObject(storeRangeString);
        if (rangeObj.get("type") != null && rangeObj.getString("type").toUpperCase().equals(UseRangeEnum.CONDITION.value())) {
            String value = rangeObj.getString("value");
            String code = formDealerRangeRuleCode(tenantId, value);
            rangeObj.put("code", code);
            return rangeObj.toJSONString();
        }
        return storeRangeString;
    }

    @Override
    public String formActivityStoreRangeRuleCode(String tenantId, String customerType, String ruleJsonString, String dealerId) {
        List<IFilter> filters = JSON.parseObject(ruleJsonString, new TypeReference<List<IFilter>>() {
        });
        Wheres where = new Wheres();
        if (ActivityCustomerTypeEnum.DEALER.value().equals(customerType)) {
            filters.forEach(filter -> {
                Filter recordTypeFilter = new Filter();
                recordTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
                recordTypeFilter.setOperator(Operator.IN);
                recordTypeFilter.setFieldValues(storeBusiness.findDealerRecordType(tenantId));
                recordTypeFilter.setConnector("AND");
                filter.getFilters().add(recordTypeFilter);
            });
        } else if (ActivityCustomerTypeEnum.STORE.value().equals(customerType)) {
            filters.forEach(filter -> {
                Filter recordTypeFilter = new Filter();
                recordTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
                recordTypeFilter.setOperator(Operator.NIN);
                recordTypeFilter.setFieldValues(storeBusiness.findDealerRecordType(tenantId));
                recordTypeFilter.setConnector("AND");
                filter.getFilters().add(recordTypeFilter);
            });
        }

        //改成a or b
        if (!Strings.isNullOrEmpty(dealerId)) {
            where.setFilters(Lists.newArrayList());
            where.setConnector("AND");
            Filter dealerIdFilter = new Filter();
            dealerIdFilter.setFieldName(storeBusiness.findDealerFieldApiName(tenantId));
            dealerIdFilter.setOperator(Operator.EQ);
            dealerIdFilter.setFieldValues(Lists.newArrayList(dealerId));
            dealerIdFilter.setConnector("OR");
            where.getFilters().add(dealerIdFilter);

            if (ActivityCustomerTypeEnum.DEALER.value().equals(customerType) || ActivityCustomerTypeEnum.DEALER_STORE.value().equals(customerType)) {
                Filter deaderFilter = new Filter();
                deaderFilter.setConnector("AND");
                Filter selfStoreFilter = new Filter();
                selfStoreFilter.setFieldName(CommonFields.ID);
                selfStoreFilter.setOperator(Operator.EQ);
                selfStoreFilter.setFieldValues(Lists.newArrayList(dealerId));
                selfStoreFilter.setConnector("OR");
                where.getFilters().add(selfStoreFilter);
            }
        }

        String conditionPattern = formConditionPattern(filters, where);
        List<ConditionDto> conditionDtoList = simplifyFilters(filters, where);

        System.out.println(conditionPattern);
        System.out.println(JSON.toJSONString(conditionDtoList));
        log.info("conditionPattern:{},conditions:{}", conditionPattern, conditionDtoList);
        return conditionAdapter.publish(Integer.valueOf(tenantId), -10000, ApiNames.ACCOUNT_OBJ, conditionPattern, conditionDtoList);
    }

    @Override
    public String newActivityStoreRangeCondition(String tenantId, String customerType, String storeRangeString, String dealerId) {
        JSONObject rangeObj = JSON.parseObject(storeRangeString);
        if (rangeObj.get("type") != null && rangeObj.getString("type").toUpperCase().equals(UseRangeEnum.CONDITION.value())) {
            String value = rangeObj.getString("value");
            String code = formActivityStoreRangeRuleCode(tenantId, customerType, value, dealerId);
            rangeObj.put("code", code);
            return rangeObj.toJSONString();
        }
        return storeRangeString;
    }

    @Override
    public Map<String, Boolean> judgeProductInActivitiesProductRange(String tenantId, String productId, List<IObjectData> activities, IObjectData serialNumber) {
        Map<String, Map<String, JSONObject>> typeMap = getTypeMap(activities, TPMActivityFields.PRODUCT_RANGE);
        Map<String, Boolean> result = new HashMap<>();
        typeMap.forEach((type, activityId2RangeMap) -> {
            if (UseRangeEnum.ALL.value().equalsIgnoreCase(type)) {
                activityId2RangeMap.keySet().forEach(v -> result.put(v, true));
            } else if (UseRangeEnum.FIXED.value().equalsIgnoreCase(type)) {
                Set<String> existsActivityIds = serialNumber != null ?
                        filterByFixedProductAndFreshStandard(tenantId, productId, activities.stream().filter(v -> activityId2RangeMap.containsKey(v.getId())).collect(Collectors.toList()), serialNumber) :
                        filterByFixedProduct(tenantId, productId, new ArrayList<>(activityId2RangeMap.keySet()));
                activityId2RangeMap.keySet().forEach(v -> result.put(v, existsActivityIds.contains(v)));
            } else if (UseRangeEnum.CONDITION.value().equalsIgnoreCase(type)) {
                activityId2RangeMap.forEach((activityId, conditionMap) -> {
                    List<Wheres> wheres = conditionMap.getJSONArray("value").toJavaList(Wheres.class);
                    result.put(activityId, productIsInConditionProduct(tenantId, productId, wheres));
                });
            } else {
                log.info("unknown type:{},rangeMap:{}", type, activityId2RangeMap);
                activityId2RangeMap.keySet().forEach(v -> result.put(v, false));
            }
        });
        return result;
    }


    private boolean productIsInConditionProduct(String tenantId, String productId, List<Wheres> wheres) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                SearchQueryUtil.filter(CommonFields.ID, Operator.EQ, Lists.newArrayList(productId))
        ));
        query.setWheres(wheres);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);

        List<IObjectData> products = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.PRODUCT_OBJ, query, Lists.newArrayList(CommonFields.ID));
        return !products.isEmpty();
    }

    private Set<String> filterByFixedProductAndFreshStandard(String tenantId, String productId, List<IObjectData> activities, IObjectData serialNumber) {
        Set<String> enableSet = Sets.newHashSet();
        if (CollectionUtils.isEmpty(activities)) {
            return enableSet;
        }
        Map<String, List<String>> freshType2ActivityIdMap = activities.stream().collect(Collectors.groupingBy(v -> v.get(TPMActivityFields.PRODUCT_RANGE_FRESH_STANDARD, String.class), Collectors.mapping(DBRecord::getId, Collectors.toList())));
        freshType2ActivityIdMap.forEach((type, activityIds) -> {
            if (TPMActivityFields.ProductRangeFreshStandard.NO_LIMIT.equalsIgnoreCase(type)) {
                enableSet.addAll(filterByFixedProduct(tenantId, productId, activityIds));
            } else if (TPMActivityFields.ProductRangeFreshStandard.BY_DATE_RANGE.equalsIgnoreCase(type)) {
                enableSet.addAll(filterProductRangeByDateRange(tenantId, productId, activityIds, serialNumber));
            } else if (TPMActivityFields.ProductRangeFreshStandard.BY_REMAINING_DAYS.equalsIgnoreCase(type)) {
                enableSet.addAll(filterProductRangeByRemainingDays(tenantId, productId, activityIds, serialNumber));
            }
        });
        return enableSet;
    }

    private List<String> filterProductRangeByRemainingDays(String tenantId, String productId, List<String> activityIds, IObjectData serialNumber) {
        IObjectData skuObj = serviceFacade.findObjectData(User.systemUser(tenantId), productId, ApiNames.PRODUCT_OBJ);
        // 产品保质期
        Integer qualityGuaranteePeriod = skuObj.get(ProductFields.QUALITY_GUARANTEE_PERIOD, Integer.class);
        if (Objects.isNull(qualityGuaranteePeriod)) {
            log.warn("missing quality guarantee period settings : {}", skuObj.getName());
            throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_SCAN_CODE_SERVICE_13));
        }

        // 生产日期
        Long manufactureDate = serialNumber.get(FMCGSerialNumberFields.MANUFACTURE_DATE, Long.class);
        if (Objects.isNull(manufactureDate)) {
            log.warn("missing manufacture settings : {}/{}", skuObj.getName(), serialNumber.getName());
            throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_SCAN_CODE_SERVICE_14));
        }

        // 生产日期 + 产品保质期 - 当前日期 = 剩余有效期
        Long remainingDays = (toDayStart(manufactureDate) + qualityGuaranteePeriod * 24 * 60 * 60 * 1000L - toDayStart(System.currentTimeMillis())) / (24 * 60 * 60 * 1000);
        log.info("product remaining quality guarantee days : {}", remainingDays);

        List<String> validActivityIds = new ArrayList<>();

        validActivityIds.addAll(filterProductRangeByMatchType(tenantId, productId, activityIds, TPMActivityProductRangeFields.MATCH_METHOD__BIG_DATE, remainingDays));
        validActivityIds.addAll(filterProductRangeByMatchType(tenantId, productId, activityIds, TPMActivityProductRangeFields.MATCH_METHOD__NEW_GOODS, remainingDays));

        return validActivityIds;
    }

    private Collection<String> filterProductRangeByMatchType(String tenantId, String productId, List<String> activityIds, String matchMethod, Long remainingDays) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                SearchQueryUtil.filter(TPMActivityProductRangeFields.ACTIVITY_ID, Operator.IN, activityIds),
                SearchQueryUtil.filter(TPMActivityProductRangeFields.PRODUCT_ID, Operator.EQ, Lists.newArrayList(productId))
        ));
        query.getFilters().add(SearchQueryUtil.filter(TPMActivityProductRangeFields.MATCH_METHOD, Operator.EQ, Lists.newArrayList(matchMethod)));
        if (matchMethod.equals(TPMActivityProductRangeFields.MATCH_METHOD__NEW_GOODS)) {
            query.getFilters().add(SearchQueryUtil.filter(TPMActivityProductRangeFields.TO_BE_EXPIRED_DAYS, Operator.LTE, Lists.newArrayList(String.valueOf(remainingDays))));
        } else {
            query.getFilters().add(SearchQueryUtil.filter(TPMActivityProductRangeFields.TO_BE_EXPIRED_DAYS, Operator.GTE, Lists.newArrayList(String.valueOf(remainingDays))));
        }
        List<IObjectData> productRanges = CommonUtils.queryAllDataInFields(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PRODUCT_RANGE_OBJ, query, Lists.newArrayList(CommonFields.ID, TPMActivityProductRangeFields.ACTIVITY_ID));

        return productRanges.stream().map(v -> v.get(TPMActivityProductRangeFields.ACTIVITY_ID, String.class)).collect(Collectors.toList());
    }

    private List<String> filterProductRangeByDateRange(String tenantId, String productId, List<String> activityIds, IObjectData serialNumber) {
        String manufactureDate = serialNumber.get(FMCGSerialNumberFields.MANUFACTURE_DATE, String.class);
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                SearchQueryUtil.filter(TPMActivityProductRangeFields.ACTIVITY_ID, Operator.IN, activityIds),
                SearchQueryUtil.filter(TPMActivityProductRangeFields.PRODUCT_ID, Operator.EQ, Lists.newArrayList(productId)),
                SearchQueryUtil.filter(TPMActivityProductRangeFields.MANUFACTURE_DATE_START, Operator.LTE, Lists.newArrayList(manufactureDate)),
                SearchQueryUtil.filter(TPMActivityProductRangeFields.MANUFACTURE_DATE_END, Operator.GTE, Lists.newArrayList(manufactureDate))
        ));
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);
        List<IObjectData> productRanges = CommonUtils.queryAllDataInFields(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PRODUCT_RANGE_OBJ, query, Lists.newArrayList(CommonFields.ID, TPMActivityProductRangeFields.ACTIVITY_ID));

        return productRanges.stream().map(v -> v.get(TPMActivityProductRangeFields.ACTIVITY_ID, String.class)).collect(Collectors.toList());
    }

    private long toDayStart(long time) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneId.systemDefault());
        return localDateTime.toLocalDate().atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }


    private Set<String> filterByFixedProduct(String tenantId, String productId, List<String> activityIds) {
        if (CollectionUtils.isEmpty(activityIds)) {
            return Sets.newHashSet();
        }
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                SearchQueryUtil.filter(TPMActivityProductRangeFields.ACTIVITY_ID, Operator.IN, activityIds),
                SearchQueryUtil.filter(TPMActivityProductRangeFields.PRODUCT_ID, Operator.EQ, Lists.newArrayList(productId))
        ));
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);

        List<IObjectData> productRanges = CommonUtils.queryAllDataInFields(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PRODUCT_RANGE_OBJ, query, Lists.newArrayList(CommonFields.ID, TPMActivityProductRangeFields.ACTIVITY_ID));
        return productRanges.stream().map(v -> v.get(TPMActivityProductRangeFields.ACTIVITY_ID, String.class)).collect(Collectors.toSet());
    }


    private List<String> getActivityDealerIds(User systemUser, IObjectData activity) {
        String dealerId = activity.get(TPMActivityFields.DEALER_ID, String.class);
        List<String> dealerIds = new ArrayList<>();

        if (Strings.isNullOrEmpty(dealerId)) {
            String unifiedActivityId = activity.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class);
            if (!Strings.isNullOrEmpty(unifiedActivityId)) {
                IObjectData unifiedActivity = serviceFacade.findObjectData(systemUser, unifiedActivityId, ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
                dealerIds.addAll(unifiedActivityCommonLogicBusiness.getDealerIdsOfUnifiedActivity(systemUser.getTenantId(), unifiedActivity));
            } else {
                dealerIds.add("-1");
            }
        } else {
            dealerIds.add(dealerId);
        }
        return dealerIds;
    }


    private List<String> findUnifiedActivityIdFromUnifiedActivityFixedStore(String tenantId, String dealerId, List<String> unifiedIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(TPMActivityDealerScopeFields.ACTIVITY_UNIFIED_CASE_ID);
        activityIdFilter.setOperator(Operator.IN);
        activityIdFilter.setFieldValues(unifiedIds);
        query.getFilters().add(activityIdFilter);

        Filter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(TPMActivityDealerScopeFields.DEALER_ID);
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(dealerId));
        query.getFilters().add(storeIdFilter);
        return CommonUtils.queryAllDataInFields(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_DEALER_SCOPE_OBJ, query, Lists.newArrayList(TPMActivityDealerScopeFields.ACTIVITY_UNIFIED_CASE_ID))
                .stream().map(v -> v.get(TPMActivityDealerScopeFields.ACTIVITY_UNIFIED_CASE_ID, String.class)).collect(Collectors.toList());
    }

    private List<String> findActivityIdFromActivityFixedStore(String tenantId, String dealerId, List<String> activityIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(0);
        query.setOffset(0);
        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(TPMActivityStoreFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.IN);
        activityIdFilter.setFieldValues(activityIds);
        query.getFilters().add(activityIdFilter);

        Filter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(TPMActivityStoreFields.STORE_ID);
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(dealerId));
        query.getFilters().add(storeIdFilter);
        return CommonUtils.queryAllDataInFields(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_STORE_OBJ, query, Lists.newArrayList(TPMActivityStoreFields.ACTIVITY_ID))
                .stream().map(v -> v.get(TPMActivityStoreFields.ACTIVITY_ID, String.class)).collect(Collectors.toList());
    }


    private Map<String, String> getCode2DataIdMap(Map<String, JSONObject> jsonMap) {
        Map<String, String> code2DataId = new HashMap<>();
        jsonMap.forEach((id, jsonData) -> {
            String code = jsonData.getString("code");
            if (Strings.isNullOrEmpty(code)) {
                log.info("activity:{},has no rule code.", id);
                return;
            }
            code2DataId.put(code, id);
        });
        return code2DataId;
    }

    private Map<String, Map<String, JSONObject>> getTypeMap(List<IObjectData> data, String storeRangeField) {
        Map<String, Map<String, JSONObject>> typeMap = new HashMap<>(4);
        data.forEach(iObjectData -> {
            String json = iObjectData.get(storeRangeField, String.class);
            JSONObject range = JSONObject.parseObject(json);
            String type = range.getString("type");
            Map<String, JSONObject> jsonMap = typeMap.getOrDefault(type, new HashMap<>());
            jsonMap.put(iObjectData.getId(), range);
            typeMap.putIfAbsent(type, jsonMap);
        });
        return typeMap;
    }


    private String formConditionPattern(List<IFilter> filters, Wheres where) {
        StringBuilder conditionCode = new StringBuilder();
        AtomicInteger index = new AtomicInteger(0);
        for (int i = 0; i < filters.size(); i++) {
            IFilter iFilter = filters.get(i);
            conditionCode.append("(");
            formCodeByFilter(iFilter.getFilters(), index, conditionCode);
            conditionCode.append(" ) ");
            if (i != filters.size() - 1) {
                conditionCode.append(iFilter.getConnector().toLowerCase()).append(" ");
            }
        }
        if (CollectionUtils.isNotEmpty(where.getFilters())) {
            conditionCode.insert(0, "( ").append(" ) and (");
            formCodeByFilter(where.getFilters(), index, conditionCode);
            conditionCode.append(" ) ");
        }
        return conditionCode.toString();
    }

    private void formCodeByFilter(List<IFilter> filterList, AtomicInteger index, StringBuilder conditionCode) {
        for (int j = 0; j < filterList.size(); j++) {
            IFilter innerFilter = filterList.get(j);
            conditionCode.append(index.getAndIncrement()).append(" ");
            if (j != filterList.size() - 1) {
                conditionCode.append(innerFilter.getConnector().toLowerCase()).append(" ");
            }
        }
    }

    private List<ConditionDto> simplifyFilters(List<IFilter> filters, Wheres wheres) {
        List<ConditionDto> conditions = new ArrayList<>();
        AtomicInteger atomicInteger = new AtomicInteger(0);
        if (CollectionUtils.isNotEmpty(filters)) {
            filters.forEach(filter -> filter.getFilters().forEach(innerFilter -> {
                ConditionDto conditionDto = new ConditionDto();
                conditionDto.setFieldName(innerFilter.getFieldName());
                conditionDto.setValues(innerFilter.getFieldValues());
                conditionDto.setOperator(OperatorRelationEnum.translatePaasOp(innerFilter.getOperator().name()));
                conditionDto.setRowNo(atomicInteger.getAndIncrement());
                conditions.add(conditionDto);
            }));
        }
        if (CollectionUtils.isNotEmpty(wheres.getFilters())) {
            wheres.getFilters().forEach(innerFilter -> {
                ConditionDto conditionDto = new ConditionDto();
                conditionDto.setFieldName(innerFilter.getFieldName());
                conditionDto.setValues(innerFilter.getFieldValues());
                conditionDto.setOperator(OperatorRelationEnum.translatePaasOp(innerFilter.getOperator().name()));
                conditionDto.setRowNo(atomicInteger.getAndIncrement());
                conditions.add(conditionDto);
            });
        }
        return conditions;
    }
}
