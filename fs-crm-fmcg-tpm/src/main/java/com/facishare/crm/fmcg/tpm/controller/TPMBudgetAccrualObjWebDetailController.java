package com.facishare.crm.fmcg.tpm.controller;

import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccrualDetailFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/2 上午11:12
 */
public class TPMBudgetAccrualObjWebDetailController extends StandardWebDetailController {

    private boolean isAllowAccrualNegativeMoney = false;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        this.isAllowAccrualNegativeMoney = TPMGrayUtils.isAllowAccrualNegativeMoney(controllerContext.getTenantId());
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result finalResult = super.after(arg, result);
        buttonFilter(finalResult);
        return finalResult;
    }


    @SneakyThrows
    private void buttonFilter(Result result) {
        List<IComponent> components = result.getLayout().toLayout().getComponents();
        if (isFake(result.getData().getId())) {
            for (IComponent component : components) {

                if ("head_info".equals(component.getName())) {
                    List<IButton> buttons = component.getButtons();
                    buttons.removeIf(button -> ObjectAction.BUDGET_ACCRUAL.getActionCode().equals(button.getAction()));
                    component.setButtons(buttons);
                }
            }
            if ("mobile".equals(arg.getLayoutAgentType())) {
                List<IButton> buttons = result.getLayout().toLayout().getButtons();
                buttons.removeIf(button -> ObjectAction.BUDGET_ACCRUAL.getActionCode().equals(button.getAction()));
                result.getLayout().toLayout().setButtons(buttons);
            }
        }
        result.getLayout().toLayout().setComponents(components);
    }

    public boolean isFake(String masterId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setSearchSource("db");
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setLimit(2000);

        Filter masterIdFilter = new Filter();
        masterIdFilter.setFieldName(TPMBudgetAccrualDetailFields.ACCRUAL_ID);
        masterIdFilter.setOperator(Operator.EQ);
        masterIdFilter.setFieldValues(Lists.newArrayList(masterId));

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMBudgetAccrualDetailFields.STATUS);
        statusFilter.setOperator(Operator.EQ);
        statusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccrualDetailFields.Status.EXCLUDE));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        Filter amountFilter = new Filter();
        amountFilter.setFieldName(TPMBudgetAccrualDetailFields.ACTUAL_AMOUNT);
        amountFilter.setOperator(Operator.GT);
        amountFilter.setFieldValues(Lists.newArrayList("0"));

        if (this.isAllowAccrualNegativeMoney) {
            query.setFilters(Lists.newArrayList(masterIdFilter, statusFilter, lifeStatusFilter));
        } else {
            query.setFilters(Lists.newArrayList(masterIdFilter, statusFilter, lifeStatusFilter, amountFilter));
        }

        List<IObjectData> aggList = serviceFacade.aggregateFindBySearchQuery(controllerContext.getTenantId(), query, ApiNames.TPM_BUDGET_ACCRUAL_DETAIL_OBJ, TPMBudgetAccrualDetailFields.ACCRUAL_ID, "count", "");
        if (!CollectionUtils.isEmpty(aggList)) {
            Integer count = aggList.get(0).get("groupbycount", Integer.class, 0);
            return count == 0;
        }
        return true;
    }
}
