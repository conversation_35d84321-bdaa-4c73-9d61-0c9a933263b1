package com.facishare.crm.fmcg.tpm.service;

import com.fxiaoke.cloud.DataPersistor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;


public class BuryService {
    private static final String MODULE = "fs_crm_fmcg_service_tpm";
    private static final String MODULE_V2 = "fs_crm_fmcg_service_tpm2";
    private static final String BUDGET_MODULE = "budget";
    private static final String SERVICE = "fs_crm_fmcg_service";

    private BuryService() {

    }

    public static void asyncTpmLog(Integer enterpriseId, Integer employeeId, String subModule, String operation) {
        try {
            Map<String, Object> map = new HashMap<>();
            buildupSimpleLog(enterpriseId, employeeId, MODULE_V2, subModule, operation, null, map);
            DataPersistor.asyncLog(SERVICE, map);
        } catch (Exception ex) {
            //ignore
        }
    }

    public static void asyncTpmLog(Integer enterpriseId, Integer employeeId, String subModule, String operation, boolean isTpm2Tenant) {

        Map<String, Object> map = new HashMap<>();
        if (isTpm2Tenant) {
            buildupSimpleLog(enterpriseId, employeeId, MODULE_V2, subModule, operation, null, map);
        } else {
            buildupSimpleLog(enterpriseId, employeeId, MODULE, subModule, operation, null, map);
        }
        DataPersistor.asyncLog(SERVICE, map);
    }

    public static void asyncTpmLogV2(Integer enterpriseId, Integer employeeId, String subModule, String operation) {
        Map<String, Object> map = new HashMap<>();
        buildupSimpleLog(enterpriseId, employeeId, MODULE_V2, subModule, operation, null, map);
        DataPersistor.asyncLog(SERVICE, map);
    }

    public static void asyncBudgetLog(String enterpriseId, Integer employeeId, String subModule, String operation) {
        Map<String, Object> map = new HashMap<>();
        buildupSimpleLog(Integer.valueOf(enterpriseId), employeeId, BUDGET_MODULE, subModule, operation, null, map);
        DataPersistor.asyncLog(SERVICE, map);
    }

    public static void asyncLog(Integer enterpriseId, Integer employeeId, String module, String subModule, String operation) {
        Map<String, Object> map = new HashMap<>();
        buildupSimpleLog(enterpriseId, employeeId, module, subModule, operation, null, map);
        DataPersistor.asyncLog(SERVICE, map);
    }

    public static void asyncTpmAmountLog(Integer enterpriseId, Integer employeeId, String subModule, String operation, Double amount) {
        Map<String, Object> map = new HashMap<>();
        buildupSimpleLog(enterpriseId, employeeId, MODULE_V2, subModule, operation, amount, map);
        DataPersistor.asyncLog(SERVICE, map);
    }

    public static void asyncBudgetAmountLog(Integer enterpriseId, Integer employeeId, String subModule, String operation, Double amount) {
        Map<String, Object> map = new HashMap<>();
        buildupSimpleLog(enterpriseId, employeeId, BUDGET_MODULE, subModule, operation, amount, map);
        DataPersistor.asyncLog(SERVICE, map);
    }

    private static void buildupSimpleLog(Integer enterpriseId, Integer employeeId, String module, String subModule, String operation, Double amount, Map<String, Object> data) {
        data.put("tenantId", enterpriseId);
        data.put("userId", employeeId);
        data.put("fullUserId", enterpriseId + "." + employeeId);
        data.put("module", module);
        data.put("subModule", subModule);
        data.put("operation", operation);
        if (amount != null) {
            data.put("amount", amount);
        }
        data.put("eventId", module + "_" + subModule + "_" + operation);
    }
}
