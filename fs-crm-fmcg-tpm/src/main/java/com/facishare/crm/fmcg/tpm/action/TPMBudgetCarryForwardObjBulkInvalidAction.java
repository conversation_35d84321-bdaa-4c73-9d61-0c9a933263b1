package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/7/25 18:32
 */
@SuppressWarnings("all")
public class TPMBudgetCarryForwardObjBulkInvalidAction extends StandardBulkInvalidAction {

    @Override
    protected void before(Arg arg) {
        throw new ValidateException(I18N.text(I18NKeys.BUDGET_CARRY_FORWARD_OBJ_BULK_INVALID_ACTION_0));
    }
}
