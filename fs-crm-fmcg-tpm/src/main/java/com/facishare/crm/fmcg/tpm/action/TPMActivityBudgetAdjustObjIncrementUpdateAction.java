package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;
import de.lab4inf.math.util.Strings;

/**
 * <AUTHOR>
 * @date 2021/7/21 下午3:16
 */
public class TPMActivityBudgetAdjustObjIncrementUpdateAction extends StandardIncrementUpdateAction {

    @Override
    protected void before(Arg arg) {

        if(actionContext.isFromFunction()||actionContext.isFromOpenAPI()){
            arg.getData().keySet().forEach(key -> {
                if (!Strings.isNullOrEmpty(key) && !key.endsWith("__c") && !key.equals("_id"))
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_BUDGET_ADJUST_OBJ_INCREMENT_UPDATE_ACTION_0));
            });
        }
        super.before(arg);
    }
}
