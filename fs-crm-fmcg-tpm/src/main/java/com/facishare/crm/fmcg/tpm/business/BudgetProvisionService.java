package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetOperator;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetProvisionService;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetMethodEnum;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ConsumeConfigType;
import com.facishare.crm.fmcg.tpm.service.abstraction.ITransactionProxy;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetFieldRelationVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetNewConsumeRuleVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetTableManualNodeVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetTableProvisionVO;
import com.facishare.crm.fmcg.tpm.web.service.BudgetNewConsumeRuleService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.StopWatch;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class BudgetProvisionService implements IBudgetProvisionService {
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private BudgetNewConsumeRuleService budgetNewConsumeRuleService;

    @Resource
    private RedissonClient redissonCmd;

    @Resource
    private ITransactionProxy transProxy;

    @Override
    public void occupyBudgetByProvision(String tenantId) {

        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1,
                Lists.newArrayList(
                        SearchQueryUtil.filter(TPMBudgetProvisionObjFields.PROVISION_STATUS, Operator.IN, Lists.newArrayList(TPMBudgetProvisionObjFields.ProvisionStatus.IN_EFFECT,
                                TPMBudgetProvisionObjFields.ProvisionStatus.OCCUPY_FAIL)),
                        SearchQueryUtil.filter(CommonFields.LIFE_STATUS, Operator.EQ, Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL)))
        );
        log.info("occupyBudgetByProvision tenantId is {}", tenantId);
        CommonUtils.executeInAllDataWithFields(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_BUDGET_PROVISION_OBJ, query, Lists.newArrayList(CommonFields.ID), (dataList) -> {
            log.info("data size:{}", dataList.size());
            dataList.forEach(data -> handlerBudgetProvision(tenantId, data.getId()));
        });
        log.info("occupyBudgetByProvision done!");
    }

    private void handlerBudgetProvision(String tenantId, String provisionId) {
        tryLock(provisionId);
        // 获取最新 预提单数据
        IObjectData data = serviceFacade.findObjectData(User.systemUser(tenantId), provisionId, ApiNames.TPM_BUDGET_PROVISION_OBJ);
        try {
            // 去查询预算表，占用预算表。
            occupyBudget(data);
        } catch (Exception e) {
            // 异常，预提失败
            updateProvision(tenantId, data, TPMBudgetProvisionObjFields.ProvisionStatus.OCCUPY_FAIL, e.getMessage(), null);
            log.info("occupyBudgetByProvision err", e);
        } finally {
            unlock(provisionId);
        }
    }

    @Override
    public void reuseOccupyBudget(IObjectData budgetProvision) {
        // 判断预提的预算是否被占用
        String tenantId = budgetProvision.getTenantId();
        String provisionStatus = budgetProvision.get(TPMBudgetProvisionObjFields.PROVISION_STATUS, String.class);
        // 当预提状态是 占用失败，且，没有对应的预算表
        if (TPMBudgetProvisionObjFields.ProvisionStatus.OCCUPY_FAIL.equals(provisionStatus)) {
            // 去查询预算表，占用预算表 。
            handlerBudgetProvision(tenantId, budgetProvision.getId());
        }
    }

    @Override
    public void cancelBudgetProvision(IObjectData budgetProvision) {

        if (Objects.isNull(budgetProvision)) {
            throw new ValidateException("budgetProvision data not found!");
        }

        String tenantId = budgetProvision.getTenantId();
        // 已占用的不可更新。
        String provisionStatus = budgetProvision.get(TPMBudgetProvisionObjFields.PROVISION_STATUS, String.class);
        if (TPMBudgetProvisionObjFields.ProvisionStatus.OCCUPIED.equals(provisionStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_PROVISION_OCCUPIED_NOT_CANCEL));
        }
        if (TPMBudgetProvisionObjFields.ProvisionStatus.INVALID.equals(provisionStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_PROVISION_STATUS_INVALID));
        }
        // 更新预提状态。
        Map<String, Object> updateMap = new HashMap<>(4);
        updateMap.put(TPMBudgetProvisionObjFields.PROVISION_STATUS, TPMBudgetProvisionObjFields.ProvisionStatus.INVALID);
        serviceFacade.updateWithMap(User.systemUser(tenantId), budgetProvision, updateMap);
        IObjectDescribe describe = serviceFacade.findObject(tenantId, ApiNames.TPM_BUDGET_PROVISION_OBJ);
        serviceFacade.logWithCustomMessage(User.systemUser(tenantId), EventType.MODIFY, ActionType.MODIFY, describe, budgetProvision, "取消预提");//ignorei18n
    }

    @Override
    public boolean existsBudgetProvisionByRuleId(String tenantId, String ruleId) {

        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1,
                Lists.newArrayList(SearchQueryUtil.filter(TPMBudgetProvisionObjFields.CONSUME_RULE_ID, Operator.EQ, Lists.newArrayList(ruleId)),
                        SearchQueryUtil.filter(TPMBudgetProvisionObjFields.PROVISION_STATUS, Operator.IN, Lists.newArrayList(TPMBudgetProvisionObjFields.ProvisionStatus.IN_EFFECT,
                                TPMBudgetProvisionObjFields.ProvisionStatus.OCCUPIED, TPMBudgetProvisionObjFields.ProvisionStatus.OCCUPY_FAIL))));
        return !CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_BUDGET_PROVISION_OBJ, query).isEmpty();
    }

    private void updateProvision(String tenantId, IObjectData data, String status, String message, String budgetId) {
        IObjectData dataDb = ObjectDataExt.of(data).copy();
        Map<String, Object> updateMap = new HashMap<>(4);
        updateMap.put(TPMBudgetProvisionObjFields.PROVISION_STATUS, status);
        updateMap.put(TPMBudgetProvisionObjFields.PROVISION_ERROR_MESSAGE, message);
        if (budgetId != null) {
            updateMap.put(TPMBudgetProvisionObjFields.BUDGET_ACCOUNT, budgetId);
        }

        serviceFacade.updateWithMap(User.systemUser(tenantId), data, updateMap);
        IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, ApiNames.TPM_BUDGET_PROVISION_OBJ);
        serviceFacade.log(User.systemUser(tenantId), EventType.MODIFY, ActionType.Modify, objectDescribe, data, updateMap, dataDb);

    }

    private void occupyBudget(IObjectData data) {
        if (data == null) {
            throw new ValidateException("budgetProvision data not found!");
        }
        StopWatch watch = new StopWatch("BudgetProvision occupyBudget.");
        String status = data.get(TPMBudgetProvisionObjFields.PROVISION_STATUS, String.class);
        if (TPMBudgetProvisionObjFields.ProvisionStatus.OCCUPIED.equals(status) ||
                TPMBudgetProvisionObjFields.ProvisionStatus.INVALID.equals(status)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_PROVISION_STATUS_INVALID_OR_OCCUPIED));
        }
        // 1. 查询消费规则
        String ruleId = data.get(TPMBudgetProvisionObjFields.CONSUME_RULE_ID, String.class);
        String tenantId = data.get(CommonFields.TENANT_ID, String.class);
        // 预算预提金额
        BigDecimal provisionAmount = data.get(TPMBudgetProvisionObjFields.PROVISION_AMOUNT, BigDecimal.class);
        String masterRelatedId = data.get(TPMBudgetProvisionObjFields.MASTER_RELATED_OBJECT_DATA_ID, String.class);
        String masterRelatedApiName = data.get(TPMBudgetProvisionObjFields.MASTER_RELATED_OBJECT_API_NAME, String.class);
        String bizTraceId = data.get(TPMBudgetProvisionObjFields.BIZ_TRACE_ID, String.class);
        String approvalTraceId = data.get(TPMBudgetProvisionObjFields.APPROVAL_TRACE_ID, String.class);
        // 业务对象
        IObjectData masterRelatedData = findRelatedObject(tenantId, masterRelatedId, masterRelatedApiName);
        if (masterRelatedData == null) {
            throw new ValidateException("not found related object...");
        }

        List<IObjectData> provisionRecordData = findProvisionRecordData(tenantId, data.getId());
        if (CollectionUtils.isEmpty(provisionRecordData)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_PROVISION_NOT_FOUND_BUDGET_PROVISION));
        }
        watch.lap("BudgetProvision findRelatedData.");

        // 2. 查询消费规则对应的预算表
        BudgetNewConsumeRuleVO consumeRule = findConsumeRuleById(tenantId, ruleId);
        String budgetMethod = consumeRule.getBudgetMethod();

        // 手动选择才使用预提
        if (!BudgetMethodEnum.MANUAL.value().equals(budgetMethod)) {
            return;
        }

        // 规则上预提必须是启用的
        if (!ConsumeConfigType.PROVISION_ENABLE.value().equals(consumeRule.getProvisionStatus())) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUMER_RULE_PROVISION_STATUS_BE_ENABLED));
        }

        List<BudgetTableManualNodeVO> budgetTableManualNodes = consumeRule.getBudgetTableManualNodes();
        if (CollectionUtils.isEmpty(budgetTableManualNodes)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUMER_RULE_NODE_CONFIG_EMPTY));
        }
        IObjectData objectData = provisionRecordData.get(0);
        String nodeId = objectData.get(TPMBudgetProvisionRecordObjFields.CONSUME_NODE_ID, String.class);
        String relatedApiName = objectData.get(TPMBudgetProvisionRecordObjFields.RELATED_OBJECT_API_NAME, String.class);
        String relatedDataId = objectData.get(TPMBudgetProvisionRecordObjFields.RELATED_OBJECT_DATA_ID, String.class);

        BudgetTableManualNodeVO budgetTableManualNodeVO = budgetTableManualNodes.stream()
                .filter(node -> node.getNodeId().equals(nodeId))
                .findFirst()
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKeys.BUDGET_PROVISION_NOT_CONSUME_NODE)));

        String budgetId = null;
        try {
            String apiName = budgetTableManualNodeVO.getBudgetTableFrozen().getApiName();
            String masterDetail = budgetTableManualNodeVO.getBudgetTableFrozen().getMasterDetail();
            BudgetTableProvisionVO budgetTableProvision = budgetTableManualNodeVO.getBudgetTableFrozen().getBudgetTableProvision();
            String type = budgetTableProvision.getBudgetType();
            String level = budgetTableProvision.getBudgetLevel();
            List<BudgetFieldRelationVO> fieldRelation = budgetTableProvision.getFieldRelation();

            // 冻结对象不一样，有主从字段，表示冻结的是从对象
            if (!Strings.isNullOrEmpty(masterDetail) && relatedApiName.equals(apiName)) {
                IObjectData relatedData = findRelatedObject(tenantId, relatedDataId, relatedApiName);
                if (relatedData == null) {
                    throw new ValidateException("not found related object...");
                }
                budgetId = findBudget(tenantId, type, level, fieldRelation, relatedData);
            } else if (apiName.equals(masterRelatedApiName)) {
                // 手动选择预算表的模块，选择的业务对象是预提关联的业务对象
                budgetId = findBudget(tenantId, type, level, fieldRelation, masterRelatedData);
            }
        } catch (Exception e) {
            log.error("查找预算表异常", e);
            throw new ValidateException("find budget Provision error :" + e.getMessage());
        }

        if (Strings.isNullOrEmpty(budgetId)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_PROVISION_BUDGET_ACCOUNT_NOT_FOUND));
        }
        watch.lap("BudgetProvision findBudgetAccountId.");
        // 存在关联的预算表。
        IBudgetOperator operator = BudgetOperatorFactory.initOperator(BizType.CONSUME, User.systemUser(tenantId), budgetId, bizTraceId, approvalTraceId, masterRelatedData);
        try {
            if (!operator.tryLock()) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_21));
            }

            final String finalBudgetId = budgetId;
            transProxy.run(() -> {
                try {
                    // 校验可用金额
                    operator.validateConsumableAmount(provisionAmount);
                    // 生成流水
                    operator.freeze(provisionAmount);
                    // 重算预算金额
                    operator.recalculate();

                    // 更新预算表
                    updateProvision(tenantId, data, TPMBudgetProvisionObjFields.ProvisionStatus.OCCUPIED, null, finalBudgetId);
                    // 手动选择预算表的类型，回填业务对象的关联预算表的字段
                    if (BudgetMethodEnum.MANUAL.value().equals(budgetMethod)) {
                        updateBusinessData(tenantId, finalBudgetId, provisionRecordData, budgetTableManualNodes);
                    }
                } catch (Exception e) {
                    log.error("预算占用事务执行异常", e);
                    throw e;
                }
            });
            watch.lap("BudgetProvision occupy amount.");
        } finally {
            operator.unlock();
        }
        watch.logSlow(300);
    }

    private void updateBusinessData(String tenantId, String budgetId, List<IObjectData> provisionRecordData, List<BudgetTableManualNodeVO> budgetTableManualNodes) {
        // 按消费节点ID分组
        Map<String, List<IObjectData>> objectListMap = provisionRecordData.stream()
                .collect(Collectors.groupingBy(v -> v.get(TPMBudgetProvisionRecordObjFields.CONSUME_NODE_ID, String.class)));

        // 构建nodeId -> nodeVO的映射,避免重复查找
        Map<String, BudgetTableManualNodeVO> nodeMap = budgetTableManualNodes.stream()
                .collect(Collectors.toMap(BudgetTableManualNodeVO::getNodeId, Function.identity()));

        objectListMap.forEach((nodeId, data) -> {
            BudgetTableManualNodeVO nodeVO = nodeMap.get(nodeId);
            if (nodeVO == null) {
                log.error("未找到消费规则节点, nodeId:{}", nodeId);
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_PROVISION_NOT_CONSUME_NODE));
            }

            // 获取关联对象ID列表
            List<String> ids = data.stream()
                    .map(o -> o.get(TPMBudgetProvisionRecordObjFields.RELATED_OBJECT_DATA_ID, String.class))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(ids)) {
                return;
            }

            String apiName = nodeVO.getBudgetTableFrozen().getApiName();
            String relatedBudgetField = nodeVO.getBudgetTableFrozen().getRelationField();

            // 批量查询并更新
            List<IObjectData> dataList = serviceFacade.findObjectDataByIds(tenantId, ids, apiName);
            if (!CollectionUtils.isEmpty(dataList)) {
                Map<String, Object> updateMap = new HashMap<>(2);
                updateMap.put(relatedBudgetField, budgetId);
                serviceFacade.batchUpdateWithMap(User.systemUser(tenantId), dataList, updateMap);
            }
        });
    }

    private List<IObjectData> findProvisionRecordData(String tenantId, String provisionId) {
        Filter budgetProvisionFilter = SearchQueryUtil.filter(TPMBudgetProvisionRecordObjFields.BUDGET_PROVISION_ID, Operator.EQ, Lists.newArrayList(provisionId));
        Filter lifeStatusFilter = SearchQueryUtil.filter(CommonFields.LIFE_STATUS, Operator.EQ, Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));
        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(budgetProvisionFilter, lifeStatusFilter);

        List<IObjectData> objectData = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_BUDGET_PROVISION_RECORD_OBJ, stq, Lists.newArrayList(CommonFields.ID,
                CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.NAME, TPMBudgetProvisionRecordObjFields.BUDGET_PROVISION_ID, TPMBudgetProvisionRecordObjFields.CONSUME_NODE_ID,
                TPMBudgetProvisionRecordObjFields.RELATED_OBJECT_DATA_ID, TPMBudgetProvisionRecordObjFields.RELATED_OBJECT_API_NAME, TPMBudgetProvisionRecordObjFields.AMOUNT));
        if (CollectionUtils.isEmpty(objectData)) {
            log.info("no find budget provision record, tenantId is {}", tenantId);
            return null;
        }
        return objectData;
    }

    private IObjectData findRelatedObject(String tenantId, String relatedId, String relatedApiName) {
        return serviceFacade.findObjectData(User.systemUser(tenantId), relatedId, relatedApiName);
    }

    private String findBudget(String tenantId, String budgetType, String level, List<BudgetFieldRelationVO> fieldRelation, IObjectData relatedObject) {

        Filter budgetTypeFilter = SearchQueryUtil.filter(TPMBudgetAccountFields.BUDGET_TYPE_ID, Operator.EQ, Lists.newArrayList(budgetType));
        Filter budgetNodeFilter = SearchQueryUtil.filter(TPMBudgetAccountFields.BUDGET_NODE_ID, Operator.EQ, Lists.newArrayList(level));
        Filter budgetStatusFilter = SearchQueryUtil.filter(TPMBudgetAccountFields.BUDGET_STATUS, Operator.EQ, Lists.newArrayList(TPMBudgetAccountFields.BUDGET_STATUS__ENABLE));
        Filter lifeStatusFilter = SearchQueryUtil.filter(CommonFields.LIFE_STATUS, Operator.EQ, Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));
        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(budgetTypeFilter, budgetNodeFilter, budgetStatusFilter, lifeStatusFilter);
        //时间字段得额外处理 一下 操作符可能是eql
        fieldRelation.forEach(budgetFieldRelationEntity -> {
            Filter dimensionFilter = new Filter();
            dimensionFilter.setFieldName(budgetFieldRelationEntity.getSourceField());
            dimensionFilter.setOperator(Operator.valueOf(budgetFieldRelationEntity.getOperator().toUpperCase()));
            if (TPMBudgetAccountFields.BUDGET_DEPARTMENT.equals(budgetFieldRelationEntity.getSourceField())) {
                List<String> departments = CommonUtils.cast(relatedObject.get(budgetFieldRelationEntity.getTargetField(), List.class, Lists.newArrayList("-1")), String.class);
                if (CollectionUtils.isEmpty(departments)) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_SERVICE_3));
                }
                dimensionFilter.setFieldValues(departments);
            } else {
                dimensionFilter.setFieldValues(Lists.newArrayList(relatedObject.get(budgetFieldRelationEntity.getTargetField(), String.class, "-*********")));
            }
            stq.getFilters().add(dimensionFilter);
        });
        List<IObjectData> objectData = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_BUDGET_ACCOUNT, stq, Lists.newArrayList(CommonFields.ID));
        if (CollectionUtils.isEmpty(objectData)) {
            log.info("no find budget, tenantId is {}", tenantId);
            return null;
        }
        if (objectData.size() > 1) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_PROVISION_SERVICE_0));
        }
        IObjectData budgetAccount = objectData.get(0);
        log.info("find budget id is {}, tenantId is {}", budgetAccount.getId(), tenantId);

        return budgetAccount.getId();
    }

    private BudgetNewConsumeRuleVO findConsumeRuleById(String tenantId, String ruleId) {
        BudgetNewConsumeRuleVO consumeRule = budgetNewConsumeRuleService.findConsumeRuleById(tenantId, ruleId);
        if (consumeRule == null) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_PROVISION_NOT_FOUND_CONSUME_RULE));
        }
        return consumeRule;
    }

    private void tryLock(String lockKey) {
        RLock lock = redissonCmd.getLock(lockKey);
        try {
            if (!lock.tryLock(10, 25, TimeUnit.SECONDS)) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_PROVISION_PROGRESS_PROVISION_OPERATIONS));
            }
        } catch (InterruptedException e) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_PROVISION_PROGRESS_PROVISION_OPERATIONS));
        }
    }

    private void unlock(String lockKey) {
        RLock lock = redissonCmd.getLock(lockKey);
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
}
