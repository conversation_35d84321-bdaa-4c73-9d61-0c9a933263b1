package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.tpm.api.agreement.InitStoreConfirmModule;
import com.facishare.crm.fmcg.tpm.web.tools.ModuleInitializationService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * <AUTHOR>
 * create time 2021/9/23 15:33
 */
@Slf4j
@SuppressWarnings("unused")
public class TPMActivityAgreementObjInitStoreConfirmModuleController extends PreDefineController<InitStoreConfirmModule.Arg, InitStoreConfirmModule.Result> {

    private static final ModuleInitializationService moduleInitializationService = SpringUtil.getContext().getBean(ModuleInitializationService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    public static final String SUPPER_USER_ID = "-10000";

    @Override
    protected InitStoreConfirmModule.Result doService(InitStoreConfirmModule.Arg arg) {
        // fast fail for not super user request
        if (!SUPPER_USER_ID.equals(controllerContext.getUser().getUpstreamOwnerIdOrUserId())) {
            throw new ValidateException("Unauthorized");
        }

        // init agreement store confirm module
        moduleInitializationService.initAgreementStoreConfirmModule(controllerContext.getTenantId());
        return new InitStoreConfirmModule.Result();
    }
}