package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/15 下午6:11
 */
public interface ITPM2Service {


    boolean isTPM2Tenant(Integer tenantId);

    List<String> queryTPMTenant();

    List<String> queryBudgetTenant();

    boolean isNeedAgreement(Integer tenantId, IObjectData activity);

    boolean existTPMLicenseTenant(Integer tenantId);

    boolean existTPMLicenseTenantV2(Integer tenantId);

    boolean existTPMCodeLicenseTenant(Integer tenantId);

    boolean existTPMLicenseCode(Integer tenantId, String licenseCode);

    boolean existSfaCarPreSaleLicenseCode(Integer tenantId, String licenseCode);

    boolean existFmcgAiProductRecognitionAppLicenseCode(Integer tenantId);
}
