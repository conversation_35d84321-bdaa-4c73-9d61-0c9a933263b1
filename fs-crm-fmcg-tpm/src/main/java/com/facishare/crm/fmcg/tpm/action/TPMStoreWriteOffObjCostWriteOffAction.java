package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.web.contract.ValidationStoreWriteOffData;
import com.facishare.crm.fmcg.tpm.web.service.StoreWriteOffService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.BaseObjectApprovalAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@SuppressWarnings("Duplicates")
public class TPMStoreWriteOffObjCostWriteOffAction extends BaseObjectApprovalAction<TPMStoreWriteOffObjCostWriteOffAction.Arg, TPMStoreWriteOffObjCostWriteOffAction.Result> {

    public static final Logger log = LoggerFactory.getLogger(TPMStoreWriteOffObjCostWriteOffAction.class);

    private final StoreWriteOffService storeWriteOffService = SpringUtil.getContext().getBean(StoreWriteOffService.class);

    private IObjectData objectData;

    private String message;

    private Boolean flag;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.COST_WRITE_OFF.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return arg.getDataIds();
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.COST_WRITE_OFF.getButtonApiName();
    }

    @Override
    protected Result doAct(Arg arg) {
        // todo 元气灰度 限制当月的，下个月才可以核销
        log.info("storeWriteOffService.getPreObjectData", arg.getDataIds());
        ValidationStoreWriteOffData.Arg validateArg = new ValidationStoreWriteOffData.Arg();
        validateArg.setIds(arg.getDataIds());
        validateArg.setTenantId(actionContext.getTenantId());
        ValidationStoreWriteOffData.Result validateResult = storeWriteOffService.validationStoreWriteOff(validateArg);
        message = validateResult.getValidationMessage();
        flag = validateResult.getValidationResult();
        return Result.of(objectData, flag, message);
    }

    protected IObjectData getPreObjectData() {
        return objectData;
    }

    protected IObjectData getPostObjectData() {
        return objectData;
    }


    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.notEmpty(dataList)) {
            objectData = dataList.get(0);
        }
    }


    @Data
    public static class Arg {

        private String objectDataId;

        private List<String> dataIds;

        public static Arg of(String objectDataId, List<String> dataIds) {
            Arg arg = new Arg();
            arg.setObjectDataId(objectDataId);
            arg.setDataIds(dataIds);
            return arg;
        }
    }


    @Data
    public static class Result {
        private ObjectDataDocument objectData;

        @SerializedName("validation_result")
        @JSONField(name = "validation_result")
        @JsonProperty("validation_result")
        private Boolean validationResult;

        @SerializedName("validation_message")
        @JSONField(name = "validation_message")
        @JsonProperty("validation_message")
        private String validationMessage;

        public static Result of(IObjectData objectData, Boolean flag, String message) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            result.setValidationMessage(message);
            result.setValidationResult(flag);
            return result;
        }

    }

}
