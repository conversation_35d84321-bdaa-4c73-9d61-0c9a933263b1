package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportDataAction;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/28 下午6:18
 */
@Slf4j
public class TPMBudgetAccountObjUpdateImportDataAction extends StandardUpdateImportDataAction {


    @Override
    protected void convertFields(List<ImportData> dataList) {
        super.convertFields(dataList);
        log.info("data:{}", JSON.toJSONString(dataList));
        List<ImportError> errorList = new ArrayList<>();

        dataList.forEach(data -> {
            ImportError error = new ImportError();
            error.setRowNo(data.getRowNo());
            error.setErrorMessage(I18N.text(I18NKeys.ERRORMESSAGE_BUDGET_ACCOUNT_OBJ_UPDATE_IMPORT_DATA_ACTION_0));
            errorList.add(error);
        });
        mergeErrorList(errorList);
    }
}
