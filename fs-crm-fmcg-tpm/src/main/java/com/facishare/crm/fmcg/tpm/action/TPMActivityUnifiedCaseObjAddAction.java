package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityUnifiedCaseFields;
import com.facishare.crm.fmcg.tpm.business.TPM2Service;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRangeFieldBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPM2Service;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypePO;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.PackTransactionProxyImpl;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.service.abstraction.PackTransactionProxy;
import com.facishare.crm.fmcg.tpm.service.abstraction.TransactionService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.service.PromotionPolicyService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/2/13 17:23
 */
@SuppressWarnings("Duplicates")
public class TPMActivityUnifiedCaseObjAddAction extends StandardAddAction implements TransactionService<BaseObjectSaveAction.Arg, BaseObjectSaveAction.Result> {

    private static final Logger LOGGER = LoggerFactory.getLogger(TPMActivityUnifiedCaseObjAddAction.class);


    private final ITPM2Service tpm2Service = SpringUtil.getContext().getBean(TPM2Service.class);

    private final IRangeFieldBusiness rangeFieldBusiness = SpringUtil.getContext().getBean(IRangeFieldBusiness.class);
    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);
    private final PromotionPolicyService promotionPolicyService = SpringUtil.getContext().getBean(PromotionPolicyService.class);
    public final IActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);
    private String ACTIVITY_TYPE_TEMPLATE_ID = "";
    private String PRODUCT_GIFT_DATA = "";
    private ActivityTypeExt activityTypeExt = null;

    @Override
    protected void before(Arg arg) {
        initData();
        //校验活动周期是否合理
        validateDateRange(arg);
        // 设置默认值
        setDefaultValue(arg);
        //校验费用的合理性
        validateCostCorrect(arg);
        //活动状态
        validateActivityStatus(arg);
        //兑付产品范围
        validateCashingProduct(arg);
        //促销类活动校验
        validatePricePolicy(arg);

        super.before(arg);
    }

    private void initData() {
        String activityTypeId = this.arg.getObjectData().toObjectData().get(TPMActivityFields.ACTIVITY_TYPE, String.class);
        if (!Strings.isNullOrEmpty(activityTypeId)) {
            LOGGER.info("activity type id is {}", activityTypeId);
            ActivityTypeExt activityTypeExt = activityTypeManager.find(actionContext.getTenantId(), activityTypeId);
            ACTIVITY_TYPE_TEMPLATE_ID = activityTypeExt.get().getTemplateId();
            this.activityTypeExt = activityTypeExt;
        }
    }

    private void validatePricePolicy(Arg arg) {
        ActivityTypePO activityTypePO = activityTypeExt.get();
        String templateId = activityTypePO.getTemplateId();
        if (templateId == null || !templateId.startsWith("promotion")) {
            return;
        }

        //判断web端请求 WEB.chrome
        String clientInfo = actionContext.getRequestContext().getClientInfo();
        if (!clientInfo.startsWith("WEB")) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_ADD_ACTION_0));
        }

        IObjectData activityUnifiedCase = arg.getObjectData().toObjectData();
        String modeType = activityUnifiedCase.get(TPMActivityUnifiedCaseFields.MODE_TYPE, String.class, "");
        if (Strings.isNullOrEmpty(modeType)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_ADD_ACTION_1));
        }

        String sourceObjectApiName = activityUnifiedCase.get(TPMActivityUnifiedCaseFields.SOURCE_OBJECT_API_NAME, String.class, "");
        if (Strings.isNullOrEmpty(sourceObjectApiName)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_ADD_ACTION_2));
        }

        PRODUCT_GIFT_DATA = (String) CommonUtils.getOrDefault(arg.getObjectData().get("product_gift_data_json"), "");

    }

    @Override
    protected Result doAct(Arg arg) {
        return packTransactionProxy.packAct(this, arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result afterResult = super.after(arg, result);

        try {
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUpstreamOwnerIdOrUserId()), BuryModule.TPM.TPM_ACTIVITY_UNIFIED_CASE, BuryOperation.CREATE, true);
            asyncAddTpmLog(arg);
        } catch (Exception e) {
            LOGGER.warn("活动方案埋点异常 tenantId：{}", actionContext.getTenantId(), e);//ignorei18n
        }
        resetActivityStatus(result);
        saveOrUpdatePromotionPolicy(result, PRODUCT_GIFT_DATA);
        return afterResult;
    }

    private void asyncAddTpmLog(Arg arg) {
        String storeRange = arg.getObjectData().toObjectData().get(TPMActivityUnifiedCaseFields.STORE_RANGE, String.class);
        JSONObject storeObj = JSON.parseObject(storeRange);
        String type = storeObj.getString("type");
        String buryModule = BuryModule.TPM.TPM_ACTIVITY_UNIFIED_CASE;
        if (type.contains("ALL")) {
            buryModule = BuryModule.TPM.TPM_ACTIVITY_UNIFIED_CASE + "_dealer_" + BuryModule.STORE_RANGE.ALL;
        } else if (type.contains("CONDITION")) {
            buryModule = BuryModule.TPM.TPM_ACTIVITY_UNIFIED_CASE + "_dealer_" + BuryModule.STORE_RANGE.CONDITION;
        } else if (type.contains("FIXED")) {
            buryModule = BuryModule.TPM.TPM_ACTIVITY_UNIFIED_CASE + "_dealer_" + BuryModule.STORE_RANGE.FIXED;
        }

        if (!Strings.isNullOrEmpty(buryModule)) {
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUpstreamOwnerIdOrUserId()), buryModule, BuryOperation.CREATE, true);
        }

        String cashType = arg.getObjectData().toObjectData().get(TPMActivityUnifiedCaseFields.WRITE_OFF_CASH, String.class);
        if (Objects.nonNull(cashType)) {
            String cashModule;
            if (Objects.equals(cashType, BuryModule.CASH_TYPE.GOODS)) {
                cashModule = BuryModule.TPM.TPM_ACTIVITY_UNIFIED_CASE + "_dealer_" + BuryModule.CASH_TYPE.GOODS;
            } else {
                cashModule = BuryModule.TPM.TPM_ACTIVITY_UNIFIED_CASE + "_dealer_" + BuryModule.CASH_TYPE.CASH;
            }
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUpstreamOwnerIdOrUserId()), cashModule, BuryOperation.CREATE, true);
        }


        BigDecimal activityAmount = arg.getObjectData().toObjectData().get(TPMActivityUnifiedCaseFields.ACTIVITY_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        String activityAmountModule = BuryModule.TPM.TPM_ACTIVITY_UNIFIED_CASE + "_" + TPMActivityUnifiedCaseFields.ACTIVITY_AMOUNT;
        BuryService.asyncTpmAmountLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUpstreamOwnerIdOrUserId()), activityAmountModule, BuryOperation.CREATE, activityAmount.doubleValue());

        BigDecimal occupyAmount = arg.getObjectData().toObjectData().get(TPMActivityUnifiedCaseFields.OCCUPY_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        String occupyAmountModule = BuryModule.TPM.TPM_ACTIVITY_UNIFIED_CASE + "_" + TPMActivityUnifiedCaseFields.OCCUPY_AMOUNT;
        BuryService.asyncTpmAmountLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUpstreamOwnerIdOrUserId()), occupyAmountModule, BuryOperation.CREATE, occupyAmount.doubleValue());
    }

    private void resetActivityStatus(Result result) {
        if (tpm2Service.isTPM2Tenant(Integer.valueOf(actionContext.getTenantId()))) {
            String lifeStatus = (String) result.getObjectData().get(CommonFields.LIFE_STATUS);
            if (!ObjectLifeStatus.NORMAL.getCode().equals(lifeStatus)) {
                Map<String, Object> updateMap = new HashMap<>();
                updateMap.put(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS, TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__APPROVAL);
                serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), result.getObjectData().toObjectData(), updateMap);
            }
        }
    }


    private void validateCashingProduct(Arg arg) {
        if (!TPMGrayUtils.activityCashingProductIsRequired(actionContext.getTenantId())) {
            return;
        }
        String writeOffCash = (String) arg.getObjectData().get(TPMActivityUnifiedCaseFields.WRITE_OFF_CASH);
        if (Objects.equals(writeOffCash, TPMActivityUnifiedCaseFields.WRITE_OFF_CASH__GOODS)) {
            List<ObjectDataDocument> details = arg.getDetails().get(ApiNames.TPM_ACTIVITY_CASHING_PRODUCT_SCOPE_OBJ);
            if (CollectionUtils.isEmpty(details)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_ADD_ACTION_3));
            }
        }
    }

    private void validateCostCorrect(Arg arg) {
        BigDecimal amount = arg.getObjectData().toObjectData().get(TPMActivityUnifiedCaseFields.ACTIVITY_AMOUNT, BigDecimal.class);
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_ADD_ACTION_4));
        }
    }

    private void setDefaultValue(Arg arg) {
        if (Strings.isNullOrEmpty((String) arg.getObjectData().get(TPMActivityUnifiedCaseFields.STORE_RANGE))) {
            arg.getObjectData().put(TPMActivityUnifiedCaseFields.STORE_RANGE, "{\"type\":\"ALL\",\"value\":\"ALL\"}");
        }
        if (Objects.isNull(arg.getObjectData().get(TPMActivityUnifiedCaseFields.ACTIVITY_AMOUNT))) {
            arg.getObjectData().put(TPMActivityUnifiedCaseFields.ACTIVITY_AMOUNT, BigDecimal.ZERO);
        }

        IObjectData objectData = arg.getObjectData().toObjectData();
        List<String> departmentList = CommonUtils.castIgnore(objectData.get(TPMActivityUnifiedCaseFields.ACTIVITY_DEPARTMENT_RANGE), String.class);
        // 如果部门为空 或者 等于 待分配，表示为选择部门，默认全公司。
        if (CollectionUtils.isEmpty(departmentList) || departmentList.contains("999998")) {
            objectData.set(TPMActivityUnifiedCaseFields.ACTIVITY_DEPARTMENT_RANGE, Lists.newArrayList("999999"));
        }

        arg.getObjectData().put(TPMActivityUnifiedCaseFields.STORE_RANGE, rangeFieldBusiness.newUnifiedDealerRangeConditionCode(actionContext.getTenantId(), (String) arg.getObjectData().get(TPMActivityUnifiedCaseFields.STORE_RANGE)));
    }

    private void validateDateRange(Arg arg) {
        arg.getObjectData().putIfAbsent(TPMActivityUnifiedCaseFields.START_DATE, TimeUtils.MIN_DATE);
        arg.getObjectData().putIfAbsent(TPMActivityUnifiedCaseFields.END_DATE, TimeUtils.MAX_DATE);

        if (Objects.equals(arg.getObjectData().get(TPMActivityUnifiedCaseFields.START_DATE), 0)) {
            arg.getObjectData().put(TPMActivityUnifiedCaseFields.START_DATE, TimeUtils.MIN_DATE);
        }

        //校验时间
        long begin = arg.getObjectData().toObjectData().get(TPMActivityUnifiedCaseFields.START_DATE, Long.class);
        long end = TimeUtils.convertToDayEndIfTimeWasDayBegin(arg.getObjectData().toObjectData().get(TPMActivityUnifiedCaseFields.END_DATE, Long.class));
        arg.getObjectData().put(TPMActivityUnifiedCaseFields.END_DATE, end);

        if (end <= begin) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_ADD_ACTION_5));
        }

        LOGGER.info("end date : {}", end);
    }

    private void validateActivityStatus(Arg arg) {
        // 校验时间是否存在
        long begin = (long) arg.getObjectData().get(TPMActivityUnifiedCaseFields.START_DATE);
        long end = (long) arg.getObjectData().get(TPMActivityUnifiedCaseFields.END_DATE);
        long now = System.currentTimeMillis();
        String status;
        if (now < begin) {
            status = TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__SCHEDULE;
        } else if (now < end) {
            status = TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__IN_PROGRESS;
        } else {
            status = TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__END;
        }
        arg.getObjectData().put(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS, status);
    }

    @Override
    public Result doActTransaction(Arg arg) {
        return super.doAct(arg);
    }

    private void saveOrUpdatePromotionPolicy(Result result, String productGiftData) {
        if (Objects.nonNull(ACTIVITY_TYPE_TEMPLATE_ID) && ACTIVITY_TYPE_TEMPLATE_ID.startsWith("promotion")) {
            promotionPolicyService.saveOrUpdate(actionContext.getTenantId(),
                    actionContext.getUser().getUpstreamOwnerIdOrUserId(),
                    result.getObjectData().toObjectData(),
                    productGiftData,
                    false);
        }
    }
}
