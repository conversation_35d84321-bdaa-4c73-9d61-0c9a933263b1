package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofAuditFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.api.cost.CalculateAmount;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityWriteOffSourceConfigEntity;
import com.facishare.enterprise.common.exception.ArgumentException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.TimeZone;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 4:39 PM
 */
@SuppressWarnings("Duplicates")
public class TPMDealerActivityCostObjCalculateAmountController extends PreDefineController<CalculateAmount.Arg, CalculateAmount.Result> {

    private static final ActivityTypeDAO activityTypeDAO = SpringUtil.getContext().getBean(ActivityTypeDAO.class);

    private static final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected CalculateAmount.Result doService(CalculateAmount.Arg arg) {
        if (Strings.isNullOrEmpty(arg.getActivityId())) {
            throw new ArgumentException("activity_id", "activity id can not be null or empty.");
        }

        IObjectData activity = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getActivityId(), ApiNames.TPM_ACTIVITY_OBJ);
        if (activity == null) {
            throw new ValidateException("activity not found.");
        }

        String activityTypeId = activity.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
        if (Strings.isNullOrEmpty(activityTypeId)) {
            throw new ArgumentException("activity_id", "activity type of current activity can not be null or empty.");
        }
        String activityStatus = activity.get(TPMActivityFields.ACTIVITY_STATUS, String.class);
        if (arg.getBegin() == null) {
            arg.setBegin(activity.get(TPMActivityFields.BEGIN_DATE, Long.class));
        }

        if (arg.getEnd() == null) {
            // 如果活动过期结束，则计算结束时间为至今。
            Long endTime = TPMActivityFields.ACTIVITY_STATUS__END.equals(activityStatus) ? System.currentTimeMillis() : activity.get(TPMActivityFields.END_DATE, Long.class);
            arg.setEnd(endTime);
        } else {
            LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(arg.getEnd()), TimeZone.getDefault().toZoneId());
            if (!TPMGrayUtils.notResetEndDate(controllerContext.getTenantId())) {
                arg.setEnd(localDateTime.plusDays(1).toLocalDate().atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli() - 1);
            }
        }

        ActivityTypeExt activityType = ActivityTypeExt.of(activityTypeDAO.get(controllerContext.getTenantId(), activityTypeId));
        ActivityWriteOffSourceConfigEntity config = activityType.writeOffSourceConfig();
        BigDecimal amount;
        switch (config.getCalculateType()) {
            case "store_cost_total":
                amount = calculateStoreTotalAmount(arg.getActivityId(), arg.getDealerId(), activityType, arg.getBegin(), arg.getEnd());
                break;
            case "store_cost_average":
                amount = calculateStoreAverageAmount(arg.getActivityId(), arg.getDealerId(), activityType, arg.getBegin(), arg.getEnd());
                break;
            case "activity":
                amount = activity.get(TPMActivityFields.ACTIVITY_AMOUNT, BigDecimal.class);
                break;
            default:
                amount = new BigDecimal("0");
                break;
        }

        return CalculateAmount.Result.builder()
                .calculateType(config.getCalculateType())
                .amount(amount)
                .build();
    }

    private BigDecimal calculateStoreAverageAmount(String activityId, String dealerId, ActivityTypeExt activityType, Long start, Long end) {
        ActivityWriteOffSourceConfigEntity source = activityType.writeOffSourceConfig();
        if (Strings.isNullOrEmpty(source.getCostFieldApiName())) {
            return new BigDecimal("0");
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(200);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(source.getReferenceActivityFieldApiName());
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));


        Filter writeOffIdFilter = new Filter();
        writeOffIdFilter.setFieldName(source.getReferenceWriteOffFieldApiName());
        writeOffIdFilter.setOperator(Operator.IS);
        writeOffIdFilter.setFieldValues(Lists.newArrayList());

        Filter timeRangeFilter = new Filter();
        timeRangeFilter.setFieldName(CommonFields.CREATE_TIME);
        timeRangeFilter.setOperator(Operator.BETWEEN);
        timeRangeFilter.setFieldValues(Lists.newArrayList(start.toString(), end.toString()));

        Filter deletedFilter = new Filter();
        deletedFilter.setFieldName(CommonFields.IS_DELETED);
        deletedFilter.setOperator(Operator.EQ);
        deletedFilter.setFieldValues(Lists.newArrayList("false"));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        query.setFilters(Lists.newArrayList(activityIdFilter, writeOffIdFilter, timeRangeFilter, deletedFilter, lifeStatusFilter));

        if (!Strings.isNullOrEmpty(source.getCostFieldApiName())) {
            Filter moneyFilter = new Filter();
            moneyFilter.setFieldName(source.getCostFieldApiName());
            moneyFilter.setOperator(Operator.ISN);
            moneyFilter.setFieldValues(Lists.newArrayList());
            query.getFilters().add(moneyFilter);
        }

        // 自嗨锅 灰度
        String recordType = getAccountRecordType(dealerId);
        String fieldApiName = source.getDealerFieldApiName();
        if (!Strings.isNullOrEmpty(recordType) && !storeBusiness.findDealerRecordType(controllerContext.getTenantId()).contains(recordType)) {
            fieldApiName = source.getAccountFieldApiName();
        }
        if (!Strings.isNullOrEmpty(dealerId) && !Strings.isNullOrEmpty(fieldApiName)) {
            Filter dealerIdFilter = new Filter();
            dealerIdFilter.setFieldName(fieldApiName);
            dealerIdFilter.setOperator(Operator.EQ);
            dealerIdFilter.setFieldValues(Lists.newArrayList(dealerId));
            query.getFilters().add(dealerIdFilter);
        }

        if (ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ.equals(source.getApiName())) {
            Filter auditStatusFilter = new Filter();
            auditStatusFilter.setFieldName(TPMActivityProofAuditFields.AUDIT_STATUS);
            auditStatusFilter.setOperator(Operator.EQ);
            auditStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityProofAuditFields.AUDIT_STATUS__PASS));
            query.getFilters().add(auditStatusFilter);
        }

        List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                User.systemUser(controllerContext.getTenantId()),
                query,
                source.getApiName(),
                Lists.newArrayList(source.getReferenceActivityFieldApiName(), source.getDealerFieldApiName(), source.getAccountFieldApiName()),
                "avg",
                source.getCostFieldApiName());

        if (!CollectionUtils.isEmpty(data)) {
            BigDecimal result = new BigDecimal("0");
            String key = String.format("avg_%s", source.getCostFieldApiName());
            for (IObjectData datum : data) {
                result = result.add(datum.get(key, BigDecimal.class));
            }
            return result;
        } else {
            return new BigDecimal("0");
        }
    }

    private BigDecimal calculateStoreTotalAmount(String activityId, String dealerId, ActivityTypeExt activityType, Long start, Long end) {
        ActivityWriteOffSourceConfigEntity source = activityType.writeOffSourceConfig();
        if (Strings.isNullOrEmpty(source.getCostFieldApiName())) {
            return new BigDecimal("0");
        }


        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(source.getReferenceActivityFieldApiName());
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));


        Filter writeOffIdFilter = new Filter();
        writeOffIdFilter.setFieldName(source.getReferenceWriteOffFieldApiName());
        writeOffIdFilter.setOperator(Operator.IS);
        writeOffIdFilter.setFieldValues(Lists.newArrayList());

        Filter timeRangeFilter = new Filter();
        timeRangeFilter.setFieldName(CommonFields.CREATE_TIME);
        timeRangeFilter.setOperator(Operator.BETWEEN);
        timeRangeFilter.setFieldValues(Lists.newArrayList(start.toString(), end.toString()));

        Filter deletedFilter = new Filter();
        deletedFilter.setFieldName(CommonFields.IS_DELETED);
        deletedFilter.setOperator(Operator.EQ);
        deletedFilter.setFieldValues(Lists.newArrayList("false"));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        query.setFilters(Lists.newArrayList(activityIdFilter, writeOffIdFilter, timeRangeFilter, deletedFilter, lifeStatusFilter));

        List<String> aggList = Lists.newArrayList(source.getReferenceActivityFieldApiName());

        String recordType = getAccountRecordType(dealerId);
        String fieldApiName = source.getDealerFieldApiName();
        if (!Strings.isNullOrEmpty(recordType) && !storeBusiness.findDealerRecordType(controllerContext.getTenantId()).contains(recordType)) {
            fieldApiName = source.getAccountFieldApiName();
        }

        if (!Strings.isNullOrEmpty(dealerId) && !Strings.isNullOrEmpty(fieldApiName)) {
            Filter dealerIdFilter = new Filter();
            dealerIdFilter.setFieldName(fieldApiName);
            dealerIdFilter.setOperator(Operator.EQ);
            dealerIdFilter.setFieldValues(Lists.newArrayList(dealerId));
            query.getFilters().add(dealerIdFilter);
            aggList.add(fieldApiName);
        }


        if (!Strings.isNullOrEmpty(source.getCostFieldApiName())) {
            Filter moneyFilter = new Filter();
            moneyFilter.setFieldName(source.getCostFieldApiName());
            moneyFilter.setOperator(Operator.ISN);
            moneyFilter.setFieldValues(Lists.newArrayList());
            query.getFilters().add(moneyFilter);
        }

        if (ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ.equals(source.getApiName())) {
            Filter auditStatusFilter = new Filter();
            auditStatusFilter.setFieldName(TPMActivityProofAuditFields.AUDIT_STATUS);
            auditStatusFilter.setOperator(Operator.EQ);
            auditStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityProofAuditFields.AUDIT_STATUS__PASS));
            query.getFilters().add(auditStatusFilter);
        }

        List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                User.systemUser(controllerContext.getTenantId()),
                query,
                source.getApiName(),
                aggList,
                "sum",
                source.getCostFieldApiName());

        if (!CollectionUtils.isEmpty(data)) {
            String key = String.format("sum_%s", source.getCostFieldApiName());
            return data.get(0).get(key, BigDecimal.class);
        } else {
            return new BigDecimal("0");
        }
    }

    private String getAccountRecordType(String dealerId) {
        String recordType = "";
        if (!Strings.isNullOrEmpty(dealerId)) {
            IObjectData accountObj = serviceFacade.findObjectDataIgnoreAll(User.systemUser(controllerContext.getTenantId()), dealerId, ApiNames.ACCOUNT_OBJ);
            recordType = accountObj.getRecordType();
        }
        return recordType;
    }
}
