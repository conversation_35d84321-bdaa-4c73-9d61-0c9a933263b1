package com.facishare.crm.fmcg.tpm.facade;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.business.abstraction.IEnableCacheService;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Author: linmj
 * Date: 2023/5/4 17:04
 */
@Service
@ServiceModule("enable_cache_inner_service")
public class EnableCacheInnerService {


    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private IEnableCacheService iEnableCacheService;

    @ServiceMethod("set_all_enable_cache")
    public String setEnableCache(JSONObject arg, ServiceContext serviceContext) {
        iEnableCacheService.setALLCache(serviceContext.getTenantId(), arg.getString("user_id"));
        return "success";
    }

    @ServiceMethod("set_store_enable_cache")
    public String setStoreEnableCache(JSONObject arg, ServiceContext serviceContext) {
        iEnableCacheService.setStoreCache(serviceContext.getTenantId(), arg.getString("user_id"), arg.getString("store_id"), arg.getBoolean("enable"));
        return "success";
    }

    @ServiceMethod("get_cache")
    public String getCache(JSONObject arg, ServiceContext serviceContext) {

        return String.valueOf(iEnableCacheService.getCache(serviceContext.getTenantId(), arg.getString("user_id"), arg.getString("store_id")));
    }

    @ServiceMethod("reset_by_activity")
    public String resetCacheByActivity(JSONObject arg, ServiceContext serviceContext) {
        iEnableCacheService.resetCacheByActivity(serviceContext.getTenantId(), serviceFacade.findObjectData(serviceContext.getUser(),arg.getString("activity_id"), ApiNames.TPM_ACTIVITY_OBJ));
        return "success";
    }

}
