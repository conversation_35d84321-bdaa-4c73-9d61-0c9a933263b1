package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.api.plugin.DealerActivityCostEnterAccount;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.business.UnifiedActivityCommonLogicBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IActivityService;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPM2Service;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityWriteOffConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityWriteOffSourceConfigEntity;
import com.facishare.crm.fmcg.tpm.service.DealerCostFundAccountService;
import com.facishare.crm.fmcg.tpm.service.abstraction.ITransactionProxy;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.poc.POCActivityService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/12/31 下午3:39
 */

public class TPMDealerActivityCostObjFlowCompletedAction extends StandardFlowCompletedAction {

    private static final Logger log = LoggerFactory.getLogger(TPMDealerActivityCostObjFlowCompletedAction.class);

    private ITPM2Service itpm2Service = SpringUtil.getContext().getBean(ITPM2Service.class);
    private final ITransactionProxy transProxy = SpringUtil.getContext().getBean(ITransactionProxy.class);
    private static final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);
    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);
    private final RedissonClient redissonCmd = SpringUtil.getContext().getBean("redissonCmd", RedissonClient.class);
    private final UnifiedActivityCommonLogicBusiness unifiedActivityCommonLogicBusiness = SpringUtil.getContext().getBean(UnifiedActivityCommonLogicBusiness.class);

    private final IActivityService activityService = SpringUtil.getContext().getBean(IActivityService.class);
    private final DealerCostFundAccountService dealerCostFundAccountService = SpringUtil.getContext().getBean(DealerCostFundAccountService.class);
    private RLock activityLock;
    private String activityId;

    private boolean enableOverWriteOff = false;
    private IObjectData cost;

    private IObjectData activity;
    private static final String LOCK_KEY = "FMCG:TPM:DEALER_ACTIVITY_COST_PAAS:ACTIVITY:%s.%s";
    private static final Long ONE_DAY = 86400000L;

    @Override
    protected void before(Arg arg) {
        this.cost = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getDataId(), ApiNames.TPM_DEALER_ACTIVITY_COST);
        if ("pass".equals(arg.getStatus())) {
            initData();
            validateActivity();
            validateAmount(cost);
            autoCloseActivity();
        }
        super.before(arg);
    }

    private void validateActivity() {
        String activityId = this.cost.get(TPMDealerActivityCostFields.ACTIVITY_ID, String.class);
        if (!Strings.isNullOrEmpty(activityId)) {
            if (activity == null) {
                activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);
            }
            String status = activity.get(TPMActivityFields.ACTIVITY_STATUS, String.class);
            String closeStatus = activity.get(TPMActivityFields.CLOSED_STATUS, String.class);
            if (TPMActivityFields.ACTIVITY_STATUS__CLOSED.equals(status) || TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closeStatus)) {
                throw new ValidateException(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_OBJ_FLOW_COMPLETED_ACTION_0));
            }
        }
    }

    private void autoCloseActivity() {
        if (activity == null) {
            activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);
        }
        boolean isOnceWriteOff = ActivityMaxWriteOffCountEnum.ONCE.value().equals(activity.get(TPMActivityFields.MAX_WRITE_OFF_COUNT, String.class));
        boolean isForceCloseActivity = Boolean.TRUE.equals(activity.get(TPMActivityFields.IS_AUTO_CLOSE, Boolean.class));
        String closeStatus = (String) activity.get(TPMActivityFields.CLOSED_STATUS);
        if (!TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closeStatus) && isForceCloseActivity && isOnceWriteOff) {
            activityService.triggerCloseTPMActivity(actionContext.getTenantId(), actionContext.getUser().getUpstreamOwnerIdOrUserId(), activityId, true, false);
        }
    }


    private void initData() {
        String activityId = cost.get(TPMDealerActivityCostFields.ACTIVITY_ID, String.class);
        if (Strings.isNullOrEmpty(activityId)) {
            return;
        }
        ActivityTypeExt activityType = activityTypeManager.findByActivityId(actionContext.getTenantId(), activityId);
        if (Objects.nonNull(activityType)) {
            ActivityWriteOffConfigEntity activityWriteOffConfigEntity = activityType.writeOffConfig();
            if (activityWriteOffConfigEntity != null && activityWriteOffConfigEntity.getEnableOverWriteOff() != null) {
                this.enableOverWriteOff = activityWriteOffConfigEntity.getEnableOverWriteOff();
                log.info("dealer activity cost enableOverWriteOff is {}", this.enableOverWriteOff);
            }
        }
    }

    public void updateStatus() {
        if ("pass".equals(arg.getStatus())) {
            Map<String, Object> updateField = new HashMap<>();
            updateField.put(TPMDealerActivityCostFields.WRITE_OFF_STATUS, TPMDealerActivityCostFields.WriteOffStatus.PASS);
            serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), cost, updateField);
        } else if ("reject".equals(arg.getStatus())) {
            Map<String, Object> updateField = new HashMap<>();
            updateField.put(TPMDealerActivityCostFields.WRITE_OFF_STATUS, TPMDealerActivityCostFields.WriteOffStatus.REJECT);
            serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), cost, updateField);
        }
    }

    private void tryLockActivity(String activityId) {
        String key = String.format(LOCK_KEY, actionContext.getTenantId(), activityId);
        this.activityLock = redissonCmd.getLock(key);

        try {
            if (!this.activityLock.tryLock(5L, 60L, TimeUnit.SECONDS)) {
                throw new ValidateException("System error, Please try again later.");
            }
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new ValidateException("Try lock activity for dealer cost flow completed cause 'InterruptedException' please try again later.");
        }
    }

    private void validateAmount(IObjectData cost) {
        this.activityId = cost.get(TPMDealerActivityCostFields.ACTIVITY_ID, String.class);
        if (TPMGrayUtils.skipValidateConfirmAmount(actionContext.getTenantId()) || enableOverWriteOff) {
            return;
        }
        BigDecimal confirmedAmount = cost.get(TPMDealerActivityCostFields.CONFIRMED_AMOUNT, BigDecimal.class);
        if (Objects.isNull(confirmedAmount)) {
            confirmedAmount = new BigDecimal("0");
        }
        if (Strings.isNullOrEmpty(activityId)) {
            throw new ValidateException(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_OBJ_FLOW_COMPLETED_ACTION_1));
        }
        log.info("try lock start:{}", activityId);
        tryLockActivity(activityId);
        log.info("try lock success:{}", activityId);
        if (activity == null) {
            activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), activityId
                    , ApiNames.TPM_ACTIVITY_OBJ);
        }
        log.info("dealerCashType:{},activityAmountStr:{},activityActualAmountStr:{},confirmedAmount:{}",
                activity.get(TPMActivityFields.DEALER_CASHING_TYPE, String.class), activity.get(TPMActivityFields.ACTIVITY_AMOUNT, String.class)
                , activity.get(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, String.class), confirmedAmount);
        String dealerCashType = activity.get(TPMActivityFields.DEALER_CASHING_TYPE, String.class);
        if (Objects.equals(TPMActivityCashingProductFields.GOODS, dealerCashType)) {
            BigDecimal activityAmount = new BigDecimal("0");
            String activityAmountStr = activity.get(TPMActivityFields.ACTIVITY_AMOUNT, String.class);
            if (!Strings.isNullOrEmpty(activityAmountStr)) {
                activityAmount = new BigDecimal(activityAmountStr);
            }

            BigDecimal activityActualAmount = new BigDecimal("0");
            String activityActualAmountStr = activity.get(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, String.class);
            if (!Strings.isNullOrEmpty(activityActualAmountStr)) {
                activityActualAmount = new BigDecimal(activityActualAmountStr);
            }

            BigDecimal leaveAmount = activityAmount.subtract(activityActualAmount);
            if (confirmedAmount.compareTo(leaveAmount) > 0) {
                throw new ValidateException(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_OBJ_FLOW_COMPLETED_ACTION_2));
            }

        }

    }

    @Override
    protected Result doAct(Arg arg) {
        return transProxy.call(() -> {
            updateStatus();
            Result inner = super.doAct(arg);
            // 重算活动方案已使用金额
            budgetService.calculateActivity(actionContext.getTenantId(), activityId);
            unifiedActivityCommonLogicBusiness.recalculateUnifiedAmountField(actionContext.getTenantId(), this.dbData.get(TPMDealerActivityCostFields.ACTIVITY_UNIFIED_CASE_ID, String.class));
            if ("pass".equals(arg.getStatus())) {
                if (ApprovalFlowTriggerType.CREATE.getTriggerTypeCode().equals(arg.getTriggerType()) || ApprovalFlowTriggerType.UPDATE.getTriggerTypeCode().equals(arg.getTriggerType())) {
                    updateActivityDealerCostEffectiveAndExpiringDate(actionContext.getTenantId(), cost);
                    dealerCostFundAccountService.doAct(buildArg(cost), actionContext);
                }
            } else {
                updateCashingFundAccount(actionContext.getTenantId(), cost);
            }
            return inner;
        });
    }

    @Override
    protected Result after(Arg arg, Result result) {


        Result result1 = super.after(arg, result);

        IObjectData cost = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getDataId(), ApiNames.TPM_DEALER_ACTIVITY_COST);
        if (ObjectLifeStatus.INEFFECTIVE.getCode().equals(cost.get(CommonFields.LIFE_STATUS, String.class))) {
            releaseRelatedData(cost);
        }

        return result1;
    }

    private void updateActivityDealerCostEffectiveAndExpiringDate(String tenantId, IObjectData dealerActivityCostObj) {
        long effectiveDate = System.currentTimeMillis();
        Integer effectivePeriod = dealerActivityCostObj.get(TPMDealerActivityCostFields.EFFECTIVE_PERIOD, Integer.class);
        if (effectivePeriod == null) {
            effectivePeriod = 0;
        }
        long expiringDate = (ONE_DAY * effectivePeriod) + effectiveDate;

        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put(TPMDealerActivityCostFields.EFFECTIVE_DATE, POCActivityService.DateUtil.getDayStartTime(new Date(effectiveDate)).getTime());
        fieldMap.put(TPMDealerActivityCostFields.EXPIRING_DATE, POCActivityService.DateUtil.getDayStartTime(new Date(expiringDate)).getTime());
        serviceFacade.updateWithMap(User.systemUser(tenantId), dealerActivityCostObj, fieldMap);
    }

    private void updateCashingFundAccount(String tenantId, IObjectData dealerActivityCostObj) {
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put(TPMDealerActivityCostFields.CASHING_FUND_ACCOUNT_ID, null);
        serviceFacade.updateWithMap(User.systemUser(tenantId), dealerActivityCostObj, fieldMap);
    }

    private DealerActivityCostEnterAccount.Arg buildArg(IObjectData cost) {
        return DealerActivityCostEnterAccount.Arg.builder()
                .dbMasterData(cost)
                .cashingProductDetails(queryDetails(cost.getId()))
                .user(actionContext.getUser())
                .build();

    }

    private List<IObjectData> queryDetails(String id) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);

        Filter idFilter = new Filter();
        idFilter.setFieldName(TPMDealerActivityCashingProductFields.DEALER_ACTIVITY_COST_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(id));

        query.setFilters(Lists.newArrayList(idFilter));
        return QueryDataUtil.find(serviceFacade, actionContext.getTenantId(), ApiNames.TPM_DEALER_ACTIVITY_CASHING_PRODUCT_OBJ,
                query, Lists.newArrayList(CommonFields.ID, CommonFields.NAME, TPMDealerActivityCostFields.COST_CASHING_QUANTITY));
    }

    @Override
    protected void finallyDo() {
        this.unlockActivity();
        super.finallyDo();
    }

    private void unlockActivity() {
        if (Objects.nonNull(this.activityLock) && this.activityLock.isLocked() && this.activityLock.isHeldByCurrentThread()) {
            this.activityLock.unlock();
        }
    }

    private void releaseRelatedData(IObjectData cost) {
        String activityId = (String) cost.get(TPMDealerActivityCostFields.ACTIVITY_ID);

        ActivityTypeExt activityType = activityTypeManager.findByActivityId(actionContext.getTenantId(), activityId);
        ActivityWriteOffSourceConfigEntity config = activityType.writeOffSourceConfig();
        if (config == null || Strings.isNullOrEmpty(config.getApiName())) {
            return;
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter referenceWriteOffFilter = new Filter();
        referenceWriteOffFilter.setFieldName(config.getReferenceWriteOffFieldApiName());
        referenceWriteOffFilter.setOperator(Operator.EQ);
        referenceWriteOffFilter.setFieldValues(Lists.newArrayList(cost.getId()));

        query.setFilters(Lists.newArrayList(referenceWriteOffFilter));

        List<IObjectData> relatedData = CommonUtils.queryData(serviceFacade, User.systemUser(actionContext.getTenantId()), config.getApiName(), query);

        for (List<IObjectData> parts : Lists.partition(relatedData, 50)) {
            Map<String, Object> map = new HashMap<>();
            map.put(config.getReferenceWriteOffFieldApiName(), null);
            serviceFacade.batchUpdateWithMap(User.systemUser(actionContext.getTenantId()), parts, map);
        }
    }
}
