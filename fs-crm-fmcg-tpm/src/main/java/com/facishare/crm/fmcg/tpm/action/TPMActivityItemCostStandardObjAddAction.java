package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.tpm.business.abstraction.ITPM2Service;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.release.GrayRelease;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/5/15 11:49 PM
 */
@SuppressWarnings("unused")
public class TPMActivityItemCostStandardObjAddAction extends StandardAddAction {

    private final ITPM2Service tpm2Service = SpringUtil.getContext().getBean(ITPM2Service.class);
    private boolean isTpm2Tenant = false;

    @Override
    protected void before(Arg arg) {
        this.isTpm2Tenant = tpm2Service.isTPM2Tenant(Integer.valueOf(actionContext.getTenantId()));
        if (!GrayRelease.isAllow("fmcg", "FMCG.TPM.COST_STANDARD", actionContext.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKeys.CURRENT_TENANT_DO_NOT_SUPPORT_COST_STANDARD));
        }
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        if (isTpm2Tenant) {
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_ACTIVITY_ITEM_COST, BuryOperation.CREATE, isTpm2Tenant);
        }
        return super.after(arg, result);
    }
}
