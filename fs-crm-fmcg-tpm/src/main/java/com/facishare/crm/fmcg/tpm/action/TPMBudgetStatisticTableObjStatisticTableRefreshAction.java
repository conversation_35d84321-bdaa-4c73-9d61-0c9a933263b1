package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetStatisticTableService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * author: wuyx
 * description:
 * createTime: 2022/8/12 19:57
 */
public class TPMBudgetStatisticTableObjStatisticTableRefreshAction extends AbstractStandardAction<TPMBudgetStatisticTableObjStatisticTableRefreshAction.Arg, TPMBudgetStatisticTableObjStatisticTableRefreshAction.Result> {

    private IObjectData objectData;

    private static final IBudgetStatisticTableService budgetStatisticTableService = SpringUtil.getContext().getBean(IBudgetStatisticTableService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.STATISTIC_TABLE_REFRESH.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getDataId());
    }

    @Override
    protected Result doAct(Arg arg) {

        Result of = Result.of(objectData);
        try {
            budgetStatisticTableService.refresh(actionContext.getTenantId(), actionContext.getUser(), arg.getDataId());
        } catch (Exception e) {
            log.error("更新预算汇总表失败 tenant_id={},data_id={}", actionContext.getTenantId(), arg.getDataId(), e);
        }
        return of;
    }

    @Override
    protected IObjectData getPreObjectData() {
        return objectData;
    }

    @Override
    protected IObjectData getPostObjectData() {
        return objectData;
    }

    @Override
    protected String getButtonApiName() {
        return "StatisticTableRefresh_button_default";
    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.notEmpty(dataList)) {
            objectData = dataList.get(0);
        }
    }

    @Data
    public static class Arg {

        @SerializedName("objectDataId")
        @JSONField(name = "objectDataId")
        @JsonProperty("objectDataId")
        private String dataId;
    }


    @Data
    public static class Result {
        private ObjectDataDocument objectData;

        public static Result of(IObjectData objectData) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            return result;
        }
    }
}
