package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.tpm.common.constant.CallBackDataKeys;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022/12/26 下午4:09
 */
@Slf4j
public class TPMBudgetAccrualObjFlowCompletedAction extends StandardFlowCompletedAction {

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        JSONObject callBackMap = JSON.parseObject(JSON.toJSONString(arg.getCallbackData()));
        boolean flag = Boolean.TRUE.equals(callBackMap.getBoolean(CallBackDataKeys.ACCRUAL_DATA_NEED_BUDGET_ACCRUAL_FLAG));
        if (ApprovalFlowTriggerType.CREATE.getTriggerTypeCode().equals(arg.getTriggerType()) || ApprovalFlowTriggerType.UPDATE.getTriggerTypeCode().equals(arg.getTriggerType())) {
            if ("pass".equalsIgnoreCase(arg.getStatus())) {
                if (flag) {
                    budgetAccrual(arg.getDataId());
                }
            }
        }
    }

    public void budgetAccrual(String dataId) {
        try {
            ActionContext newActionContext = new ActionContext(actionContext.getRequestContext(), actionContext.getObjectApiName(), "BudgetAccrual");
            newActionContext.setAttribute("skipBaseValidate", true);
            TPMBudgetAccrualObjBudgetAccrualAction.Result accrualResult = serviceFacade.triggerAction(newActionContext, TPMBudgetAccrualObjBudgetAccrualAction.Arg.of(null, dataId), TPMBudgetAccrualObjBudgetAccrualAction.Result.class);
            log.info("budget accrual action result:{}", accrualResult);
        } catch (Exception e) {
            log.info("trigger budget accrual err.", e);
        }
    }
}
