package com.facishare.crm.fmcg.tpm.business.dto;

import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Calendar;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/10/20 17:40
 */
@Data
@ToString
@Builder
@SuppressWarnings("Duplicates")
public class FiscalMonth implements Serializable {

    private int year;

    private int quarter;

    private int month;

    private long start;

    private long end;

    public static FiscalMonth of(
            int year,
            int month
    ) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, year);
        cal.set(Calendar.MONTH, month - 1);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        long start = cal.getTimeInMillis();
        cal.add(Calendar.MONTH, 1);
        long end = cal.getTimeInMillis();

        int quarter;
        switch (month) {
            case 1:
            case 2:
            case 3:
                quarter = 1;
                break;
            case 4:
            case 5:
            case 6:
                quarter = 2;
                break;
            case 7:
            case 8:
            case 9:
                quarter = 3;
                break;
            case 10:
            case 11:
            case 12:
                quarter = 4;
                break;
            default:
                throw new MetaDataBusinessException("fiscal month value error");
        }

        return FiscalMonth.builder()
                .year(year)
                .quarter(quarter)
                .month(month)
                .start(start)
                .end(end)
                .build();
    }
}
