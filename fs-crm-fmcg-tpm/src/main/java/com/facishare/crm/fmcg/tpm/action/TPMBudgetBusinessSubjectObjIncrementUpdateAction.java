package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetBusinessSubjectFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetActivitySubjectService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.base.Strings;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/7/18 下午3:01
 */
public class TPMBudgetBusinessSubjectObjIncrementUpdateAction extends StandardIncrementUpdateAction {

    private IBudgetActivitySubjectService budgetActivitySubjectService = SpringUtil.getContext().getBean(IBudgetActivitySubjectService.class);

    @Override
    protected void before(Arg arg) {
        if (Objects.isNull(arg.getData().get(TPMBudgetBusinessSubjectFields.SUBJECT_LEVEL))) {
            String parentId = (String) arg.getData().get(TPMBudgetBusinessSubjectFields.PARENT_SUBJECT_ID);
            int level = 1;
            if (!Strings.isNullOrEmpty(parentId)) {
                IObjectData parentData = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), parentId, ApiNames.TPM_BUDGET_BUSINESS_SUBJECT);
                level = parentData.get(TPMBudgetBusinessSubjectFields.SUBJECT_LEVEL, Integer.class, 1) + 1;
            }
            arg.getData().put(TPMBudgetBusinessSubjectFields.SUBJECT_LEVEL, level);
        }
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result finalResult = super.after(arg, result);
        IObjectData argObj = arg.getData().toObjectData();
        if (argObj.containsField(TPMBudgetBusinessSubjectFields.PARENT_SUBJECT_ID)) {
            IObjectData resultObjectData = serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), argObj.getId(), ApiNames.TPM_BUDGET_BUSINESS_SUBJECT);
            if (CommonFields.LIFE_STATUS__NORMAL.equals(resultObjectData.get(CommonFields.LIFE_STATUS, String.class))) {
                budgetActivitySubjectService.resetSubjectLevel(User.systemUser(actionContext.getTenantId()), resultObjectData.getId(), resultObjectData.get(TPMBudgetBusinessSubjectFields.SUBJECT_LEVEL, Integer.class, 1) + 1, Lists.newArrayList(resultObjectData.getId()));
            }
        }

        return finalResult;
    }
}
