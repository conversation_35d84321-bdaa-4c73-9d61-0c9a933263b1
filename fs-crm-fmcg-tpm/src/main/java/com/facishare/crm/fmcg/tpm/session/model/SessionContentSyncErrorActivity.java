package com.facishare.crm.fmcg.tpm.session.model;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.NodeType;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @author: wuyx
 * @description:
 * @createTime: 2022/1/7 17:32
 */
//IgnoreI18nFile
public class SessionContentSyncErrorActivity extends SessionContent {

    /**
     * 节点名称
     */
    protected String nodeName;
    /**
     * 对象
     */
    protected String objName;

    /**
     * 关联对象名称
     */
    protected String referenceObjName;

    protected String errorMessage;

    /**
     * 节点类型 [活动方案，活动协议......]
     */
    protected NodeType nodeType;

    /**
     * 是否为活动类型： true 活动类型 ， false 活动方案
     */
    protected boolean activityType;

    public SessionContentSyncErrorActivity(String nodeName,
                                           String objName,
                                           String referenceObjName,
                                           String errorMessage,
                                           boolean activityType,
                                           NodeType nodeType) {
        this.nodeName = nodeName;
        this.objName = objName;
        this.referenceObjName = referenceObjName;
        this.activityType = activityType;
        this.errorMessage = errorMessage;
        this.nodeType = nodeType;
        setFields();
    }

    protected void setFields() {
        this.title = getTitle();
        this.subtitle = getSubtitle();
        this.first = getFirst();
        this.content = getSessionContent();
        this.link = getLink();
        this.pushTitle = getPushTitle();
    }

    protected String getTitle() {
        return "错误提醒";
    }

    protected String getSubtitle() {
        return "";
    }

    protected String getFirst() {
        return errorMessage;
    }

    public void setFirst(String first) {
        this.first = first;
    }

    protected String getLink() {
      return "";
    }

    protected String getPushTitle() {
        return this.title;
    }

    private List<Map<String, StringBuilder>> getSessionContent() {
        return Lists.newArrayList();
    }

}
