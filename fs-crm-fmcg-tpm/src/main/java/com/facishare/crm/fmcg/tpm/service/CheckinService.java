package com.facishare.crm.fmcg.tpm.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.tpm.api.visit.VisitActionDataDTO;
import com.facishare.crm.fmcg.tpm.api.visit.VisitAgreementActionDataDTO;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.fmcg.framework.http.CheckinProxy;
import com.fmcg.framework.http.contract.checkin.UpdateAction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/14/20 3:08 PM
 */
@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class CheckinService {

    @Resource
    private CheckinProxy checkinProxy;

    @Resource
    private ServiceFacade serviceFacade;

    public String updateProofAction(User user, String visitId, String actionId, VisitActionDataDTO data) {
        try {
            UpdateAction.Arg arg = new UpdateAction.Arg();

            if(user.isOutUser()){
                arg.setUpperEa(serviceFacade.getEAByEI(user.getTenantId()));
                arg.setOuterUserId(user.getOutUserIdLong());
            }
            arg.setEid(user.getTenantIdInt());
            arg.setUserId(user.getUserIdInt());
            arg.setCheckId(visitId);
            arg.setActionId(actionId);
            arg.setDataJson(JSON.toJSONString(data));
            arg.setActionStatus(1);
            arg.setActionCode("activity_proof");

            log.info("update action arg : {}", JSON.toJSON(arg));

            JSONObject result = checkinProxy.updateAction(arg);

            log.info("update action result : {}", result.toJSONString());

            return result.toJSONString();
        } catch (Exception ex) {
            return "{}";
        }
    }



    public String updateProofAuditAction(User user, String visitId, String actionId, VisitActionDataDTO data) {
        try {
            UpdateAction.Arg arg = new UpdateAction.Arg();

            if(user.isOutUser()){
                arg.setUpperEa(serviceFacade.getEAByEI(user.getTenantId()));
                arg.setOuterUserId(user.getOutUserIdLong());
            }
            arg.setEid(user.getTenantIdInt());
            arg.setUserId(user.getUserIdInt());
            arg.setCheckId(visitId);
            arg.setActionId(actionId);
            arg.setDataJson(JSON.toJSONString(data));
            arg.setActionStatus(1);
            arg.setActionCode("activity_proof_audit");

            log.info("update action arg : {}", JSON.toJSON(arg));

            JSONObject result = checkinProxy.updateAction(arg);

            log.info("update action result : {}", result.toJSONString());

            return result.toJSONString();
        } catch (Exception ex) {
            return "{}";
        }
    }

    public String updateAgreementAction(User user, String visitId, String actionId, VisitAgreementActionDataDTO data) {
        try {
            UpdateAction.Arg arg = new UpdateAction.Arg();

            if(user.isOutUser()){
                arg.setUpperEa(serviceFacade.getEAByEI(user.getTenantId()));
                arg.setOuterUserId(user.getOutUserIdLong());
            }
            arg.setEid(user.getTenantIdInt());
            arg.setUserId(user.getUserIdInt());
            arg.setCheckId(visitId);
            arg.setActionId(actionId);
            arg.setDataJson(JSON.toJSONString(data));
            arg.setActionStatus(1);
            arg.setActionCode("activity_protocol");

            log.info("update agreement action arg : {}", JSON.toJSON(arg));

            JSONObject result = checkinProxy.updateAction(arg);

            log.info("update agreement action result : {}", result.toJSONString());

            return result.toJSONString();
        } catch (Exception ex) {
            return "{}";
        }
    }
}
