package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetBusinessSubjectFields;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2022/6/1 17:38
 */
public class TPMBudgetBusinessSubjectObjAddAction extends StandardAddAction {

    @Override
    protected void before(Arg arg) {

        setDefaultLevel();
        super.before(arg);
    }

    private void setDefaultLevel() {
        if (Objects.isNull(arg.getObjectData().get(TPMBudgetBusinessSubjectFields.SUBJECT_LEVEL))) {
            String parentId = (String) arg.getObjectData().get(TPMBudgetBusinessSubjectFields.PARENT_SUBJECT_ID);
            int level = 1;
            if (!Strings.isNullOrEmpty(parentId)) {
                IObjectData parentData = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), parentId, ApiNames.TPM_BUDGET_BUSINESS_SUBJECT);
                level = parentData.get(TPMBudgetBusinessSubjectFields.SUBJECT_LEVEL, Integer.class, 1) + 1;
            }
            arg.getObjectData().put(TPMBudgetBusinessSubjectFields.SUBJECT_LEVEL, level);
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        BuryService.asyncTpmLog(Integer.parseInt(actionContext.getTenantId()), actionContext.getUser().getUserIdInt(), BuryModule.TPM.TPM_BUDGET_BUSINESS_SUBJECT, BuryOperation.CREATE, true);
        return super.after(arg, result);
    }
}
