package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.tpm.business.abstraction.IProductCategoryService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/9/29 18:27
 */
@Service
public class ProductCategoryService implements IProductCategoryService {

    @Resource(name = "objectDataPgService")
    private IObjectDataService objectDataService;

    @Override
    public boolean notTheSpecifiedProductCategoryLevel(String tenantId, int level, String categoryId) {
        //todo:linmj
        String sqlTemp = "with recursive son_tree as\n" +
                "(\n" +
                " select name, id, pid, 1 as rank from product_category  where id = '%s'\n" +
                "  union all\n" +
                "  select a.name, a.id, a.pid, b.rank + 1 from product_category a join son_tree b on a.id = b.pid where tenant_id = '%s' and is_deleted = 0\n" +
                ") select max(rank) max_rank from son_tree;";
        String sql = String.format(sqlTemp, categoryId, tenantId);
        int countLevel = 0;
        try {
            List<Map> list = objectDataService.findBySql(tenantId, sql);
            if (!CollectionUtils.isEmpty(list)) {
                Map<?, ?> map = list.get(0);
                if (!MapUtils.isEmpty(map)) {
                    countLevel = Integer.parseInt(map.get("max_rank").toString());
                }
            }
        } catch (MetadataServiceException e) {
            throw new ValidateException(e.getMessage());
        }
        return level != countLevel;
    }
}
