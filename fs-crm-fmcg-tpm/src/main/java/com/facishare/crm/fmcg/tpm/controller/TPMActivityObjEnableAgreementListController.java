package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityAgreementFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.api.activity.EnableAgreementList;
import com.facishare.crm.fmcg.tpm.api.activity.YinLuAgreementEnableFilter;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDescribeCacheService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRangeFieldBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IUnifiedActivityCommonLogicBusiness;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityAgreementConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityAgreementTimeSettingEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.poc.POCActivityService;
import com.facishare.organization.api.model.department.arg.GetDepartmentDtoArg;
import com.facishare.organization.api.model.department.result.GetDepartmentDtoResult;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.privilege.DataPrivilegeService;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.fmcg.tpm.controller.TPMActivityAgreementObjRelatedListController.YINLU_FILTER_TIME;

/**
 * <AUTHOR>
 * @Date 2022/5/24 16:50
 */
@Slf4j
public class TPMActivityObjEnableAgreementListController extends PreDefineController<EnableAgreementList.Arg, EnableAgreementList.Result> {

    private StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);

    private DepartmentProviderService departmentProviderService = SpringUtil.getContext().getBean(DepartmentProviderService.class);

    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);

    private static final IDescribeCacheService I_DESCRIBE_CACHE_SERVICE = SpringUtil.getContext().getBean(IDescribeCacheService.class);

    private static final OrganizationService organizationService = SpringUtil.getContext().getBean(OrganizationService.class);

    private final IUnifiedActivityCommonLogicBusiness unifiedActivityCommonLogicBusiness = SpringUtil.getContext().getBean(IUnifiedActivityCommonLogicBusiness.class);

    private IRangeFieldBusiness rangeFieldBusiness = SpringUtil.getContext().getBean(IRangeFieldBusiness.class);

    private final DataPrivilegeService dataPrivilegeService = SpringUtil.getContext().getBean(DataPrivilegeService.class);
    private final ControllerLocateService controllerLocateService = SpringUtil.getContext().getBean(ControllerLocateService.class);
    private static final String VISIT_COMPLETED_STATUS = "4";

    private static final String SIGNED = "signed";
    private static final String UNSIGNED = "unsigned";

    private ISearchTemplate activitySearchTemplate;

    private IObjectDescribe activityDescribe;

    private User systemUser;

    private boolean isAdminRequest = false;


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected void before(EnableAgreementList.Arg arg) {
        this.systemUser = User.systemUser(controllerContext.getTenantId());
        this.isAdminRequest = serviceFacade.isAdmin(controllerContext.getUser());
        this.activityDescribe = serviceFacade.findDescribeAndLayout(this.systemUser, ApiNames.TPM_ACTIVITY_OBJ, false, null).getObjectDescribe();
        this.activitySearchTemplate = serviceFacade.findSearchTemplateByIdAndType(this.systemUser, "", ApiNames.TPM_ACTIVITY_OBJ, "All");
        super.before(arg);
    }

    @Override
    protected EnableAgreementList.Result doService(EnableAgreementList.Arg arg) {
        log.info("enable agreement arg:{}", arg);

        if (Strings.isNullOrEmpty(arg.getVisitId()) || Strings.isNullOrEmpty(arg.getActionId())) {
            throw new ValidateException("visit_id or action_id can not be empty.");
        }

        if (Strings.isNullOrEmpty(arg.getStoreId())) {
            throw new ValidateException("store id can not be empty.");
        }

        IObjectData store = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getStoreId(), ApiNames.ACCOUNT_OBJ);
        if (store == null) {
            log.info("can not find store by store id.");
            throw new ValidateException("can not find store by store id.");
        }

        String dealerId = storeBusiness.findDealerId(controllerContext.getTenantId(), store);
        IObjectData dealer = null;
        if (!Strings.isNullOrEmpty(dealerId)) {
            dealer = serviceFacade.findObjectDataIncludeDeleted(User.systemUser(controllerContext.getTenantId()), dealerId, ApiNames.ACCOUNT_OBJ);
        } else if (storeBusiness.findDealerRecordType(controllerContext.getTenantId()).contains(store.getRecordType())) {
            dealer = store;
        }

        if (VISIT_COMPLETED_STATUS.equals(arg.getVisitStatus())) {
            return visitCompleted(arg.getVisitId(), arg.getActionId(), store, dealer);
        } else {
            return visiting(arg.getVisitId(), arg.getActionId(), store, dealer);
        }
    }

    private EnableAgreementList.Result visitCompleted(String visitId, String actionId, IObjectData store, IObjectData dealer) {

        EnableAgreementList.Result result = new EnableAgreementList.Result();
        result.setStoreId(store.getId());
        result.setStoreName(store.getName());
        if (dealer != null) {
            result.setDealerId(dealer.getId());
            result.setDealerName(dealer.getName());
        }
        List<IObjectData> agreements = queryAgreementByVisitId(visitId, actionId);
        List<EnableAgreementList.ActivityVO> activityVOS = new ArrayList<>();
        result.setActivityList(activityVOS);
        Map<Integer, EnableAgreementList.NameAndId> id2DepartmentNameMap = new HashMap<>();
        Map<String, Integer> counterMap = queryActivityAgreement(agreements.stream().map(v -> v.get(TPMActivityAgreementFields.ACTIVITY_ID, String.class)).filter(v -> !Strings.isNullOrEmpty(v)).collect(Collectors.toList()), arg.getStoreId());
        for (IObjectData agreement : agreements) {
            EnableAgreementList.ActivityVO activityVO = new EnableAgreementList.ActivityVO();
            activityVOS.add(activityVO);
            activityVO.setAgreementId(agreement.getId());
            activityVO.setId(agreement.get(TPMActivityAgreementFields.ACTIVITY_ID, String.class));
            activityVO.setAgreementRecordType(agreement.getRecordType());
            if (Strings.isNullOrEmpty(activityVO.getId())) {
                continue;
            }
            IObjectData activity = serviceFacade.findObjectData(controllerContext.getUser(), activityVO.getId(), ApiNames.TPM_ACTIVITY_OBJ);
            if (activity == null) {
                log.info("activity({}) is null. maybe no data power.", activityVO);
                continue;
            }

            activityVO.setActivityStatus(activity.get(TPMActivityFields.ACTIVITY_STATUS, String.class));
            activityVO.setBeginDate(activity.get(TPMActivityFields.BEGIN_DATE, Long.class));
            activityVO.setCount(counterMap.getOrDefault(activityVO.getId(), 0));
            activityVO.setEndDate(activity.get(TPMActivityFields.END_DATE, Long.class));
            activityVO.setName(activity.getName());
            activityVO.setStatus(SIGNED);
            List<Integer> departmentIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(activity.get(TPMActivityFields.DEPARTMENT_RANGE, List.class))) {
                departmentIds.addAll((List<Integer>) activity.get(TPMActivityFields.DEPARTMENT_RANGE, List.class));
            } else {
                List<Integer> finalDepartmentIds = departmentIds;
                activity.get(TPMActivityFields.MULTI_DEPARTMENT_RANGE, List.class).forEach(departmentId -> finalDepartmentIds.add(Integer.valueOf(departmentId.toString())));
            }
            departmentIds = departmentIds.stream().distinct().collect(Collectors.toList());
            activityVO.setMultiDepartmentRange(getDepartmentVO(departmentIds, id2DepartmentNameMap));
        }

        return result;
    }


    private EnableAgreementList.Result visiting(String visitId, String actionId, IObjectData store, IObjectData dealer) {
        EnableAgreementList.Result result = new EnableAgreementList.Result();
        result.setStoreId(store.getId());
        result.setStoreName(store.getName());
        if (dealer != null) {
            result.setDealerId(dealer.getId());
            result.setDealerName(dealer.getName());
        }
        List<IObjectData> agreements = queryAgreementByVisitId(visitId, actionId);
        Map<String, IObjectData> activity2Agreement = agreements.stream().filter(v -> !Objects.isNull(v.get(TPMActivityAgreementFields.ACTIVITY_ID))).collect(Collectors.toMap(v -> v.get(TPMActivityAgreementFields.ACTIVITY_ID, String.class), v -> v, (old, now) -> old));
        List<ActivityTypeExt> typeExtList = activityTypeManager.queryActivityTypeContainsAgreement(controllerContext.getTenantId());
        if (CollectionUtils.isEmpty(typeExtList)) {
            return result;
        }
        List<EnableAgreementList.ActivityVO> activityVOS = new ArrayList<>();
        result.setActivityList(activityVOS);
        Map<Integer, EnableAgreementList.NameAndId> id2DepartmentNameMap = new HashMap<>();
        Map<String, ActivityTypeExt> activityTypeExtMap = typeExtList.stream().collect(Collectors.toMap(v -> v.get().getId().toString(), v -> v, (old, now) -> old));
        List<IObjectData> activities = TPMGrayUtils.isYinLu(controllerContext.getTenantId()) ? queryActivitiesForYL() : queryActivities(typeExtList, dealer == null ? null : dealer.getId(), store.getId());

        Map<String, Integer> counterMap = queryActivityAgreement(activities.stream().map(DBRecord::getId).collect(Collectors.toList()), arg.getStoreId());

        for (IObjectData activity : activities) {
            EnableAgreementList.ActivityVO activityVO = new EnableAgreementList.ActivityVO();
            activityVOS.add(activityVO);
            String activityType = activity.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
            if (activity2Agreement.containsKey(activity.getId())) {
                IObjectData agreement = activity2Agreement.get(activity.getId());
                activityVO.setAgreementId(agreement.getId());
                activityVO.setAgreementRecordType(agreement.getRecordType());
            } else if (activityTypeExtMap.containsKey(activityType)) {
                activityVO.setAgreementRecordType(activityTypeExtMap.get(activityType).agreementNode().getObjectRecordType());
            }

            activityVO.setId(activity.getId());
            if (Strings.isNullOrEmpty(activityVO.getId())) {
                continue;
            }
            activityVO.setActivityStatus(activity.get(TPMActivityFields.ACTIVITY_STATUS, String.class));
            activityVO.setBeginDate(activity.get(TPMActivityFields.BEGIN_DATE, Long.class));
            activityVO.setCount(counterMap.getOrDefault(activityVO.getId(), 0));
            activityVO.setEndDate(activity.get(TPMActivityFields.END_DATE, Long.class));
            activityVO.setName(activity.getName());
            activityVO.setStatus(activity2Agreement.containsKey(activity.getId()) ? SIGNED : UNSIGNED);
            List<Integer> departmentIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(activity.get(TPMActivityFields.DEPARTMENT_RANGE, List.class))) {
                activity.get(TPMActivityFields.DEPARTMENT_RANGE, List.class, Lists.newArrayList()).stream().forEach(v -> departmentIds.add(Integer.valueOf(v.toString())));
            }
            if (!CollectionUtils.isEmpty(activity.get(TPMActivityFields.MULTI_DEPARTMENT_RANGE, List.class))) {
                activity.get(TPMActivityFields.MULTI_DEPARTMENT_RANGE, List.class, Lists.newArrayList()).stream().forEach(v -> departmentIds.add(Integer.valueOf(v.toString())));
            }
            List<Integer> finalDepartmentIds = departmentIds.stream().distinct().collect(Collectors.toList());
            activityVO.setMultiDepartmentRange(getDepartmentVO(finalDepartmentIds, id2DepartmentNameMap));
        }

        return result;
    }


    private List<IObjectData> queryAgreementByVisitId(String visitId, String actionId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter visitFilter = new Filter();
        visitFilter.setFieldName(TPMActivityAgreementFields.VISIT_ID);
        visitFilter.setOperator(Operator.EQ);
        visitFilter.setFieldValues(Lists.newArrayList(visitId));

        Filter actionFilter = new Filter();
        actionFilter.setFieldName(TPMActivityAgreementFields.ACTION_ID);
        actionFilter.setOperator(Operator.EQ);
        actionFilter.setFieldValues(Lists.newArrayList(actionId));

        query.setFilters(Lists.newArrayList(visitFilter, actionFilter));

        if (TPMGrayUtils.isYinLu(controllerContext.getTenantId())) {
            //创建时间大于 5.28号的才可以被查询
            Filter timeFilter = new Filter();
            timeFilter.setFieldName(CommonFields.CREATE_TIME);
            timeFilter.setOperator(Operator.GT);
            timeFilter.setFieldValues(Lists.newArrayList(YINLU_FILTER_TIME));
            query.getFilters().add(timeFilter);
        }

        return CommonUtils.queryData(serviceFacade, User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query);
    }

    private List<EnableAgreementList.NameAndId> getDepartmentVO(List<Integer> departmentIds, Map<Integer, EnableAgreementList.NameAndId> map) {
        List<EnableAgreementList.NameAndId> list = new ArrayList<>();
        departmentIds.forEach(id -> {
            if (map.containsKey(id)) {
                list.add(map.get(id));
            } else {
                GetDepartmentDtoArg getDepartmentDtoArg = new GetDepartmentDtoArg();
                getDepartmentDtoArg.setDepartmentId(id);
                getDepartmentDtoArg.setEnterpriseId(Integer.parseInt(controllerContext.getTenantId()));
                GetDepartmentDtoResult getDepartmentDtoResult = departmentProviderService.getDepartmentDto(getDepartmentDtoArg);
                EnableAgreementList.NameAndId nameAndId = new EnableAgreementList.NameAndId();
                nameAndId.setId(getDepartmentDtoResult.getDepartment().getDepartmentId());
                nameAndId.setName(getDepartmentDtoResult.getDepartment().getName());
                list.add(nameAndId);
                map.put(id, nameAndId);
            }
        });
        return list;
    }

    private Map<String, Integer> queryActivityAgreement(List<String> activityIds, String storeId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1000);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityAgreementFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.IN);
        activityFilter.setFieldValues(activityIds);

        Filter storeFilter = new Filter();
        storeFilter.setFieldName(TPMActivityAgreementFields.STORE_ID);
        storeFilter.setOperator(Operator.EQ);
        storeFilter.setFieldValues(Lists.newArrayList(storeId));

        query.setFilters(Lists.newArrayList(activityFilter, storeFilter));

        List<IObjectData> results = serviceFacade.aggregateFindBySearchQuery(controllerContext.getTenantId(), query, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, TPMActivityAgreementFields.ACTIVITY_ID, "count", "");

        return results.stream().collect(Collectors.toMap(v -> v.get(TPMActivityAgreementFields.ACTIVITY_ID, String.class), v -> v.get("groupbycount", Integer.class), (before, after) -> before));
    }

    private List<IObjectData> queryActivitiesForYL(List<IFilter> filters) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(-1);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");

        query.setFilters(filters);

        SearchQueryUtil.handleDataPrivilege(dataPrivilegeService, controllerContext.getUser(), query, this.activityDescribe, this.activitySearchTemplate, this.isAdminRequest);


        return CommonUtils.queryData(serviceFacade, controllerContext.getUser(), ApiNames.TPM_ACTIVITY_OBJ, query);
    }

    private List<IObjectData> queryActivitiesForYL() {
        List<IObjectData> activities = Lists.newArrayList();

        List<IFilter> filters = Lists.newArrayList();


        ControllerContext controllerContext = new ControllerContext(
                RequestContextManager.getContext(),
                "TPMActivityObj",
                "YinLuAgreementEnableFilter"
        );
        YinLuAgreementEnableFilter.Arg enableFilterArg = new YinLuAgreementEnableFilter.Arg();
        enableFilterArg.setAccountId(arg.getStoreId());
        enableFilterArg.setUserId(controllerContext.getUser().getUserId());
        Controller controller;
        try {
            controller = controllerLocateService.locateController(controllerContext, enableFilterArg);
        } catch (Exception ex) {
            log.error("locateController error:", ex);
            return activities;
        }

        Object serviceResult = controller.service(enableFilterArg);
        if (serviceResult instanceof YinLuAgreementEnableFilter.Result) {
            YinLuAgreementEnableFilter.Result enableListResult = (YinLuAgreementEnableFilter.Result) serviceResult;
            if (CollectionUtils.isEmpty(enableListResult.getPlanIds())) {
                return activities;
            }
            long now = POCActivityService.DateUtil.getDayBegin().getTime();
            if (enableListResult.isOutRoute()) {
                filters.add(buildFilter(TPMActivityFields.IS_AGREEMENT_REQUIRED, Operator.EQ, Lists.newArrayList("true")));
                filters.add(buildFilter("agreement_begin_date__c", Operator.LTE, Lists.newArrayList(String.valueOf(now))));
                filters.add(buildFilter("agreement_end_date__c", Operator.GTE, Lists.newArrayList(String.valueOf(now))));
                filters.add(buildFilter(TPMActivityFields.MULTI_DEPARTMENT_RANGE, Operator.IN, enableListResult.getParentDepartmentIds()));
                List<IObjectData> data = queryActivitiesForYL(filters);
                log.info("out route yl activity ids:{}", data.stream().map(IObjectData::getId).collect(Collectors.toList()));
                return data;
            }

            filters.add(buildFilter(TPMActivityFields.IS_AGREEMENT_REQUIRED, Operator.EQ, Lists.newArrayList("true")));
            filters.add(buildFilter("agreement_begin_date__c", Operator.LTE, Lists.newArrayList(String.valueOf(now))));
            filters.add(buildFilter("agreement_end_date__c", Operator.GTE, Lists.newArrayList(String.valueOf(now))));
            filters.add(buildFilter("activity_time_span_plan_id__c", Operator.IN, enableListResult.getPlanIds()));
            List<IObjectData> data = queryActivitiesForYL(filters);
            log.info("yl activity ids:{}", data.stream().map(IObjectData::getId).collect(Collectors.toList()));
            return data;
        }


        return activities;
    }

    private IFilter buildFilter(String filterApiName, Operator op, List<String> fieldValue) {
        IFilter filter = new Filter();
        filter.setFieldName(filterApiName);
        filter.setOperator(op);
        filter.setFieldValues(fieldValue);
        return filter;
    }

    private List<IObjectData> queryActivities(List<ActivityTypeExt> typeExtList, String dealerId, String storeId) {
        List<ActivityTypeExt> configIsEmptyTypes = typeExtList.stream().filter(activityTypeExt -> {
            ActivityAgreementConfigEntity activityAgreementConfig = activityTypeExt.agreementNode().getActivityAgreementConfig();
            if (Objects.isNull(activityAgreementConfig)) {
                return true;
            } else {
                if ((Objects.isNull(activityAgreementConfig.getBeginTimeSetting()) || activityAgreementConfig.getBeginTimeSetting().getValue() == 0)
                        && (Objects.isNull(activityAgreementConfig.getEndTimeSetting()) || activityAgreementConfig.getEndTimeSetting().getValue() == 0)) {
                    return true;
                } else {
                    return false;
                }
            }
        }).collect(Collectors.toList());

        List<String> configIsEmptyTypeIds = configIsEmptyTypes.stream()
                .map(m -> m.get().getId().toString())
                .collect(Collectors.toList());

        log.info("enable agreement configIsEmptyTypeIds : {}", JSON.toJSONString(configIsEmptyTypeIds));

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setNeedReturnCountNum(false);
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        long now = System.currentTimeMillis();
        StringBuilder sqlPattern = new StringBuilder("(");
        List<IFilter> filters = new ArrayList<>();
        int count = 0;

        if (!CollectionUtils.isEmpty(configIsEmptyTypeIds)) {
            Filter beginDateFilter = new Filter();
            beginDateFilter.setFieldName(TPMActivityFields.BEGIN_DATE);
            beginDateFilter.setOperator(Operator.LT);
            beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));
            filters.add(beginDateFilter);

            Filter endDateFilter = new Filter();
            endDateFilter.setFieldName(TPMActivityFields.END_DATE);
            endDateFilter.setOperator(Operator.GT);
            endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));
            filters.add(endDateFilter);

            Filter activityTypeFilter = new Filter();
            activityTypeFilter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
            activityTypeFilter.setOperator(Operator.IN);
            activityTypeFilter.setFieldValues(configIsEmptyTypeIds);
            filters.add(activityTypeFilter);

            Filter activityStatusFilter = new Filter();
            activityStatusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
            activityStatusFilter.setOperator(Operator.EQ);
            activityStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS));
            filters.add(activityStatusFilter);

            Filter lifeStatusFilter = new Filter();
            lifeStatusFilter.setFieldName(TPMActivityFields.LIFE_STATUS);
            lifeStatusFilter.setOperator(Operator.EQ);
            lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));
            filters.add(lifeStatusFilter);

            sqlPattern.append("( 1 and 2 and 3 and 4 and 5 )");
            count = 5;
        }

        List<ActivityTypeExt> configIsNotEmptyTypes = typeExtList.stream()
                .filter(activityTypeExt -> !configIsEmptyTypeIds.contains(activityTypeExt.get().getId().toString()))
                .collect(Collectors.toList());

        log.info("enable agreement configIsNotEmptyTypes size: {}", configIsNotEmptyTypes.size());

        if (!CollectionUtils.isEmpty(configIsNotEmptyTypes)) {
            if (!CollectionUtils.isEmpty(configIsEmptyTypeIds)) {
                sqlPattern.append(" or (");
            } else {
                sqlPattern.append(" (");
            }
            for (int i = 0; i < configIsNotEmptyTypes.size(); i++) {

                if (configIsNotEmptyTypes.size() != 1) {
                    if (i == 0) {
                        sqlPattern.append(" (");
                    } else {
                        sqlPattern.append(" or (");
                    }
                }

                ActivityTypeExt ext = configIsNotEmptyTypes.get(i);

                Filter beginDateFilter = new Filter();
                beginDateFilter.setFieldName(calculateFieldNameByTimeSetting(ext.agreementConfig().getBeginTimeSetting(), TPMActivityFields.BEGIN_DATE));
                beginDateFilter.setOperator(Operator.LT);
                beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(calculateStartTimeByTimeSetting(ext.agreementConfig().getBeginTimeSetting(), now))));
                filters.add(beginDateFilter);

                Filter endDateFilter = new Filter();
                endDateFilter.setFieldName(calculateFieldNameByTimeSetting(ext.agreementConfig().getEndTimeSetting(), TPMActivityFields.END_DATE));
                endDateFilter.setOperator(Operator.GT);
                endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(calculateEndTimeByTimeSetting(ext.agreementConfig().getEndTimeSetting(), now))));
                filters.add(endDateFilter);

                Filter activityTypeFilter = new Filter();
                activityTypeFilter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
                activityTypeFilter.setOperator(Operator.EQ);
                activityTypeFilter.setFieldValues(Lists.newArrayList(ext.get().getId().toString()));
                filters.add(activityTypeFilter);

                sqlPattern.append(count += 1).append(" and ").append(count += 1).append(" and ").append(count += 1);
                if (configIsNotEmptyTypes.size() != 1) {
                    sqlPattern.append(" )");
                }
            }
            sqlPattern.append(" )");
        }
        sqlPattern.append(" ) ");

        if (!controllerContext.getUser().isOutUser() && !TPMGrayUtils.denyDepartmentFilterOnActivity(controllerContext.getTenantId())) {
            List<Integer> departments = organizationService.getDepartmentIds(controllerContext.getUser().getTenantIdInt(), Integer.parseInt(controllerContext.getUser().getUpstreamOwnerIdOrUserId()));
            Filter multiDepartmentFilter = new Filter();
            multiDepartmentFilter.setFieldName(TPMActivityFields.MULTI_DEPARTMENT_RANGE);
            multiDepartmentFilter.setOperator(Operator.HASANYOF);
            multiDepartmentFilter.setFieldValues(departments.stream().map(Object::toString).collect(Collectors.toList()));
            filters.add(multiDepartmentFilter);
            if (I_DESCRIBE_CACHE_SERVICE.isExistField(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, TPMActivityFields.DEPARTMENT_RANGE)) {
                Filter departmentFilter = new Filter();
                departmentFilter.setFieldName(TPMActivityFields.DEPARTMENT_RANGE);
                departmentFilter.setOperator(Operator.IN);
                departmentFilter.setFieldValues(departments.stream().map(Object::toString).collect(Collectors.toList()));
                sqlPattern.append(" and ( ").append(count += 1).append(" or ").append(count += 1).append(") ");
                filters.add(departmentFilter);
            } else {
                sqlPattern.append(" and ").append(count += 1).append(" ");
            }
        }


        Filter noDealerFilter = new Filter();
        noDealerFilter.setFieldName(TPMActivityFields.DEALER_ID);
        noDealerFilter.setOperator(Operator.IS);
        noDealerFilter.setFieldValues(new ArrayList<>());
        filters.add(noDealerFilter);

        if (!Strings.isNullOrEmpty(dealerId)) {
            Filter dealerFilter = new Filter();
            dealerFilter.setFieldName(TPMActivityFields.DEALER_ID);
            dealerFilter.setOperator(Operator.EQ);
            dealerFilter.setFieldValues(Lists.newArrayList(dealerId));
            filters.add(dealerFilter);
            sqlPattern.append(" and ( ").append(count += 1).append(" or ").append(count += 1).append(" ) ");
        } else {
            sqlPattern.append("and ").append(count += 1).append(" ");
        }

        Filter closeStatusFilter = new Filter();
        closeStatusFilter.setFieldName(TPMActivityFields.CLOSE_STATUS);
        closeStatusFilter.setOperator(Operator.EQ);
        closeStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.CLOSE_STATUS__UNCLOSED));
        filters.add(closeStatusFilter);
        sqlPattern.append(" and ").append(count += 1).append(" ");

        query.setPattern(sqlPattern.toString());

        query.setFilters(filters);

        log.info("enable agreement sqlPattern :{}", sqlPattern);
        log.info("enable agreement query :{}", JSON.toJSONString(query));
        SearchQueryUtil.handleDataPrivilege(dataPrivilegeService, controllerContext.getUser(), query, this.activityDescribe, this.activitySearchTemplate, this.isAdminRequest);
        List<IObjectData> activities = CommonUtils.queryData(serviceFacade, controllerContext.getUser(), ApiNames.TPM_ACTIVITY_OBJ, query);
        List<IObjectData> finalActivities = new ArrayList<>();

        Map<String, Boolean> validateMap = rangeFieldBusiness.judgeStoreInActivitiesStoreRange(controllerContext.getTenantId(), storeId, dealerId, activities, false, true);
        for (IObjectData activity : activities) {
            if (validateMap.getOrDefault(activity.getId(), false)) {
                finalActivities.add(activity);
            }
        }

        return finalActivities;
    }


    private String calculateFieldNameByTimeSetting(ActivityAgreementTimeSettingEntity setting, String defaultValue) {
        if (Objects.isNull(setting) || Strings.isNullOrEmpty(setting.getType())) {
            return defaultValue;
        }

        if (setting.getType().contains("begin")) {
            return TPMActivityFields.BEGIN_DATE;
        }

        if (setting.getType().contains("end")) {
            return TPMActivityFields.END_DATE;
        }

        return defaultValue;
    }


    private long calculateStartTimeByTimeSetting(ActivityAgreementTimeSettingEntity setting, long now) {
        if (Objects.isNull(setting) || Strings.isNullOrEmpty(setting.getType())) {
            return now;
        }
/*        if (setting.getType().contains("end")) {
            now = now + (24 * 60 * 60 * 1000);
        }*/
        return calculateTimeByTimeSetting(setting, now);
    }

    private long calculateEndTimeByTimeSetting(ActivityAgreementTimeSettingEntity setting, long now) {
        if (Objects.isNull(setting) || Strings.isNullOrEmpty(setting.getType())) {
            return now;
        }

        return calculateTimeByTimeSetting(setting, now);
    }

    private long calculateTimeByTimeSetting(ActivityAgreementTimeSettingEntity setting, long now) {
        if (setting.getType().contains("before")) {
            return now + setting.getValue();
        }

        if (setting.getType().contains("after")) {
            return now - setting.getValue();
        }

        return now;
    }


    @Override
    protected EnableAgreementList.Result after(EnableAgreementList.Arg arg, EnableAgreementList.Result result) {
        return super.after(arg, result);
    }
}
