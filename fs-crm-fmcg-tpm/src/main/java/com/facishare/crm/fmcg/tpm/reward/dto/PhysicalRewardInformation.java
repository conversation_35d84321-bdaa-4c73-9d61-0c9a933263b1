package com.facishare.crm.fmcg.tpm.reward.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import kotlin.jvm.Transient;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ToString
@Builder
public class PhysicalRewardInformation implements Serializable {

    private String name;


    @JSONField(name = "physical_item_id")
    @JsonProperty(value = "physical_item_id")
    @SerializedName("physical_item_id")
    private String physicalItemId;


    @JSONField(name = "reward_detail_id")
    @JsonProperty(value = "reward_detail_id")
    @SerializedName("reward_detail_id")
    private String rewardDetailId;

    /**
     * 0: 标准实物
     * 1： 红包
     * 2： 谢谢惠顾
     */
    private int type;

    @JSONField(name = "reward_get_method")
    @JsonProperty(value = "reward_get_method")
    @SerializedName("reward_get_method")
    private List<String> rewardGetMethod;

    @JSONField(name = "image_url")
    @JsonProperty(value = "image_url")
    @SerializedName("image_url")
    private String imageUrl;

    @JSONField(name = "record_token")
    @JsonProperty(value = "record_token")
    @SerializedName("record_token")
    private String recordToken;

    private Integer limit;

    private String date;

    private BigDecimal amount;

    @JSONField(name = "goods_id")
    @JsonProperty(value = "goods_id")
    @SerializedName("goods_id")
    private String goodsId;

}
