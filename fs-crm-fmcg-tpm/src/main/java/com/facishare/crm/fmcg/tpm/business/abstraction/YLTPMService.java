package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.crm.fmcg.tpm.api.agreement.YLActivityDetailBulkCreate;
import com.facishare.crm.fmcg.tpm.api.agreement.YLAgreementBulkCreate;
import com.facishare.crm.fmcg.tpm.api.agreement.YLAgreementStatusUpdate;

public interface YLTPMService {

    YLAgreementBulkCreate.Result agreementBulkCreate(YLAgreementBulkCreate.Arg arg);

    YLAgreementStatusUpdate.Result agreementStatusUpdate(YLAgreementStatusUpdate.Arg arg);

    YLActivityDetailBulkCreate.Result activityDetailBulkCreate(YLActivityDetailBulkCreate.Arg arg);
}
