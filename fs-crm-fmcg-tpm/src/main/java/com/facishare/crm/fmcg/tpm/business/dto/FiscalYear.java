package com.facishare.crm.fmcg.tpm.business.dto;

import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/10/20 17:40
 */
@Data
@ToString
@Builder
@SuppressWarnings("Duplicates")
public class FiscalYear implements Serializable {

    private int year;

    private long start;

    private long end;

    private Map<Integer, FiscalQuarter> quarter;

    private Map<Integer, FiscalMonth> month;

    private static final int MONTH_COUNT_OF_YEAR = 12;
    private static final int MONTH_COUNT_OF_QUARTER = 3;

    public static FiscalYear of(int year) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, year);
        cal.set(Calendar.MONTH, 0);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        long begin = cal.getTimeInMillis();
        cal.add(Calendar.YEAR, 1);
        long end = cal.getTimeInMillis();

        List<FiscalMonth> month = Lists.newArrayList();
        for (int i = 1; i <= MONTH_COUNT_OF_YEAR; i++) {
            month.add(FiscalMonth.of(year, i));
        }

        return FiscalYear.builder()
                .year(year)
                .start(begin)
                .end(end)
                .month(month.stream().collect(Collectors.toMap(FiscalMonth::getMonth, v -> v)))
                .quarter(month.stream()
                        .collect(Collectors.groupingBy(FiscalMonth::getQuarter))
                        .entrySet().stream()
                        .collect(Collectors.toMap(Map.Entry::getKey, v -> FiscalQuarter.of(year, v.getKey(), v.getValue())))
                )
                .build();
    }
}
