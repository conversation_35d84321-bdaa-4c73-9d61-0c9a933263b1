package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofDetailFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofDisplayImgFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofFields;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.utils.LayoutUtil;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDescribeLayoutController;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@SuppressWarnings("Duplicates")
public class TPMActivityProofObjDescribeLayoutController extends AbstractStandardDescribeLayoutController<TPMActivityProofObjDescribeLayoutController.Arg> {

    public static final String ADD_ACTION = "add";
    public static final String EDIT_ACTION = "edit";
    private boolean enableAI = false;
    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);

    // 提取常量，使用不可变集合
    private static final List<String> COMMON_HIDE_LAYOUT = Lists.newArrayList(
            ApiNames.TPM_ACTIVITY_PROOF_PRODUCT_DETAIL_OBJ,
            ApiNames.TPM_ACTIVITY_PROOF_MATERIAL_DETAIL_OBJ
    );

    private static final List<String> AI_ONLY_HIDE_LAYOUT = Lists.newArrayList(
            ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ
    );

    @Override
    protected List<IObjectDescribe> getDetailDescribes(IObjectDescribe masterDescribe) {
        List<IObjectDescribe> detailDescribes = super.getDetailDescribes(masterDescribe);
        if (CollectionUtils.isEmpty(detailDescribes)) {
            return detailDescribes;
        }

        String activityId = arg.getActivity_id();
        if (StringUtils.isBlank(activityId)) {
            return detailDescribes;
        }

        ActivityTypeExt activityType = activityTypeManager.findByActivityId(controllerContext.getTenantId(), activityId);
        enableAI = activityType.proofConfig().getAiConfig() != null && Boolean.TRUE.equals(activityType.proofConfig().getAiConfig().getEnableAiDisplayRecognition());
        List<String> hideLayouts = new ArrayList<>(COMMON_HIDE_LAYOUT);
        if (enableAI) {
            hideLayouts.addAll(AI_ONLY_HIDE_LAYOUT);
        }

        String layoutType = arg.getLayout_type();
        if (ADD_ACTION.equals(layoutType) || EDIT_ACTION.equals(layoutType)) {
            detailDescribes.removeIf(object -> hideLayouts.contains(object.getApiName()));
        }
        return detailDescribes;
    }

    @Override
    protected StandardDescribeLayoutController.Result after(Arg arg, StandardDescribeLayoutController.Result result) {
        StandardDescribeLayoutController.Result res = super.after(arg, result);
        Boolean includeLayout = arg.getInclude_layout();
        if (Boolean.FALSE.equals(includeLayout)) {
            return res;
        }

        overrideMasterLayout(res);
        overrideDetailLayout(res);
        return res;
    }

    private void overrideMasterLayout(StandardDescribeLayoutController.Result res) {
        Set<String> hideFields = Sets.newHashSet();
        Set<String> readonlyFields = Sets.newHashSet();
        Set<String> requiredFields = Sets.newHashSet();

        if (ADD_ACTION.equals(arg.getLayout_type()) || EDIT_ACTION.equals(arg.getLayout_type())) {
            hideFields.add(TPMActivityProofFields.SYSTEM_JUDGMENT_STATUS);
            hideFields.add(TPMActivityProofFields.OPEN_AI);
            hideFields.add(TPMActivityProofFields.AI_IDENTIFY_STATUS);
        }
        LayoutUtil.setLayoutFieldHiddenOrReadOnly(LayoutExt.of(res.getLayout().toLayout()), hideFields, readonlyFields, requiredFields, null, null);
    }

    private void overrideDetailLayout(StandardDescribeLayoutController.Result result) {

        Map<String, Set<String>> detailRemoveFieldsMap = Maps.newHashMap();

        detailRemoveFieldsMap.put(ApiNames.TPM_ACTIVITY_PROOF_DISPLAY_IMG_OBJ, Sets.newHashSet(
                TPMActivityProofDisplayImgFields.SYSTEM_JUDGMENT_STATUS
                , TPMActivityProofDisplayImgFields.AUDIT_STATUS
                , TPMActivityProofDisplayImgFields.AI_DISPLAY_FORM_ID
                , TPMActivityProofDisplayImgFields.AI_LAYER_NUMBER
                , TPMActivityProofDisplayImgFields.AI_GROUP_NUMBER
                , TPMActivityProofDisplayImgFields.AI_VISIBLE_NUMBER
        ));

        if (ADD_ACTION.equals(arg.getLayout_type()) || EDIT_ACTION.equals(arg.getLayout_type())) {
            if (!enableAI) {
                detailRemoveFieldsMap.put(ApiNames.TPM_ACTIVITY_PROOF_PRODUCT_DETAIL_OBJ, Sets.newHashSet(
                        TPMActivityProofDetailFields.AI_NUMBER
                        , TPMActivityProofDetailFields.AI_FACE_NUMBER
                        , TPMActivityProofDetailFields.AI_SKU_NUMBER
                        , TPMActivityProofDetailFields.PRODUCT_DISPLAY_STATUS
                        , TPMActivityProofDetailFields.MATERIAL_DISPLAY_STATUS
                ));
            }
        }

        LayoutUtil.overrideDetailLayout(result, detailRemoveFieldsMap, Maps.newHashMap());
    }


    @EqualsAndHashCode(callSuper = true)
    @Data
    @ToString
    public static class Arg extends StandardDescribeLayoutController.Arg implements Serializable {
        @JSONField(name = "M50")
        private String activity_id;
    }
}
