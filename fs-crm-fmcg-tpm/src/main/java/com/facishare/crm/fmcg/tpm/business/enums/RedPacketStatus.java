package com.facishare.crm.fmcg.tpm.business.enums;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/11/14 17:03
 */
public enum RedPacketStatus {
    /**
     * 待提现
     */
    WITHDRAWN_WAIT("WITHDRAWN_WAIT"),
    /**
     * 已过期
     */
    EXPIRED("EXPIRED"),
    /**
     * 提现中
     */
    WITHDRAWING("WITHDRAWING"),
    /**
     * 已提现
     */
    WITHDRAWN("WITHDRAWN"),
    /**
     * 已发放
     */
    GRANT("GRANT");

    private String value;

    private RedPacketStatus(String value) {
        this.value = value;
    }

    private static final Map<String, RedPacketStatus> VALUE_MAP = Stream.of(values()).collect(Collectors.toMap(RedPacketStatus::value, v -> v, (a, b) -> a));

    public String value() {
        return this.value;
    }

    public RedPacketStatus of(String value) {
        return VALUE_MAP.get(value);
    }
}
