package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.business.abstraction.AbstractDisPlayReportBaseService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDisplayReportValidator;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofAiConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.utils.I18NEnums;
import com.facishare.crm.fmcg.tpm.web.contract.model.TPMProofPeriodTime;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.stone.commons.domain.utils.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@SuppressWarnings("Duplicates")
public class DisplayReportValidator extends AbstractDisPlayReportBaseService implements IDisplayReportValidator {

    @Resource
    private ActivityTypeManager activityTypeManager;
    @Resource
    private ServiceFacade serviceFacade;

    @Override
    public void validateDisplayReport(BaseObjectSaveAction.Arg arg) {
        log.info("验证展示报表数据开始");
        IObjectData objectData = arg.getObjectData().toObjectData();
        List<ObjectDataDocument> detailObjectDataDocument = arg.getDetails().get(ApiNames.TPM_ACTIVITY_DETAIL_OBJ);
        List<IObjectData> detailObjectData = ObjectDataDocument.ofDataList(detailObjectDataDocument);
        String tenantId = objectData.getTenantId();
        String describeApiName = objectData.getDescribeApiName();
        if (describeApiName.equals(ApiNames.TPM_ACTIVITY_OBJ)) {
            validateReportForActivity(tenantId, objectData, detailObjectData);
        } else if (describeApiName.equals(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ)) {
            validateReportForAgreement(tenantId, objectData);
        } else {
            log.info("来源不是活动也不是协议，不处理");
        }
    }

    @Override
    public void addProofValidation(String tenantId, BaseObjectSaveAction.Arg arg) {
        // 通过活动协议新建举证两种情况
        // 1：关联外勤字段，一个外勤一个协议下只会有一个举证，且判断举证是否可举证的时段内
        // 2：协议下直接新建，判断举证是否可举证的时段内
        IObjectData objectData = arg.getObjectData().toObjectData();
        List<ObjectDataDocument> objectDataDocuments = arg.getDetails().get(ApiNames.TPM_ACTIVITY_PROOF_DISPLAY_IMG_OBJ);
        if (CollectionUtils.isEmpty(objectDataDocuments)) {
            throw new ValidateException(I18N.text(I18NEnums.DISPLAY_PROOF_IMG_NOT_EMPTY_BY_AI_IDENTIFY.getCode()));
        }
        if (super.isRecognized(tenantId, objectData)) {
            return;
        }
        List<IObjectData> detailData = ObjectDataDocument.ofDataList(objectDataDocuments);

        // 判断举证是否关联了协议
        String activityAgreementId = objectData.get(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID, String.class);
        String activityId = objectData.get(TPMActivityProofFields.ACTIVITY_ID, String.class);

        if (StringUtils.isNotBlank(activityAgreementId)) {
            // 如果提供了协议ID，使用协议验证逻辑
            validateProofByAgreement(tenantId, objectData, activityAgreementId);
            // 协议类的验证举证陈列图片从对象
            validateProofDetailByAgreement(tenantId, detailData, activityAgreementId);
        } else {
            // 如果只提供了活动ID，使用活动验证逻辑
            validateProofByActivity(tenantId, activityId);
            validateProofDetailByActivity(tenantId, detailData, activityId);
        }
    }

    public void validateReportForAgreement(String tenantId, IObjectData objectData) {
        //获取对象里的活动id
        String activityId = objectData.get(TPMActivityAgreementFields.ACTIVITY_ID, String.class);
        if (StringUtils.isBlank(activityId)) {
            throw new ValidateException("活动ID不能为空");//ignorei18n
        }

        // 查询举证时段
        IObjectData data = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), activityId, ApiNames.TPM_ACTIVITY_OBJ);
        if (data == null) {
            throw new ValidateException("未找到对应的活动数据");//ignorei18n
        }

        String proofPeriodValue = data.get(TPMActivityFields.PROOF_PERIOD, String.class);
        if (Strings.isNullOrEmpty(proofPeriodValue) || (proofPeriodValue.contains("type") && proofPeriodValue.contains("ALL"))) {
            log.info("活动{}, 证据时段为空，不处理", data.getId());
            return;
        }
        List<TPMDisplayReportService.ProofPeriod> proofPeriods = JSONArray.parseArray(proofPeriodValue, TPMDisplayReportService.ProofPeriod.class);

        // 协议的开始结束时间
        Long beginData = objectData.get(TPMActivityAgreementFields.BEGIN_DATE, Long.class);
        Long endData = objectData.get(TPMActivityAgreementFields.END_DATE, Long.class);
        List<TPMDisplayReportService.ProofPeriod> adjustedProofPeriods = calculateProofPeriods(proofPeriods, beginData, endData);
        // 如果为空 ，提示 协议活动需要举证，当前协议周期不在活动举证阶段内，请调整协议周期后重新提交
        if (CollectionUtils.isEmpty(adjustedProofPeriods)) {
            throw new ValidateException(I18N.text(I18NEnums.ACTIVITY_AGREEMENT_NEED_PROOF_PERIOD_IN_ACTIVITY.getCode()));
        }
    }

    public void validateReportForActivity(String tenantId, IObjectData objectData, List<IObjectData> detailObjectData) {

        //活动类型中 活动举证节点启用AI后，新建 活动 时，活动项目从对象 必须按 陈列形式+项目定义活动标准
        String activityType = objectData.get(TPMActivityFields.ACTIVITY_TYPE, String.class);

        ActivityTypeExt activityTypeExt = activityTypeManager.find(tenantId, activityType);
        if (activityTypeExt == null) {
            log.error("未找到活动类型,活动类型:{},活动ID:{}", activityType, objectData.getId());
            throw new ValidateException("未找到对应的活动类型配置");//ignorei18n
        }

        if (activityTypeExt.proofConfig() != null) {
            ActivityProofAiConfigEntity aiConfig = activityTypeExt.proofConfig().getAiConfig();
            // 如果启用了AI识别,需要校验活动项目和陈列形式
            if (aiConfig != null && aiConfig.getEnableAiDisplayRecognition())
                validateProofAIConfig(tenantId, objectData, detailObjectData);
        }

        //活动申请时段的校验
        String proofPeriodValue = objectData.get(TPMActivityFields.PROOF_PERIOD, String.class);
        if (Strings.isNullOrEmpty(proofPeriodValue) || (proofPeriodValue.contains("type") && proofPeriodValue.contains("ALL"))) {
            log.info("活动{}, 证据时段为空，不处理", objectData.getId());
            return;
        }
        // 将proofPeriod转换为List<ProofPeriod>
        List<TPMDisplayReportService.ProofPeriod> proofPeriodList = JSONArray.parseArray(proofPeriodValue, TPMDisplayReportService.ProofPeriod.class);

        if (CollectionUtils.isEmpty(proofPeriodList)) {
            log.info("活动{}, 证据时段为空，不处理", objectData.getId());
            return;
        }
        // 判断proofPeriodList 的大小是否超过了 20 个
        if (proofPeriodList.size() > 20) {
            throw new ValidateException(I18N.text(I18NEnums.PROOF_PERIOD_COUNT_EXCEED_MAX_COUNT.getCode()));
        }
        // 判断proofTimePeriodList中的开始日期和结束日期是否在objectData的开始日期和结束日期之间
        long beginDate = objectData.get(TPMActivityFields.BEGIN_DATE, Long.class);
        long endDate = objectData.get(TPMActivityFields.END_DATE, Long.class);
        for (TPMDisplayReportService.ProofPeriod proofPeriod : proofPeriodList) {
            long proofBeginDate = proofPeriod.getBeginDate();
            long proofEndDate = proofPeriod.getEndDate();
            if (proofBeginDate < beginDate || proofEndDate > endDate) {
                log.error("活动{}, 证据时段{}不在活动申请时段{}和{}之间", objectData.getId(), proofPeriod.getStage(), beginDate, endDate);
                throw new ValidateException(I18N.text(I18NEnums.PROOF_PERIOD_NOT_IN_ACTIVITY_TIME_RANGE.getCode()));
            }
        }
        // 判断proofTimePeriodList中的开始日期和结束日期的范围是否存在重叠，有存在重叠的，则抛出异常
        // 按开始时间排序,优化时间区间重叠判断逻辑
        proofPeriodList.sort((a, b) -> {
            long beginDateA = a.getBeginDate();
            long beginDateB = b.getBeginDate();
            return Long.compare(beginDateA, beginDateB);
        });

        for (int i = 0; i < proofPeriodList.size() - 1; i++) {
            TPMDisplayReportService.ProofPeriod current = proofPeriodList.get(i);
            TPMDisplayReportService.ProofPeriod next = proofPeriodList.get(i + 1);

            long currentEndDate = current.getEndDate();
            long nextBeginDate = next.getBeginDate();

            if (currentEndDate >= nextBeginDate) {
                log.error("活动{}, 证据时段{}和{}存在重叠", objectData.getId(), current.getStage(), next.getStage());
                throw new ValidateException(I18N.text(I18NEnums.PROOF_PERIOD_EXISTS_DUPLICATE_PERIOD_IN_ACTIVITY.getCode()));
            }
        }

    }

    public void validateProofAIConfig(String tenantId, IObjectData objectData, List<IObjectData> detailObjectData) {
        // 校验活动项目不能为空
        if (CollectionUtils.isEmpty(detailObjectData)) {
            log.error("活动类型开启了AI识别,但活动项目为空,活动ID:{}", objectData.getId());
            throw new ValidateException(I18N.text(I18NEnums.ACTIVITY_ITEM_NO_EMPTY_BY_AI_IDENTIFY.getCode()));
        }

        // 校验所有活动项目必须有陈列形式
        boolean hasEmptyDisplayForm = detailObjectData.stream()
                .anyMatch(data -> Objects.isNull(data.get(TPMActivityDetailFields.DISPLAY_FORM_ID)));
        if (hasEmptyDisplayForm) {
            log.error("活动类型开启了AI识别,但存在活动项目陈列形式为空,活动ID:{}", objectData.getId());
            throw new ValidateException(I18N.text(I18NEnums.ACTIVITY_DISPLAY_NO_EMPTY_BY_AI_IDENTIFY.getCode()));
        }

        // 1. 提取所有活动项目的陈列标准ID
        List<String> standardDisplayFormIds = detailObjectData.stream()
                .map(v -> v.get(TPMActivityDetailFields.DISPLAY_PROJECT_JUDGMENT_STANDARD, String.class))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(standardDisplayFormIds)) {
            // 2. 批量查询陈列标准对象
            List<IObjectData> standardDisplayFormList = serviceFacade.findObjectDataByIds(
                    tenantId,
                    standardDisplayFormIds,
                    "DisplayProjectJudgmentStandard__c");

            // 3. 构建陈列标准ID与楼层数的映射
            Map<String, BigDecimal> standardDisplayFormMap = standardDisplayFormList.stream()
                    .filter(v -> v.get("floor_number__c", BigDecimal.class) != null)
                    .collect(Collectors.toMap(
                            IObjectData::getId,
                            v -> v.get("floor_number__c", BigDecimal.class),
                            (existing, replacement) -> existing));

            // 4. 校验每个活动项目的陈列标准
            for (IObjectData datum : detailObjectData) {
                String standardDisplayFormId = datum.get(TPMActivityDetailFields.DISPLAY_PROJECT_JUDGMENT_STANDARD, String.class);
                BigDecimal activityAmount = datum.get(TPMActivityDetailFields.ACTIVITY_AMOUNT_STANDARD, BigDecimal.class);

                // 如果该活动项目有陈列标准ID
                if (StringUtils.isNotBlank(standardDisplayFormId)) {
                    BigDecimal floorNumber = standardDisplayFormMap.get(standardDisplayFormId);

                    // 检查楼层数是否匹配
                    if (floorNumber != null && !floorNumber.equals(activityAmount)) {
                        log.error("活动类型开启了AI识别,但存在活动项目陈列标准字段和标准陈列形式不匹配,活动ID:{}", objectData.getId());
                        throw new ValidateException(I18N.text(I18NEnums.ACTIVITY_DISPLAY_NO_MATCH_BY_AI_IDENTIFY.getCode()));
                    }
                }
            }
        }
    }

    /**
     * 使用活动ID验证举证
     *
     * @param tenantId   租户ID
     * @param activityId 活动ID
     */
    private void validateProofByActivity(String tenantId, String activityId) {
        // 验证举证时段
        List<TPMProofPeriodTime.ProofPeriod> proofPeriods = queryTPMProofPeriodTime(tenantId,
                TPMProofTimePeriodDetailFields.ACTIVITY_ID, activityId);
        validateProofTimeRange(proofPeriods);
    }


    /**
     * 验证基于活动的举证陈列图片
     *
     * @param tenantId            租户ID
     * @param detailData 举证陈列图片数据
     * @param activityId          活动ID
     */
    private void validateProofDetailByActivity(String tenantId, List<IObjectData> detailData, String activityId) {

        // 校验举证陈列图片中举证图片字段如果有为空的，则抛出异常
        boolean hasEmptyImage = detailData.stream()
                .anyMatch(DisplayReportValidator::isImageEmpty);
        if (hasEmptyImage) {
            log.error("举证陈列图片不可都为空");
            throw new ValidateException(I18N.text(I18NEnums.DISPLAY_PROOF_IMG_NOT_ALL_EMPTY.getCode()));
        }

        // 查询活动的从对象 TPMActivityDetailObj
        List<IObjectData> activityDetails = queryTPMActivityDetailByObjectId(tenantId, activityId);

        // 使用公共方法进行验证
        validateProofDisplayImages(
                detailData,
                activityId,
                activityDetails,
                TPMActivityDetailFields.DISPLAY_FORM_ID,
                TPMActivityDetailFields.ACTIVITY_ITEM_ID,
                TPMActivityDetailFields.ACTIVITY_AMOUNT_STANDARD,
                "活动" //ignorei18n
        );
    }

    /**
     * 计算合适的协议举证时段
     * 1. 先按stage排序proofPeriods
     * 2. 将proofPeriods内的时间段作为一个整体与[beginDate, endDate]取交集
     * 3. 得出proofPeriods中在[beginDate, endDate]时间段内的所有时间段
     * 4. 如果新的proofPeriods中第一个和最后一个与[beginDate, endDate]有重合，则取[beginDate,
     * endDate]中的时间
     *
     * @param proofPeriods 原始活动举证时段列表
     * @param beginDate    协议开始日期
     * @param endDate      协议结束日期
     * @return 处理后的协议举证时段列表
     */
    public List<TPMDisplayReportService.ProofPeriod> calculateProofPeriods(List<TPMDisplayReportService.ProofPeriod> proofPeriods, long beginDate, long endDate) {
        if (CollectionUtils.isEmpty(proofPeriods)) {
            return new ArrayList<>();
        }

        try {
            List<TPMDisplayReportService.ProofPeriod> sortedList = new ArrayList<>(proofPeriods);
            sortedList.sort(Comparator.comparingInt(TPMDisplayReportService.ProofPeriod::getStage));

            List<TPMDisplayReportService.ProofPeriod> filteredList = new ArrayList<>();
            for (TPMDisplayReportService.ProofPeriod period : sortedList) {
                if (period == null) {
                    continue;
                }

                if (period.getEndDate() <= beginDate || period.getBeginDate() >= endDate) {
                    continue;
                }

                TPMDisplayReportService.ProofPeriod newPeriod = TPMDisplayReportService.ProofPeriod.builder()
                        .stage(period.getStage())
                        .beginDate(period.getBeginDate())
                        .endDate(period.getEndDate())
                        .build();

                filteredList.add(newPeriod);
            }

            if (filteredList.isEmpty()) {
                log.error("filteredList 举证周期列表为空");
                return filteredList;
            }

            TPMDisplayReportService.ProofPeriod firstPeriod = filteredList.get(0);
            if (firstPeriod.getBeginDate() < beginDate) {
                firstPeriod.setBeginDate(beginDate);
            }

            TPMDisplayReportService.ProofPeriod lastPeriod = filteredList.get(filteredList.size() - 1);
            if (lastPeriod.getEndDate() > endDate) {
                lastPeriod.setEndDate(endDate);
            }

            for (int i = 0; i < filteredList.size(); i++) {
                filteredList.get(i).setStage(i + 1);
            }

            return filteredList;
        } catch (Exception e) {
            log.error("计算举证周期时发生异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 使用协议ID验证举证
     *
     * @param tenantId            租户ID
     * @param objectData          举证对象数据
     * @param activityAgreementId 活动协议ID
     */
    private void validateProofByAgreement(String tenantId, IObjectData objectData, String activityAgreementId) {
        // 获取外勤ID
        String visitId = objectData.get(TPMActivityProofFields.VISIT_ID, String.class);

        // 如果存在外勤ID，验证外勤下是否已有举证
        if (StringUtils.isNotBlank(visitId)) {
            List<IObjectData> visitProofs = queryTPMActivityProofByVisitId(tenantId, visitId, activityAgreementId);
            if (!CollectionUtils.isEmpty(visitProofs)) {
                log.error("外勤动作下存在已经关联的举证，不能新建，visitId: {}, activityAgreementId: {}", visitId, activityAgreementId);
                throw new ValidateException(I18N.text(I18NEnums.PROOF_NOT_ADD_CHECK_IN_PROOF_EXISTS.getCode()));
            }
        }

        // 验证举证时段
        List<TPMProofPeriodTime.ProofPeriod> proofPeriods = queryTPMProofPeriodTime(tenantId,
                TPMProofTimePeriodDetailFields.AGREEMENT_ID, activityAgreementId);
        validateProofTimeRange(proofPeriods);
    }


    /**
     * 验证基于协议的举证陈列图片
     *
     * @param tenantId            租户ID
     * @param detailData 举证陈列图片数据
     * @param activityAgreementId 活动协议ID
     */
    private void validateProofDetailByAgreement(String tenantId, List<IObjectData> detailData, String activityAgreementId) {

        // 校验举证陈列图片中举证图片字段如果有为空的，则抛出异常
        boolean hasEmptyImage = detailData.stream()
                .anyMatch(DisplayReportValidator::isImageEmpty);
        if (hasEmptyImage) {
            log.error("举证陈列图片中存在图片字段为空的记录");
            throw new ValidateException(I18N.text(I18NEnums.DISPLAY_PROOF_IMG_PROOF_IMG_FIELD_NOT_EMPTY.getCode()));
        }

        // 查询协议的从对象 TPMActivityAgreementDetailObj
        List<IObjectData> agreementDetails = queryTPMActivityAgreementDetailsByAgreementId(tenantId, activityAgreementId);

        // 使用公共方法进行验证
        validateProofDisplayImages(
                detailData,
                activityAgreementId,
                agreementDetails,
                TPMActivityAgreementDetailFields.DISPLAY_FORM_ID,
                TPMActivityAgreementDetailFields.ACTIVITY_ITEM_ID,
                TPMActivityAgreementDetailFields.AMOUNT_STANDARD,
                "协议" //ignorei18n
        );
    }

    /**
     * 验证举证是否在有效的时间范围内
     *
     * @param proofPeriods 举证时段列表
     */
    private void validateProofTimeRange(List<TPMProofPeriodTime.ProofPeriod> proofPeriods) {
        if (!CollectionUtils.isEmpty(proofPeriods)) {
            // 验证举证的创建时间是否在举证时段proofPeriods对象的时间段内
            long createTime = System.currentTimeMillis();
            boolean isInProofPeriod = proofPeriods.stream()
                    .anyMatch(period -> createTime >= period.getBeginDate() && createTime <= period.getEndDate());

            if (!isInProofPeriod) {
                log.error("举证不在可举证的时段内，不能新建，createTime: {}", createTime);
                throw new ValidateException(I18N.text(I18NEnums.PROOF_NOT_ENABLE_RANGE_DISABLE_ADD.getCode()));
            }
        }
    }

    /**
     * 验证举证陈列图片的通用方法
     *
     * @param detailData 举证陈列图片数据
     * @param objectId            关联对象ID（活动ID或协议ID）
     * @param details             关联对象的明细数据
     * @param displayFormField    明细中的陈列形式字段名
     * @param activityItemField   明细中的活动项目字段名
     * @param amountStandardField 明细中的标准数量字段名
     * @param objectType          对象类型名称（用于错误消息）
     */
    private void validateProofDisplayImages(
            List<IObjectData> detailData,
            String objectId,
            List<IObjectData> details,
            String displayFormField,
            String activityItemField,
            String amountStandardField,
            String objectType) {

        // 检查明细数据
        if (CollectionUtils.isEmpty(details)) {
            log.error("未找到{} {} 的明细数据", objectType, objectId);
            throw new ValidateException("not found object detail!");
        }

        // 构建明细数据映射
        Map<String, String> detailMap = details.stream()
                .collect(Collectors.toMap(data -> {
                    String displayFormId = data.get(displayFormField, String.class);
                    String activityProjectId = data.get(activityItemField, String.class);
                    return displayFormId + "_" + activityProjectId;
                }, DBRecord::getId, (oldValue, newValue) -> oldValue));

        // 校验举证陈列图片
        Map<String, IObjectData> proofDisplayImgMap = new HashMap<>();
        detailData.forEach(data -> {
            String displayFormId = data.get(TPMActivityProofDisplayImgFields.DISPLAY_FORM_ID, String.class);
            String activityProjectId = data.get(TPMActivityProofDisplayImgFields.ACTIVITY_ITEM_ID, String.class);
            if (Strings.isNullOrEmpty(activityProjectId)) {
                return;
            }
            String key = displayFormId + "_" + activityProjectId;

            if (proofDisplayImgMap.containsKey(key)) {
                log.error("举证陈列图片中存在重复的陈列形式、活动项目组合: {}", key);
                throw new ValidateException(I18N.text(I18NEnums.DISPLAY_PROOF_IMG_EXISTS_DUPLICATE_ITEM_DETAIL.getCode()));
            }
            proofDisplayImgMap.put(key, data);

            if (!detailMap.containsKey(key)) {
                log.error("举证陈列图片中的组合不匹配{}项目: {}", objectType, key);
                throw new ValidateException(String.format(I18N.text(I18NEnums.DISPLAY_PROOF_IMG_WITH_ITEM_DETAIL_NO_MATCH.getCode()), objectType));
            }
        });
    }

    /**
     * 查询举证时段配置
     */
    private List<TPMProofPeriodTime.ProofPeriod> queryTPMProofPeriodTime(String tenantId, String field, String activityAgreementId) {
        List<IObjectData> proofPeriodDetails = queryTPMProofTimePeriodDetailByObjectId(tenantId,
                field, activityAgreementId);

        if (CollectionUtils.isEmpty(proofPeriodDetails)) {
            return Lists.newArrayList();
        }

        // 按阶段排序
        proofPeriodDetails = proofPeriodDetails.stream()
                .sorted(Comparator.comparing(c -> c.get(TPMProofTimePeriodDetailFields.STAGE, Double.class)))
                .collect(Collectors.toList());

        // 转换为业务对象
        return proofPeriodDetails.stream()
                .map(detail -> JSONObject.parseObject(detail.toJsonString(),
                        TPMProofPeriodTime.ProofPeriod.class))
                .collect(Collectors.toList());
    }

    private static boolean isImageEmpty(IObjectData data) {
        return data.get(TPMActivityProofDisplayImgFields.IMAGE) == null || "".equals(data.get(TPMActivityProofDisplayImgFields.IMAGE));
    }

    /**
     * 根据访问ID和活动协议ID查询举证数据
     */
    private List<IObjectData> queryTPMActivityProofByVisitId(String tenantId, String visitId, String activityAgreementId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter visitIdFilter = new Filter();
        visitIdFilter.setFieldName(TPMActivityProofFields.VISIT_ID);
        visitIdFilter.setOperator(Operator.EQ);
        visitIdFilter.setFieldValues(Lists.newArrayList(visitId));

        Filter agreementFilter = new Filter();
        agreementFilter.setFieldName(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID);
        agreementFilter.setOperator(Operator.EQ);
        agreementFilter.setFieldValues(Lists.newArrayList(activityAgreementId));

        query.setFilters(Lists.newArrayList(visitIdFilter, agreementFilter));

        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_PROOF_OBJ, query, TPMActivityProofFields.ALL);
    }

    private List<IObjectData> queryTPMProofTimePeriodDetailByObjectId(String tenantId, String fieldName, String id) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter refrenceIdFilter = new Filter();
        refrenceIdFilter.setFieldName(fieldName);
        refrenceIdFilter.setOperator(Operator.EQ);
        refrenceIdFilter.setFieldValues(Lists.newArrayList(id));

        query.setFilters(Lists.newArrayList(refrenceIdFilter));

        List<IObjectData> proofPeriodDetails = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_PROOF_TIME_PERIOD_DETAIL_OBJ, query);
        if (CollectionUtils.isEmpty(proofPeriodDetails)) {
            return Lists.newArrayList();
        }
        return proofPeriodDetails;
    }

    /**
     * 根据协议ID查询协议明细数据
     *
     * @param tenantId            租户ID
     * @param activityAgreementId 活动协议ID
     * @return 协议明细列表
     */
    private List<IObjectData> queryTPMActivityAgreementDetailsByAgreementId(String tenantId, String activityAgreementId) {
        if (StringUtils.isBlank(activityAgreementId)) {
            log.error("协议ID为空，无法查询协议明细");
            return Lists.newArrayList();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter agreementIdFilter = new Filter();
        agreementIdFilter.setFieldName(TPMActivityAgreementDetailFields.ACTIVITY_AGREEMENT_ID);
        agreementIdFilter.setOperator(Operator.EQ);
        agreementIdFilter.setFieldValues(Lists.newArrayList(activityAgreementId));

        query.setFilters(Lists.newArrayList(agreementIdFilter));

        List<IObjectData> details = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_DETAIL_OBJ, query);
        if (CollectionUtils.isEmpty(details)) {
            log.warn("未找到协议 {} 的明细数据", activityAgreementId);
            return Lists.newArrayList();
        }
        return details;
    }

    private List<IObjectData> queryTPMActivityDetailByObjectId(String tenantId, String id) {

        if (StringUtils.isEmpty(id)) {
            return Lists.newArrayList();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter refrenceIdFilter = new Filter();
        refrenceIdFilter.setFieldName(TPMActivityDetailFields.ACTIVITY_ID);
        refrenceIdFilter.setOperator(Operator.EQ);
        refrenceIdFilter.setFieldValues(Lists.newArrayList(id));

        query.setFilters(Lists.newArrayList(refrenceIdFilter));

        List<IObjectData> details = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_DETAIL_OBJ, query);
        if (CollectionUtils.isEmpty(details)) {
            return Lists.newArrayList();
        }
        return details;
    }

}
