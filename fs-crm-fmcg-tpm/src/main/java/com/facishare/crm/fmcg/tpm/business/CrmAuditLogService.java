package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.tpm.business.abstraction.ICrmAuditLogService;
import com.facishare.crm.fmcg.tpm.business.dto.CrmAuditLogDTO;
import com.fxiaoke.log.AuditLog;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/12/8 上午10:55
 */
@Slf4j
@Component
public class CrmAuditLogService implements ICrmAuditLogService {

    private static final String APP_NAME = ConfigHelper.getProcessInfo().getName();
    private static final String SERVER_IP = ConfigHelper.getProcessInfo().getIp();
    private static final String PROFILE = ConfigHelper.getProcessInfo().getProfile();

    @Override
    public void sendLog(CrmAuditLogDTO log) {
        log.setAppName(APP_NAME);
        log.setServerIp(SERVER_IP);
        log.setProfile(PROFILE);
        log.setTraceId(TraceContext.get().getTraceId());
        try {
            // 发送到kafka的topic名称，默认为biz-audit-log，如果修改，需要配套修改消费kafka的程序，决定存入es的哪个索引
            BizLogClient.send("biz-audit-log", Pojo2Protobuf.toMessage(log, AuditLog.class).toByteArray());
        } catch (Exception ex) {
            CrmAuditLogService.log.error("biz-audit-log send log error", ex);
        }
    }
}
