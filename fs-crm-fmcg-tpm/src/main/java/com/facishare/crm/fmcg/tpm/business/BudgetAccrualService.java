package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccrualDetailFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccrualFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetAccrualService;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetAccrualRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetAccrualRuleNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetAccrualRulePO;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.web.condition.ConditionAdapter;
import com.facishare.paas.appframework.common.service.CRMNotificationService;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.metadata.FieldLayoutPojo;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.expression.SimpleExpression;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.github.autoconf.helper.ConfigHelper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/26 下午6:01
 */
//IgnoreI18nFile
@Slf4j
@Service
public class BudgetAccrualService implements IBudgetAccrualService {

    @Resource
    private ConditionAdapter conditionAdapter;

    @Resource
    private BudgetAccrualRuleDAO budgetAccrualRuleDAO;

    @Resource(name = "tpmOrganizationService")
    private OrganizationService organizationService;

    @Resource
    private BudgetTypeDAO budgetTypeDAO;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private ExpressionCalculateLogicService expressionCalculateLogicService;

    @Resource
    private BudgetAccountService budgetAccountService;

    @Resource
    private CRMNotificationService crmNotificationService;


    @Override
    public void triggerAccrualRule(String tenantId, String triggerAction, IObjectData triggerData, Map<String, Object> updateMaps) {
        User systemUser = User.systemUser(tenantId);
        try {
            updateMaps = updateMaps == null ? new HashMap<>() : updateMaps;
            //updateMaps.forEach(triggerData::set);
            BudgetAccrualRulePO accrualRulePO = queryAccrualRulePO(systemUser, triggerData);
            if (accrualRulePO == null) {
                log.info("no fit accrual rule");
                return;
            }

            if (!triggerAction.equals(accrualRulePO.getAccrualSourceNode().getTriggerTime())) {
                log.info("triggerAction not fit.ruleId:{},triggerTime:{}", accrualRulePO.getId(), accrualRulePO.getAccrualSourceNode().getTriggerTime());
                return;
            }

            if ("change".equals(accrualRulePO.getAccrualSourceNode().getTriggerTime()) && CollectionUtils.isNotEmpty(accrualRulePO.getAccrualSourceNode().getChangeField()) &&
                    accrualRulePO.getAccrualSourceNode().getChangeField().stream().noneMatch(updateMaps.keySet()::contains)) {
                log.info("edit but change fields not match.changeMap:{},ruleAllowFields:{}", updateMaps, accrualRulePO);
                return;
            }

            if (!conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(tenantId), -10000, triggerData.getDescribeApiName(), accrualRulePO.getAccrualSourceNode().getConditionCode(), ObjectDataExt.toMap(triggerData))) {
                log.info("edit not fit condition.");
                return;
            }

            //频率判断
            if (!Strings.isNullOrEmpty(accrualRulePO.getAccrualSourceNode().getFrequency()) && isOverAccrualFrequent(tenantId, triggerData.getDescribeApiName(), triggerData.getId(), accrualRulePO.getId().toString())) {
                log.info("is over frequent ");
                return;
            }

            BigDecimal baseAmount = calculateExpression(systemUser, accrualRulePO.getAccrualSourceNode().getAmountSource(), triggerData);
            IObjectData accrualMasterData = formBaseObjectDataByApiName(ApiNames.TPM_BUDGET_ACCRUAL_OBJ);
            accrualMasterData.set(TPMBudgetAccrualFields.BASE_AMOUNT, baseAmount);
            accrualMasterData.set(TPMBudgetAccrualFields.RULE_ID, accrualRulePO.getId().toString());
            accrualMasterData.set(TPMBudgetAccrualFields.RELATED_OBJECT_API_NAME, triggerData.getDescribeApiName());
            accrualMasterData.set(TPMBudgetAccrualFields.RELATED_OBJECT_DATA_ID, triggerData.getId());
            Map<String, List<ObjectDataDocument>> detailMaps = new HashMap<>();
            Set<String> budgetSet = new HashSet<>();
            if (CollectionUtils.isNotEmpty(accrualRulePO.getAccrualRuleNodes())) {
                List<ObjectDataDocument> details = new ArrayList<>();
                detailMaps.put(ApiNames.TPM_BUDGET_ACCRUAL_DETAIL_OBJ, details);
                BigDecimal percentSum = BigDecimal.ZERO;
                BigDecimal hundredPercent = new BigDecimal("100");
                BigDecimal leftAmount = baseAmount;
                for (BudgetAccrualRuleNodeEntity budgetAccrualRuleNodeEntity : accrualRulePO.getAccrualRuleNodes()) {
                    //judge is validate

                    if (!conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(tenantId), -10000, triggerData.getDescribeApiName(), budgetAccrualRuleNodeEntity.getConditionCode(), ObjectDataExt.toMap(triggerData))) {
                        log.info("this node can not fit condition:{}", budgetAccrualRuleNodeEntity);
                        continue;
                    }

                    IObjectData budget = budgetAccountService.getBudgetByFieldRelation(systemUser, budgetAccrualRuleNodeEntity.getBudgetType(), budgetAccrualRuleNodeEntity.getNodeId(), budgetAccrualRuleNodeEntity.getFieldRelation(), triggerData);

                    if (budget == null) {
                        throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_ACCRUAL_SERVICE_0), accrualRulePO.getName()));
                    }
                    if (budgetSet.contains(budget.getId())) {
                        throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_ACCRUAL_SERVICE_1), accrualRulePO.getName()));
                    }
                    budgetSet.add(budget.getId());

                    BigDecimal ratio = new BigDecimal(budgetAccrualRuleNodeEntity.getRatio());
                    percentSum = percentSum.add(ratio);
                    IObjectData detail = formBaseObjectDataByApiName(ApiNames.TPM_BUDGET_ACCRUAL_DETAIL_OBJ);
                    if (percentSum.compareTo(hundredPercent) == 0) {
                        detail.set(TPMBudgetAccrualDetailFields.ACTUAL_AMOUNT, leftAmount);
                    } else {
                        BigDecimal amount = baseAmount.multiply(ratio).divide(hundredPercent, 2, RoundingMode.DOWN);
                        leftAmount = leftAmount.subtract(amount);
                        detail.set(TPMBudgetAccrualDetailFields.ACTUAL_AMOUNT, amount);
                    }
                    detail.set(TPMBudgetAccrualDetailFields.STATUS, TPMBudgetAccrualDetailFields.Status.EXCLUDE);
                    detail.set(TPMBudgetAccrualDetailFields.PERCENTAGE, budgetAccrualRuleNodeEntity.getRatio());
                    detail.set(TPMBudgetAccrualDetailFields.BUDGET_ACCOUNT_ID, budget.getId());
                    detail.set(TPMBudgetAccrualDetailFields.BUDGET_TYPE_ID, budgetAccrualRuleNodeEntity.getBudgetType());
                    details.add(ObjectDataDocument.of(detail));
                }
                if (CollectionUtils.isNotEmpty(details)) {
                    updateAccrualStatus(systemUser, triggerData, "计提完成", false, null, null);
                    BaseObjectSaveAction.Arg accrualAddArg = new BaseObjectSaveAction.Arg();
                    accrualAddArg.setObjectData(ObjectDataDocument.of(accrualMasterData));
                    accrualAddArg.setDetails(detailMaps);
                    ActionContext actionContext = new ActionContext(RequestContext.builder().user(systemUser).tenantId(tenantId).build(), ApiNames.TPM_BUDGET_ACCRUAL_OBJ, "Add");
                    serviceFacade.triggerAction(actionContext, accrualAddArg, BaseObjectSaveAction.Result.class);
                }
            }
        } catch (Exception e) {
            List<String> crmUserIds = serviceFacade.getUsersByRole(systemUser, "00000000000000000000000000000006");
            updateAccrualStatus(systemUser, triggerData, String.format("%s的原因，计提单数据未生成", Strings.isNullOrEmpty(e.getMessage()) ? "由于未知异常" : e.getMessage()), true, crmUserIds.stream().map(Integer::parseInt).collect(Collectors.toSet()), null);
            log.info("trigger rule err.", e);
        }
    }

    public void updateAccrualStatus(User user, IObjectData triggerData, String message, boolean isSendCrmMessage, Set<Integer> receiverIds, IObjectData accrualData) {
        if (isSendCrmMessage) {
            String objectName = serviceFacade.findDescribeAndLayout(user, triggerData.getDescribeApiName(), false, null).getObjectDescribe().getDisplayName();
            String noticeMessageFormat = "「%s」「%s」计提异常，请及时查看";
            Map<String, String> urlParameter = new HashMap<>();

            urlParameter.put("objectApiName", triggerData.getDescribeApiName());
            urlParameter.put("objectId", triggerData.getId());
            NewCrmNotification newCrmNotification = NewCrmNotification.builder().senderId(initTraceId(user.getTenantId(), "TPM-Accrual", UUID.randomUUID().toString()))
                    .senderId("-10000").type(92).urlParameter(urlParameter).receiverIDs(receiverIds).title(String.format("「%s」计提异常", objectName)).fullContent(String.format(noticeMessageFormat, objectName, triggerData.getName())).build();
            crmNotificationService.sendNewCrmNotification(user, newCrmNotification);
        }
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put(TPMBudgetAccrualFields.TRIGGER_RULE_RESULT, message);
        try {
            serviceFacade.updateWithMap(user, triggerData, updateMap);
        } catch (Exception e) {
            log.warn("update field err.", e);
        }
    }

    @Override
    public void addTriggerRuleStatusField(String tenantId, String describeApiName) {
        User user = User.systemUser(tenantId);
        IObjectDescribe describe = serviceFacade.findDescribeAndLayout(user, describeApiName, false, null).getObjectDescribe();
        if (!describe.getFieldDescribeMap().containsKey(TPMBudgetAccrualFields.TRIGGER_RULE_RESULT)) {
            List<FieldLayoutPojo> layoutLIst = CommonUtils.cast(serviceFacade.findCustomFieldDescribe(tenantId, describeApiName, "owner").getLayout_list(), FieldLayoutPojo.class);
            layoutLIst.forEach(layout -> {
                layout.setReadonly(true);
                layout.setRequired(false);
                layout.setShow(true);
                layout.setRenderType("long_text");
            });
            String fieldJson = "{\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"is_unique\":false,\"description\":\"\",\"type\":\"long_text\",\"default_to_zero\":false,\"is_required\":false,\"is_extend\":true,\"define_type\":\"custom\",\"is_single\":false,\"max_length\":2000,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"min_length\":0,\"default_value\":\"\",\"label\":\"计提状态\",\"api_name\":\"trigger_rule_result__c\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\"}";
            serviceFacade.addDescribeCustomField(user, describeApiName, fieldJson, layoutLIst, null);
        }
    }


    @Override
    public boolean existsAccrualRuleDataByApiName(String tenantId, String apiName, String ruleId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");
        Filter bizTraceIdFilter = new Filter();
        bizTraceIdFilter.setFieldName(TPMBudgetAccrualFields.RELATED_OBJECT_API_NAME);
        bizTraceIdFilter.setOperator(Operator.EQ);
        bizTraceIdFilter.setFieldValues(Lists.newArrayList(apiName));

        Filter ruleIdFilter = new Filter();
        ruleIdFilter.setFieldName(TPMBudgetAccrualFields.RULE_ID);
        ruleIdFilter.setOperator(Operator.EQ);
        ruleIdFilter.setFieldValues(Lists.newArrayList(ruleId));

        query.setFilters(Lists.newArrayList(bizTraceIdFilter, ruleIdFilter));
        return !serviceFacade.findBySearchQueryWithFieldsIgnoreAll(User.systemUser(tenantId), ApiNames.TPM_BUDGET_ACCRUAL_OBJ, query, Lists.newArrayList("_id")).getData().isEmpty();
    }


    private IObjectData formBaseObjectDataByApiName(String apiName) {
        IObjectData data = new ObjectData();
        data.setDescribeApiName(apiName);
        data.setOwner(Lists.newArrayList("-10000"));

        data.setRecordType("default__c");
        return data;
    }

    private BigDecimal calculateExpression(User user, String expression, IObjectData data) {
        IObjectDescribe describe = serviceFacade.findDescribeAndLayout(user, data.getDescribeApiName(), false, null).getObjectDescribe();
        SimpleExpression simpleExpression = new SimpleExpression();
        simpleExpression.setId("jie_ge_niu_bi__c");
        simpleExpression.setDecimalPlaces(2);
        simpleExpression.setNullAsZero(true);
        simpleExpression.setReturnType("number");
        simpleExpression.setExpression(expression);
        expressionCalculateLogicService.bulkCalculateWithExpression(describe, Lists.newArrayList(data), Lists.newArrayList(simpleExpression));
        return data.get("jie_ge_niu_bi__c", BigDecimal.class);
    }

    private BudgetAccrualRulePO queryAccrualRulePO(User user, IObjectData objectData) {

        String recordType = objectData.getRecordType();
        if (ApiNames.TPM_ACTIVITY_OBJ.equals(objectData.getDescribeApiName()) ||
                ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ.equals(objectData.getDescribeApiName())) {
            recordType = objectData.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
        }

        return budgetAccrualRuleDAO.queryUniqueRuleByApiNameAndRecordType(user.getTenantId(), objectData.getDescribeApiName(), recordType);
    }

    private boolean isOverAccrualFrequent(String tenantId, String apiName, String objectId, String ruleId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(1);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);

        Filter ruleFilter = new Filter();
        ruleFilter.setFieldName(TPMBudgetAccrualFields.RULE_ID);
        ruleFilter.setOperator(Operator.EQ);
        ruleFilter.setFieldValues(Lists.newArrayList(ruleId));

        Filter relatedApiNameFilter = new Filter();
        relatedApiNameFilter.setFieldName(TPMBudgetAccrualFields.RELATED_OBJECT_API_NAME);
        relatedApiNameFilter.setOperator(Operator.EQ);
        relatedApiNameFilter.setFieldValues(Lists.newArrayList(apiName));

        Filter relatedObjectIdFilter = new Filter();
        relatedObjectIdFilter.setFieldName(TPMBudgetAccrualFields.RELATED_OBJECT_DATA_ID);
        relatedObjectIdFilter.setOperator(Operator.EQ);
        relatedObjectIdFilter.setFieldValues(Lists.newArrayList(objectId));

        query.setFilters(Lists.newArrayList(ruleFilter, relatedApiNameFilter, relatedObjectIdFilter));

        return !serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_BUDGET_ACCRUAL_OBJ, query).getData().isEmpty();
    }


    private String initTraceId(String tenantId, String op, String identity) {
        return String.format("%s/E-%s.%s.%s",
                ConfigHelper.getProcessInfo().getName(),
                tenantId,
                op,
                identity
        );
    }
}
