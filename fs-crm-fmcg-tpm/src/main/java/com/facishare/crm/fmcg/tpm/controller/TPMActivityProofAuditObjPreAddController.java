package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Sets;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.tpm.api.proof.AuditPreAdd;
import com.facishare.crm.fmcg.tpm.business.TPM2Service;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPM2Service;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofAuditSourceConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.dto.DescribeResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.IFormComponent;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/2/20 4:12 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityProofAuditObjPreAddController extends PreDefineController<AuditPreAdd.Arg, AuditPreAdd.Result> {

    private static final ITPM2Service tpm2Service = SpringUtil.getContext().getBean(TPM2Service.class);
    private boolean isTpm2Tenant = false;
    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);

    @SneakyThrows
    @Override
    protected AuditPreAdd.Result doService(AuditPreAdd.Arg arg) {
        if (!serviceFacade.funPrivilegeCheck(controllerContext.getUser(), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, "Add")) {
            throw new ValidateException(I18N.text(I18NKeys.DO_NOT_HAVE_PROOF_AUDIT_CREATE_RIGHT));
        }

        if (Strings.isNullOrEmpty(arg.getVisitId()) || Strings.isNullOrEmpty(arg.getActionId())) {
            throw new ValidateException("visit_id or action_id can not be empty.");
        }

        isTpm2Tenant = tpm2Service.isTPM2Tenant(Integer.valueOf(controllerContext.getTenantId()));


        return tpm2(arg);
    }


    private AuditPreAdd.Result tpm2(AuditPreAdd.Arg arg) throws MetadataServiceException {
        IObjectData storeData = null;
        IObjectData activityData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getActivityId(), ApiNames.TPM_ACTIVITY_OBJ);
        String activityType = activityData.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
        ActivityTypeExt activityTypeExt = activityTypeManager.find(controllerContext.getTenantId(), activityType);
        if(!Boolean.TRUE.equals(activityTypeExt.get().getForbidRelateCustomer())){
            storeData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getStoreId(), ApiNames.ACCOUNT_OBJ);
            if (storeData == null) {
                throw new ValidateException("store not found.");
            }
        }
        ActivityProofAuditSourceConfigEntity sourceConfigEntity = activityTypeExt.auditSourceConfig();
        String auditedMasterApiName = sourceConfigEntity.getMasterApiName();
        String auditedDetailApiName = sourceConfigEntity.getDetailApiName();
        String auditedMasterRecordType = sourceConfigEntity.getMasterRecordType();


        IObjectData auditedMasterData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getAuditedObjId(), auditedMasterApiName);
        if (auditedMasterData == null) {
            throw new ValidateException("can not find proof data.");
        }
        boolean agreementRequired = isTpm2Tenant ? activityTypeExt.agreementNode() != null : activityData.get(TPMActivityFields.IS_AGREEMENT_REQUIRED, Boolean.class);

        Map<String, List<JSONObject>> fieldDescribeMap = new HashMap<>();
        //fieldDescribeMap.put(auditedMasterApiName, getUsefulFieldDescribe(auditedMasterApiName, auditedMasterRecordType, new HashSet<>(sourceConfigEntity.getDisplayFieldApiNamesOfMaster())));
        //fieldDescribeMap.put(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, getUsefulFieldDescribe(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, activityTypeExt.auditNode().getObjectRecordType(), new HashSet<>(sourceConfigEntity.getDisplayFieldApiNamesOfAuditMaster())));
        if (!Strings.isNullOrEmpty(auditedDetailApiName)) {
            fieldDescribeMap.put(auditedDetailApiName, getUsefulFieldDescribe(auditedDetailApiName, auditedMasterRecordType, sourceConfigEntity.getDisplayFieldApiNamesOfDetail()));
        }
        fieldDescribeMap.put(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ, getUsefulFieldDescribe(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ, activityTypeExt.auditNode().getObjectRecordType(), sourceConfigEntity.getDisplayFieldApiNamesOfAuditDetail()));

        AuditPreAdd.Result result = new AuditPreAdd.Result();
        result.setFieldDescribeMap(fieldDescribeMap);

        JSONObject objectData = new JSONObject();
        objectData.put(TPMActivityProofAuditFields.VISIT_ID, arg.getVisitId());
        objectData.put(TPMActivityProofAuditFields.ACTION_ID, arg.getActionId());
        result.setObjectData(objectData);
        result.setDetails(new HashMap<>());
        result.setAuditedDetails(Lists.newArrayList());

        if (Strings.isNullOrEmpty(arg.getAuditId())) {
            if (agreementRequired) {
                String agreementId = auditedMasterData.get(sourceConfigEntity.getAgreementFieldApiName(), String.class);
                if (Strings.isNullOrEmpty(agreementId))
                    agreementId = ApiNames.TPM_ACTIVITY_PROOF_OBJ.equals(auditedMasterApiName) ? auditedMasterData.get(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID, String.class) : "";

                if (!Strings.isNullOrEmpty(agreementId)) {
                    IObjectData agreement = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), agreementId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
                    objectData.put(TPMActivityProofAuditFields.ACTIVITY_AGREEMENT_ID, agreementId);
                    objectData.put(TPMActivityProofAuditFields.ACTIVITY_AGREEMENT_ID + "__r", agreement.getName());
                }
            }
            if(storeData != null){
                objectData.put(TPMActivityProofAuditFields.STORE_ID, storeData.getId());
                objectData.put(TPMActivityProofAuditFields.STORE_ID + "__r", storeData.getName());
            }

            objectData.put(TPMActivityProofFields.ACTIVITY_ID, activityData.getId());
            objectData.put(TPMActivityProofFields.ACTIVITY_ID + "__r", activityData.getName());

            if (!Strings.isNullOrEmpty(sourceConfigEntity.getDealerFieldApiName())) {
                String dealerId = auditedMasterData.get(sourceConfigEntity.getDealerFieldApiName(), String.class);
                if (Strings.isNullOrEmpty(dealerId)) {
                    log.info("audited data :{} has no dealer.", auditedMasterData);
                } else {
                    objectData.put(TPMActivityProofAuditFields.DEALER_ID, dealerId);
                    IObjectData dealer = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), dealerId, ApiNames.ACCOUNT_OBJ);
                    objectData.put(TPMActivityProofAuditFields.DEALER_ID + "__r", dealer.getName());
                }
            }

            if (!Strings.isNullOrEmpty(sourceConfigEntity.getReferenceAuditSourceFieldApiName())) {
                objectData.put(sourceConfigEntity.getReferenceAuditSourceFieldApiName(), auditedMasterData.getId());
                objectData.put(sourceConfigEntity.getReferenceAuditSourceFieldApiName() + "__r", auditedMasterData.getName());
            }

            if (!Strings.isNullOrEmpty(sourceConfigEntity.getCostFieldApiName())) {
                objectData.put(TPMActivityProofAuditFields.TOTAL, auditedMasterData.get(sourceConfigEntity.getCostFieldApiName()));
            }

            if (!Strings.isNullOrEmpty(auditedDetailApiName)) {
                List<IObjectData> auditedDetails = getDetails(auditedDetailApiName, sourceConfigEntity.getMasterDetailFieldApiName(), auditedMasterData.getId());
                List<JSONObject> auditDetailJsonList = new ArrayList<>();
                List<JSONObject> auditedDetailJsonList = new ArrayList<>();

                auditedDetails.forEach(detail -> {
                    JSONObject auditDetail = new JSONObject();
                    auditDetail.put(TPMActivityProofAuditDetailFields.AUDIT_ITEM, detail.getName());
                    auditDetail.put(TPMActivityProofAuditDetailFields.ACTIVITY_PROOF_AUDIT_ID, auditedMasterData.getId());
                    if (ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ.equals(auditedDetailApiName)) {
                        auditDetail.put(TPMActivityProofAuditDetailFields.ACTIVITY_AGREEMENT_DETAIL_ID, detail.get(TPMActivityProofDetailFields.ACTIVITY_AGREEMENT_DETAIL_ID));
                        auditDetail.put(TPMActivityProofAuditDetailFields.ACTIVITY_ITEM_ID, detail.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID));
                        auditDetail.put(TPMActivityProofAuditDetailFields.ACTIVITY_DETAIL_ID, detail.get(TPMActivityProofDetailFields.ACTIVITY_DETAIL_ID));
                        auditDetail.put(TPMActivityProofAuditDetailFields.ACTIVITY_PROOF_DETAIL_ID, detail.getId());
                        auditDetail.put(TPMActivityProofAuditDetailFields.ACTIVITY_ITEM_COST_STANDARD_ID, detail.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_COST_STANDARD_ID));
                    }
                    auditedDetailJsonList.add(map2JsonObj(ObjectDataExt.toMap(detail)));
                    auditDetailJsonList.add(auditDetail);
                });
                IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(),ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ);
                serviceFacade.fillObjectDataWithRefObject(describe, auditDetailJsonList.stream().map(ObjectData::new).collect(Collectors.toList()), User.systemUser(controllerContext.getTenantId()));

                result.getDetails().put(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ, auditDetailJsonList);
                result.setAuditedDetails(auditedDetailJsonList);
            }

        } else {
            List<String> masterFieldApiNames = Lists.newArrayList(CommonFields.ID, TPMActivityProofAuditFields.ACTIVITY_ID, TPMActivityProofAuditFields.ACTION_ID, TPMActivityProofAuditFields.VISIT_ID, TPMActivityProofAuditFields.STORE_ID, TPMActivityProofAuditFields.DEALER_ID, TPMActivityProofAuditFields.ACTIVITY_AGREEMENT_ID, sourceConfigEntity.getReferenceAuditSourceFieldApiName(), TPMActivityProofAuditFields.TOTAL);
            IObjectData auditData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getAuditId(), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);
            result.setRealMasterData(map2JsonObj(ObjectDataExt.toMap(auditData)));
            IObjectDescribe auditDescribe = serviceFacade.findObject(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);
            serviceFacade.fillObjectDataWithRefObject(auditDescribe, Lists.newArrayList(auditData), User.systemUser(controllerContext.getTenantId()));
            ObjectDataExt.toMap(auditData).forEach((k, v) -> {
                if ((masterFieldApiNames.contains(k) || k.endsWith("__r")) && v != null) {
                    objectData.put(k, v);
                }
            });
            if (!Strings.isNullOrEmpty(sourceConfigEntity.getReferenceAuditSourceDetailFieldApiName())) {
                List<IObjectData> auditedDetails = getDetails(auditedDetailApiName, sourceConfigEntity.getMasterDetailFieldApiName(), auditedMasterData.getId());
                Map<String, IObjectData> auditDetailMap = getDetails(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ, TPMActivityProofAuditDetailFields.ACTIVITY_PROOF_AUDIT_ID, auditData.getId()).stream().collect(Collectors.toMap(v -> v.get(sourceConfigEntity.getReferenceAuditSourceDetailFieldApiName(), String.class), v -> v, (before, after) -> before));
                List<JSONObject> auditDetailJsonList = Lists.newArrayList();
                List<JSONObject> auditedDetailJsonList = Lists.newArrayList();
                auditedDetails.forEach(detail -> {
                    auditedDetailJsonList.add(map2JsonObj(ObjectDataExt.toMap(detail)));
                    auditDetailJsonList.add(map2JsonObj(ObjectDataExt.toMap(auditDetailMap.getOrDefault(detail.getId(), new ObjectData()))));
                });
                result.getDetails().put(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ, auditDetailJsonList);
                result.setAuditedDetails(auditedDetailJsonList);
            }
        }

        return result;
    }

    private JSONObject map2JsonObj(Map<String, Object> map) {
        JSONObject object = new JSONObject();
        map.forEach((k, v) -> {
            if (v != null)
                object.put(k, v);
        });
        return object;
    }

    private List<IObjectData> getDetails(String detailApiName, String masterFieldApiName, String masterId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        IFilter masterDetailFilter = new Filter();
        masterDetailFilter.setFieldName(masterFieldApiName);
        masterDetailFilter.setOperator(Operator.EQ);
        masterDetailFilter.setFieldValues(Lists.newArrayList(masterId));
        query.setFilters(Lists.newArrayList(masterDetailFilter));

        return CommonUtils.queryData(serviceFacade, User.systemUser(controllerContext.getTenantId()), detailApiName, query);
    }

    private List<JSONObject> getUsefulFieldDescribe(String describeApiName, String recordType, List<String> displayFieldApiNames) throws MetadataServiceException {
        DescribeResult describe = serviceFacade.findDescribeAndLayout(User.systemUser(controllerContext.getTenantId()), describeApiName, true, recordType);
        Set<String> layoutFieldApiNames = Sets.newHashSet();
        describe.getLayout().getComponents().stream().filter(iComponent -> iComponent instanceof IFormComponent).forEach(iComponent -> ((IFormComponent) iComponent).getFieldSections().forEach(iFieldSection -> iFieldSection.getFields().forEach(iFormField -> layoutFieldApiNames.add(iFormField.getFieldName()))));
        Map<String, IFieldDescribe> fieldMap = describe.getObjectDescribe().getFieldDescribeMap();
        List<JSONObject> dataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(displayFieldApiNames)) {
            displayFieldApiNames.forEach(apiName -> {
                if (layoutFieldApiNames.contains(apiName)) {
                    dataList.add(new JSONObject(FieldDescribeExt.of(fieldMap.get(apiName)).toMap()));
                }
            });
        }
        return dataList;
    }


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }
}
