package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.business.abstraction.IStoreCostService;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityStoreWriteOffSourceConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.dispatcher.ObjectDataProxy;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.ActionContextUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class StoreCostService implements IStoreCostService {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private ActivityTypeManager activityTypeManager;
    @Resource
    private RedissonClient redissonCmd;
    @Resource
    private ObjectDataProxy objectDataProxy;

    private static final String STORE_WRITE_OFF = "FMCG_STORE_WRITE_OFF_%s_%s_%s_%s";
    private static final long LOCK_WAIT = 5L;
    private static final long LOCK_LEASE = 10L;

    @Override
    public void addStoreWriteOffV2(String tenantId, String sourceApiName, IObjectData iObjectData) {
        //如果是元气的企业，跳过
        if (TPMGrayUtils.isYuanQi(tenantId)) {
            return;
        }

        String activityId = iObjectData.get("activity_id", String.class);
        if (StringUtils.isEmpty(activityId)) {
            return;
        }

        if (TPMGrayUtils.isSkipAddStoreWriteOffByActivity(tenantId)) {
            IObjectData objectData = serviceFacade.findObjectData(User.systemUser(tenantId), activityId, ApiNames.TPM_ACTIVITY_OBJ);
            String status = objectData.get("is_monthly__c", String.class);
            // field_8464z__c  1 : 按月 2: 按季(函数生成)
            if (!"1".equals(status)) {
                return;
            }
        }

        ActivityTypeExt activityType = activityTypeManager.findByActivityId(tenantId, activityId);
        String activityTypeId = activityType.get().getId().toString();
        log.info("activityType id : {}", activityTypeId);
        ActivityStoreWriteOffSourceConfigEntity configEntity = activityType.storeWriteOffSourceConfig();

        log.info("add store write off configEntity is {}", configEntity);
        if (configEntity == null || !sourceApiName.equals(configEntity.getApiName())) {
            return;
        }
        String apiName = configEntity.getApiName();
        String costFieldApiName = configEntity.getCostFieldApiName();
        //如果依据节点是活动检核
        boolean isProofAudit = ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ.equals(apiName);
        String storeWriteOffFieldApiName = isProofAudit ? TPMActivityProofAuditFields.STORE_WRITE_OFF_ID : configEntity.getReferenceStoreWriteOffFieldApiName();
        String dealerFieldApiName = isProofAudit ? TPMActivityProofAuditFields.DEALER_ID : configEntity.getDealerFieldApiName();
        String accountFieldApiName = isProofAudit ? TPMActivityProofAuditFields.STORE_ID : configEntity.getAccountFieldApiName();
        String agreementFieldApiName = isProofAudit ? TPMActivityProofAuditFields.ACTIVITY_AGREEMENT_ID : configEntity.getAgreementFieldApiName();

        String calculateType = configEntity.getCalculateType();
        String dataId = iObjectData.getId();
        String accountField = iObjectData.get(accountFieldApiName, String.class);
        String activityIdField = iObjectData.get(TPMStoreWriteOffFields.ACTIVITY_ID, String.class);

        String key = String.format(STORE_WRITE_OFF, tenantId, apiName, accountField, activityIdField);
        //redis 如果 未查到门店核销对象，就先给该线程加锁，防止并发，误查。
        //尝试加锁 等待时间5 秒。10秒后释放锁，返回false
        if (this.tryLock(key)) {
            try {
                //通过创建时间去获取 年月
                String years = getYears(iObjectData.get(TPMStoreWriteOffFields.CREATE_TIME, String.class));
                log.info("param years is {}", years);
                if (StringUtils.isEmpty(years)) {
                    return;
                }
                List<IObjectData> storeObjectData = getStoreWriteOffObjectData(tenantId, iObjectData, years, accountFieldApiName, agreementFieldApiName);
                //如果已存在核销
                if (CollectionUtils.isNotEmpty(storeObjectData)) {
                    //释放所
                    this.releaseLock(key);
                    //更新 根据 节点配置项，更新费用核销
                    updateStoreWriteOffData(tenantId, apiName, costFieldApiName, storeWriteOffFieldApiName, calculateType, dataId, storeObjectData);
                } else {
                    log.info("start create storeWriteOff data");
                    //创建 门店费用核销
                    IObjectData storeCostData = buildStoreCostData(tenantId, iObjectData, activityTypeId, apiName, costFieldApiName, dealerFieldApiName, accountFieldApiName, agreementFieldApiName, years);

                    log.info("save ObjectData is {}, tenantId {}", storeCostData, tenantId);
                    //IObjectData saveObjectData = serviceFacade.saveObjectData(User.systemUser(tenantId), storeCostData);

                    Map<String, Boolean> isCashingGoodsMap = Maps.newHashMap();
                    isCashingGoodsMap.put(BuryModule.CASH_TYPE.IS_CASHING_GOODS_KEY, false);

                    Map<String, List<IObjectData>> detailsData = getCostCashingDetails(tenantId, activityType, dataId, iObjectData.get(TPMActivityProofAuditFields.ACTIVITY_AGREEMENT_ID, String.class), isCashingGoodsMap);

                    Map<String, IObjectDescribe> describeMap = new HashMap<>();
                    if (!detailsData.isEmpty()) {
                        describeMap.put(ApiNames.TPM_STORE_WRITE_OFF_CASHING_PRODUCT_OBJ, serviceFacade.findObject(tenantId, ApiNames.TPM_STORE_WRITE_OFF_CASHING_PRODUCT_OBJ));
                    }
                    SaveMasterAndDetailData.Arg saveArg = SaveMasterAndDetailData.Arg.builder()
                            .masterObjectData(storeCostData)
                            .detailObjectData(detailsData)
                            .objectDescribes(describeMap)
                            .build();
                    SaveMasterAndDetailData.Result result = serviceFacade.saveMasterAndDetailData(User.systemUser(tenantId), saveArg);
                    IObjectData saveObjectData = result.getMasterObjectData();

                    // 获取当前依据的对象，并去更新 关联 门店费用核销的 字段
                    log.info("save success saveObjectData is {}", saveObjectData);
                    updateNoteObject(tenantId, dataId, saveObjectData.getId(), apiName, storeWriteOffFieldApiName);

                    //修改记录
                    IObjectDescribe describe = serviceFacade.findObjectIncludeDeleted(tenantId, ApiNames.TPM_STORE_WRITE_OFF_OBJ);
                    serviceFacade.log(User.systemUser(tenantId), EventType.ADD, ActionType.Add, describe, saveObjectData);
                    //埋点、
                    addAsyncTpmLog(tenantId, activityId, isCashingGoodsMap);
                }
            } catch (Exception ex) {
                log.error("storeWriteOff add cause unknown exception : ", ex);
            } finally {
                this.releaseLock(key);
            }
        }
    }

    @NotNull
    private IObjectData buildStoreCostData(String tenantId,
                                           IObjectData iObjectData,
                                           String activityTypeId,
                                           String apiName,
                                           String costFieldApiName,
                                           String dealerFieldApiName,
                                           String accountFieldApiName,
                                           String agreementFieldApiName,
                                           String years) {
        IObjectData storeCostData = new ObjectData();
        storeCostData.setDescribeApiName(ApiNames.TPM_STORE_WRITE_OFF_OBJ);
        storeCostData.setTenantId(tenantId);
        storeCostData.setOwner(Lists.newArrayList("-10000"));
        storeCostData.set(TPMStoreWriteOffFields.ACTIVITY_ID, iObjectData.get(TPMStoreWriteOffFields.ACTIVITY_ID));

        //活动类型字段 取之 活动方案的 活动类型字段
        storeCostData.set(TPMStoreWriteOffFields.ACTIVITY_TYPE, activityTypeId);

        //依据字段
        storeCostData.set(TPMStoreWriteOffFields.ACTIVITY_ASSOCIATION, iObjectData.get(agreementFieldApiName));
        storeCostData.set(TPMStoreWriteOffFields.DEALER_ID, iObjectData.get(dealerFieldApiName));
        storeCostData.set(TPMStoreWriteOffFields.STORE, iObjectData.get(accountFieldApiName));
        //核销费用 字段
        storeCostData.set(TPMStoreWriteOffFields.CONFIRMED_AMOUNT, "");
        storeCostData.set(TPMStoreWriteOffFields.YEARS, years);
        storeCostData.set(TPMStoreWriteOffFields.CREATE_TIME, System.currentTimeMillis());
        storeCostData.set(TPMStoreWriteOffFields.STORE_STANDARD, true);
        storeCostData.set(TPMStoreWriteOffFields.WRITE_OFF_STATUS, TPMStoreWriteOffFields.WriteOffStatus.AUTOMATIC_WRITE_OFF);
        storeCostData.set(TPMStoreWriteOffFields.WRITE_OFF_VALUE, TPMStoreWriteOffFields.WriteOffValue.AGREE);
        storeCostData.set(TPMStoreWriteOffFields.WRITE_OFF_OWNER, Lists.newArrayList("-10000"));
        //如果检核对象是不合格的，取0
        Object amount = iObjectData.get(costFieldApiName);
        if (ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ.equals(apiName)
                && !"pass".equals(iObjectData.get(TPMActivityProofAuditFields.AUDIT_STATUS))) {
            amount = 0;
        }
        storeCostData.set(TPMStoreWriteOffFields.AUDITED_AMOUNT, amount);
        storeCostData.set(TPMStoreWriteOffFields.REF_AUDIT_COST, amount);
        storeCostData.set(TPMStoreWriteOffFields.ENTER_INTO_ACCOUNT, false);

        //系统字段
        storeCostData.set(TPMStoreWriteOffFields.OWNER, iObjectData.getOwner());
        if (CollectionUtils.isNotEmpty(iObjectData.getDataOwnDepartment())) {
            storeCostData.set(TPMStoreWriteOffFields.OWNER_DEPARTMENT, iObjectData.getDataOwnDepartment().get(0));
        }

        if (ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ.equals(apiName)) {
            // field_u01Eo__c  是否发放返利券 蒙牛企业自定义字段
            if ("pass".equals(iObjectData.get(TPMActivityProofAuditFields.AUDIT_STATUS))) {
                storeCostData.set("field_u01Eo__c", "1");
            } else {
                storeCostData.set("field_u01Eo__c", "2");
            }
        }

        storeCostData.set(TPMStoreWriteOffFields.RECORD_TYPE, "default__c");
        return storeCostData;
    }

    private void updateStoreWriteOffData(String tenantId, String apiName, String costFieldApiName, String storeWriteOffFieldApiName, String calculateType, String dataId, List<IObjectData> storeObjectData) {
        IObjectData objectData = storeObjectData.get(0);
        // 获取当前依据的对象，并去更新 关联 门店费用核销的 字段
        updateNoteObject(tenantId, dataId, objectData.getId(), apiName, storeWriteOffFieldApiName);

        BigDecimal amount;
        //更新 根据 节点配置项，更新费用核销
        switch (calculateType) {
            case "store_cost_total":
                amount = calculateStoreAmount(tenantId, objectData.getId(), apiName, costFieldApiName, storeWriteOffFieldApiName, "sum");
                break;
            case "store_cost_average":
                amount = calculateStoreAmount(tenantId, objectData.getId(), apiName, costFieldApiName, storeWriteOffFieldApiName, "avg");
                break;
            default:
                amount = new BigDecimal("0");
                break;
        }
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put(TPMStoreWriteOffFields.AUDITED_AMOUNT, amount);
        updateMap.put(TPMStoreWriteOffFields.REF_AUDIT_COST, amount);

        serviceFacade.updateWithMap(User.systemUser(tenantId), objectData, updateMap);
    }

    private boolean tryLock(String key) {

        RLock lock = redissonCmd.getLock(key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            return true;
        }
        try {
            return lock.tryLock(LOCK_WAIT, LOCK_LEASE, TimeUnit.SECONDS);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new MetaDataBusinessException(String.format("storeCost try lock cause thread interrupted exception : %s", key));
        }
    }

    private void addAsyncTpmLog(String tenantId, String activityId, Map<String, Boolean> isCashingGoodsMap) {
        try {

            IObjectData activityObjData = objectDataProxy.findById(activityId, tenantId, ApiNames.TPM_ACTIVITY_OBJ);
            if (Objects.nonNull(activityObjData) && Boolean.FALSE.equals(isCashingGoodsMap.get(BuryModule.CASH_TYPE.IS_CASHING_GOODS_KEY))) {
                if (activityObjData.containsField(TPMActivityFields.STORE_CASHING_TYPE)
                        && BuryModule.CASH_TYPE.GOODS.equals(activityObjData.get(TPMActivityFields.STORE_CASHING_TYPE, String.class))) {
                    isCashingGoodsMap.put(BuryModule.CASH_TYPE.IS_CASHING_GOODS_KEY, true);
                }
            }

            String subModule = BuryModule.TPM.TPM_STORE_WRITE_OFF + "_" + BuryModule.CASH_TYPE.CASH;
            if (Boolean.TRUE.equals(isCashingGoodsMap.get(BuryModule.CASH_TYPE.IS_CASHING_GOODS_KEY))) {
                subModule = BuryModule.TPM.TPM_STORE_WRITE_OFF + "_" + BuryModule.CASH_TYPE.GOODS;
            }
            BuryService.asyncTpmLog(Integer.valueOf(tenantId), Integer.valueOf(User.systemUser(tenantId).getUserId()), subModule, BuryOperation.CREATE, true);
        } catch (MetadataServiceException exception) {
            log.warn("门店费用核销埋点异常 tenantId:{}", tenantId, exception);
        }
    }

    @NotNull
    private Map<String, List<IObjectData>> getCostCashingDetails(String tenantId, ActivityTypeExt activityType, String dataId, String activityAgreementId, Map<String, Boolean> isCashingGoodsMap) {
        Map<String, List<IObjectData>> detailsData = new HashMap<>();

        if (Objects.isNull(activityType.agreementNode()) || Strings.isEmpty(activityAgreementId)) {
            return detailsData;
        }

        IObjectData agreementObj = serviceFacade.findObjectData(User.systemUser(tenantId), activityAgreementId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        if (Objects.isNull(agreementObj) || !TPMActivityCashingProductFields.GOODS.equals(agreementObj.get(TPMActivityAgreementFields.AGREEMENT_CASHING_TYPE, String.class))) {
            return detailsData;
        }
        isCashingGoodsMap.put(BuryModule.CASH_TYPE.IS_CASHING_GOODS_KEY, true);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(2000);
        query.setOffset(0);
        query.setNeedReturnCountNum(true);
        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityAgreementCashingProductFields.ACTIVITY_AGREEMENT_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityAgreementId));

        query.setFilters(Lists.newArrayList(activityFilter));
        QueryResult<IObjectData> bySearchQuery = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_AGREEMENT_CASHING_PRODUCT_OBJ, query);
        if (bySearchQuery.getTotalNumber() == 0) {
            return detailsData;
        }
        List<IObjectData> sourceCashingProductDetailsData = bySearchQuery.getData();

        List<String> productIds = sourceCashingProductDetailsData.stream().map(cash -> cash.get(TPMActivityAgreementCashingProductFields.PRODUCT_ID, String.class)).distinct().collect(Collectors.toList());
        Map<String, IObjectData> productIdPriceMap = getProductIdPriceMap(tenantId, productIds);

        List<IObjectData> cashingProductDetails = Lists.newArrayList();
        for (IObjectData sourceCashingProduct : sourceCashingProductDetailsData) {
            String productId = sourceCashingProduct.get(TPMActivityAgreementCashingProductFields.PRODUCT_ID, String.class);
            IObjectData cashingProductDetail = new ObjectData();
            cashingProductDetail.set(CommonFields.OWNER, sourceCashingProduct.getOwner());
            cashingProductDetail.set(CommonFields.DATA_OWN_DEPARTMENT, sourceCashingProduct.getDataOwnDepartment());
            cashingProductDetail.set(TPMStoreWriteOffCashingProductFields.QUANTITY, sourceCashingProduct.get(TPMActivityAgreementCashingProductFields.QUANTITY));
            cashingProductDetail.set(TPMStoreWriteOffCashingProductFields.AGREEMENT_CASHING_PRODUCT_ID, sourceCashingProduct.getId());
            cashingProductDetail.set(TPMStoreWriteOffCashingProductFields.STORE_WRITE_OFF, dataId);

            BigDecimal agreementPrice = sourceCashingProduct.get(TPMActivityAgreementCashingProductFields.PRICE, BigDecimal.class, BigDecimal.ZERO);
            cashingProductDetail.set(TPMStoreWriteOffCashingProductFields.AGREEMENT_PRICE, agreementPrice);

            BigDecimal price = productIdPriceMap.get(productId).get("price", BigDecimal.class);
            String unit = productIdPriceMap.get(productId).get("unit", String.class);
            cashingProductDetail.set(TPMStoreWriteOffCashingProductFields.PRICE, price);
            cashingProductDetail.set(TPMStoreWriteOffCashingProductFields.UNIT, unit);
            cashingProductDetail.setTenantId(tenantId);
            cashingProductDetail.setOwner(Lists.newArrayList("-10000"));
            cashingProductDetail.setDescribeApiName(ApiNames.TPM_STORE_WRITE_OFF_CASHING_PRODUCT_OBJ);
            cashingProductDetail.setRecordType("default__c");
            cashingProductDetails.add(cashingProductDetail);
        }
        if (CollectionUtils.isEmpty(cashingProductDetails)) {
            return Maps.newHashMap();
        }

        detailsData.put(ApiNames.TPM_STORE_WRITE_OFF_CASHING_PRODUCT_OBJ, cashingProductDetails);
        return detailsData;
    }

    @NotNull
    private Map<String, IObjectData> getProductIdPriceMap(String tenantId, List<String> productIds) {

        SearchTemplateQuery queryProduct = new SearchTemplateQuery();
        queryProduct.setLimit(2000);
        queryProduct.setOffset(0);
        queryProduct.setNeedReturnCountNum(true);
        queryProduct.setSearchSource("db");

        Filter productIdFilter = new Filter();
        productIdFilter.setFieldName(CommonFields.ID);
        productIdFilter.setOperator(Operator.IN);
        productIdFilter.setFieldValues(Lists.newArrayList(productIds));

        queryProduct.setFilters(Lists.newArrayList(productIdFilter));

        IActionContext actionContext = ActionContextUtil.getNewContext(tenantId);
        actionContext.setUserId(User.systemUser(tenantId).getUserId());
        actionContext.setPrivilegeCheck(false);

        List<IObjectData> products = serviceFacade.findBySearchTemplateQueryWithFields(actionContext,
                ApiNames.PRODUCT_OBJ,
                queryProduct,
                Lists.newArrayList(CommonFields.ID, "price", "unit")
        ).getData();

        return products.stream().collect(Collectors.toMap(IObjectData::getId, o -> o));
    }

    private void releaseLock(String key) {

        RLock lock = redissonCmd.getLock(key);
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    private String getYears(String createTime) {
        String parseTime = "";
        try {
            //根据创建时间获取 年月
            Calendar calendar = Calendar.getInstance();
            long time = Long.parseLong(createTime);
            calendar.setTime(new Date(time));
            calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), 1, 0, 0, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date date = calendar.getTime();
            parseTime = String.valueOf(date.getTime());
        } catch (Exception ex) {
            log.error("createTime format error : ", ex);
        }
        return parseTime;
    }

    private void updateNoteObject(String tenantId, String dataId, String objectId, String apiName, String storeWriteOffFieldApiName) {
        try {
            IObjectData data = serviceFacade.findObjectData(User.systemUser(tenantId), dataId, apiName);
            Map<String, Object> map = new HashMap<>();
            map.put(storeWriteOffFieldApiName, objectId);
            serviceFacade.updateWithMap(User.systemUser(tenantId), data, map);
            log.info("update apiName is {}, updateField is {}, field value is {}", apiName, storeWriteOffFieldApiName, objectId);
        } catch (Exception e) {
            log.error(String.format("update %s ref storeWriteOff field %s error", apiName, storeWriteOffFieldApiName), e);
        }
    }

    private List<IObjectData> getStoreWriteOffObjectData(String tenantId,
                                                         IObjectData dataDocument,
                                                         String years,
                                                         String accountFieldApiName,
                                                         String agreementFieldApiName) {

        String accountField = dataDocument.get(accountFieldApiName, String.class);
        String activityId = dataDocument.get(TPMStoreWriteOffFields.ACTIVITY_ID, String.class);
        log.info("accountField = {}, activityId = {}, years = {}", accountField, activityId, years);
        //根据 以「门店」 + 「活动」 +「协议」 +「年月」为唯一属性，查询是否已有。

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);

        Filter yearsFilter = new Filter();
        yearsFilter.setFieldName(TPMStoreWriteOffFields.YEARS);
        yearsFilter.setOperator(Operator.EQ);
        yearsFilter.setFieldValues(Lists.newArrayList(years));

        Filter storeFilter = new Filter();
        storeFilter.setFieldName(TPMStoreWriteOffFields.STORE);
        storeFilter.setOperator(Operator.EQ);
        storeFilter.setFieldValues(Lists.newArrayList(accountField));

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMStoreWriteOffFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));

        List<IFilter> filterList = Lists.newArrayList(yearsFilter, storeFilter, activityFilter);
        //活动协议
        String agreementField = dataDocument.get(agreementFieldApiName, String.class);
        log.info("agreementField = {}", agreementField);
        if (StringUtils.isNotEmpty(agreementField)) {
            Filter associationFilter = new Filter();
            associationFilter.setFieldName(TPMStoreWriteOffFields.ACTIVITY_ASSOCIATION);
            associationFilter.setOperator(Operator.EQ);
            associationFilter.setFieldValues(Lists.newArrayList(agreementField));
            filterList.add(associationFilter);
        }
        query.setFilters(filterList);
        return serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_STORE_WRITE_OFF_OBJ, query).getData();
    }

    private BigDecimal calculateStoreAmount(String tenantId,
                                            String objectId,
                                            String apiName,
                                            String costFieldApiName,
                                            String storeWriteOffFieldApiName,
                                            String type) {
        //求和
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setSearchSource("db");
        query.setLimit(200);
        query.setOffset(0);

        //关联门店费用核销的 id
        Filter storeFilter = new Filter();
        storeFilter.setFieldName(storeWriteOffFieldApiName);
        storeFilter.setOperator(Operator.EQ);
        storeFilter.setFieldValues(Lists.newArrayList(objectId));
        query.setFilters(Lists.newArrayList(storeFilter));

        //如果依据对象是 活动举证检核的，，条件上加 检核结果 = pass 通过
        if (ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ.equals(apiName)) {
            Filter statusFilter = new Filter();
            statusFilter.setFieldName(TPMActivityProofAuditFields.AUDIT_STATUS);
            statusFilter.setOperator(Operator.EQ);
            statusFilter.setFieldValues(Lists.newArrayList("pass"));
            query.getFilters().add(statusFilter);
        }

        List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                User.systemUser(tenantId),
                query,
                apiName,
                Lists.newArrayList(storeWriteOffFieldApiName),
                type,
                costFieldApiName);

        if (CollectionUtils.isNotEmpty(data)) {
            String key = String.format("%s_%s", type, costFieldApiName);
            BigDecimal amount = data.get(0).get(key, BigDecimal.class);
            log.info("aggregate search date size is {}, amount is {}", data.size(), amount);
            return Objects.nonNull(amount) ? amount.setScale(2, RoundingMode.HALF_UP) : new BigDecimal("0");
        } else {
            return new BigDecimal("0");
        }
    }


}
