package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;

/**
 * <AUTHOR>
 * @date 2022/8/30 下午2:26
 */
public class TPMBudgetAccountObjListController extends StandardListController {

    @Override
    protected Result after(Arg arg, Result result) {
        Result finalResult = super.after(arg, result);
        removeTakeApartButton(arg, result);
        return finalResult;
    }


    private void removeTakeApartButton(Arg arg, Result result) {
        if (arg.isIncludeButtonInfo()) {
            if (result.getButtonInfo().getButtonMap() != null) {
                for (ObjectDataDocument obj : result.getDataList()) {
                    if (result.getButtonInfo().getButtonMap().containsKey(obj.getId())) {
                        result.getButtonInfo().getButtonMap().get(obj.getId()).remove(ObjectAction.BUDGET_TAKE_APART.getButtonApiName());
                    }
                }
            }
        }
    }

}
