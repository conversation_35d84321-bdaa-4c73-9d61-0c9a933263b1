package com.facishare.crm.fmcg.tpm.business.enums;

public enum MainType {

    UNFREEZE("unfreeze", "解冻"),
    INCOME("income", "收入"),
    EXPENDITURE("expenditure", "支出"),
    FREEZE("freeze", "冻结");

    MainType(String value, String label) {
        this.label = label;
        this.value = value;
    }

    private final String label;
    private final String value;

    public String value() {
        return this.value;
    }

    public String label() {
        return this.label;
    }
}
