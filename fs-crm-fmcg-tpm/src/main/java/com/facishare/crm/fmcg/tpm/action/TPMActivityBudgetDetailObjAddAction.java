package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;

/**
 * <AUTHOR>
 * @date 2021/8/25 下午5:38
 */
public class TPMActivityBudgetDetailObjAddAction extends StandardAddAction {

    @Override
    protected void before(Arg arg) {
        if (actionContext.isFromFunction() || actionContext.isFromOpenAPI()) {
            throw new ValidateException(I18N.text(I18NKeys.THE_REQUEST_FROM_OUTER_CAN_NOT_CREATE_BUDGET_DETAIL));
        }
        super.before(arg);
    }
}
