package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountFields;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetAccountDetailService;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.utils.FormatUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/30 下午5:39
 */
public class TPMBudgetAccountObjBulkInvalidAction extends StandardBulkInvalidAction {

    private static final IBudgetAccountDetailService budgetAccountDetailService = SpringUtil.getContext().getBean(IBudgetAccountDetailService.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        bulkValidateBudgetStatus();
    }

    private void bulkValidateBudgetStatus() {
        List<String> enableList = Lists.newArrayList();
        List<String> disableList = Lists.newArrayList();
        for (IObjectData budget : this.dataList) {
            String budgetStatus = budget.get(TPMBudgetAccountFields.BUDGET_STATUS, String.class);
            if (TPMBudgetAccountFields.BUDGET_STATUS__ENABLE.equals(budgetStatus)) {
                enableList.add(budget.getName());
            } else {
                if (budgetAccountDetailService.existBudgetDetail(actionContext.getTenantId(), budget.getId())) {
                    disableList.add(budget.getName());
                }
            }

        }
        if (!enableList.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_OBJ_BULK_INVALID_ACTION_0) + FormatUtil.join(disableList, ","));
        }

        if (!disableList.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_OBJ_BULK_INVALID_ACTION_1) + FormatUtil.join(disableList, ","));
        }

    }

    @Override
    protected Result after(Arg arg, Result result) {

        Result finalRes = super.after(arg, result);
        Set<String> ids = finalRes.getObjectDataList().stream().map(ObjectDataDocument::getId).collect(Collectors.toSet());
        result.getFailureObjectDataList().forEach(v -> ids.remove(v.getId()));
        ids.forEach(v -> BuryService.asyncBudgetLog(actionContext.getTenantId(), actionContext.getUser().getUserIdInt(), BuryModule.Budget.BUDGET_ACCOUNT, BuryOperation.DELETE));
        return finalRes;
    }
}
