package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.tpm.business.abstraction.IDBRouterService;
import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.facishare.paas.pod.exception.DbRouterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class DBRouterService implements IDBRouterService {

    @Resource
    private DbRouterClient dbRouterClient;

    @Override
    public boolean existsPGDBRouter(String tenantId) {
        try {
            RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, "CRM", "fs-metadata-service", "pg");
            if (routerInfo == null) {
                return false;
            }
        } catch (DbRouterException e) {
            return false;
        } catch (Exception e) {
            log.info("query db route. e:", e);
            return false;
        }
        return true;
    }
}