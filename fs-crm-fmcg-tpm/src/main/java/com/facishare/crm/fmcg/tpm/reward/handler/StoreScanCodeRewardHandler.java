package com.facishare.crm.fmcg.tpm.reward.handler;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.adapter.exception.RewardFmcgException;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.constant.ScanCodeActionConstants;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.api.scan.StoreReward;
import com.facishare.crm.fmcg.tpm.business.RedPacketService;
import com.facishare.crm.fmcg.tpm.business.dto.GetRewardDetailDTO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityRewardRulePO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RewardDetailEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RewardMethodEnum;
import com.facishare.crm.fmcg.tpm.reward.abstraction.ActivityRewardBase;
import com.facishare.crm.fmcg.tpm.reward.abstraction.ActivityRewardHandler;
import com.facishare.crm.fmcg.tpm.reward.dto.RedPacketInformation;
import com.facishare.crm.fmcg.tpm.reward.dto.SnInformation;
import com.facishare.crm.fmcg.tpm.reward.service.AdvancedRewardLimitService;
import com.facishare.crm.fmcg.tpm.utils.lock.DistributedLock;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;


@Slf4j
@Component
public class StoreScanCodeRewardHandler extends ActivityRewardBase implements ActivityRewardHandler<StoreReward.Arg, StoreReward.Result> {

    private static final String SN_LOCK_PREFIX = "store_scan_code:sn:";

    private static final String ACTIVITY_LOCK_PREFIX = "store_scan_code:activity:";

    @Resource
    private RedPacketService redPacketService;

    @Resource
    private DistributedLock distributedLock;

    @Resource
    private AdvancedRewardLimitService advancedRewardLimitService;

    @Override
    public StoreReward.Result handle(StoreReward.Arg arg) {
        log.info("store scan code reward handler, arg:{}", arg);
        String codeSplit[] = arg.getCode().split("#");
        String snId = codeSplit[0];
        String storeId = codeSplit[1];
        String activityId = codeSplit[2];
        String upperTenantId = redPacketService.getTopTenantId(arg.getTenantCode(), arg.getEnvironment());

        IObjectData snObj = serviceFacade.findObjectData(User.systemUser(upperTenantId), snId, ApiNames.FMCG_SERIAL_NUMBER_OBJ);
        log.info("start dealer black filter");
        if (fmcgSerialNumberService.filterSerialNumberByActivityBlackList(upperTenantId, Lists.newArrayList(snObj), activityId).contains(snId)) {
            log.info("黑名单不进行后续激励。tenantId:{},activityId:{},snId:{}", upperTenantId, activityId, snId);
            throw new RewardFmcgException("90014", "黑名单");
        }
        IObjectData skuObj = fmcgSerialNumberService.getProductObjFromSerialNumberObj(upperTenantId, snObj.get("product_id", String.class));
        RLock lock = tryLock(SN_LOCK_PREFIX + snObj.getId());
        try {
            GetRewardDetailDTO rewardDetail = reward(upperTenantId, arg.getTenantId(), storeId, activityId, snId, arg.getOuterUserId(), arg.getOuterTenantId());
            RedPacketInformation redPacketInformation = RedPacketInformation.builder().amount(rewardDetail.getRedPacket().get(RedPacketRecordObjFields.REWARD_AMOUNT, BigDecimal.class)).build();
            SnInformation snInformation = SnInformation.builder().productName(skuObj.getName()).rewardMethod(RewardMethodEnum.RED_PACKET.code()).build();
            return StoreReward.Result.builder().snInformation(snInformation).redPacketInformation(redPacketInformation).build();
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private GetRewardDetailDTO reward(String upperTenantId, String tenantId, String storeId, String activityId, String snId, String outerUserId, String outerTenantId) {
        ActivityRewardRulePO activityRewardRulePO = activityRewardRuleDAO.getByRelatedObject(upperTenantId, ApiNames.TPM_ACTIVITY_OBJ, activityId);
        String businessId = String.format("store_scan_code:%s", snId);
        if (hasRewarded(upperTenantId, businessId)) {
            throw new RewardFmcgException("90013", "已被领取");
        }
        RewardDetailEntity rewardDetail = activityRewardRulePO.getRewardDetails().get(activityRewardRulePO.getRewardDetails().size() - 1);
        IObjectData storeObj = serviceFacade.findObjectData(User.systemUser(upperTenantId), storeId, ApiNames.ACCOUNT_OBJ);
        IObjectData serialNumber = serviceFacade.findObjectData(User.systemUser(upperTenantId), snId, ApiNames.FMCG_SERIAL_NUMBER_OBJ);
        String productId = serialNumber.get(FMCGSerialNumberFields.PRODUCT_ID, String.class);
        IObjectData scanRecordObj = formScanRecordObj(tenantId, storeId, snId, outerUserId);
        IObjectData virtualSnStatus = formVirtualSnStatus(upperTenantId, tenantId, snId, storeId, outerUserId, scanRecordObj);

        GetRewardDetailDTO storeRewardDetail = getRewardDetail(upperTenantId, activityId, businessId, rewardDetail, virtualSnStatus);
        List<GetRewardDetailDTO> detailDTOS = getRewardDetails(upperTenantId, activityId, snId, businessId, storeId);

        List<IObjectData> rewardDetails = new ArrayList<>();
        Map<IObjectData, List<IObjectData>> redPacketDetailMap = new HashMap<>();
        Map<String, List<IObjectData>> othersMap = new HashMap<>();
        othersMap.put(scanRecordObj.getDescribeApiName(), Lists.newArrayList(scanRecordObj));
        if (storeRewardDetail != null) {
            detailDTOS.add(storeRewardDetail);
            Optional.ofNullable(storeRewardDetail.getActivityRewardDetails()).ifPresent(details -> details.forEach(detail -> detail.set(TPMActivityRewardDetailFields.ACCOUNT_ID__C, storeId)));
        }
        detailDTOS.forEach(detail -> {
            if (CollectionUtils.isNotEmpty(detail.getActivityRewardDetails())) {
                rewardDetails.addAll(detail.getActivityRewardDetails());
            }
            if (Objects.nonNull(detail.getRedPacket())) {
                redPacketDetailMap.put(detail.getRedPacket(), detail.getRedPacketDetails());
            }
        });
        distributedLock.executeByLock(ACTIVITY_LOCK_PREFIX + activityId, () -> {
            if (advancedRewardLimitService.overlimit(upperTenantId, activityId, storeObj.get(AccountFields.MENGNIU_STORE_TYPE, String.class), storeId, productId)) {
                log.info("advanced reward limit over.");
                throw new RewardFmcgException("90014", "受激励次数限制不发放。");
            }
            validateActivityAmount(upperTenantId, activityId, redPacketDetailMap.keySet());
            saveRewardData(upperTenantId, businessId, rewardDetails, redPacketDetailMap, null, othersMap, null);
        });
        return storeRewardDetail;
    }

    private boolean hasRewarded(String upperTenantId, String businessId) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.BUSINESS_ID, Operator.EQ, Lists.newArrayList(businessId)),
                SearchQueryUtil.filter(CommonFields.IS_DELETED, Operator.EQ, Lists.newArrayList("0"))
        ));

        int count = serviceFacade.countObjectDataFromDB(upperTenantId, ApiNames.TPM_ACTIVITY_REWARD_DETAIL_OBJ, query);
        return count > 0;
    }

    private IObjectData formScanRecordObj(String tenantId, String storeId, String snId, String outerUserId) {
        IObjectData scanRecordObj = new ObjectData();
        scanRecordObj.setDescribeApiName("scan_openning_box_code_record__c");
        scanRecordObj.setOwner(Lists.newArrayList("-10000"));
        scanRecordObj.setTenantId(tenantId);
        scanRecordObj.set("account_id__c", storeId);
        scanRecordObj.set("sn_id__c", snId);
        scanRecordObj.set("scanner__c", Lists.newArrayList(outerUserId));
        IObjectData scannerObj = serviceFacade.findObjectData(User.systemUser(tenantId), outerUserId, ApiNames.PUBLIC_EMPLOYEE_OBJ);
        scanRecordObj.set("scanner_name__c", scannerObj.getName());
        scanRecordObj.setId(IdGenerator.get());
        scanRecordObj.setName("scan-" + scanRecordObj.getId());
        return scanRecordObj;
    }


    private IObjectData formVirtualSnStatus(String upperTenantId, String tenantId, String snId, String storeId, String outerUserId, IObjectData relatedObj) {
        IObjectData serialNumberStatus = new ObjectData();
        serialNumberStatus.set(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID, snId);
        serialNumberStatus.set(FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, tenantId);
        serialNumberStatus.set(FMCGSerialNumberStatusFields.PERSONNEL_API_NAME, ApiNames.PUBLIC_EMPLOYEE_OBJ);
        serialNumberStatus.set(FMCGSerialNumberStatusFields.PERSONNEL_ID, outerUserId);
        serialNumberStatus.set(FMCGSerialNumberStatusFields.ACCOUNT_ID, storeId);
        serialNumberStatus.set(FMCGSerialNumberStatusFields.CHANNEL_TYPE, FMCGSerialNumberStatusFields.ChannelType.STORE);
        serialNumberStatus.set(FMCGSerialNumberStatusFields.ACTION_ID, fmcgSerialNumberService.getActionIdByActionUniqueId(upperTenantId, ScanCodeActionConstants.STORE_SCAN_CODE));
        serialNumberStatus.set(FMCGSerialNumberStatusFields.BUSINESS_OBJECT, relatedObj.getDescribeApiName());
        serialNumberStatus.set(FMCGSerialNumberStatusFields.BUSINESS_OBJECT_ID, relatedObj.getId());
        serialNumberStatus.set(FMCGSerialNumberStatusFields.BUSINESS_OBJECT_NAME, relatedObj.getName());
        serialNumberStatus.set(FMCGSerialNumberStatusFields.VIRTUAL_FLAG, "1");

        return serialNumberStatus;
    }
}
