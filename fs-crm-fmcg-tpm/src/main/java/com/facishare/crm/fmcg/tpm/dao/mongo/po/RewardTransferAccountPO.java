package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.crm.fmcg.tpm.api.rule.CloudAccountDTO;
import com.facishare.crm.fmcg.tpm.api.rule.TransferAccountBaseDTO;
import com.facishare.crm.fmcg.tpm.api.rule.WxMerchantAccountDTO;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Property;

@Data
@ToString
@Entity(value = "fmcg_tpm_reward_transfer_account", noClassnameStored = true)
public class RewardTransferAccountPO extends MongoPO {

    public static final String F_PLATFORM = "platform";
    public static final String F_DEALER_ID = "dealer_id";
    public static final String F_BROKER_ID = "broker_id";
    public static final String F_SUB_MCH_ID = "sub_mch_id";
    public static final String F_NAME = "name";
    public static final String F_CODE = "code";

    @Property(F_PLATFORM)
    private String platform;

    @Property(F_NAME)
    private String name;

    @Property(F_CODE)
    private String code;


    @Property(F_DEALER_ID)
    private String dealerId;


    @Property(F_BROKER_ID)
    private String brokerId;


    @Property(F_SUB_MCH_ID)
    private String subMchId;


    public TransferAccountBaseDTO toDTO() {
        if (RewardAccountPlatFormEnum.WX_MERCHANT_ACCOUNT.code().equals(this.platform)) {
            WxMerchantAccountDTO wxMerchantAccountDTO = new WxMerchantAccountDTO();
            wxMerchantAccountDTO.setSubMchId(this.subMchId);
            wxMerchantAccountDTO.setName(this.name);
            wxMerchantAccountDTO.setCode(this.code);
            return wxMerchantAccountDTO;
        } else if (RewardAccountPlatFormEnum.CLOUD_ACCOUNT.code().equals(this.platform)) {
            CloudAccountDTO cloudAccountDTO = new CloudAccountDTO();
            cloudAccountDTO.setName(this.name);
            cloudAccountDTO.setCode(this.code);
            cloudAccountDTO.setDealerId(this.dealerId);
            cloudAccountDTO.setBrokerId(this.brokerId);
            return cloudAccountDTO;
        }
        return null;
    }

}
