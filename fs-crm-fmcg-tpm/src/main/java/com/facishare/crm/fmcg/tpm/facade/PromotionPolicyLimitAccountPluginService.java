package com.facishare.crm.fmcg.tpm.facade;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.web.service.PromotionPolicyService;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.domain.AddActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.BulkInvalidActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.EditActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.InvalidActionDomainPlugin;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * author: wuyx
 * description:
 * createTime: 2023/7/11 19:34
 */
@Slf4j
@Service
@ServiceModule("tpm_price_policy_limit_account_action")
public class PromotionPolicyLimitAccountPluginService {

    @Resource
    private PromotionPolicyService promotionPolicyService;

    @ServiceMethod("add_before")
    public AddActionDomainPlugin.Result before(AddActionDomainPlugin.Arg arg, ServiceContext serviceContext) {

        IObjectData objectData = arg.getObjectData().toObjectData();
        log.info("tpm_price_policy_limit_account_action add_before objectData ={}", JSON.toJSONString(objectData));
        if (Objects.nonNull(objectData) && !TPMGrayUtils.isAllowActivityIdPromotionPolicy(serviceContext.getTenantId())) {
            boolean validationActivityIds = validationActivityId(objectData);
            if (!validationActivityIds) {
                throw new ValidateException(I18N.text(I18NKeys.PROMOTION_POLICY_LIMIT_ACCOUNT_PLUGIN_SERVICE_0));
            }
            if (promotionPolicyService.pricePolicyIsContainsActivity(objectData)) {
                throw new ValidateException(I18N.text(I18NKeys.PROMOTION_POLICY_LIMIT_ACCOUNT_PLUGIN_SERVICE_1));
            }
        }
        return new AddActionDomainPlugin.Result();
    }

    @ServiceMethod("edit_before")
    public EditActionDomainPlugin.Result before(EditActionDomainPlugin.Arg arg, ServiceContext serviceContext) {

        IObjectData objectData = arg.getObjectData().toObjectData();
        if (Objects.nonNull(objectData) && !TPMGrayUtils.isAllowActivityIdPromotionPolicy(serviceContext.getTenantId())) {
            boolean validationActivityIds = validationActivityId(objectData);
            if (!validationActivityIds) {
                throw new ValidateException(I18N.text(I18NKeys.PROMOTION_POLICY_LIMIT_ACCOUNT_PLUGIN_SERVICE_2));
            }
            if (promotionPolicyService.pricePolicyIsContainsActivity(objectData)) {
                throw new ValidateException(I18N.text(I18NKeys.PROMOTION_POLICY_LIMIT_ACCOUNT_PLUGIN_SERVICE_3));
            }
        }
        return new EditActionDomainPlugin.Result();
    }

    @ServiceMethod("bulkInvalid_before")
    public BulkInvalidActionDomainPlugin.Result before(BulkInvalidActionDomainPlugin.Arg arg, ServiceContext serviceContext) {

        List<ObjectDataDocument> objectDataList = arg.getObjectDataList();

        if (CollectionUtils.isEmpty(objectDataList) || TPMGrayUtils.isAllowActivityIdPromotionPolicy(serviceContext.getTenantId())) {
            return new BulkInvalidActionDomainPlugin.Result();
        }

        List<String> excludeNames = promotionPolicyService.queryIsNotValidaNames(objectDataList);

        if (CollectionUtils.isNotEmpty(excludeNames)) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.PROMOTION_POLICY_LIMIT_ACCOUNT_PLUGIN_SERVICE_4), excludeNames.toString()));
        }

        return new BulkInvalidActionDomainPlugin.Result();
    }

    @ServiceMethod("invalid_before")
    public InvalidActionDomainPlugin.Result before(InvalidActionDomainPlugin.Arg arg, ServiceContext serviceContext) {

        IObjectData objectData = arg.getObjectData().toObjectData();
        if (Objects.nonNull(objectData) && !TPMGrayUtils.isAllowActivityIdPromotionPolicy(serviceContext.getTenantId())) {
            boolean validationIsInvalid = promotionPolicyService.validationIsInvalid(objectData);
            if (!validationIsInvalid) {
                throw new ValidateException(I18N.text(I18NKeys.PROMOTION_POLICY_LIMIT_ACCOUNT_PLUGIN_SERVICE_5));
            }
        }
        return new InvalidActionDomainPlugin.Result();
    }

    private boolean validationActivityId(IObjectData objectData) {
        return promotionPolicyService.validationIsNeedActivityId(objectData);
    }
}
