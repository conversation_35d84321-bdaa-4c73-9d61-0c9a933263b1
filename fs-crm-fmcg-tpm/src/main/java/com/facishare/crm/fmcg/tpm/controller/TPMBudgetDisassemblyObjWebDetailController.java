package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.TPMBudgetDisassemblyFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * ligt 2022-08
 */
@SuppressWarnings("all")
public class TPMBudgetDisassemblyObjWebDetailController extends StandardWebDetailController {

    @Override
    protected Result after(Arg arg, Result result) {
        Result rst = super.after(arg, result);
        removeUselessButtons(rst);
        return rst;
    }

    private void removeUselessButtons(Result rst) {
        String lifeStatus = (String) rst.getData().get(ObjectLifeStatus.LIFE_STATUS_API_NAME);
        List<String> blockedActions = Lists.newArrayList("Clone");

        if (!"ineffective".equals(lifeStatus)) {
            if (!TPMGrayUtils.isAllowEditDisassemblyCustomField(controllerContext.getTenantId())) {
                blockedActions.add("Edit");
            }
            blockedActions.add("Abolish");
        }

        String status = (String) rst.getData().get(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS);
        if (!Objects.equals(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__UNFROZEN_FAILED, status)
                && !Objects.equals(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__FAILED, status)) {
            blockedActions.add(ObjectAction.DISASSEMBLY_RETRY.getActionCode());
        }

        LayoutExt layoutExt = LayoutExt.of(rst.getLayout());
        Optional<IComponent> component = layoutExt.getHeadInfoComponent();
        if (component.isPresent()) {
            IComponent headInfoComponent = component.get();
            headInfoComponent.setButtons(
                    headInfoComponent.getButtons()
                            .stream()
                            .filter(button -> !blockedActions.contains(button.getAction()))
                            .collect(Collectors.toList())

            );
        }
    }
}
