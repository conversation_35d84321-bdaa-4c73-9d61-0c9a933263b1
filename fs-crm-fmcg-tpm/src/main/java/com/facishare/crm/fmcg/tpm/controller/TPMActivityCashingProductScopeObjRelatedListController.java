package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.NodeType;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.solr.common.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class TPMActivityCashingProductScopeObjRelatedListController extends StandardRelatedListController {
    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);
    private String activityUnifiedCashingProductRangeType = "";

    @Override
    protected void before(Arg arg) {


        ObjectDataDocument objectData = arg.getObjectData();
        if (Objects.isNull(objectData)) {
            super.before(arg);
            return;
        }

        String objectApiName = (String) objectData.get(CommonFields.OBJECT_DESCRIBE_API_NAME);
        if (!ApiNames.TPM_ACTIVITY_CASHING_PRODUCT_OBJ.equals(objectApiName)) {
            super.before(arg);
            return;
        }
        IObjectData masterData = arg.getMasterData().toObjectData();


        String activityTypeId = masterData.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
        ActivityTypeExt activityType = activityTypeManager.find(controllerContext.getTenantId(), activityTypeId);

        boolean enableActivityPlan = isEnableActivityPlan(activityType);
        boolean activityPlanRequired = activityPlanRequired(activityType);
        String activityUnifiedCaseId = masterData.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class);

        if (activityPlanRequired && enableActivityPlan && StringUtils.isEmpty(activityUnifiedCaseId)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_CASHING_PRODUCT_SCOPE_OBJ_RELATED_LIST_CONTROLLER_0));
        }
        if (enableActivityPlan && (activityPlanRequired || !StringUtils.isEmpty(activityUnifiedCaseId))) {
            setActivityUnifiedCashingProductRangeType(activityUnifiedCaseId);
        } else if (!activityPlanRequired && enableActivityPlan && StringUtils.isEmpty(activityUnifiedCaseId)) {
            activityUnifiedCashingProductRangeType = "ALL";
        }

        //活动方案如果为全部直接查产品
        if (Objects.equals("ALL", activityUnifiedCashingProductRangeType)) {
            this.controllerContext = new ControllerContext(
                    controllerContext.getRequestContext(),
                    ApiNames.PRODUCT_OBJ,
                    controllerContext.getMethodName());
        }


        if (!ApiNames.PRODUCT_OBJ.equals(controllerContext.getObjectApiName())) {
            String searchQueryInfo = arg.getSearchQueryInfo();
            if (StringUtils.isEmpty(searchQueryInfo)) {
                return;
            }
            ISearchTemplateQuery iSearchTemplateQuery = SearchTemplateQuery.fromJsonString(searchQueryInfo);
            for (Wheres where : iSearchTemplateQuery.getWheres()) {
                List<IFilter> filters = where.getFilters();
                if (CollectionUtils.isNotEmpty(filters)) {
                    filters.removeIf(filter -> "product_status".equals(filter.getFieldName()));
                }
            }
            arg.setSearchQueryInfo(iSearchTemplateQuery.toJsonString());
        }

        super.before(arg);
    }

    private void setActivityUnifiedCashingProductRangeType(String activityUnifiedCaseId) {
        IObjectData activityUnified = serviceFacade.findObjectDataIgnoreAll(User.systemUser(controllerContext.getTenantId()), activityUnifiedCaseId, ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
        JSONObject activityUnifiedCashingProductRange = getCashingProductRange(activityUnified);
        activityUnifiedCashingProductRangeType = activityUnifiedCashingProductRange.getString("type").toUpperCase();
    }

    private JSONObject getCashingProductRange(IObjectData objectData) {
        String cashingProductRangeStr = objectData.get(TPMActivityFields.CASHING_PRODUCT_RANGE, String.class);
        //cashingProductRangeStr是空代表的此条数据是老数据，需要按照老数据处理逻辑：指定产品
        if (StringUtils.isEmpty(cashingProductRangeStr)) {
            cashingProductRangeStr = "{\"type\":\"FIXED\",\"value\":\"FIXED\"}";
        }
        return JSON.parseObject(cashingProductRangeStr);
    }

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        super.beforeQueryData(overrideQuery(query));
    }

    @Override
    protected Result after(Arg arg, Result result) {
        return overrideInformation(super.after(arg, result));
    }

    private Result overrideInformation(Result result) {
        ObjectDataDocument objectData = arg.getObjectData();
        if (Objects.isNull(objectData)) {
            return result;
        }

        String objectApiName = (String) objectData.get(CommonFields.OBJECT_DESCRIBE_API_NAME);
        if (!ApiNames.TPM_ACTIVITY_CASHING_PRODUCT_OBJ.equals(objectApiName)) {
            return result;
        }
        //activityUnifiedCashingProductRangeType为ALL时查产品对象，不需要特殊处理
        if (Objects.equals("ALL", activityUnifiedCashingProductRangeType)) {
            return result;
        }
        List<String> productIds = result.getDataList().stream().map(data -> (String) data.get(TPMActivityCashingProductScopeFields.PRODUCT_ID)).collect(Collectors.toList());

        List<IObjectData> products = serviceFacade.findObjectDataByIdsIgnoreRelevantTeam(controllerContext.getTenantId(), Lists.newArrayList(productIds), ApiNames.PRODUCT_OBJ);
        IObjectDescribe productDescribe = serviceFacade.findObject(controllerContext.getTenantId(), ApiNames.PRODUCT_OBJ);
        serviceFacade.fillObjectDataWithRefObject(productDescribe, products, User.systemUser(controllerContext.getTenantId()), null, false);
        Map<String, IObjectData> productMap = products.stream().collect(Collectors.toMap(IObjectData::getId, v -> v, (oldData, newData) -> newData));
        List<ObjectDataDocument> resultList = Lists.newArrayList();

        for (ObjectDataDocument data : result.getDataList()) {
            ObjectDataDocument product = new ObjectDataDocument();
            String productId = (String) data.get(TPMActivityCashingProductScopeFields.PRODUCT_ID);
            IObjectData productObjData = productMap.get(productId);
            if (productObjData == null) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_CASHING_PRODUCT_SCOPE_OBJ_RELATED_LIST_CONTROLLER_1));
            }
            product.putAll(ObjectDataDocument.of(productObjData));

            product.put(TPMActivityCashingProductScopeFields.PRODUCT_ID, product.getId());
            product.put(TPMActivityCashingProductFields.UNIFIED_CASE_PRODUCT_ID, data.getId());
            product.put(TPMActivityCashingProductFields.UNIFIED_CASE_PRODUCT_ID__R, data.get(TPMActivityCashingProductScopeFields.DISPLAY_NAME));
            product.put(TPMActivityCashingProductScopeFields.DISPLAY_NAME, data.get(TPMActivityCashingProductScopeFields.DISPLAY_NAME));
            product.put(TPMActivityCashingProductScopeFields.PRODUCT_ID__R, data.get(TPMActivityCashingProductScopeFields.PRODUCT_ID__R));
            product.put(TPMActivityCashingProductScopeFields.PRODUCT_ID__RELATION_IDS, data.get(TPMActivityCashingProductScopeFields.PRODUCT_ID__RELATION_IDS));

            resultList.add(product);
        }


        result.setDataList(resultList);
        return result;


    }

    private SearchTemplateQuery overrideQuery(SearchTemplateQuery query) {
        ObjectDataDocument objectData = arg.getObjectData();
        if (Objects.isNull(objectData)) {
            return query;
        }

        String objectApiName = (String) objectData.get(CommonFields.OBJECT_DESCRIBE_API_NAME);
        if (!ApiNames.TPM_ACTIVITY_CASHING_PRODUCT_OBJ.equals(objectApiName)) {
            return query;
        }
        //activityUnifiedCashingProductRangeType为ALL时查产品对象，不需要特殊处理
        if (Objects.equals("ALL", activityUnifiedCashingProductRangeType)) {
            return query;
        }
        ObjectDataDocument masterData = arg.getMasterData();
        if (Objects.isNull(masterData)) {
            return query;
        }
        String unifiedCaseId = (String) masterData.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID);
        if (StringUtils.isEmpty(unifiedCaseId)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_CASHING_PRODUCT_SCOPE_OBJ_RELATED_LIST_CONTROLLER_2));
        }
        Filter filter = new Filter();
        filter.setFieldName(TPMActivityCashingProductScopeFields.ACTIVITY_UNIFIED_CASE_ID);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(unifiedCaseId));
        query.getFilters().add(filter);

        return query;
    }

    private boolean isEnableActivityPlan(ActivityTypeExt activityType) {

        boolean enableActivityPlan = false;
        if (Objects.nonNull(activityType) && !Objects.isNull(activityType.node(NodeType.PLAN_TEMPLATE))) {
            enableActivityPlan = true;
        }
        return enableActivityPlan;
    }

    private boolean activityPlanRequired(ActivityTypeExt activityType) {

        boolean activityPlanRequired = false;
        if (Objects.nonNull(activityType)) {
            ActivityNodeEntity node = activityType.node(NodeType.PLAN_TEMPLATE);
            if (!Objects.isNull(node) && Objects.nonNull(node.getActivityPlanConfig())) {
                return Boolean.TRUE.equals(node.getActivityPlanConfig().getEnableRelationPreNodeRequired());
            }
        }
        return activityPlanRequired;
    }
}
