package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;

/**
 * @author: wuyx
 * description:
 * createTime: 2022/3/7 18:15
 */
public interface IActivityService {

    boolean validateActivityEnableEdit(String tenantId, String activityId);
    boolean validateActivityProofEnableEdit(String tenantId, String proofId, String activityId);

    void preValidateAccount(User user, IObjectData activity);

    void ifAllowCreateDataDueToOnceWriteOff(String tenantId, IObjectData activity);

    void triggerCloseTPMActivity(String tenantId, String userId, String activityId, boolean needForce, boolean needTriggerApproval);

    List<IObjectData> findActivityByStore(String tenantId, List<String> departmentIds, IObjectData store, List<String> includeActivityIds,List<String> activityTypeIds);

    List<String> getDepartmentByStore(String tenantId, IObjectData store);

}
