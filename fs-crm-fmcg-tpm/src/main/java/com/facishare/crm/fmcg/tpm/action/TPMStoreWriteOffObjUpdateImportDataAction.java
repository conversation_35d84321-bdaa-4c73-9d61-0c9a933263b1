package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportDataAction;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class TPMStoreWriteOffObjUpdateImportDataAction extends StandardUpdateImportDataAction {

    @Override
    protected void convertFields(List<ImportData> dataList) {
        super.convertFields(dataList);
    }
}
