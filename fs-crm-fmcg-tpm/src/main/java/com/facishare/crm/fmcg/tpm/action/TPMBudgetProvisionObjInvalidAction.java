package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;

/**
 * Author: linmj
 * Date: 2023/10/30 15:33
 */
public class TPMBudgetProvisionObjInvalidAction extends StandardInvalidAction {

    @Override
    protected void before(Arg arg) {
       throw new ValidateException(I18N.text(I18NKeys.TPM_BUDGET_PROVISION_OBJ_INVALID_ACTION_0));
    }
}
