package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.tpm.business.abstraction.IFiscalTimeService;
import com.facishare.crm.fmcg.tpm.business.dto.FiscalMonth;
import com.facishare.crm.fmcg.tpm.business.dto.FiscalQuarter;
import com.facishare.crm.fmcg.tpm.business.dto.FiscalYear;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import groovy.lang.Tuple2;
import org.springframework.stereotype.Component;

import java.util.Calendar;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/10/20 17:25
 */
@Component
@SuppressWarnings("Duplicates")
public class FiscalTimeService implements IFiscalTimeService {

    @Override
    public long correctPeriodTime(String tenantId, String dim, long time) {
        return calculateTimeSpan(tenantId, dim, time).getFirst();
    }

    @Override
    public boolean notSamePeriod(String tenantId, String dim, long a, long b) {
        return correctPeriodTime(tenantId, dim, a) != correctPeriodTime(tenantId, dim, b);
    }

    @Override
    public Long nextPeriod(String tenantId, String dim, long cur) {
        cur = correctPeriodTime(tenantId, dim, cur);

        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(cur);

        switch (dim) {
            case "year":
                cal.add(Calendar.YEAR, 1);
                return cal.getTimeInMillis();
            case "quarter":
                cal.add(Calendar.MONTH, 3);
                return cal.getTimeInMillis();
            case "month":
                cal.add(Calendar.MONTH, 1);
                return cal.getTimeInMillis();
            default:
                throw new MetaDataBusinessException("nextPeriod calculate error.");
        }
    }

    @Override
    public boolean notNextPeriod(String tenantId, String dim, long cur, long next) {
        cur = correctPeriodTime(tenantId, dim, cur);
        next = correctPeriodTime(tenantId, dim, next);

        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(cur);

        switch (dim) {
            case "year":
                cal.add(Calendar.YEAR, 1);
                return next != cal.getTimeInMillis();
            case "quarter":
                cal.add(Calendar.MONTH, 3);
                return next != cal.getTimeInMillis();
            case "month":
                cal.add(Calendar.MONTH, 1);
                return next != cal.getTimeInMillis();
            default:
                return true;
        }
    }

    @Override
    public Long parentPeriod(String tenantId, String parentDim, String childDim, long cur) {
        cur = correctPeriodTime(tenantId, childDim, cur);

        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(cur);

        switch (childDim) {

            case "year":

                switch (parentDim) {
                    case "year":
                        return cal.getTimeInMillis();
                    case "quarter":
                    case "month":
                    default:
                        throw new MetaDataBusinessException("child period :year,parentPeriod calculate error.");
                }

            case "quarter":

                switch (parentDim) {
                    case "year":
                        return correctPeriodTime(tenantId, "year", cur);
                    case "quarter":
                        return correctPeriodTime(tenantId, "quarter", cur);
                    case "month":
                    default:
                        throw new MetaDataBusinessException("child period :quarter,parentPeriod calculate error.");
                }

            case "month":
                switch (parentDim) {
                    case "year":
                        return correctPeriodTime(tenantId, "year", cur);
                    case "quarter":
                        return correctPeriodTime(tenantId, "quarter", cur);
                    case "month":
                        return correctPeriodTime(tenantId, "month", cur);
                    default:
                        throw new MetaDataBusinessException("child period :month,parentPeriod calculate error.");
                }

            default:
                throw new MetaDataBusinessException("parentPeriod calculate error.");

        }

    }

    @Override
    public Tuple2<Long, Long> calculateTimeSpan(String tenantId, String dim, long time) {
        FiscalYear year = fiscalYear(tenantId, dim, time);
        Tuple2<Long, Long> result = null;
        switch (dim) {
            case "year":
                result = new Tuple2<>(year.getStart(), year.getEnd());
                break;
            case "quarter":
                for (FiscalQuarter v : year.getQuarter().values()) {
                    if (v.getStart() <= time && v.getEnd() > time) {
                        result = new Tuple2<>(v.getStart(), v.getEnd());
                    }
                }
                break;
            case "month":
                for (FiscalMonth v : year.getMonth().values()) {
                    if (v.getStart() <= time && v.getEnd() > time) {
                        result = new Tuple2<>(v.getStart(), v.getEnd());
                    }
                }
                break;
            default:
                throw new MetaDataBusinessException("time dimension value error.");
        }
        if (result == null) {
            throw new MetaDataBusinessException("calculate time span error.");
        }
        return result;
    }

    @Override
    public FiscalYear fiscalYear(String tenantId, String dim, long time) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(time);
        int year = cal.get(Calendar.YEAR);
        return FiscalYear.of(year);
    }
}
