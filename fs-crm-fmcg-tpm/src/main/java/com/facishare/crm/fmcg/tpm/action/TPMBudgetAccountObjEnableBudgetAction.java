package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountFields;
import com.facishare.crm.fmcg.tpm.business.BudgetOperatorFactory;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetAccountDetailService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetOperator;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.tpm.service.TransactionProxy;
import com.facishare.crm.fmcg.tpm.utils.TraceUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectApprovalAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/9/6 下午2:51
 */
public class TPMBudgetAccountObjEnableBudgetAction extends BaseObjectApprovalAction<TPMBudgetAccountObjEnableBudgetAction.Arg, TPMBudgetAccountObjEnableBudgetAction.Result> {

    private static final TransactionProxy transactionProxy = SpringUtil.getContext().getBean(TransactionProxy.class);

    private static final IBudgetAccountDetailService budgetAccountDetailService = SpringUtil.getContext().getBean(IBudgetAccountDetailService.class);

    private IObjectData objectData;

    private IBudgetOperator budgetOperator;

    private User systemUser;

    private String approvalId;

    private String businessId;

    private Map<String, Object> updateMap = Maps.newHashMap();


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.ENABLE_BUDGET.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getDataId());
    }


    @Override
    protected Result doAct(Arg arg) {
        if (this.objectData != null) {
            budgetOperator.tryLock();
            if (arg.isSkipTriggerApprovalFlow() || !triggerApproval()) {
                enableBudget();
            }
            stopWatch.lap("refresh_data");
        }

        return Result.of(objectData);
    }

    private void enableBudget() {
        boolean notExistsDetail = CollectionUtils.isEmpty(budgetAccountDetailService.queryByBusinessType(systemUser, this.objectData.getId(), BizType.INIT_MONEY));
        transactionProxy.run(() -> {
            if (notExistsDetail) {
                this.budgetOperator.setWhat(this.objectData);
                this.budgetOperator.income(this.objectData.get(TPMBudgetAccountFields.BASE_AMOUNT, BigDecimal.class));
                this.budgetOperator.recalculate();
            }
            String budgetStatus = this.objectData.get(TPMBudgetAccountFields.BUDGET_STATUS, String.class);
            if (!TPMBudgetAccountFields.BUDGET_STATUS__ENABLE.equals(budgetStatus)) {
                serviceFacade.updateWithMap(this.systemUser, this.objectData, this.updateMap);
                this.updateMap.forEach(this.objectData::set);
            }
            stopWatch.lap("enable_budget");
        });
    }

    private boolean triggerApproval() {
        Map<String, Map<String, Object>> dataMap = Maps.newHashMap();
        dataMap.put(objectData.getId(), this.updateMap);

        Map<String, Map<String, Object>> callBackMap = Maps.newHashMap();
        callBackMap.put(this.objectData.getId(), this.customCallbackData);
        this.customCallbackData.putAll(dataMap);
        stopWatch.lap("init_callback");
        //todo: trigger a button approval
        //startApprovalFlow(Lists.newArrayList(objectData), ApprovalFlowTriggerType.UPDATE, dataMap, callBackMap);
        stopWatch.lap("trigger_approval");
        return isApprovalFlowStartSuccess(objectData.getId());
    }

    protected IObjectData getPreObjectData() {
        return objectData;
    }

    protected IObjectData getPostObjectData() {
        return objectData;
    }

    protected String getButtonApiName() {
        return ObjectAction.ENABLE_BUDGET.getButtonApiName();
    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.isNotEmpty(dataList)) {
            this.objectData = this.dataList.get(0);
            this.systemUser = User.systemUser(actionContext.getTenantId());
            this.approvalId = TraceUtil.initApprovalTraceId();
            this.businessId = TraceUtil.initBusinessTraceId();
            this.budgetOperator = BudgetOperatorFactory.initOperator(BizType.INIT_MONEY, this.systemUser, this.objectData.getId(), this.businessId, this.approvalId, objectData, false,true);
            this.updateMap.put(TPMBudgetAccountFields.BUDGET_STATUS, TPMBudgetAccountFields.BUDGET_STATUS__ENABLE);
        }
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);

        this.initCallBack();

        this.validate();
    }


    private void validate() {
        if (!CommonFields.LIFE_STATUS__NORMAL.equals(this.objectData.get(CommonFields.LIFE_STATUS, String.class))) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_OBJ_ENABLE_BUDGET_ACTION_0));
        }
    }

    @Data
    public static class Arg {

        @SerializedName("objectDataId")
        @JSONField(name = "objectDataId")
        @JsonProperty("objectDataId")
        private String dataId;

        private boolean skipTriggerApprovalFlow;

        private Map<String, Object> callbackData;

        public static Arg of(Map<String, Object> callbackData, String dataId) {
            Arg arg = new Arg();
            arg.setDataId(dataId);
            arg.setSkipTriggerApprovalFlow(true);
            arg.setCallbackData(callbackData);
            return arg;
        }
    }


    @Data
    public static class Result {
        private ObjectDataDocument objectData;

        public static Result of(IObjectData objectData) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            return result;
        }
    }

    private void initCallBack() {
        if (!Objects.isNull(arg.getCallbackData())) {
            this.customCallbackData.putAll(arg.getCallbackData());
        }
        this.customCallbackData.put("action", ObjectAction.ENABLE_BUDGET.getActionCode());
        this.customCallbackData.put(TraceUtil.FMCG_TPM_BUDGET_APPROVAL_CALLBACK_TRACE_ID_KEY, this.approvalId);
        this.customCallbackData.put(TraceUtil.FMCG_TPM_BUDGET_BUSINESS_CALLBACK_TRACE_ID_KEY, this.businessId);
    }

    @Override
    protected void finallyDo() {
        try {
            super.finallyDo();
        } finally {
            if (budgetOperator != null) {
                budgetOperator.unlock();
            }
        }
    }
}
