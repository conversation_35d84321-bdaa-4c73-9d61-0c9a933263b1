package com.facishare.crm.fmcg.tpm.reward.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

public interface RewardNotifyRetry {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "id")
        @JsonProperty(value = "id")
        @SerializedName("id")
        private String id;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "id")
        @JsonProperty(value = "id")
        @SerializedName("id")
        private String id;
    }
}
