package com.facishare.crm.fmcg.tpm.business.enums;

import lombok.Getter;
import java.util.Arrays;

/**
 * 陈列AI识别计算类型枚举
 */
@Getter
public enum CalculateTypeEnum {
    
    /**
     * 所有照片排面数求和
     */
    SUM_ALL_PHOTOS(0),
    
    /**
     * 所有照片排面数求最大值
     */
    MAX_ALL_PHOTOS(1),
    
    /**
     * 统计陈列形式下单张图片排面汇总后，多张对比取最大值（不做过滤）
     */
    MAX_SINGLE_PHOTO_SUM_NO_FILTER(2),
    
    /**
     * 陈列形式下单张图片排面汇总后，多张对比取最大值（做错误场景过滤）
     */
    MAX_SINGLE_PHOTO_SUM_WITH_FILTER(3),
    
    /**
     * 统计陈列形式多张照片取单张最大值
     */
    MAX_SINGLE_PHOTO(4),
    
    /**
     * 陈列形式多张照片取单张最大值（做错误场景图片过滤）
     */
    MAX_SINGLE_PHOTO_WITH_FILTER(5);
    
    /**
     * 计算类型值
     */
    private final Integer value;
    
    CalculateTypeEnum(Integer value) {
        this.value = value;
    }
    
    /**
     * 根据值获取枚举
     *
     * @param value 值
     * @return 对应的枚举，如果未找到返回null
     */
    public static CalculateTypeEnum fromValue(Integer value) {
        if (value == null) {
            return null;
        }
        
        return Arrays.stream(values())
                .filter(type -> type.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 判断给定的值是否为有效的计算类型
     *
     * @param value 要检查的值
     * @return 如果是有效的计算类型则返回true，否则返回false
     */
    public static boolean isValidValue(Integer value) {
        return fromValue(value) != null;
    }
}