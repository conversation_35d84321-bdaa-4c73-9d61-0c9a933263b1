package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.paas.metadata.api.IObjectData;

/**
 * Author: linmj
 * Date: 2023/11/28 15:02
 */
public interface IRewardExceptionRecordService {

    void create(String tenantId);

    void writeRecord(String tenantId, String bizCode, String bizType, String errorMessage, String status);

    IObjectData getByBizCode(String tenantId, String bizCode);

    void update(String tenantId, IObjectData record, String status,String message);
}
