package com.facishare.crm.fmcg.tpm.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.common.adapter.dto.pay.CloudTransferPlatformEnum;
import com.facishare.crm.fmcg.tpm.api.rule.WxMerchantAccountDTO;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.adapter.abstraction.IPayService;
import com.facishare.crm.fmcg.common.adapter.dto.UserInfo;
import com.facishare.crm.fmcg.common.adapter.dto.pay.TransferDetail;
import com.facishare.crm.fmcg.common.adapter.dto.pay.TransferDetailStatusEnum;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.CloudAccount;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.CloudTransfer;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.QueryCloudTransferDetails;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.WXCloudPayReceiverAccount;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.BatchWXTenantTransfer;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.QueryWXTenantTransferDetail;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.WXPersonalAccount;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.WXTenantAccount;
import com.facishare.crm.fmcg.common.adapter.exception.RewardFmcgException;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.tpm.retry.setter.RedPacketWithdrawSetter;
import com.facishare.crm.fmcg.tpm.service.abstraction.IWithdrawRecordService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Author: ljs
 * Date: 2023/11/17 19:33
 */
//IgnoreI18nFile
@Slf4j
@Service
public class WithdrawRecordService implements IWithdrawRecordService {

    @Resource
    private IPayService payService;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private RedissonClient redissonCmd;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private RedPacketWithdrawSetter redPacketWithdrawSetter;


    public boolean refreshWithdrawStatus(IObjectData withdrawRecord) {
        String status = withdrawRecord.get(WithdrawRecordObjFields.PAYMENT_STATUS, String.class);
//        if (WithdrawPaymentStatusEnum.PROCESSING.code().equals(status)) {
//            return queryWithdrawRecordStatus(withdrawRecord);
//        }
        switch (WithdrawPaymentStatusEnum.codeOf(status)) {
            case INIT:
                return publishWithdraw(withdrawRecord);
            case PROCESSING:
                return queryWithdrawRecordStatus(withdrawRecord);
            default:

        }
        return false;
    }


    public boolean publishWithdraw(IObjectData withdrawRecord) {

        String lockKey = withdrawRecord.getId();
        tryLock(lockKey);
        try {
            log.info("object lockKey {} start withdraw", lockKey);
            String paymentBusinessId = withdrawRecord.get(WithdrawRecordObjFields.BUSINESS_ID, String.class);
            String fromAccountType = withdrawRecord.get(WithdrawRecordObjFields.TRANSFEROR_ACCOUNT_TYPE, String.class);
            String fromTransferAccount = withdrawRecord.get(WithdrawRecordObjFields.TRANSFEROR_ACCOUNT, String.class);

            String fromTenantId = withdrawRecord.get(WithdrawRecordObjFields.TRANSFEROR_TENANT_ID, String.class);

            if (Strings.isNullOrEmpty(fromTenantId)) {
                updateWithdrawRecordTransferInfo(withdrawRecord, null, null, WithdrawPaymentStatusEnum.EXCEPT.code(), "转出账户为空。");
                return false;
            }

            String fromTenantAccount = String.valueOf(eieaConverter.enterpriseIdToAccount(Integer.parseInt(fromTenantId)));

            //查看当前的明细状态和记录状态的是否一致
            TransferDetail transferDetail = getTransferDetail(fromTenantId, fromAccountType, paymentBusinessId);
            if (transferDetail == null) {

                UserInfo userInfo = UserInfo.builder().tenantId(fromTenantId).userId("-10000").build();
                BigDecimal amount = withdrawRecord.get(WithdrawRecordObjFields.WITHDRAWAL_AMOUNT, BigDecimal.class);
                // 提现没有备注
                String remarks = withdrawRecord.get(WithdrawRecordObjFields.REMARKS, String.class);
                if (WithdrawRecordObjFields.TransferorAccountType.CLOUD.equals(fromAccountType)) {
                    cloudTransfer(userInfo, fromTenantAccount, fromTenantId, fromTransferAccount, paymentBusinessId, amount, remarks, withdrawRecord);
                } else if (WithdrawRecordObjFields.TransferorAccountType.WECHAT_MERCHANT.equals(fromAccountType)) {
                    wxTransfer(userInfo, fromTenantAccount, fromTenantId, fromTransferAccount, paymentBusinessId, amount, remarks, withdrawRecord);
                } else {
                    log.info("异常转出账户类型：{}", fromAccountType);
                    updateWithdrawRecordTransferInfo(withdrawRecord, null, null, WithdrawPaymentStatusEnum.EXCEPT.code(), "异常转出账户类型。");
                }
            } else if (TransferDetailStatusEnum.SUCCESS.codes().contains(transferDetail.getStatus())) {
                updateWithdrawRecordTransferInfo(withdrawRecord, null, null, WithdrawPaymentStatusEnum.SUCCESS.code(), null);
            }

        } finally {
            unlock(lockKey);
        }
        return false;
    }

    private void wxTransfer(UserInfo userInfo,
                            String fromTenantAccount,
                            String fromTenantId,
                            String transferAccount,
                            String newPaymentBusinessId,
                            BigDecimal amount,
                            String remarks,
                            IObjectData withdrawRecord) {
        BatchWXTenantTransfer.Arg batchWXTenantTransferArg = new BatchWXTenantTransfer.Arg();
        batchWXTenantTransferArg.setBatchName(newPaymentBusinessId);
        batchWXTenantTransferArg.setBatchRemarks(newPaymentBusinessId);
        batchWXTenantTransferArg.setBatchTransferId(newPaymentBusinessId);
        WXTenantAccount wxTenantAccount = new WXTenantAccount();
        wxTenantAccount.setTenantAccount(fromTenantAccount);
        if (!"default".equals(transferAccount)) {
            wxTenantAccount.setAccount(transferAccount);
            WxMerchantAccountDTO merchantAccountDTO = JSON.parseObject(transferAccount, WxMerchantAccountDTO.class);
            wxTenantAccount.setSubMchId(merchantAccountDTO.getSubMchId());
        }
        batchWXTenantTransferArg.setPayeeWXAccount(wxTenantAccount);
        WXPersonalAccount wxPersonalAccount = new WXPersonalAccount();
        wxPersonalAccount.setAmount(amount);
        wxPersonalAccount.setOpenId(withdrawRecord.get(WithdrawRecordObjFields.TRANSFEREE_WECHAT_OPEN_ID, String.class));
        wxPersonalAccount.setRemarks(remarks);
        wxPersonalAccount.setBusinessId(newPaymentBusinessId);
        wxPersonalAccount.setAppId(withdrawRecord.get(WithdrawRecordObjFields.TRANSFEREE_WECHAT_APP_ID, String.class));
        batchWXTenantTransferArg.setReceiverAccounts(Lists.newArrayList(wxPersonalAccount));

        BatchWXTenantTransfer.Result queryResult;
        try {
            queryResult = payService.batchWXTenantTransfer(userInfo, batchWXTenantTransferArg);
        } catch (Exception e) {
            String message = e.getMessage();
            if (!(e instanceof RewardFmcgException)) {
                message = "微信转账发生未知异常";
            }
            updateWithdrawRecordTransferInfo(withdrawRecord, newPaymentBusinessId, null, WithdrawPaymentStatusEnum.FAIL.code(), message);
//            throw e;
            return;
        }
        updateWithdrawRecordTransferInfo(withdrawRecord, queryResult.getBatchTransferId(), queryResult.getDetailResults().get(0).getTransferId(), WithdrawPaymentStatusEnum.PROCESSING.code(), null);
        redPacketWithdrawSetter.setUpdateStatusTask(withdrawRecord.getTenantId(), withdrawRecord.getId(), withdrawRecord.getDescribeApiName());
    }

    private void cloudTransfer(UserInfo userInfo,
                               String fromTenantAccount,
                               String fromTenantId,
                               String transferAccount,
                               String newPaymentBusinessId,
                               BigDecimal amount,
                               String remarks,
                               IObjectData withdrawRecord) {
        CloudTransfer.Arg transferArg = new CloudTransfer.Arg();
        transferArg.setBusinessId(newPaymentBusinessId);
        transferArg.setAmount(amount);
        transferArg.setRemarks(remarks);
        String realName = withdrawRecord.get(WithdrawRecordObjFields.TRANSFEREE_NAME, String.class);
        String idCard = withdrawRecord.get(WithdrawRecordObjFields.TRANSFEREE_ID, String.class);
        String phone = withdrawRecord.get(WithdrawRecordObjFields.TRANSFEREE_PHONE, String.class);
        String openId = withdrawRecord.get(WithdrawRecordObjFields.TRANSFEREE_WECHAT_OPEN_ID, String.class);
        String wxAppId = withdrawRecord.get(WithdrawRecordObjFields.TRANSFEREE_WECHAT_APP_ID, String.class);
        String userId = withdrawRecord.get(WithdrawRecordObjFields.REWARD_PERSON_ID, String.class);
        CloudAccount account = new CloudAccount();
        account.setTenantAccount(fromTenantAccount);
        if (!"default".equals(transferAccount)) {
//            account.setCloudAccountDealerId(transferAccount);
            try {
                JSONObject transferAccountJson = JSONObject.parseObject(transferAccount);
                account.setCloudAccountDealerId(transferAccountJson.getString("dealerId"));
                account.setCloudAccountBrokerId(transferAccountJson.getString("brokerId"));
            } catch (Exception exception) {
                log.error("transferAccount error is ", exception);
            }
        }
        transferArg.setPayerCloudAccount(account);
        WXCloudPayReceiverAccount wxCloudPayReceiverAccount = new WXCloudPayReceiverAccount(realName, idCard, phone, openId, CloudTransferPlatformEnum.FSHARE.label(), realName, userId);
        wxCloudPayReceiverAccount.setAppId(wxAppId);
        transferArg.setReceiverPayAccount(wxCloudPayReceiverAccount);
        CloudTransfer.Result transferResult;
        try {
            transferResult = payService.cloudTransfer(userInfo, transferArg);
        } catch (Exception e) {
            String message = e.getMessage();
            if (!(e instanceof RewardFmcgException)) {
                message = "云账户转账发生未知异常";
            }
            updateWithdrawRecordTransferInfo(withdrawRecord, newPaymentBusinessId, null, WithdrawPaymentStatusEnum.FAIL.code(), message);
//            throw e;
            return;
        }
        updateWithdrawRecordTransferInfo(withdrawRecord, transferResult.getBusinessId(), transferResult.getTransferId(), WithdrawPaymentStatusEnum.PROCESSING.code(), null);
        //publish a new task to update status
        redPacketWithdrawSetter.setUpdateStatusTask(withdrawRecord.getTenantId(), withdrawRecord.getId(), withdrawRecord.getDescribeApiName());
    }

    private boolean queryWithdrawRecordStatus(IObjectData withdrawRecord) {
        String paymentBusinessId = withdrawRecord.get(WithdrawRecordObjFields.BUSINESS_ID, String.class);
        String fromAccountType = withdrawRecord.get(WithdrawRecordObjFields.TRANSFEROR_ACCOUNT_TYPE, String.class);
        String fromTenantId = withdrawRecord.get(WithdrawRecordObjFields.TRANSFEROR_TENANT_ID, String.class);

        TransferDetail transferDetail = getTransferDetail(fromTenantId, fromAccountType, paymentBusinessId);
        if (transferDetail == null) {
            log.info("transfer detail is null. paymentBusinessId:{}", paymentBusinessId);
            updateWithdrawRecordTransferInfo(withdrawRecord, null, null, WithdrawPaymentStatusEnum.FAIL.code(), "转账发起失败");
        } else if (TransferDetailStatusEnum.FAIL.codes().contains(transferDetail.getStatus())
                || TransferDetailStatusEnum.CANCEL.codes().contains(transferDetail.getStatus())) {
            if (transferDetail.getStatus().equals("15")) {
                transferDetail.setFailReason("取消支付");
            }
            log.info("transfer fail. transferDetail:{}", transferDetail);
            String message = Strings.isNullOrEmpty(transferDetail.getFailReason()) ? "未知转账失败原因" : transferDetail.getFailReason();
            updateWithdrawRecordTransferInfo(withdrawRecord, null, null, WithdrawPaymentStatusEnum.FAIL.code(), message);
        } else if (TransferDetailStatusEnum.SUCCESS.codes().contains(transferDetail.getStatus())) {
            updateWithdrawRecordTransferInfo(withdrawRecord, null, null, WithdrawPaymentStatusEnum.SUCCESS.code(), null, transferDetail);
        } else if ("4".equals(transferDetail.getStatus())) {
            // 状态 4 表示  业务服务费余额不足。 代充值后，继续执行
            log.info("order hanging up. transferDetail:{}", transferDetail);
            updateWithdrawRecordTransferInfo(withdrawRecord, null, null, WithdrawPaymentStatusEnum.PROCESSING.code(), transferDetail.getFailReason());
        } else {
            log.info("transfer processing. transferDetail:{}", transferDetail);
            return true;
        }
        return false;
    }

    private void updateRedPacketStatusWithRewardDetailStatus(String tenantId, String withdrawRecordId, String status, TransferDetail transferDetail) {

        Map<String, Object> updateMap = new HashMap<>(8);
        if (WithdrawPaymentStatusEnum.SUCCESS.code().equals(status)) {
            updateMap.put(RedPacketRecordObjFields.WITHDRAWAL_STATUS, RedPacketRecordObjFields.WithdrawalStatus.WITHDRAWN);
            updateMap.put(RedPacketRecordObjFields.PAYMENT_STATUS, RedPacketRecordObjFields.PaymentStatus.SUCCESS);
            if (transferDetail != null && TransferDetailStatusEnum.SUCCESS.codes().contains(transferDetail.getStatus())) {
                if (!Strings.isNullOrEmpty(transferDetail.getBrokerId())) {
                    updateMap.put(RedPacketRecordObjFields.FROM_CLOUD_ACCOUNT_BROKER_ID, transferDetail.getBrokerId());
                    updateMap.put(RedPacketRecordObjFields.FROM_CLOUD_ACCOUNT_DEALER_ID, transferDetail.getDealerId());
                } else if (!Strings.isNullOrEmpty(transferDetail.getSubMchId())) {
                    updateMap.put(RedPacketRecordObjFields.FROM_WX_SUB_MCH_ID, transferDetail.getSubMchId());
                }
            }
        } else if (WithdrawPaymentStatusEnum.FAIL.code().equals(status) ||
                WithdrawPaymentStatusEnum.EXCEPT.code().equals(status)) {
            updateMap.put(RedPacketRecordObjFields.WITHDRAWAL_STATUS, RedPacketRecordObjFields.WithdrawalStatus.AWAIT);
            updateMap.put(RedPacketRecordObjFields.PAYMENT_STATUS, RedPacketRecordObjFields.PaymentStatus.INIT);
        }

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);

        Filter withdrawRecordIdFilter = new Filter();
        withdrawRecordIdFilter.setFieldName(RedPacketRecordObjFields.WITHDRAW_RECORD_ID);
        withdrawRecordIdFilter.setOperator(Operator.EQ);
        withdrawRecordIdFilter.setFieldValues(Lists.newArrayList(withdrawRecordId));

        query.setFilters(Lists.newArrayList(withdrawRecordIdFilter));
        User user = User.systemUser(tenantId);
        List<String> queryFields = Lists.newArrayList(CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.NAME, CommonFields.ID,
                CommonFields.TENANT_ID, RedPacketRecordObjFields.WITHDRAWAL_STATUS);
        List<String> ids = new ArrayList<>();
        CommonUtils.executeInAllDataWithFields(serviceFacade, user, ApiNames.RED_PACKET_RECORD_OBJ, query, queryFields, dataList -> {
            log.info("execute data size :{}", dataList.size());
            serviceFacade.batchUpdateWithMap(user, dataList, updateMap);
            ids.addAll(dataList.stream().map(DBRecord::getId).collect(Collectors.toList()));
        });
        if (CollectionUtils.isNotEmpty(ids)
                && WithdrawPaymentStatusEnum.SUCCESS.code().equals(status)) {
            //更新活动奖励发放明细 的发放状态
            updateRedPacketRewardDetailStatus(tenantId, ids);
        }
    }

    private void updateRedPacketRewardDetailStatus(String tenantId, List<String> redPacketIds) {
        // 查询从对象 红包奖励明细  RedPacketRecordDetailObj
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);

        Filter redPacketIdFilter = new Filter();
        redPacketIdFilter.setFieldName(RedPacketRecordDetailObjFields.RED_PACKET_RECORD_ID);
        redPacketIdFilter.setOperator(Operator.IN);
        redPacketIdFilter.setFieldValues(redPacketIds);

        query.setFilters(Lists.newArrayList(redPacketIdFilter));

        List<String> queryFields = Lists.newArrayList(CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.NAME, CommonFields.ID,
                CommonFields.TENANT_ID);
        User user = User.systemUser(tenantId);
        List<String> ids = new ArrayList<>();
        CommonUtils.executeInAllDataWithFields(serviceFacade, user, ApiNames.RED_PACKET_RECORD_DETAIL_OBJ, query, queryFields, dataList -> {
            ids.addAll(dataList.stream().map(DBRecord::getId).collect(Collectors.toList()));
        });
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        // 活动奖励发放明细 TPMActivityRewardDetailObj  related_object_data_id 动态关联字段 关联奖励明细
        query.getFilters().clear();
        Filter detailIdsFilter = new Filter();
        detailIdsFilter.setFieldName(TPMActivityRewardDetailFields.RELATED_OBJECT_DATA_ID);
        detailIdsFilter.setOperator(Operator.IN);
        detailIdsFilter.setFieldValues(ids);

        query.setFilters(Lists.newArrayList(detailIdsFilter));
        List<String> queryWithFields = Lists.newArrayList(CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.NAME, CommonFields.ID,
                CommonFields.TENANT_ID, TPMActivityRewardDetailFields.STATUS);

        Map<String, Object> updateMap = new HashMap<>(4);
        updateMap.put(TPMActivityRewardDetailFields.STATUS, TPMActivityRewardDetailFields.Status.DONE);
        CommonUtils.executeInAllDataWithFields(serviceFacade, user, ApiNames.TPM_ACTIVITY_REWARD_DETAIL_OBJ, query, queryWithFields, dataList -> {
            serviceFacade.batchUpdateWithMap(user, dataList, updateMap);
        });
    }

    private TransferDetail getTransferDetail(String fromTenantId, String fromAccountType, String paymentBusinessId) {
        TransferDetail transferDetail = null;
        if (WithdrawRecordObjFields.TransferorAccountType.WECHAT_MERCHANT.equals(fromAccountType)) {
            transferDetail = getWxTransferDetail(fromTenantId, paymentBusinessId);
        } else if (WithdrawRecordObjFields.TransferorAccountType.CLOUD.equals(fromAccountType)) {
            transferDetail = getCloudTransferDetail(fromTenantId, paymentBusinessId);
        } else {
            log.info("暂不支持的账户类型:{}", fromAccountType);
        }
        return transferDetail;
    }

    private TransferDetail getWxTransferDetail(String tenantId, String businessId) {
        QueryWXTenantTransferDetail.Arg batchQueryWXTenantTransferDetailArg = new QueryWXTenantTransferDetail.Arg();
        batchQueryWXTenantTransferDetailArg.setBatchTransferId(businessId);
        batchQueryWXTenantTransferDetailArg.setBusinessId(businessId);
        UserInfo userInfo = UserInfo.builder().userId("-10000").tenantId(tenantId).build();
        QueryWXTenantTransferDetail.Result queryResult = payService.queryWXTenantTransferDetails(userInfo, batchQueryWXTenantTransferDetailArg);
        return CollectionUtils.isNotEmpty(queryResult.getTransferDetails()) ? queryResult.getTransferDetails().get(0) : null;
    }

    private TransferDetail getCloudTransferDetail(String tenantId, String businessId) {
        QueryCloudTransferDetails.Arg arg = new QueryCloudTransferDetails.Arg();
        arg.setBusinessId(businessId);
        UserInfo userInfo = UserInfo.builder().userId("-10000").tenantId(tenantId).build();
        QueryCloudTransferDetails.Result result = payService.queryCloudTransferDetails(userInfo, arg);
        return CollectionUtils.isNotEmpty(result.getTransferDetails()) ? result.getTransferDetails().get(0) : null;
    }

    public void updateWithdrawRecordTransferInfo(IObjectData withdrawRecord,
                                                 String businessId,
                                                 String orderId,
                                                 String status,
                                                 String failMessage) {
        updateWithdrawRecordTransferInfo(withdrawRecord, businessId, orderId, status, failMessage, null);
    }

    public void updateWithdrawRecordTransferInfo(IObjectData withdrawRecord,
                                                 String businessId,
                                                 String orderId,
                                                 String status,
                                                 String failMessage,
                                                 TransferDetail transferDetail) {
        Map<String, Object> updateMap = new HashMap<>();
        String updateMessage = "";
        if (businessId != null) {
            updateMap.put(WithdrawRecordObjFields.TRADING_ID, businessId);
            updateMessage += fromUpdateMessage(WithdrawRecordObjFields.TRADING_ID, businessId);
        }
        if (orderId != null) {
            updateMap.put(WithdrawRecordObjFields.ORDER_ID, orderId);
            updateMessage += fromUpdateMessage(WithdrawRecordObjFields.ORDER_ID, orderId);
        }
        if (status != null) {
            updateMap.put(WithdrawRecordObjFields.PAYMENT_STATUS, status);
            updateMessage += fromUpdateMessage(WithdrawRecordObjFields.PAYMENT_STATUS, status);
            if (WithdrawPaymentStatusEnum.SUCCESS.code().equals(status)) {
                long currentTimeMillis = System.currentTimeMillis();
                updateMap.put(WithdrawRecordObjFields.WITHDRAWAL_SUCCESS_TIME, currentTimeMillis);
            }
        }
        if (failMessage != null) {
            updateMap.put(WithdrawRecordObjFields.PAYMENT_ERROR_MESSAGE, failMessage);
            if (failMessage.isEmpty()) {
                failMessage = "空";
            }
            updateMessage += fromUpdateMessage(WithdrawRecordObjFields.PAYMENT_ERROR_MESSAGE, failMessage);
        }

        if (transferDetail != null && TransferDetailStatusEnum.SUCCESS.codes().contains(transferDetail.getStatus())) {
            if (!Strings.isNullOrEmpty(transferDetail.getBrokerId())) {
                updateMap.put(WithdrawRecordObjFields.FROM_CLOUD_ACCOUNT_BROKER_ID, transferDetail.getBrokerId());
                updateMap.put(WithdrawRecordObjFields.FROM_CLOUD_ACCOUNT_DEALER_ID, transferDetail.getDealerId());
            } else if (!Strings.isNullOrEmpty(transferDetail.getSubMchId())) {
                updateMap.put(WithdrawRecordObjFields.FROM_WX_SUB_MCH_ID, transferDetail.getSubMchId());
            }
        }

        if (updateMap.isEmpty()) {
            return;
        }
        User sysUser = User.systemUser(withdrawRecord.getTenantId());
        serviceFacade.updateWithMap(sysUser, withdrawRecord, updateMap);
        IObjectDescribe objectDescribe = serviceFacade.findObject(withdrawRecord.getTenantId(), withdrawRecord.getDescribeApiName());
        serviceFacade.logWithCustomMessage(sysUser, EventType.MODIFY, ActionType.Modify, objectDescribe, withdrawRecord, updateMessage.substring(0, updateMessage.length() - 1));
        if (!WithdrawPaymentStatusEnum.PROCESSING.code().equals(status)) {
            log.info("Withdraw PaymentStatus code is {}, withdrawRecord id is {}", status, withdrawRecord.getId());
            // 更新红包记录的提现状态///
            updateRedPacketStatusWithRewardDetailStatus(withdrawRecord.getTenantId(), withdrawRecord.getId(), status, transferDetail);
        }
    }

    private String fromUpdateMessage(String apiName, String value) {
        return String.format("字段【%s】 变更为  %s ，", apiName, value);
    }


    private void tryLock(String key) {
        RLock lock = redissonCmd.getLock(key);
        try {
            if (!lock.tryLock(20000, TimeUnit.MILLISECONDS)) {
                throw new RewardFmcgException("20001", I18N.text(I18NKeys.REWARD_WITHDRAW_RECORD_SERVICE_0));
            }
        } catch (InterruptedException e) {
            throw new RewardFmcgException("20001", I18N.text(I18NKeys.REWARD_WITHDRAW_RECORD_SERVICE_1));
        }
    }

    private void unlock(String key) {
        RLock lock = redissonCmd.getLock(key);
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
}
