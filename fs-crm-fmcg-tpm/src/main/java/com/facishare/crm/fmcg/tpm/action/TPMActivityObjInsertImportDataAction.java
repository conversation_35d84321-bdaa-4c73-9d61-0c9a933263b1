package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


@Slf4j
public class TPMActivityObjInsertImportDataAction extends StandardInsertImportDataAction {

    private static final String AP_ID = "_AP_ID";

    @Override
    protected void before(Arg arg) {
        log.info("TPMActivityObjInsertImportDataAction start ...");
        super.before(arg);
        this.dataList.forEach(data -> {
            data.getData().set(AP_ID, data.getRowNo());
        });
    }

    @Override
    protected List<IObjectData> importData(List<IObjectData> validList) {
        log.info("validList size : {}", validList.size());

        List<IObjectData> validateData = new ArrayList<>();
        List<ImportError> errorList = new ArrayList<>();

        for (IObjectData objectData : validList) {
            ActionContext addActionContext = new ActionContext(actionContext.getRequestContext(), ApiNames.TPM_ACTIVITY_OBJ, "Add");
            addActionContext.setAttribute("triggerWorkflow", arg.getIsWorkFlowEnabled());
            addActionContext.setAttribute("triggerFlow", arg.getIsApprovalFlowEnabled());
            addActionContext.setAttribute("importTriggerFlow", arg.getIsApprovalFlowEnabled());
            BaseObjectSaveAction.Arg saveArg = new BaseObjectSaveAction.Arg();
            saveArg.setObjectData(ObjectDataDocument.of(objectData));
            try {
                BaseObjectSaveAction.Result result = serviceFacade.triggerAction(addActionContext, saveArg, BaseObjectSaveAction.Result.class);
                if (Objects.nonNull(result)) {
                    validateData.add(result.getObjectData().toObjectData());
                }
            } catch (Exception exception) {
                log.info("TPMActivityObjInsertImportDataAction triggerAction TPMActivityObjAddAction error", exception);
                buildErrorList(errorList, objectData.get(AP_ID, Integer.class), exception.getMessage());

            }
        }
        mergeErrorList(errorList);
        actionContext.setAttribute("triggerWorkflow", false);
        actionContext.setAttribute("triggerFlow", false);
        return validateData;
    }


    @Override
    protected Result after(Arg arg, Result result) {
        return super.after(arg, result);
    }

    private void buildErrorList(List<ImportError> errorList, int rowNo, String message) {
        ImportError error = new ImportError();
        error.setRowNo(rowNo);
        error.setErrorMessage(message);
        errorList.add(error);
    }

}
