package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.ButtonApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityAgreementFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.business.abstraction.IActivityProofButtonService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import lombok.SneakyThrows;

import java.util.List;

public class TPMActivityProofObjWebDetailController extends StandardWebDetailController {

    private IActivityProofButtonService activityProofButtonService = SpringUtil.getContext().getBean(IActivityProofButtonService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        buttonFilter(result);
        return super.after(arg, result);
    }

    @SneakyThrows
    private void buttonFilter(Result result) {
        User user = controllerContext.getUser();
        if (user.isOutUser()) {
            user = User.systemUser(controllerContext.getTenantId());
        }
        activityProofButtonService.addProofWebDetailCustomButton(controllerContext.getTenantId(), user, this.arg, result);
        IObjectData proof = result.getData().toObjectData();
        String activityId = proof.get(TPMActivityProofFields.ACTIVITY_ID, String.class);
        activityProofButtonService.filterRioStoreConfirmButton(user, activityId, arg.getLayoutAgentType(), result);
    }

}
