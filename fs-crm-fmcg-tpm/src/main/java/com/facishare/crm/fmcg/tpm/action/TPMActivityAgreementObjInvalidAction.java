package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPM2Service;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/11 下午5:03
 */
@SuppressWarnings("Duplicates")
public class TPMActivityAgreementObjInvalidAction extends StandardInvalidAction {

    private final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);
    private final ITPM2Service tpm2Service = SpringUtil.getContext().getBean(ITPM2Service.class);

    private boolean isTpm2Tenant = false;

    @Override
    protected void before(Arg arg) {
        this.isTpm2Tenant = tpm2Service.isTPM2Tenant(Integer.valueOf(actionContext.getTenantId()));

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setNeedRightJoin(false);
        query.setSearchSource("db");

        Filter idFilter = new Filter();
        idFilter.setFieldName(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(arg.getObjectDataId()));

        Filter deletedFilter = new Filter();
        deletedFilter.setFieldName(CommonFields.IS_DELETED);
        deletedFilter.setOperator(Operator.EQ);
        deletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(idFilter, deletedFilter));

        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query).getData();

        if (!data.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.INVALID_AGREEMENT_ERROR_ITEM_USED));
        }

        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        storeBusiness.updateStoreLabel(actionContext.getTenantId(), Lists.newArrayList((String) result.getObjectData().get(TPMActivityAgreementFields.STORE_ID)), AccountFields.CUSTOMER_LABEL_AGREEMENT_STORE, 0);
        BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUpstreamOwnerIdOrUserId()), BuryModule.TPM.TPM_ACTIVITY_AGREEMENT, BuryOperation.DELETE, isTpm2Tenant);
        return super.after(arg, result);
    }
}
