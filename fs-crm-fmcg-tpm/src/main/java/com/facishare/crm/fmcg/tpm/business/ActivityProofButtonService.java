package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.business.abstraction.IActivityProofButtonService;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofAuditSourceConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDetailController;
import com.facishare.paas.appframework.core.predef.controller.BaseListController;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ActivityProofButtonService implements IActivityProofButtonService {

    protected static final ButtonDocument PROOF_AUDIT_BUTTON = new ButtonDocument();
    protected static final ButtonDocument PROOF_CHECK_BUTTON = new ButtonDocument();

    @Resource
    private IActivityTypeManager activityTypeManager;
    @Resource
    private ServiceFacade serviceFacade;

    @Override
    public BaseListController.ButtonInfo addProofCustomButton(String tenantId, User user, boolean includeButtonInfo, BaseListController.ButtonInfo buttonInfo, List<ObjectDataDocument> dataList) {

        if (TPMGrayUtils.isRioTenant(tenantId)) {
            return buttonInfo;
        }
        if (includeButtonInfo && serviceFacade.funPrivilegeCheck(user, ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, "Add")) {
            if (buttonInfo.getButtons() == null) {
                buttonInfo.setButtons(Lists.newArrayList(getProofAuditButton(), getProofCheckButton()));
            } else {
                buttonInfo.getButtons().addAll(Lists.newArrayList(getProofAuditButton(), getProofCheckButton()));
            }

            List<String> activityIds = dataList.stream().map(data -> (String) data.get(TPMActivityProofFields.ACTIVITY_ID)).distinct().collect(Collectors.toList());
            Map<String, Boolean> activityIdIsAuditProofMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(activityIds)) {
                for (String activityId : activityIds) {
                    ActivityTypeExt activityTypeExt;
                    try {
                        activityTypeExt = activityTypeManager.findByActivityId(tenantId, activityId);
                    } catch (Exception e) {
                        log.info("get activityType err.", e);
                        activityIdIsAuditProofMap.put(activityId, false);
                        continue;
                    }

                    ActivityProofAuditSourceConfigEntity activityProofAuditSourceConfigEntity = activityTypeExt.auditSourceConfig();

                    if (Objects.nonNull(activityProofAuditSourceConfigEntity) && ApiNames.TPM_ACTIVITY_PROOF_OBJ.equals(activityProofAuditSourceConfigEntity.getMasterApiName())) {
                        activityIdIsAuditProofMap.put(activityId, true);
                    } else {
                        activityIdIsAuditProofMap.put(activityId, false);
                    }
                }
            }

            List<String> buttonArr1 = Lists.newArrayList("ProofCheck_button_default");
            List<String> buttonArr2 = Lists.newArrayList("ProofAudit_button_default", "ProofCheck_button_default");
            if (buttonInfo.getButtonMap() == null) {
                buttonInfo.setButtonMap(new HashMap<>());
                for (ObjectDataDocument obj : dataList) {
                    if (Boolean.FALSE.equals(activityIdIsAuditProofMap.get((String) obj.get(TPMActivityProofFields.ACTIVITY_ID)))) {
                        continue;
                    }
                    if (TPMActivityProofFields.AUDIT_STATUS__SCHEDULE.equals(obj.get(TPMActivityProofFields.AUDIT_STATUS)) && ObjectLifeStatus.NORMAL.getCode().equals(obj.get(CommonFields.LIFE_STATUS))) {
                        buttonInfo.getButtonMap().put(obj.getId(), buttonArr2);
                    } else {
                        buttonInfo.getButtonMap().put(obj.getId(), buttonArr1);
                    }
                }
            } else {
                for (ObjectDataDocument obj : dataList) {
                    if (Boolean.FALSE.equals(activityIdIsAuditProofMap.get((String) obj.get(TPMActivityProofFields.ACTIVITY_ID)))) {
                        continue;
                    }

                    if (buttonInfo.getButtonMap().containsKey(obj.getId())) {
                        if (TPMActivityProofFields.AUDIT_STATUS__SCHEDULE.equals(obj.get(TPMActivityProofFields.AUDIT_STATUS)) && ObjectLifeStatus.NORMAL.getCode().equals(obj.get(CommonFields.LIFE_STATUS))) {
                            buttonInfo.getButtonMap().get(obj.getId()).addAll(buttonArr2);
                        } else {
                            buttonInfo.getButtonMap().get(obj.getId()).addAll(buttonArr1);
                        }
                    } else {
                        if (TPMActivityProofFields.AUDIT_STATUS__SCHEDULE.equals(obj.get(TPMActivityProofFields.AUDIT_STATUS)) && ObjectLifeStatus.NORMAL.getCode().equals(obj.get(CommonFields.LIFE_STATUS))) {
                            buttonInfo.getButtonMap().put(obj.getId(), buttonArr2);
                        } else {
                            buttonInfo.getButtonMap().put(obj.getId(), buttonArr1);
                        }
                    }
                }
            }
        }

        return buttonInfo;
    }

    @Override
    public void addProofWebDetailCustomButton(String tenantId, User user, AbstractStandardDetailController.Arg arg, AbstractStandardDetailController.Result result) throws MetadataServiceException {
        if (result.getLayout() == null) {
            return;
        }
        List<IComponent> components = result.getLayout().toLayout().getComponents();
        if (TPMGrayUtils.isRioTenant(tenantId)) {
            result.getLayout().toLayout().setComponents(components);
            return;
        }
        ObjectDataDocument data = result.getData();
        ActivityTypeExt activityTypeExt = activityTypeManager.findByActivityId(tenantId, (String) data.getOrDefault(TPMActivityProofFields.ACTIVITY_ID, ""));

        ActivityProofAuditSourceConfigEntity activityProofAuditSourceConfigEntity = activityTypeExt.auditSourceConfig();
        if (Objects.nonNull(activityProofAuditSourceConfigEntity) && ApiNames.TPM_ACTIVITY_PROOF_OBJ.equals(activityProofAuditSourceConfigEntity.getMasterApiName())) {

            for (IComponent component : components) {
                if ("head_info".equals(component.getName())) {
                    List<IButton> buttons = component.getButtons();
                    if (TPMActivityProofFields.AUDIT_STATUS__SCHEDULE.equals(data.get(TPMActivityProofFields.AUDIT_STATUS)) && ObjectLifeStatus.NORMAL.getCode().equals(data.get(CommonFields.LIFE_STATUS))) {
                        buttons.addAll(Lists.newArrayList(getWebDetailProofCheckButton(), getWebDetailProofAuditButton()));
                    } else {
                        buttons.addAll(Lists.newArrayList(getWebDetailProofCheckButton()));
                    }
                    component.setButtons(buttons);
                }
            }
            if ("mobile".equals(arg.getLayoutAgentType())) {
                List<IButton> buttons = result.getLayout().toLayout().getButtons();
                if (TPMActivityProofFields.AUDIT_STATUS__SCHEDULE.equals(data.get(TPMActivityProofFields.AUDIT_STATUS)) && ObjectLifeStatus.NORMAL.getCode().equals(data.get(CommonFields.LIFE_STATUS))) {
                    buttons.addAll(Lists.newArrayList(getWebDetailProofCheckButton(), getWebDetailProofAuditButton()));
                } else {
                    buttons.addAll(Lists.newArrayList(getWebDetailProofCheckButton()));
                }
                result.getLayout().toLayout().setButtons(buttons);
            }
        }

        result.getLayout().toLayout().setComponents(components);
    }

    @Override
    public void filterRioStoreConfirmButton(User user, String activityId, String layoutAgentType, AbstractStandardDetailController.Result result) throws MetadataServiceException {
        if (result.getLayout() == null) {
            return;
        }
        if (TPMGrayUtils.isRioTenant(user.getTenantId())) {
            if (Strings.isNullOrEmpty(activityId)) {
                return;
            }
            List<IComponent> components = result.getLayout().toLayout().getComponents();

            IObjectData activity = serviceFacade.findObjectDataIgnoreAll(user, activityId, ApiNames.TPM_ACTIVITY_OBJ);
            String activityConfirm = activity.get(TPMActivityFields.IS_NEED_RIO_STORE_CONFIRM, String.class);
            if (!"1".equals(activityConfirm)) {
                components.stream().filter(component -> "head_info".equals(component.getName())).findFirst().ifPresent(component -> {
                    List<IButton> buttons = component.getButtons();
                    buttons.removeIf(button -> ButtonApiNames.REGISTRATION_INVITATION.equals(button.getName()));
                    component.setButtons(buttons);
                });

                if ("mobile".equals(layoutAgentType)) {
                    List<IButton> buttons = result.getLayout().toLayout().getButtons();
                    buttons.removeIf(button -> ButtonApiNames.REGISTRATION_INVITATION.equals(button.getName()));
                    result.getLayout().toLayout().setButtons(buttons);
                }
            }
            if (!"mobile".equals(layoutAgentType)) {
                components.stream().filter(component -> "head_info".equals(component.getName())).findFirst().ifPresent(component -> {
                    List<IButton> buttons = component.getButtons();
                    buttons.removeIf(button -> ButtonApiNames.REGISTRATION_INVITATION.equals(button.getName()));
                    component.setButtons(buttons);
                });
            }
            result.getLayout().toLayout().setComponents(components);
        }
    }

    @Override
    public void hideRioStoreConfirmButtonInList(String tenantId, StandardListController.Arg arg, BaseListController.Result result) throws MetadataServiceException {
        if (arg.isIncludeButtonInfo()) {
            if (result.getButtonInfo().getButtonMap() != null) {
                if (TPMGrayUtils.isRioTenant(tenantId)) {
                    List<String> activityIds = result.getDataList().stream().map(v -> (String) v.get(TPMActivityAgreementFields.ACTIVITY_ID)).collect(Collectors.toList());
                    Map<String, IObjectData> activityDataMap = serviceFacade.findObjectDataByIds(tenantId, activityIds, ApiNames.TPM_ACTIVITY_OBJ).stream().collect(Collectors.toMap(DBRecord::getId, v -> v, (a, b) -> a));
                    for (ObjectDataDocument obj : result.getDataList()) {
                        String activityId = (String) obj.get(TPMActivityAgreementFields.ACTIVITY_ID);
                        if (activityDataMap.containsKey(activityId)) {
                            String storeConfirmStatus = (String) activityDataMap.get(activityId).get(TPMActivityFields.IS_NEED_RIO_STORE_CONFIRM);
                            if ("1".equals(storeConfirmStatus)) {
                                if (result.getButtonInfo().getButtonMap().containsKey(obj.getId())) {
                                    result.getButtonInfo().getButtonMap().get(obj.getId()).remove(ButtonApiNames.REGISTRATION_INVITATION);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private ButtonDocument getProofCheckButton() {
        PROOF_CHECK_BUTTON.put("_id", "ProofCheck_button_default");
        PROOF_CHECK_BUTTON.put("api_name", "ProofCheck_button_default");
        PROOF_CHECK_BUTTON.put("action", "ProofCheck");
        PROOF_CHECK_BUTTON.put("label", I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_PROOF_CHECK));
        PROOF_CHECK_BUTTON.put("action_type", "system");
        PROOF_CHECK_BUTTON.put("actions", Lists.newArrayList());
        PROOF_CHECK_BUTTON.put("button_type", "common");
        PROOF_CHECK_BUTTON.put("describe_api_name", "TPMActivityProofObj");
        PROOF_CHECK_BUTTON.put("is_active", true);
        PROOF_CHECK_BUTTON.put("is_deleted", false);
        PROOF_CHECK_BUTTON.put("use_pages", Lists.newArrayList("list"));
        PROOF_CHECK_BUTTON.put("where", Lists.newArrayList());

        return PROOF_CHECK_BUTTON;
    }

    private ButtonDocument getProofAuditButton() {
        PROOF_AUDIT_BUTTON.put("_id", "ProofAudit_button_default");
        PROOF_AUDIT_BUTTON.put("api_name", "ProofAudit_button_default");
        PROOF_AUDIT_BUTTON.put("action", "ProofAudit");
        PROOF_AUDIT_BUTTON.put("label", I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_PROOF_AUDIT));
        PROOF_AUDIT_BUTTON.put("action_type", "system");
        PROOF_AUDIT_BUTTON.put("actions", Lists.newArrayList());
        PROOF_AUDIT_BUTTON.put("button_type", "common");
        PROOF_AUDIT_BUTTON.put("describe_api_name", "TPMActivityProofObj");
        PROOF_AUDIT_BUTTON.put("is_active", true);
        PROOF_AUDIT_BUTTON.put("is_deleted", false);
        PROOF_AUDIT_BUTTON.put("use_pages", Lists.newArrayList("list"));
        PROOF_AUDIT_BUTTON.put("where", Lists.newArrayList());

        return PROOF_AUDIT_BUTTON;
    }

    private IButton getWebDetailProofCheckButton() {
        IButton button = new Button();
        button.setName("ProofCheck_button_default");
        button.setIsHidden(false);
        button.setAction("ProofCheck");
        button.setLabel(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_PROOF_CHECK));
        button.setActionType("common");
        return button;
    }

    private IButton getWebDetailProofAuditButton() {
        IButton button = new Button();
        button.setName("ProofAudit_button_default");
        button.setIsHidden(false);
        button.setAction("ProofAudit");
        button.setLabel(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_PROOF_AUDIT));
        button.setActionType("common");
        return button;
    }
}
