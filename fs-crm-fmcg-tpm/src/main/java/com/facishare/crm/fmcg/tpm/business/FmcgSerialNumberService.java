package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.common.adapter.EnterpriseConnectionService;
import com.facishare.crm.fmcg.common.adapter.exception.RewardFmcgException;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.constant.ScanCodeActionConstants;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.business.abstraction.IFmcgSerialNumberService;
import com.facishare.crm.fmcg.tpm.business.dto.StorageFlowInfoDTO;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.fcp.util.MD5Util;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fmcg.framework.http.FMCGSnProxy;
import com.fmcg.framework.http.MengNiuProxy;
import com.fmcg.framework.http.contract.mengniu.QueryMarkCodeSource;
import com.fmcg.framework.http.contract.sales.QuerySnAction;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Author: linmj
 * Date: 2023/9/27 16:19
 */
@Slf4j
@Component
public class FmcgSerialNumberService implements IFmcgSerialNumberService {

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private FMCGSnProxy fmcgSnProxy;

    @Resource
    private MengNiuProxy mengNiuProxy;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    protected EnterpriseConnectionService enterpriseConnectionService;

    private static final Cache<String, Map<String, String>> actionIdCache = CacheBuilder.newBuilder().maximumSize(3000).expireAfterWrite(30, TimeUnit.MINUTES).build();

    private static final Cache<String, Map<String, String>> actionUniqueIdCache = CacheBuilder.newBuilder().maximumSize(3000).expireAfterWrite(30, TimeUnit.MINUTES).build();

    private static final Cache<String, String> statusExceptionCache = CacheBuilder.newBuilder().maximumSize(2000).expireAfterWrite(30, TimeUnit.SECONDS).build();

    private static Map<String, List<String>> ALLOW_SALES_OUT_STORE_ID_MAP = new HashMap<>();

    private static String CLIENT_ID;
    private static String SK;

    private static final int BILL_TYPE_SELL = 203;

    static {
        ConfigFactory.getConfig("fs-fmcg-tpm-config", iconfig -> {
            String jsonStr = iconfig.get("ALLOW_SALES_OUT_STORE_ID_MAP");
            if (!Strings.isNullOrEmpty(jsonStr)) {
                ALLOW_SALES_OUT_STORE_ID_MAP = JSON.parseObject(jsonStr, new TypeReference<Map<String, List<String>>>() {
                });
            }
            ALLOW_SALES_OUT_STORE_ID_MAP.putIfAbsent("777421", Lists.newArrayList("64a8cb022f35960001793186"));
            ALLOW_SALES_OUT_STORE_ID_MAP.putIfAbsent("40163027", Lists.newArrayList("65522453f856b20001c17d23"));
        });
        ConfigFactory.getConfig("gray-rel-fmcg", iconfig -> {
            CLIENT_ID = iconfig.get("sales_mengniu_openapi_client_id");
            SK = iconfig.get("sales_mengniu_openapi_sk");
        });
    }

    @Override
    public List<IObjectData> queryAllSerialNumberStatusBySerialNumberId(String tenantId, String serialNumberId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(-1);

        Filter serialNumberFilter = new Filter();
        serialNumberFilter.setFieldName(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID);
        serialNumberFilter.setFieldValues(Lists.newArrayList(serialNumberId));
        serialNumberFilter.setOperator(Operator.EQ);

        query.setFilters(Lists.newArrayList(serialNumberFilter));
        List<IObjectData> dataList = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.FMCG_SERIAL_NUMBER_STATUS_OBJ, query, Lists.newArrayList(CommonFields.ID, FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID, FMCGSerialNumberStatusFields.ACCOUNT_ID, FMCGSerialNumberStatusFields.ACTION_ID, FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, FMCGSerialNumberStatusFields.PERSONNEL_ID, FMCGSerialNumberStatusFields.NEXT_BUSINESS_ID, FMCGSerialNumberStatusFields.CHANNEL_TYPE, FMCGSerialNumberStatusFields.PERSONNEL_API_NAME, FMCGSerialNumberStatusFields.BUSINESS_OCCURRENCE_TIME, FMCGSerialNumberStatusFields.BUSINESS_OBJECT_NAME, FMCGSerialNumberStatusFields.BUSINESS_OBJECT_ID, FMCGSerialNumberStatusFields.BUSINESS_OBJECT, CommonFields.OBJECT_DESCRIBE_API_NAME));
        dataList.sort((a, b) -> -a.get(FMCGSerialNumberStatusFields.BUSINESS_OCCURRENCE_TIME, Long.class, 0L).compareTo(b.get(FMCGSerialNumberStatusFields.BUSINESS_OCCURRENCE_TIME, Long.class, 0L)));

        return dataList;
    }

    @Override
    public IObjectData querySerialNumberByName(String tenantId, String name) {
        if (Strings.isNullOrEmpty(name)) {
            return null;
        }
        IFilter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.NAME);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(name));

        SearchTemplateQuery query = QueryDataUtil.minimumQuery(idFilter);
        query.setLimit(1);

        List<IObjectData> data = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.FMCG_SERIAL_NUMBER_OBJ, query,
                Lists.newArrayList("_id", CommonFields.TENANT_ID, FMCGSerialNumberFields.PRODUCT_ID, FMCGSerialNumberFields.MANUFACTURE_DATE)
        );
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        return data.get(0);
    }

    @Override
    public IObjectData getStoreSignSerialNumberStatusObj(String tenantId, String snCodeId, boolean enableSablesOutLogic) {
        String storeSignActionId = getActionIdByActionUniqueId(tenantId, ScanCodeActionConstants.STORE_SIGN);
        String returnGoodsActionId = getActionIdByActionUniqueId(tenantId, ScanCodeActionConstants.RETURN_GOODS);
        String exchangeReturnActionId = getActionIdByActionUniqueId(tenantId, ScanCodeActionConstants.EXCHANGE_RETURN_IN);
        String storeCheckActionId = getActionIdByActionUniqueId(tenantId, ScanCodeActionConstants.STORE_STOCK_CHECK);
        List<String> storeSignActionIds = Lists.newArrayList(storeSignActionId, storeCheckActionId);
        List<IObjectData> actions = getActionsDescByActionIds(tenantId, snCodeId, Lists.newArrayList(storeSignActionId, returnGoodsActionId, exchangeReturnActionId, storeCheckActionId));
        try {
            if (CollectionUtils.isEmpty(actions)) {
                throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_FMCG_SERIAL_NUMBER_SERVICE_2));
            }
            String lastStatusActionId = actions.get(0).get(FMCGSerialNumberStatusFields.ACTION_ID, String.class);
            if (returnGoodsActionId.equals(lastStatusActionId) || exchangeReturnActionId.equals(lastStatusActionId)) {
                throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_FMCG_SERIAL_NUMBER_SERVICE_3));
            }

            return actions.stream().filter(status -> storeSignActionIds.contains(status.get(FMCGSerialNumberStatusFields.ACTION_ID, String.class)))
                    .findFirst().orElseThrow(() -> new RewardFmcgException("10010", "商品码未签收。"));//ignorei18n
        } catch (RewardFmcgException e) {
            if (enableSablesOutLogic) {
                return getSalesOutSerialNumberStatusObj(tenantId, snCodeId);
            }
            throw e;
        }

    }

    private IObjectData getSalesOutSerialNumberStatusObj(String tenantId, String snCodeId) {
        List<String> allowList = getSalesOutStoreIds(tenantId);
        if (CollectionUtils.isEmpty(allowList)) {
            log.info("tenantId:{},allowList is empty.", tenantId);
            throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_FMCG_SERIAL_NUMBER_SERVICE_2));
        }
        String salesOutActionId = getActionIdByActionUniqueId(tenantId, ScanCodeActionConstants.SALES_OUT_OF_WAREHOUSE);
        List<IObjectData> actions = getActionsDescByActionIds(tenantId, snCodeId, Lists.newArrayList(salesOutActionId));
        if (CollectionUtils.isEmpty(actions)) {
            log.info("tenantId:{},sales Out actions is empty.", tenantId);
            throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_FMCG_SERIAL_NUMBER_SERVICE_2));
        }
        IObjectData status = actions.get(0);
        String currentTenantId = status.get(FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, String.class);
        String storeId = enterpriseConnectionService.getStoreId(tenantId, currentTenantId);
        if (!allowList.contains(storeId)) {
            log.info("tenantId:{},storeId:{},allowList:{}", tenantId, storeId, allowList);
            throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_FMCG_SERIAL_NUMBER_SERVICE_2));
        }
        //替换门店为经销商门店
        status.set(FMCGSerialNumberStatusFields.ACCOUNT_ID, storeId);
        log.info("use sale out logic,tenantId:{},snCodeId:{},storeId:{}", tenantId, snCodeId, storeId);
        return status;
    }

    private List<String> getSalesOutStoreIds(String tenantId) {
        return ALLOW_SALES_OUT_STORE_ID_MAP.getOrDefault(tenantId, Lists.newArrayList());
    }

    private List<IObjectData> getActionsDescByActionIds(String tenantId, String snCodeId, List<String> actionIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(100);
        query.setOffset(0);

        Filter codeFilter = new Filter();
        codeFilter.setFieldName(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID);
        codeFilter.setOperator(Operator.EQ);
        codeFilter.setFieldValues(Lists.newArrayList(snCodeId));

        Filter actionFilter = new Filter();
        actionFilter.setFieldName(FMCGSerialNumberStatusFields.ACTION_ID);
        actionFilter.setOperator(Operator.IN);
        actionFilter.setFieldValues(actionIds);

        query.setFilters(Lists.newArrayList(codeFilter, actionFilter));

        List<IObjectData> actions = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.FMCG_SERIAL_NUMBER_STATUS_OBJ, query, Lists.newArrayList(CommonFields.ID, FMCGSerialNumberStatusFields.CHANNEL_TYPE, FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID, FMCGSerialNumberStatusFields.ACCOUNT_ID, FMCGSerialNumberStatusFields.ACTION_ID, FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, FMCGSerialNumberStatusFields.PERSONNEL_API_NAME, FMCGSerialNumberStatusFields.PERSONNEL_ID, FMCGSerialNumberStatusFields.NEXT_BUSINESS_ID, FMCGSerialNumberStatusFields.BUSINESS_OBJECT_ID, FMCGSerialNumberStatusFields.BUSINESS_OCCURRENCE_TIME, CommonFields.OBJECT_DESCRIBE_API_NAME));

        actions.sort((a, b) -> -a.get(FMCGSerialNumberStatusFields.BUSINESS_OCCURRENCE_TIME, Long.class, 0L).compareTo(b.get(FMCGSerialNumberStatusFields.BUSINESS_OCCURRENCE_TIME, Long.class, 0L)));
        return actions;
    }


    @Override
    public IObjectData getProductObjFromSerialNumberObj(String tenantId, String productId) {
        if (Strings.isNullOrEmpty(productId)) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_FMCG_SERIAL_NUMBER_SERVICE_0));
        }

        IFilter idFilter = new Filter();
        idFilter.setFieldName("_id");
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(productId));

        SearchTemplateQuery query = QueryDataUtil.minimumQuery(idFilter);
        query.setLimit(1);

        List<IObjectData> data = QueryDataUtil.find(serviceFacade, tenantId, "ProductObj", query,
                Lists.newArrayList("_id", ProductFields.NAME, ProductFields.PRODUCT_CODE, ProductFields.QUALITY_GUARANTEE_PERIOD)
        );
        if (CollectionUtils.isEmpty(data)) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_FMCG_SERIAL_NUMBER_SERVICE_1));
        }
        return data.get(0);
    }

    @Override
    public IObjectData getSerialNumberObjByRealCode(String tenantId, String realCode) {
        IFilter codeFilter = new Filter();
        codeFilter.setFieldName(FMCGSerialNumberFields.CODE_TRUE);
        codeFilter.setOperator(Operator.EQ);
        codeFilter.setFieldValues(Lists.newArrayList(realCode));

        SearchTemplateQuery query = QueryDataUtil.minimumQuery(codeFilter);
        query.setLimit(1);

        List<IObjectData> data = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.FMCG_SERIAL_NUMBER_OBJ, query,
                Lists.newArrayList("_id", FMCGSerialNumberFields.PRODUCT_ID, FMCGSerialNumberFields.MANUFACTURE_DATE)
        );
        if (CollectionUtils.isEmpty(data)) {
            throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_FMCG_SERIAL_NUMBER_SERVICE_4));
        }
        return data.get(0);
    }

    @Override
    public String getActionIdByActionUniqueId(String tenantId, String actionUniqueId) {
        Map<String, String> map = actionIdCache.getIfPresent(tenantId);
        if (map == null) {
            map = new HashMap<>();
            List<QuerySnAction.FMCGSerialNumberActionEntity> actionEntities = getActions(tenantId, actionUniqueId);
            if (!CollectionUtils.isEmpty(actionEntities)) {
                for (QuerySnAction.FMCGSerialNumberActionEntity action : actionEntities) {
                    map.put(action.getUniqueId(), action.getId());
                }
            }
            actionIdCache.put(tenantId, map);
        } else {
            if (map.get(actionUniqueId) == null) {
                List<QuerySnAction.FMCGSerialNumberActionEntity> actionEntities = getActions(tenantId, actionUniqueId);
                if (!CollectionUtils.isEmpty(actionEntities)) {
                    for (QuerySnAction.FMCGSerialNumberActionEntity action : actionEntities) {
                        map.put(action.getUniqueId(), action.getId());
                    }
                }
            }
        }
        String id = map.get(actionUniqueId);
        log.info("actionUniqueId:{},id:{}", actionUniqueId, id);
        return id;
    }

    @Override
    public String getActionUniqueIdByActionId(String tenantId, String actionId) {
        if (Strings.isNullOrEmpty(actionId)) {
            return null;
        }
        Map<String, String> map = actionUniqueIdCache.getIfPresent(tenantId);
        if (map == null) {
            map = new HashMap<>();
            List<QuerySnAction.FMCGSerialNumberActionEntity> actionEntities = getActions(tenantId, null);
            if (!CollectionUtils.isEmpty(actionEntities)) {
                for (QuerySnAction.FMCGSerialNumberActionEntity action : actionEntities) {
                    map.put(action.getId(), action.getUniqueId());
                }
            }
            actionUniqueIdCache.put(tenantId, map);
        } else {
            if (map.get(actionId) == null) {
                List<QuerySnAction.FMCGSerialNumberActionEntity> actionEntities = getActions(tenantId, null);
                if (!CollectionUtils.isEmpty(actionEntities)) {
                    for (QuerySnAction.FMCGSerialNumberActionEntity action : actionEntities) {
                        map.put(action.getId(), action.getUniqueId());
                    }
                }
            }
        }
        String id = map.get(actionId);
        log.info("actionUniqueId:{},id:{}", actionId, id);
        return id;
    }

    @Override
    public String getLastStatusExceptionTypes(String tenantId, String snId, String actionId) {
        String key = String.format("%s_%s_%s", tenantId, snId, actionId);
        try {
            return statusExceptionCache.get(key, () -> {
                SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                        SearchQueryUtil.filter(FMCGSerialNumberStatusFields.ACTION_ID, Operator.EQ, Lists.newArrayList(actionId)),
                        SearchQueryUtil.filter(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID, Operator.EQ, Lists.newArrayList(snId)),
                        SearchQueryUtil.filter(FMCGSerialNumberStatusFields.WHETHER_ABNORMAL, Operator.EQ, Lists.newArrayList("true"))
                ));
                query.setOrders(Lists.newArrayList(new OrderBy(FMCGSerialNumberStatusFields.BUSINESS_OCCURRENCE_TIME, false)));
                List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.FMCG_SERIAL_NUMBER_STATUS_OBJ, query,
                        Lists.newArrayList(CommonFields.ID, FMCGSerialNumberStatusFields.WHETHER_ABNORMAL, FMCGSerialNumberStatusFields.EXCEPTION_TYPE, FMCGSerialNumberStatusFields.BUSINESS_OCCURRENCE_TIME, FMCGSerialNumberStatusFields.ACTION_ID, CommonFields.OBJECT_DESCRIBE_API_NAME));
                if (CollectionUtils.isNotEmpty(data)) {
                    log.info("data:{}", data);
                    return data.get(0).get(FMCGSerialNumberStatusFields.EXCEPTION_TYPE, String.class);
                }
                return "";
            });
        } catch (ExecutionException e) {
            log.info("get last status exception types error", e);
            throw new ValidateException("获取码状态异常失败。");//ignorei18n
        }
    }

    @Override
    public List<String> filterSerialNumberByDealerBlackList(String tenantId, List<IObjectData> serialNumberList, List<IObjectData> dealerBlackList) {
        try {
            // 参数校验
            if (CollectionUtils.isEmpty(serialNumberList) || CollectionUtils.isEmpty(dealerBlackList)) {
                return Collections.emptyList();
            }

            // 1. 获取所有码的ID
            List<String> serialNumberIdList = serialNumberList.stream()
                    .map(DBRecord::getId)
                    .collect(Collectors.toList());

            // 2. 查询所有码的签收入库状态，只保留最新一条
            List<IObjectData> statusList = querySignInStatusList(tenantId, serialNumberIdList);

            // 3. 构建有码状态的serialId集合
            Set<String> hasStatusSerialIdSet = statusList.stream()
                    .map(status -> status.get(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID, String.class))
                    .collect(Collectors.toSet());

            // 4. 分离有码状态和无码状态的serialNumber对象
            List<IObjectData> hasStatusSerialList = serialNumberList.stream()
                    .filter(sn -> hasStatusSerialIdSet.contains(sn.getId()))
                    .collect(Collectors.toList());
            List<IObjectData> noStatusSerialList = serialNumberList.stream()
                    .filter(sn -> !hasStatusSerialIdSet.contains(sn.getId()))
                    .collect(Collectors.toList());

            // 5. 有码状态的，走本地过滤逻辑
            List<String> blackListByStatus = Collections.emptyList();
            if (CollectionUtils.isNotEmpty(hasStatusSerialList)) {
                log.info("hasStatusSerialList size is {}", hasStatusSerialList.size());
                try {
                    List<String> accountIdList = dealerBlackList.stream()
                            .map(v -> v.get("account_id__c", String.class))
                            .collect(Collectors.toList());
                    // 只传有码状态的statusList
                    List<IObjectData> filteredStatusList = statusList.stream()
                            .filter(status -> hasStatusSerialIdSet.contains(status.get(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID, String.class)))
                            .collect(Collectors.toList());
                    blackListByStatus = filterSerialByBlackList(tenantId, filteredStatusList, accountIdList);
                } catch (Exception e) {
                    log.error("处理有码状态序列号时发生错误, tenantId: {}", tenantId, e);
                }
            }

            // 6. 无码状态的，走蒙牛Proxy逻辑
            List<String> blackListByProxy = Collections.emptyList();
            if (CollectionUtils.isNotEmpty(noStatusSerialList)) {
                log.info("noStatusSerialList size is {}", noStatusSerialList.size());
                try {
                    List<String> serialNumberNameList = noStatusSerialList.stream()
                            .map(v -> v.get(CommonFields.NAME, String.class))
                            .collect(Collectors.toList());
                    List<String> accountNoList = dealerBlackList.stream()
                            .map(v -> v.get("account_no__c", String.class))
                            .collect(Collectors.toList());
                    blackListByProxy = filterSerialByBlackList(serialNumberNameList, accountNoList);
                } catch (Exception e) {
                    log.error("处理无码状态序列号时发生错误", e);
                }
            }

            // 7. 合并两个结果
            List<String> result = new ArrayList<>();
            result.addAll(blackListByStatus);
            result.addAll(blackListByProxy);
            return result;

        } catch (Exception e) {
            log.error("filterSerialNumberByDealerBlackList error, tenantId: {}", tenantId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<String> filterSerialNumberByActivityBlackList(String tenantId, List<IObjectData> serialNumberList, String activityId) {
        // 通过 activity_id 查询黑名单对象serial_number_dealer_blacklist__c，匹配activityId的数据
        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName("activity_id__c");
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        SearchTemplateQuery query = QueryDataUtil.minimumQuery(activityIdFilter);
        List<IObjectData> blackListObjs = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                "serial_number_dealer_blacklist__c",
                query,
                Lists.newArrayList(CommonFields.ID, CommonFields.TENANT_ID, CommonFields.OBJECT_DESCRIBE_API_NAME,
                        "activity_id__c","account_id__c", "account_no__c")
        );
        return filterSerialNumberByDealerBlackList(tenantId, serialNumberList, blackListObjs);
    }

    /**
     * 查询签收入库状态的码状态对象，按创建时间倒序排序，并且每个码只保留最新一条
     */
    private List<IObjectData> querySignInStatusList(String tenantId, List<String> serialNumberList) {
        if (CollectionUtils.isEmpty(serialNumberList)) {
            return Collections.emptyList();
        }
        // 签收入库
        String signCodeSignActionId = getActionIdByActionUniqueId(tenantId, ScanCodeActionConstants.SIGN_CODE_SIGN);
        // 其他入库 / 扫码入库
        String otherReturnInId = getActionIdByActionUniqueId(tenantId, ScanCodeActionConstants.OTHER_RETURN_IN);
        log.info("querySignInStatusList serialNumberList size :{}", serialNumberList.size());
        Filter serialNumberFilter = new Filter();
        serialNumberFilter.setFieldName(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID);
        serialNumberFilter.setOperator(Operator.IN);
        serialNumberFilter.setFieldValues(serialNumberList);

        Filter actionFilter = new Filter();
        actionFilter.setFieldName(FMCGSerialNumberStatusFields.ACTION_ID);
        actionFilter.setOperator(Operator.IN);
        actionFilter.setFieldValues(Lists.newArrayList(signCodeSignActionId, otherReturnInId));

        SearchTemplateQuery statusQuery = new SearchTemplateQuery();
        statusQuery.setFilters(Lists.newArrayList(serialNumberFilter, actionFilter));
        statusQuery.setLimit(-1);
        // 按照 CREATE_TIME 倒序排序
        statusQuery.setOrders(Lists.newArrayList(new OrderBy(CommonFields.CREATE_TIME, false)));

        List<IObjectData> allStatusList = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.FMCG_SERIAL_NUMBER_STATUS_OBJ,
                statusQuery,
                Lists.newArrayList(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID, FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, FMCGSerialNumberStatusFields.ACCOUNT_ID, CommonFields.CREATE_TIME, FMCGSerialNumberStatusFields.BUSINESS_OCCURRENCE_TIME)
        );

        // 只保留每个码最新的一条
        Map<String, IObjectData> latestStatusMap = new LinkedHashMap<>();
        for (IObjectData status : allStatusList) {
            String serialId = status.get(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID, String.class);
            if (!latestStatusMap.containsKey(serialId)) {
                latestStatusMap.put(serialId, status);
            }
        }
        return new ArrayList<>(latestStatusMap.values());
    }

    /**
     * 提取serialNumber与企业ID的映射
     */
    private Map<String, Integer> extractSerialToAccountMap(List<IObjectData> statusList) {
        Map<String, Integer> serialToAccountMap = new HashMap<>();
        for (IObjectData status : statusList) {
            String serialId = status.get(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID, String.class);
            String tenantId = status.get(FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, String.class);
            if (!Strings.isNullOrEmpty(serialId) && !Strings.isNullOrEmpty(tenantId)) {
                serialToAccountMap.put(serialId, Integer.parseInt(tenantId));
            }
        }
        return serialToAccountMap;
    }

    /**
     * 查询互联企业对象，获取企业ID与客户ID的映射
     */
    private Map<String, String> queryAccountToCustomerMap(String tenantId, List<String> enterpriseList) {
        if (CollectionUtils.isEmpty(enterpriseList)) {
            return Collections.emptyMap();
        }

        log.info("queryAccountToCustomerMap enterpriseList is :{}", enterpriseList);

        Filter accountFilter = new Filter();
        accountFilter.setFieldName("enterprise_account");
        accountFilter.setOperator(Operator.IN);
        accountFilter.setFieldValues(enterpriseList);

        SearchTemplateQuery relationQuery = new SearchTemplateQuery();
        relationQuery.setFilters(Lists.newArrayList(accountFilter));
        relationQuery.setLimit(-1);

        List<IObjectData> relationList = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ENTERPRISE_RELATION_OBJ,
                relationQuery,
                Lists.newArrayList(CommonFields.ID, CommonFields.OBJECT_DESCRIBE_API_NAME,"mapper_account_id", "enterprise_account")
        );

        Map<String, String> accountToCustomerMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(relationList)) {
            for (IObjectData relation : relationList) {
                String enterpriseId = relation.get("enterprise_account", String.class);
                String accountId = relation.get("mapper_account_id", String.class);
                if (!Strings.isNullOrEmpty(accountId) && !Strings.isNullOrEmpty(enterpriseId)) {
                    accountToCustomerMap.put(enterpriseId, accountId);
                }
            }
        }
        return accountToCustomerMap;
    }

    private Map<Integer, String> getEnterpriseIdToAccount(Collection<Integer> enterpriseList) {
        return eieaConverter.enterpriseIdToAccount(enterpriseList);
    }

    /**
     * 过滤出黑名单serialNumber
     */
    private List<String> filterSerialByBlackList(String tenantId, List<IObjectData> statusList, List<String> accountIdList) {
        try {
            List<String> resultSerialNumbers = new ArrayList<>();
            if (CollectionUtils.isEmpty(statusList) || CollectionUtils.isEmpty(accountIdList)) {
                return resultSerialNumbers;
            }
            log.info("filterSerialByBlackList 02 dealerBlackList size {}, statusList size {} tenantId: {}", accountIdList.size(), statusList.size(), tenantId);

            // 提取 serialNumber 与企业ID(tenantId) 的映射
            Map<String, Integer> serialToTenantMap = extractSerialToAccountMap(statusList);
            if (serialToTenantMap.isEmpty()) {
                return Collections.emptyList();
            }

            Map<Integer, String> enterpriseIdToAccount = getEnterpriseIdToAccount(serialToTenantMap.values());

            // 查询互联企业对象，获取企业ID(tenantId)/企业账号EA 与客户ID(accountId) 的映射
            Map<String, String> tenantToAccountMap = queryAccountToCustomerMap(tenantId, new ArrayList<>(enterpriseIdToAccount.values()));
            if (tenantToAccountMap.isEmpty()) {
                return Collections.emptyList();
            }

            for (Map.Entry<String, Integer> entry : serialToTenantMap.entrySet()) {
                String serialId = entry.getKey();
                Integer curTenantId = entry.getValue();
                String accountId = tenantToAccountMap.get(enterpriseIdToAccount.getOrDefault(curTenantId, ""));
                if (accountId != null && accountIdList.contains(accountId)) {
                    resultSerialNumbers.add(serialId);
                }
            }
            log.info("filterSerialByBlackList resultSerialNumbers is {}", resultSerialNumbers);
            return resultSerialNumbers;
        } catch (Exception e) {
            log.error("filterSerialByBlackList error, tenantId: {}, statusList size: {}, accountIdList size: {}", tenantId,
                    statusList != null ? statusList.size() : 0,
                    accountIdList != null ? accountIdList.size() : 0, e);
            return Collections.emptyList();
        }
    }

    /**
     * 当签收入库状态数据为空时，调用mengNiuProxy.queryMarkCodeRelation，按需求过滤黑名单
     */
    private List<String> filterSerialByBlackList(List<String> serialNumberNameList, List<String> accountNoList) {
        List<String> resultSerialNumbers = new ArrayList<>();
        if (CollectionUtils.isEmpty(serialNumberNameList) || CollectionUtils.isEmpty(accountNoList)) {
            return resultSerialNumbers;
        }
        log.info("filterSerialByBlackList 01 dealerBlackList size {}", accountNoList.size());
        for (String serialNumberName : serialNumberNameList) {
            try {
                // 调用mengNiuProxy.queryMarkCodeRelation
                QueryMarkCodeSource.Arg arg = new QueryMarkCodeSource.Arg();
                JSONObject serialJson = new JSONObject();
                serialJson.put("markCode", serialNumberName);
                arg.setRequestData(serialJson.toJSONString());
                long time = System.currentTimeMillis();
                String sign = MD5Util.toMD5Hex((CLIENT_ID + SK + time).getBytes()).toUpperCase(Locale.ROOT);
                QueryMarkCodeSource.Result data = mengNiuProxy.queryMarkCodeSource(CLIENT_ID, sign, Long.toString(time), arg);

                if (data == null || data.getData() == null) {
                    log.warn("No data returned for serialNumber: {}", serialNumberName);
                    continue;
                }
                JSONArray storageFlowInfoVoList = ((JSONObject) data.getData()).getJSONArray("storageFlowInfoVoList");
                if (CollectionUtils.isEmpty(storageFlowInfoVoList)) {
                    log.info("No storageFlowInfoVoList for serialNumber: {}", serialNumberName);
                    continue;
                }
                log.info("storageFlowInfoVoList size is {} for serialNumber: {}", storageFlowInfoVoList.size(), serialNumberName);

                List<StorageFlowInfoDTO> storageFlowInfoList = storageFlowInfoVoList.toJavaList(StorageFlowInfoDTO.class);
                List<String> sellOrgNameList = storageFlowInfoList.stream()
                        .filter(v -> Objects.nonNull(v.getBillType()) && v.getBillType().equals(BILL_TYPE_SELL))
                        .map(StorageFlowInfoDTO::getSellOrgName)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(sellOrgNameList)) {
                    log.info("No sellOrgName found for billType {} in serialNumber: {}", BILL_TYPE_SELL, serialNumberName);
                    continue;
                }
                boolean isBlack = sellOrgNameList.stream()
                        .map(name -> {
                            String trimmed = name.trim();
                            String[] parts = trimmed.split("\\s+");
                            if (parts.length > 1) {
                                return parts[1];
                            } else {
                                log.warn("sellOrgName format unexpected: '{}' for serialNumber: {}", name, serialNumberName);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .anyMatch(accountNoList::contains);
                if (isBlack) {
                    resultSerialNumbers.add(serialNumberName);
                }
            } catch (Exception e) {
                log.warn("filterByMengNiuProxy error, serialNumber: {}, error: {}", serialNumberName, e.getMessage(), e);
            }
        }
        log.info("filterSerialByBlackList filterByMengNiuProxy resultSerialNumbers is {}", resultSerialNumbers);
        return resultSerialNumbers;
    }

    private List<QuerySnAction.FMCGSerialNumberActionEntity> getActions(String tenantId, String actionUniqueId) {
        QuerySnAction.Arg querySnActionArg = new QuerySnAction.Arg();
        if (!Strings.isNullOrEmpty(actionUniqueId)) {
            querySnActionArg.setUniqueId(actionUniqueId);
        }
        QuerySnAction.Result querySnActionResult = fmcgSnProxy.querySnAction(Integer.valueOf(tenantId), -10000, querySnActionArg);
        if (querySnActionResult.getErrCode() != 0) {
            throw new RewardFmcgException(String.valueOf(querySnActionResult.getErrCode()), querySnActionResult.getErrMessage());
        }
        return querySnActionResult.getResult().getActions();
    }
}
