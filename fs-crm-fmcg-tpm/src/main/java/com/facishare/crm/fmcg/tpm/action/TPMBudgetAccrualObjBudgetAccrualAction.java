package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.tpm.business.BudgetAccrualService;
import com.facishare.crm.fmcg.tpm.business.BudgetOperatorFactory;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetAccountDetailService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetOperator;
import com.facishare.crm.fmcg.tpm.business.abstraction.IQueryService;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.service.TransactionProxy;
import com.facishare.crm.fmcg.tpm.utils.TraceUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectApprovalAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/22 下午5:24
 */
//IgnoreI18nFile
public class TPMBudgetAccrualObjBudgetAccrualAction extends BaseObjectApprovalAction<TPMBudgetAccrualObjBudgetAccrualAction.Arg, TPMBudgetAccrualObjBudgetAccrualAction.Result> {

    private static Logger logger = LoggerFactory.getLogger(TPMBudgetAccrualObjBudgetAccrualAction.class);

    private TransactionProxy transactionProxy = SpringUtil.getContext().getBean(TransactionProxy.class);

    private IQueryService queryService = SpringUtil.getContext().getBean(IQueryService.class);

    private IBudgetAccountDetailService budgetAccountDetailService = SpringUtil.getContext().getBean(IBudgetAccountDetailService.class);

    private Map<String, IBudgetOperator> budgetOperatorMap = Maps.newHashMap();

    private IObjectData objectData;

    private List<IObjectData> details = Lists.newArrayList();

    private User systemUser;

    private String businessId;

    private String approvalId;

    private static final BudgetAccrualService budgetAccrualService = SpringUtil.getContext().getBean(BudgetAccrualService.class);

    private boolean isTriggerException = true;

    private boolean isAllowAccrualNegativeMoney = false;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.BUDGET_ACCRUAL.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getDataId());
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);

        checkIfExecuted();

    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.isNotEmpty(dataList)) {
            this.objectData = this.dataList.get(0);
            this.details.addAll(queryService.queryDataListByRelatedField(actionContext.getTenantId(), ApiNames.TPM_BUDGET_ACCRUAL_DETAIL_OBJ, TPMBudgetAccrualDetailFields.ACCRUAL_ID, this.objectData.getId()));
            this.systemUser = User.systemUser(actionContext.getTenantId());
            this.businessId = TraceUtil.initBusinessTraceId();
            this.approvalId = TraceUtil.initApprovalTraceId();
            this.isAllowAccrualNegativeMoney = TPMGrayUtils.isAllowAccrualNegativeMoney(actionContext.getTenantId());
        }
    }


    @Override
    protected Result doAct(Arg arg) {

        this.tryLockBudget();
        //order amount big - > small
        //聚合查询 因为目标对象是同一个 所以可能花费超额  lock可以用线程lock 锁一个就行
        transactionProxy.run(() -> {

            Map<String, List<IObjectData>> budgetId2DetailsMap = new HashMap<>();

            this.details.forEach(detail -> fillBudgetIdMap(detail, budgetId2DetailsMap));

            dealBudgetTrade(budgetId2DetailsMap);

        });


        return new Result();
    }

    private void checkIfExecuted() {
        String relatedApiName = this.objectData.get(TPMBudgetAccrualFields.RELATED_OBJECT_API_NAME, String.class);
        if ("cy_test_fail_obj__c".equals(relatedApiName)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCRUAL_OBJ_BUDGET_ACCRUAL_ACTION_0));
        }

        if (!CommonFields.LIFE_STATUS__NORMAL.equals(this.objectData.get(CommonFields.LIFE_STATUS))) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCRUAL_OBJ_BUDGET_ACCRUAL_ACTION_1));
        }
        List<IObjectData> needAccrualDetails = new ArrayList<>();

        this.details.forEach(detail -> {
            if (CollectionUtils.isEmpty(budgetAccountDetailService.queryDetailsByRelatedData(actionContext.getTenantId(), detail.getDescribeApiName(), detail.getId()))) {
                needAccrualDetails.add(detail);
            }
        });
        if (CollectionUtils.isEmpty(needAccrualDetails)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCRUAL_OBJ_BUDGET_ACCRUAL_ACTION_2));
        }
        this.details = needAccrualDetails;
    }

    private void dealBudgetTrade(Map<String, List<IObjectData>> budgetId2DetailsMap) {
        for (String budgetId : budgetId2DetailsMap.keySet()) {
            BigDecimal baseAmount = BigDecimal.ZERO;
            List<IObjectData> details = budgetId2DetailsMap.get(budgetId);
            for (IObjectData detail : details) {
                baseAmount = baseAmount.add(detail.get(TPMBudgetAccrualDetailFields.ACTUAL_AMOUNT, BigDecimal.class, BigDecimal.ZERO));
            }
            IBudgetOperator operator = budgetOperatorMap.get(budgetId);
            if (!TPMBudgetAccountFields.BUDGET_STATUS__ENABLE.equals(operator.getAccount().get(TPMBudgetAccountFields.BUDGET_STATUS))) {
                throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_ACCRUAL_OBJ_BUDGET_ACCRUAL_ACTION_3), operator.getAccount().getName()));
            }

            if (this.isAllowAccrualNegativeMoney && baseAmount.compareTo(BigDecimal.ZERO) < 0) {
                operator.validateConsumableAmount(baseAmount.negate());
            }
            //先增加金额 再扣减 避免先负数情况
            details.sort((a, b) -> -a.get(TPMBudgetAccrualDetailFields.ACTUAL_AMOUNT, BigDecimal.class, BigDecimal.ZERO).compareTo(b.get(TPMBudgetAccrualDetailFields.ACTUAL_AMOUNT, BigDecimal.class, BigDecimal.ZERO)));
            List<IObjectData> updateDetails = new ArrayList<>();
            details.forEach(detail -> {
                operator.setWhat(detail);
                BigDecimal amount = detail.get(TPMBudgetAccrualDetailFields.ACTUAL_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
                if (amount.compareTo(BigDecimal.ZERO) >= 0) {
                    operator.income(amount);
                    updateDetails.add(detail);
                } else {
                    //todo:暂时不处理负数情况，也不做入账计提处理。
                    // operator.expenditure(amount);
                    if (this.isAllowAccrualNegativeMoney) {
                        operator.expenditure(amount.negate());
                        updateDetails.add(detail);
                    }
                }
            });

            //only this details those have been related by account detail need update status.
            Map<String, Object> updateMap = new HashMap<>();
            updateMap.put(TPMBudgetAccrualDetailFields.STATUS, TPMBudgetAccrualDetailFields.Status.INCLUDE);
            serviceFacade.batchUpdateWithMap(systemUser, updateDetails, updateMap);
            operator.recalculate();
        }
    }

    private void fillBudgetIdMap(IObjectData detail, Map<String, List<IObjectData>> budgetId2DetailsMap) {
        String budgetId = detail.get(TPMBudgetAccrualDetailFields.BUDGET_ACCOUNT_ID, String.class);
        List<IObjectData> details = budgetId2DetailsMap.getOrDefault(budgetId, Lists.newArrayList());
        details.add(detail);
        budgetId2DetailsMap.putIfAbsent(budgetId, details);
    }

    private void tryLockBudget() {
        this.details.forEach(detail -> {
            String budgetAccountId = detail.get(TPMBudgetAccrualDetailFields.BUDGET_ACCOUNT_ID, String.class);
            IBudgetOperator operator = BudgetOperatorFactory.initOperator(BizType.ACCRUAL, systemUser, budgetAccountId, businessId, approvalId);
            budgetOperatorMap.putIfAbsent(operator.getAccount().getId(), operator);
            if (!operator.tryLock()) {
                throw new ValidateException("lock fail, please retry.");
            }
        });
    }

    @Data
    public static class Arg {

        @SerializedName("objectDataId")
        @JSONField(name = "objectDataId")
        @JsonProperty("objectDataId")
        private String dataId;

        private boolean skipTriggerApprovalFlow;

        private Map<String, Object> callbackData;

        public static Arg of(Map<String, Object> callbackData, String dataId) {
            Arg arg = new Arg();
            arg.setDataId(dataId);
            arg.setSkipTriggerApprovalFlow(true);
            arg.setCallbackData(callbackData);
            return arg;
        }
    }


    @Data
    public static class Result {
        private ObjectDataDocument objectData;

        public static Result of(IObjectData objectData) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            return result;
        }
    }


    @Override
    protected void finallyDo() {
        try {
            super.finallyDo();
            if (isTriggerException) {
                dealException();
            }
        } finally {
            if (CollectionUtils.isNotEmpty(budgetOperatorMap.values())) {
                this.budgetOperatorMap.values().forEach(IBudgetOperator::unlock);
            }
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result finalResult = super.after(arg, result);
        isTriggerException = false;
        return finalResult;
    }

    private void dealException() {
        User user = User.systemUser(actionContext.getTenantId());
        if (this.objectData == null || Strings.isNullOrEmpty(this.objectData.get(TPMBudgetAccrualFields.RELATED_OBJECT_DATA_ID, String.class))) {
            logger.warn("no objectData or related object data is null");
            return;
        }
        IObjectData triggerData = serviceFacade.findObjectDataIncludeDeleted(user, this.objectData.get(TPMBudgetAccrualFields.RELATED_OBJECT_DATA_ID, String.class), this.objectData.get(TPMBudgetAccrualFields.RELATED_OBJECT_API_NAME, String.class));
        List<String> crmUserIds = serviceFacade.getUsersByRole(systemUser, "00000000000000000000000000000006");
        budgetAccrualService.updateAccrualStatus(user, triggerData, "数据关联预算计提单明细未入账，单据编号：" + this.objectData.getName(), true, crmUserIds.stream().map(Integer::parseInt).collect(Collectors.toSet()), this.objectData);
    }


}
