package com.facishare.crm.fmcg.tpm.business.abstraction;

import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/9/29 17:35
 */
public interface IBudgetSubjectService {

    List<String> queryLowerIds(String tenantId, String subjectId, int level);

    boolean isParent(String tenantId, String parent, String subjectId);

    String queryParentId(String tenantId, String subjectId);

    boolean notTheSpecifiedLevel(String tenantId, String subjectId, int level);
}