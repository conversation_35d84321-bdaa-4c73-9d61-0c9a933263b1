package com.facishare.crm.fmcg.tpm.reward.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * Author: linmj
 * Date: 2024/5/15 16:25
 */
public interface SelfDefineReward {


    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "tenantId")
        @JsonProperty(value = "tenantId")
        @SerializedName("tenantId")
        private String tenantId;

        @JSONField(name = "serialNumberStatusId")
        @JsonProperty(value = "serialNumberStatusId")
        @SerializedName("serialNumberStatusId")
        private String serialNumberStatusId;

        @JSONField(name = "businessId")
        @JsonProperty(value = "businessId")
        @SerializedName("businessId")
        private String businessId;

        @JSONField(name = "isAllowStoreCheck")
        @JsonProperty(value = "isAllowStoreCheck")
        @SerializedName("isAllowStoreCheck")
        private Boolean isAllowStoreCheck;
    }


    @Data
    @ToString
    class Result implements Serializable {

        @JSONField(name = "tips")
        @JsonProperty(value = "tips")
        @SerializedName("tips")
        private String tips;

        @JSONField(name = "serialNumberId")
        @JsonProperty(value = "serialNumberId")
        @SerializedName("serialNumberId")
        private String serialNumberId;

        @JSONField(name = "activityId")
        @JsonProperty(value = "activityId")
        @SerializedName("activityId")
        private String activityId;

    }
}
