package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.business.TPM2Service;
import com.facishare.crm.fmcg.tpm.business.abstraction.IActivityProofButtonService;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.SneakyThrows;

import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 4:39 PM
 */
public class TPMActivityProofObjListController extends StandardListController {

    private TPM2Service tpm2Service = SpringUtil.getContext().getBean(TPM2Service.class);
    private IActivityProofButtonService activityProofButtonService = SpringUtil.getContext().getBean(IActivityProofButtonService.class);


    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        if (TPMGrayUtils.TPMUseEs(controllerContext.getTenantId())) {
            query.setSearchSource("es");
        }
        super.beforeQueryData(query);
    }

    @SneakyThrows
    @Override
    protected Result after(Arg arg, Result result) {
        Result fianalResult = super.after(arg, result);
        if (tpm2Service.isTPM2Tenant(Integer.valueOf(controllerContext.getTenantId()))) {
            fakeButton(arg, fianalResult);
            activityProofButtonService.hideRioStoreConfirmButtonInList(controllerContext.getTenantId(), arg, fianalResult);
        }
        return fianalResult;
    }

    private void fakeButton(Arg arg, Result result) {

        User user = controllerContext.getUser();
        if (user.isOutUser()) {
            user = User.systemUser(controllerContext.getTenantId());
        }
        boolean includeButtonInfo = arg.isIncludeButtonInfo();
        ButtonInfo buttonInfo = result.getButtonInfo();
        List<ObjectDataDocument> dataList = result.getDataList();
        String tenantId = controllerContext.getTenantId();

        ButtonInfo buttonInfoResult = activityProofButtonService.addProofCustomButton(tenantId, user, includeButtonInfo, buttonInfo, dataList);
        result.setButtonInfo(buttonInfoResult);
    }
}
