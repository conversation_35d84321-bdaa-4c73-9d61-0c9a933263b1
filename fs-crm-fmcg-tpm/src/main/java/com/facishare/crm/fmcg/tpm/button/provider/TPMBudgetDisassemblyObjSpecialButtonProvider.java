package com.facishare.crm.fmcg.tpm.button.provider;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.button.abs.AbstractTPMSpecialButtonProvider;
import com.facishare.crm.fmcg.tpm.utils.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public class TPMBudgetDisassemblyObjSpecialButtonProvider extends AbstractTPMSpecialButtonProvider {


    @Override
    public String getApiName() {
        return ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ;
    }

    @Override
    public List<IButton> getSpecialButtons() {

        List<IButton> buttons = super.getSpecialButtons();
        buttons.add(ButtonUtils.buildButton(ObjectAction.DISASSEMBLY_RETRY));
        return buttons;
    }
}
