package com.facishare.crm.fmcg.tpm.reward.outernotify;

import com.alibaba.fastjson.JSON;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import de.mkammerer.argon2.Argon2;
import de.mkammerer.argon2.Argon2Factory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Component
@SuppressWarnings("Duplicates")
@Slf4j
public class RewardsNotifyAuthenticationCmd {

    public static void main(String[] args) {
        String appId = "MN_REWARDS_z702Nm5y";
        String secret = "XLFnIQ1ktl~g_Qlp";
        String messageId = "0A786ACE00015D9548B960469CB64FF6";

        RewardsNotifyAuthenticationCmd cmd = new RewardsNotifyAuthenticationCmd();
        cmd.argon2 = Argon2Factory.create();

        String token = cmd.innerSign(appId, secret, messageId);
        log.info("token : {}", token);

        boolean auth = cmd.innerVerify(appId, secret, messageId, token);
        log.info("auth : {}", auth);
    }

    private static final Map<String, Object> JWT_HEADER = new HashMap<>();

    static {
        JWT_HEADER.put("typ", "JWT");
        JWT_HEADER.put("alg", "HS256");
    }

    private Map<String, String> secretConfig;
    private Argon2 argon2;

    @PostConstruct
    void init() {
        if (Objects.isNull(argon2)) {
            argon2 = Argon2Factory.create();
        }

        ConfigFactory.getConfig("fs-fmcg-framework-config", conf -> {
            String data = conf.get("meng-niu-rewards-notify-secret");
            if (Strings.isNullOrEmpty(data)) {
                this.secretConfig = new HashMap<>();
            } else {
                this.secretConfig = JSON.parseObject(data, new com.alibaba.fastjson.TypeReference<Map<String, String>>() {
                });
            }
        });
    }

    public String sign(String appId, String messageId) {
        if (!this.secretConfig.containsKey(appId)) {
            throw new MetaDataBusinessException("app id not supported");
        }
        return innerSign(appId, this.secretConfig.get(appId), messageId);
    }

    public boolean verify(String appId, String messageId, String token) {
        if (!this.secretConfig.containsKey(appId)) {
            return false;
        }
        try {
            return innerVerify(appId, this.secretConfig.get(appId), messageId, token);
        } catch (Exception ex) {
            log.error("verify exception : ", ex);
            return false;
        }
    }

    private String innerSign(String appId, String secret, String messageId) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.SECOND, 120);

        long expires = cal.getTime().getTime() / 1000;
        String ticket = argon2.hash(3, 65536, 1, String.format("A.%s_M.%s_T.%s", appId, messageId, expires).toCharArray());

        Algorithm alg = Algorithm.HMAC256(secret);
        return JWT.create().withHeader(JWT_HEADER)
                .withClaim("TICKET", ticket)
                .withExpiresAt(cal.getTime())
                .sign(alg);
    }

    private boolean innerVerify(String appId, String secret, String messageId, String token) {
        JWTVerifier verifier = JWT.require(Algorithm.HMAC256(secret)).build();
        DecodedJWT decoded = verifier.verify(token);

        long expires = decoded.getExpiresAt().getTime() / 1000;
        String ticket = decoded.getClaim("TICKET").asString();

        return argon2.verify(ticket, String.format("A.%s_M.%s_T.%s", appId, messageId, expires).toCharArray());
    }
}
