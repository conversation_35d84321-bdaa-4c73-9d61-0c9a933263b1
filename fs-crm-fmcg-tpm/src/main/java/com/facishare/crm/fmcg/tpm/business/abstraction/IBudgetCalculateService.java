package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.ControlStrategyType;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.dao.pg.config.MetadataTransactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/8 下午2:39
 */
@MetadataTransactional
public interface IBudgetCalculateService {

    void recalculateBudgetAmount(User user, String budgetAccountId);

    Map<String, BigDecimal> getRealBudgetAmountMap(User user, String budgetAccountId);

    Map<String, BigDecimal> batchGetBudgetAvailableAmount(User user, List<String> budgetAccountIds);

    void updateBudgetAmountFields(User user, String budgetAccountId, Map<String, BigDecimal> amountMap);

    boolean checkBudgetAllowDeduction(User user, ControlStrategyType controlStrategyType, List<String> relatedBudgetAccountIds, List<String> brotherBudgetAccountIds, BigDecimal needAmount);

    void recalculateStaticDepartmentAmount(User user, String budgetId);
}
