package com.facishare.crm.fmcg.tpm.session.impl;

import java.util.*;

import com.facishare.converter.impl.EIEAConverterImpl;
import com.facishare.crm.fmcg.tpm.service.AppAdminService;
import com.facishare.crm.fmcg.tpm.session.model.SessionContent;
import com.facishare.crm.fmcg.tpm.session.model.SessionContentSyncErrorActivity;
import com.facishare.qixin.api.model.EmployeeId;

import com.facishare.crm.fmcg.tpm.session.SessionSendService;
import com.facishare.qixin.api.constant.MessageType;
import com.facishare.qixin.api.constant.OSS1SubCategory;
import com.facishare.qixin.api.model.AuthInfo;
import com.facishare.qixin.api.model.LastItem;
import com.facishare.qixin.api.model.message.content.OTTemplateMessage;
import com.facishare.qixin.api.model.pushSession.arg.ExternalMessage;
import com.facishare.qixin.api.model.pushSession.arg.PushOSS1SessionArg;
import com.facishare.qixin.api.model.session.PushItem;
import com.facishare.qixin.api.service.PushSessionService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @author: wuyx
 * @description:
 * @createTime: 2022/1/6 14:23
 */
//IgnoreI18nFile
@Slf4j
@Component
public class SessionSendServiceImpl implements SessionSendService {
    @Resource(name = "pushSessionSendService")
    private PushSessionService pushSessionService;

    @Resource
    protected EIEAConverterImpl eieaConverterImpl;

    @Autowired
    private AppAdminService appAdminService;

    /**
     * 项目管理Session
     *
     * @param arg
     */
    @Override
    public void pushOSS1SessionProjectManage(PushOSS1SessionArg arg) {
        pushSessionService.pushOSS1SessionProjectManage(arg);
    }

    /**
     * OSS1统一session入口
     *
     * @param arg
     */
    @Override
    public void pushOSS1Session(PushOSS1SessionArg arg) {
        pushSessionService.pushOSS1Session(arg);
    }

    @Override
    public void sendTextMessageDefault(String message, String ea, List<Integer> employeeIds) {
        sendTextMessage(message, ea, employeeIds, OSS1SubCategory.WQZS);
    }

    @Override
    public void sendTextMessage(String message, String ea, List<Integer> employeeIds, OSS1SubCategory category) {
        ExternalMessage externalMessage = new ExternalMessage();
        externalMessage.setMessgeType(MessageType.TEXT);// 文档类型
        externalMessage.setTextContent(message);

        // 通知栏提醒
        PushItem pushItem = new PushItem();
        pushItem.setPushMessage(message);
        pushItem.setIsWithSound(true);

        LastItem lastItem = new LastItem();
        lastItem.setLastTime(System.currentTimeMillis());
        lastItem.setSummary("文件");

        PushOSS1SessionArg pushArg = new PushOSS1SessionArg();
        pushArg.setNotReadCount(0);
        pushArg.setPushItem(pushItem); // 通知栏
        pushArg.setLastItem(lastItem); // 更改最后通知提示
        pushArg.setEnterpriseAccount(ea);
        pushArg.setEmployeeIds(employeeIds); // 接受者
        pushArg.setOss1SubCategory(category); // session类型
        if (Objects.isNull(category)) {
            pushArg.setOss1SubCategory(OSS1SubCategory.WJTZ); // session类型
        }
        pushArg.setExternalMessage(externalMessage);
        AuthInfo authInfo = new AuthInfo();
        EmployeeId employeeId = EmployeeId.build(ea, 0);
        authInfo.setEmployeeId(employeeId);
        authInfo.setAppId(SessionContent.APP_ID);

        pushArg.setAuthInfo(authInfo);
        pushOSS1Session(pushArg);
    }


    /**
     * 接口实现中发送session
     *
     * @param receivers 接收人列表
     */
    @Override
    public void doSendSession(String ea, List<Integer> receivers, SessionContent sessionContent, int flag) {
        if (CollectionUtils.isEmpty(receivers)) {
            return;
        }

        ExternalMessage externalMessage = new ExternalMessage();
        externalMessage.setMessgeType(MessageType.TEMPLATE);
        // 标题
        OTTemplateMessage otTemplateMessageContent = new OTTemplateMessage();
        OTTemplateMessage.Title title = new OTTemplateMessage.Title();
        title.content = sessionContent.title;
        title.color = "#333333";
        // 副标题
        title.time = sessionContent.subtitle;
        otTemplateMessageContent.title = title;

        OTTemplateMessage.Frist first = new OTTemplateMessage.Frist();
        first.content = sessionContent.first;
        otTemplateMessageContent.first = first;

        // label:value
        List<OTTemplateMessage.Info> infos = Lists.newArrayList();
        for (Map<String, StringBuilder> contentLine : sessionContent.content) {
            OTTemplateMessage.Info info = new OTTemplateMessage.Info();
            Object label = contentLine.get("label");
            Object value = contentLine.get("value");
            if (Objects.nonNull(label)) {
                info.label = label.toString();
            }
            if (Objects.nonNull(value)) {
                info.value = value.toString();
            }
            infos.add(info);
        }
        otTemplateMessageContent.infos = infos;

        // 详情点击
        if (!Strings.isNullOrEmpty(sessionContent.link)) {
            OTTemplateMessage.Button button = new OTTemplateMessage.Button();
            button.url = sessionContent.link;
            button.title = StringUtils.isEmpty(sessionContent.buttonTitle) ? "查看详情" : sessionContent.buttonTitle;
            otTemplateMessageContent.button = button;
        }

        // 通知栏提醒
        PushItem pushItem = new PushItem();
        pushItem.setPushMessage(sessionContent.pushTitle);
        pushItem.setIsWithSound(true);

        // 更改通知栏最后提示
        LastItem lastItem = new LastItem();
        lastItem.setLastTime(System.currentTimeMillis());
        lastItem.setSummary(sessionContent.title);

        externalMessage.setOtTemplateMessageContent(otTemplateMessageContent);

        if (flag == 1) {
            doSendSession(receivers, externalMessage, OSS1SubCategory.WQZS, pushItem, lastItem, ea);
        } else if (flag == 0) {
            doSendSession(receivers, externalMessage, OSS1SubCategory.KQZS, pushItem, lastItem, ea);
        } else if (flag == 2) {
            doSendSession(receivers, externalMessage, OSS1SubCategory.WJTZ, pushItem, lastItem, ea);
        } else if (flag == 3) {
            doSendSession(receivers, externalMessage, OSS1SubCategory.TPM, pushItem, lastItem, ea);
        }
    }

    @Override
    public void doSendSessionToSystemAdmins(String tenantId, SessionContent sessionContent, int flag) {
        String ea = eieaConverterImpl.enterpriseIdToAccount(Integer.parseInt(tenantId));

        // 查询应用管理员
        List<Integer> receivers = appAdminService.getAppAdminList(ea);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(receivers)) {
            log.info("企业未设置[应用管理员] tenantId={}", tenantId);
            return;
        }
        doSendSession(ea, receivers, sessionContent, flag);
    }

    private void doSendSession(List<Integer> employeeIds, ExternalMessage externalMessage, OSS1SubCategory oss1SubCategory,
                               PushItem pushItem, LastItem lastItem, String ea) {
        PushOSS1SessionArg arg = new PushOSS1SessionArg();

        if (oss1SubCategory == OSS1SubCategory.WJTZ) {
            arg.setNotReadCount(0);
        }

        if (Objects.nonNull(pushItem)) {
            arg.setPushItem(pushItem); // 通知栏
        }
        // 更改最后通知提示
        if (Objects.nonNull(lastItem)) {
            arg.setLastItem(lastItem);
        }
        arg.setEmployeeIds(employeeIds); // 接受者
        arg.setOss1SubCategory(oss1SubCategory); // session类型
        arg.setExternalMessage(externalMessage); // 内容

        AuthInfo authInfo = new AuthInfo();
        authInfo.setAppId(SessionContent.APP_ID);

        EmployeeId employeeId = EmployeeId.build(ea, 0);
        authInfo.setEmployeeId(employeeId);

        arg.setAuthInfo(authInfo);
        arg.setEnterpriseAccount(ea);
        try {
            log.info("send message start arg:{}", new Gson().toJson(arg));
            pushOSS1Session(arg);
            log.info("send message success. receivers {}, message type {}, ea {}", employeeIds, oss1SubCategory.getValue(), ea);
        } catch (Exception e) {
            log.error("send message error:", e);
        }
    }
}
