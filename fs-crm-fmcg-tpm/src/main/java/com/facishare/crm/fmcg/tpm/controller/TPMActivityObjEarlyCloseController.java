package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 4:39 PM
 */
//IgnoreI18nFile
@SuppressWarnings("Duplicates")
public class TPMActivityObjEarlyCloseController extends PreDefineController<TPMActivityObjEarlyCloseController.Arg, TPMActivityObjEarlyCloseController.Result> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected Result doService(Arg arg) {
        IObjectData data = serviceFacade.findObjectData(controllerContext.getUser(), arg.getObjectDataId(), ApiNames.TPM_ACTIVITY_OBJ);

        String status = data.get(TPMActivityFields.ACTIVITY_STATUS, String.class);
        if (!Objects.equals(status, TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EARLY_CLOSE_CONTROLLER_0));
        }

        Map<String, Object> updater = Maps.newHashMap();
        updater.put(TPMActivityFields.ACTIVITY_STATUS, TPMActivityFields.ACTIVITY_STATUS__CLOSED);
        updater.put("early_close_reason__c", arg.getReason());
        updater.put("early_close_description__c", arg.getDescription());

        IObjectData newData = serviceFacade.updateWithMap(controllerContext.getUser(), data, updater);

        IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ);
        serviceFacade.logWithCustomMessage(
                controllerContext.getUser(),
                EventType.MODIFY,
                ActionType.MODIFY,
                describe,
                newData,
                "关闭活动。"
        );
        return Result.builder().objectData(ObjectDataDocument.of(newData)).build();
    }

    @Data
    @ToString
    public static class Arg implements Serializable {

        @JSONField(name = "object_data_id")
        @JsonProperty("object_data_id")
        @SerializedName("object_data_id")
        private String objectDataId;

        @JSONField(name = "reason")
        @JsonProperty("reason")
        @SerializedName("reason")
        private String reason;

        @JSONField(name = "description")
        @JsonProperty("description")
        @SerializedName("description")
        private String description;
    }

    @Data
    @ToString
    @Builder
    public static class Result implements Serializable {

        @SerializedName("object_data")
        @JSONField(name = "object_data")
        @JsonProperty("object_data")
        private ObjectDataDocument objectData;
    }
}
