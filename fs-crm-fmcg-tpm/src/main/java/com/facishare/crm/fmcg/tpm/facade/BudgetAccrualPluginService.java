package com.facishare.crm.fmcg.tpm.facade;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetAccrualService;
import com.facishare.crm.fmcg.tpm.business.dto.BudgetConsumeSession;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActionModeType;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.domain.*;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fmcg.framework.http.ApprovalFlowProxy;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/9/21 下午3:42
 */

@Slf4j
@Service
@ServiceModule("budget_accrual")
public class BudgetAccrualPluginService {

    private static final Set<String> APPROVAL_TRIGGER_TYPE = Sets.newHashSet("1", "2");

    @Resource
    private IBudgetAccrualService budgetAccrualService;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private ApprovalFlowProxy approvalFlowProxy;

    @ServiceMethod("add_after")
    public AddActionDomainPlugin.Result after(AddActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        log.info("arg:{}", JSON.toJSONString(arg));
        IObjectData masterData = arg.getObjectData().toObjectData();
        String lifeStatus = masterData.get(CommonFields.LIFE_STATUS, String.class);
        if (CommonFields.LIFE_STATUS__NORMAL.equals(lifeStatus)) {
            budgetAccrualService.triggerAccrualRule(serviceContext.getTenantId(), ActionModeType.ADD.getKey(), masterData, null);
        }
        return new AddActionDomainPlugin.Result();
    }

    @ServiceMethod("add_finally_do")
    public AddActionDomainPlugin.Result finallyDo(AddActionDomainPlugin.Arg arg, ServiceContext serviceContext) {

        return new AddActionDomainPlugin.Result();
    }

    @ServiceMethod("increment_update_after")
    public IncrementUpdateActionDomainPlugin.Result after(IncrementUpdateActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        log.info("arg:{}", JSON.toJSONString(arg));
        IObjectData masterData = arg.getObjectData().toObjectData();
        String lifeStatus = masterData.get(CommonFields.LIFE_STATUS, String.class);
        if (CommonFields.LIFE_STATUS__NORMAL.equals(lifeStatus)) {
            budgetAccrualService.triggerAccrualRule(serviceContext.getTenantId(), ActionModeType.CHANGE.getKey(), masterData, ObjectDataExt.toMap(masterData));
        }
        return new IncrementUpdateActionDomainPlugin.Result();
    }

    @ServiceMethod("increment_update_finally_do")
    public IncrementUpdateActionDomainPlugin.Result finallyDo(IncrementUpdateActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        return new IncrementUpdateActionDomainPlugin.Result();
    }

    @ServiceMethod("edit_after")
    public EditActionDomainPlugin.Result after(EditActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        log.info("arg:{}", JSON.toJSONString(arg));
        IObjectData masterData = arg.getObjectData().toObjectData();
        String lifeStatus = masterData.get(CommonFields.LIFE_STATUS, String.class);
        if (CommonFields.LIFE_STATUS__NORMAL.equals(lifeStatus)) {
            budgetAccrualService.triggerAccrualRule(serviceContext.getTenantId(), ActionModeType.CHANGE.getKey(), masterData, diff(serviceContext.getTenantId(), arg.getDbMasterData().toObjectData(), masterData));
        }
        return new EditActionDomainPlugin.Result();
    }

    @ServiceMethod("edit_finally_do")
    public EditActionDomainPlugin.Result finallyDo(EditActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        return new EditActionDomainPlugin.Result();
    }

    @ServiceMethod("flow_completed_before")
    public FlowCompletedActionDomainPlugin.Result before(FlowCompletedActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        log.info("arg:{}", JSON.toJSONString(arg));

        if("test_approval_fail__c".endsWith(arg.getObjectApiName())){
            if("pass".equals(arg.getStatus())){
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCRUAL_PLUGIN_SERVICE_0));
            }
        }

        //可能需要对生命状态做回退
        if (!APPROVAL_TRIGGER_TYPE.contains(arg.getTriggerType())) {
            return new FlowCompletedActionDomainPlugin.Result();
        }

        BudgetConsumeSession session = new BudgetConsumeSession(arg.getObjectData().getId());
        session.setAttribute("flow_before_life_status", String.valueOf(arg.getDbData().get(CommonFields.LIFE_STATUS)));
        return new FlowCompletedActionDomainPlugin.Result();
    }

    @ServiceMethod("flow_completed_after")
    public FlowCompletedActionDomainPlugin.Result after(FlowCompletedActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        IObjectData masterData = arg.getObjectData().toObjectData();
        BudgetConsumeSession session = new BudgetConsumeSession(masterData.getId());
        if ("pass".equals(arg.getStatus())) {
            try {
                if (ApprovalFlowTriggerType.CREATE.getTriggerTypeCode().equals(arg.getTriggerType())) {
                    budgetAccrualService.triggerAccrualRule(serviceContext.getTenantId(), ActionModeType.ADD.getKey(), masterData, null);
                } else if (ApprovalFlowTriggerType.UPDATE.getTriggerTypeCode().equals(arg.getTriggerType())) {
                    budgetAccrualService.triggerAccrualRule(serviceContext.getTenantId(), ActionModeType.CHANGE.getKey(), masterData, getSnapShotMap(serviceContext.getTenantId(), masterData.getDescribeApiName(), arg.getCallbackData()));
                }
            } catch (Exception e) {
                log.error("complete after err.", e);
                String lifeStatus = session.getAttribute("flow_before_life_status");
                if (!Strings.isNullOrEmpty(lifeStatus)) {
                    Map<String, Object> updateMap = new HashMap<>();
                    updateMap.put(CommonFields.LIFE_STATUS, lifeStatus);
                    serviceFacade.updateWithMap(User.systemUser(serviceContext.getTenantId()), masterData, updateMap);
                }
                throw new ValidateException(e.getMessage());
            }
        }
        return new FlowCompletedActionDomainPlugin.Result();
    }

    @ServiceMethod("flow_completed_finally_do")
    public FlowCompletedActionDomainPlugin.Result finallyDo(FlowCompletedActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        return new FlowCompletedActionDomainPlugin.Result();
    }


    private Map<String, Object> diff(String tenantId, IObjectData dbData, IObjectData newData) {
        IObjectDescribe describe = serviceFacade.findObject(tenantId, dbData.getDescribeApiName());
        Map<String, Object> updateFields = ObjectDataExt.of(dbData).diff(newData, describe);
        if (CollectionUtils.notEmpty(updateFields)) {
            ObjectDataExt dataExt = ObjectDataExt.of(Maps.newHashMap(updateFields));
            dataExt.removeInvalidFieldForApproval(describe);
        }
        return updateFields;
    }

    private Map<String, Object> getSnapShotMap(String tenantId, String describeApiName, Map<String, Object> callBackData) {

        IObjectDescribe describe = serviceFacade.findDescribeAndLayout(User.systemUser(tenantId), describeApiName, false, null).getObjectDescribe();
        Set<String> fieldsSet = describe.getFieldDescribeMap().keySet();
        Map<String, Object> resultMap = new HashMap<>();
        callBackData.forEach((k, v) -> {
            if (fieldsSet.contains(k)) resultMap.put(k, v);
        });
        log.info("snapMap:{}", resultMap);
        return resultMap;
    }
}
