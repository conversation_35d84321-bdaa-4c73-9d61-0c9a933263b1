package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMStoreWriteOffFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Calendar;
import java.util.Collections;

/**
 * TPM活动协议详情对象相关列表控制器
 */
@SuppressWarnings("Duplicates,unused")
public class TPMActivityAgreementDetailObjRelatedListController extends StandardRelatedListController {

    private static final Logger log = LoggerFactory.getLogger(TPMActivityAgreementDetailObjRelatedListController.class);

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        if (TPMGrayUtils.isYuanQiFilterAgreementDetailCurrent(controllerContext.getTenantId())) {
            overrideQuery(query);
        }
        super.beforeQueryData(query);
    }

    private void overrideQuery(SearchTemplateQuery query) {
        // 增加空值检查，避免空指针异常
        // 参数校验
        if (arg == null || arg.getExtraData() == null) {
            return;
        }
        Long yearsTime;
        try {
            // 检查入参 __is_store_write_off 是否为 true
            Object isStoreWriteOffObj = arg.getExtraData().get("__is_store_write_off");
            if (!(isStoreWriteOffObj instanceof Boolean)) {
                return;
            }

            boolean isStoreWriteOff = (Boolean) isStoreWriteOffObj;
            if (!isStoreWriteOff) {
                log.debug("__is_store_write_off为false,skip");
                return;
            }
            // 协议 id
            String storeWriteOffId = (String) arg.getExtraData().get("__store_write_off_id");
            log.info("__store_write_off_id:{}", storeWriteOffId);
            yearsTime = queryStoreWriteOffTimeYears(controllerContext.getTenantId(), storeWriteOffId);
            if (yearsTime == null) {
                return;
            }
        } catch (Exception e) {
            log.info("TPMActivityAgreementDetailObjRelated overrideQuery error", e);
            return;
        }

        // 获取当前时间
        Calendar calendar = Calendar.getInstance();
        // 设置时间为当月1号0点0分0秒
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        // 获取当月时间戳
        long currentMonthTimestamp = calendar.getTimeInMillis();
        log.info("yearsTime is {}", yearsTime);
        // 添加创建时间过滤条件
        Filter createTimeFilter = new Filter();
        createTimeFilter.setFieldName("field_H4qJ4__c");
        createTimeFilter.setOperator(Operator.EQ);
        createTimeFilter.setFieldValues(Collections.singletonList(String.valueOf(yearsTime)));

        Filter createTimeEmptyFilter = new Filter();
        createTimeEmptyFilter.setFieldName("field_H4qJ4__c");
        createTimeEmptyFilter.setOperator(Operator.IS);
        createTimeEmptyFilter.setFieldValues(Lists.newArrayList());

        Wheres defaultWheres = new Wheres();
        defaultWheres.setFilters(Lists.newArrayList(createTimeFilter));
        Wheres emptyWheres = new Wheres();
        emptyWheres.setFilters(Lists.newArrayList(createTimeEmptyFilter));

        query.setWheres(Lists.newArrayList(defaultWheres, emptyWheres));

        log.info("TPMActivityAgreementDetailObjRelatedListController添加月份时间戳过滤条件,当前月份时间戳: {}", currentMonthTimestamp);
    }

    private Long queryStoreWriteOffTimeYears(String tenantId, String storeWriteOffId) {
        if (Strings.isNullOrEmpty(storeWriteOffId)) {
            return null;
        }
        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(tenantId), storeWriteOffId, ApiNames.TPM_STORE_WRITE_OFF_OBJ);
        if (objectData == null) {
            return null;
        }
        return objectData.get(TPMStoreWriteOffFields.YEARS, Long.class);
    }
}