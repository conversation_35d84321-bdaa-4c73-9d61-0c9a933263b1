package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAsyncBulkInvalidAction;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;

/**
 * Author: linmj
 * Date: 2023/10/30 15:33
 */
public class WithdrawRecordObjAsyncBulkInvalidAction extends StandardAsyncBulkInvalidAction {

    @Override
    protected void before(StandardBulkInvalidAction.Arg arg) {
        throw new ValidateException("invalid action not allowed!");
    }
}
