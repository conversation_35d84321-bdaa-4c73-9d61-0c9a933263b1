package com.facishare.crm.fmcg.tpm.action;


import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.api.consume.EndConsume;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityUnifiedCaseFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetConsumeV2Service;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.PackTransactionProxyImpl;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.service.abstraction.PackTransactionProxy;
import com.facishare.crm.fmcg.tpm.service.abstraction.TransactionService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectApprovalAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/14 19:24
 */
@Slf4j
public class TPMActivityUnifiedCaseObjCloseTPMActivityAction extends BaseObjectApprovalAction<TPMActivityUnifiedCaseObjCloseTPMActivityAction.Arg, TPMActivityUnifiedCaseObjCloseTPMActivityAction.Result> implements TransactionService<TPMActivityUnifiedCaseObjCloseTPMActivityAction.Arg, TPMActivityUnifiedCaseObjCloseTPMActivityAction.Result> {

    private IObjectData objectData;
    private IObjectData dbData;

    public static final Logger LOGGER = LoggerFactory.getLogger(TPMActivityUnifiedCaseObjCloseTPMActivityAction.class);

    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);
    private static final IBudgetConsumeV2Service budgetConsumeV2 = SpringUtil.getContext().getBean(IBudgetConsumeV2Service.class);


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList("CloseTPMActivity");
    }

    protected String getButtonApiName() {
        return "CloseTPMActivity_button_default";
    }

    @Override
    protected List<String> getDataPrivilegeIds(TPMActivityUnifiedCaseObjCloseTPMActivityAction.Arg arg) {
        return Lists.newArrayList(arg.getDataId());
    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.notEmpty(dataList)) {
            objectData = dataList.get(0);
            dbData = ObjectDataExt.of(objectData).copy();
        }
    }

    @Override
    protected TPMActivityUnifiedCaseObjCloseTPMActivityAction.Result doAct(TPMActivityUnifiedCaseObjCloseTPMActivityAction.Arg arg) {

        String dataId = arg.getDataId();
        IObjectData activityCase = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), dataId, ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
        String closeStatus = (String) activityCase.get(TPMActivityUnifiedCaseFields.CLOSE_STATUS);
        if (TPMActivityUnifiedCaseFields.CLOSE_STATUS__CLOSED.equals(closeStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_CLOSE_ACTIVITY_ACTION_0));
        }

        //a. 判断方案是否在审批中，，审批中不可结案
        String activityStatus = activityCase.get(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS, String.class);
        LOGGER.info("activityStatus is {}", activityStatus);
        if (TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__APPROVAL.equals(activityStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_CLOSE_ACTIVITY_ACTION_1));
        }
        //b. 判断关联的活动申请是否存在未结案的，存在则提示不可结案
        validateActivityRelationClose(arg);
        // 确认弹框
        if (!Boolean.TRUE.equals(arg.getIsConfirmWriteOff())) {
            String text = I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_CLOSE_ACTIVITY_ACTION_3);
            return Result.of(activityCase, true, text);
        }
        return packTransactionProxy.packAct(this, arg);
    }

    private void validateActivityRelationClose(Arg arg) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);

        IFilter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(arg.getDataId()));

        IFilter closeStatusFilter = new Filter();
        closeStatusFilter.setFieldName(TPMActivityFields.CLOSED_STATUS);
        closeStatusFilter.setOperator(Operator.EQ);
        closeStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.CLOSE_STATUS__UNCLOSED));

        query.setFilters(Lists.newArrayList(closeStatusFilter, activityFilter));
        List<IObjectData> activities = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query).getData();
        if (!activities.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_OBJ_CLOSE_ACTIVITY_ACTION_2));
        }

    }

    @Override
    protected TPMActivityUnifiedCaseObjCloseTPMActivityAction.Result after(TPMActivityUnifiedCaseObjCloseTPMActivityAction.Arg arg, TPMActivityUnifiedCaseObjCloseTPMActivityAction.Result result) {
        BuryService.asyncTpmLog(Integer.parseInt(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUpstreamOwnerIdOrUserId()), BuryModule.TPM.TPM_ACTIVITY_UNIFIED_CASE, BuryOperation.CLOSE_THE_CASE, true);
        return super.after(arg, result);
    }

    @Override
    public Result doActTransaction(Arg arg) {
        String dataId = arg.getDataId();
        IObjectData activityCase = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), dataId, ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);

        EndConsume.Result endResult = budgetConsumeV2.endConsume(actionContext.getUser(), activityCase.getDescribeApiName(), dataId, true);
        if (endResult.getNeedConfirm()) {
            throw new ValidateException(endResult.getTips());
        }

        // 更新结案状态 结案时间
        Date date = new Date();
        long endDate = this.objectData.get(TPMActivityUnifiedCaseFields.END_DATE, Long.class);
        Map<String, Object> update = new HashMap<>();
        this.objectData.set(TPMActivityUnifiedCaseFields.CLOSE_STATUS, TPMActivityUnifiedCaseFields.CLOSE_STATUS__CLOSED);
        this.objectData.set(TPMActivityUnifiedCaseFields.CLOSE_TIME, date.getTime());
        update.put(TPMActivityUnifiedCaseFields.CLOSE_STATUS, TPMActivityUnifiedCaseFields.CLOSE_STATUS__CLOSED);
        update.put(TPMActivityUnifiedCaseFields.CLOSE_TIME, date.getTime());
        if (endDate >= date.getTime()) {
            update.put(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS, TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__CLOSED);
            this.objectData.set(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS, TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__CLOSED);
        }
        serviceFacade.updateWithMap(actionContext.getUser(), activityCase, update);

        //log
        serviceFacade.log(actionContext.getUser(), EventType.MODIFY, ActionType.Modify, this.objectDescribe, this.objectData, update, dbData);
        log.info("TPMActivityUnifiedCaseObj close activity need trigger approval :{}", this.needTriggerApprovalFlow());
        return TPMActivityUnifiedCaseObjCloseTPMActivityAction.Result.of(objectData, false, null);
    }


    @Data
    public static class Arg {

        @SerializedName("objectDataId")
        @JSONField(name = "objectDataId")
        @JsonProperty("objectDataId")
        private String dataId;

        @SerializedName("is_confirm_write_off")
        @JSONField(name = "is_confirm_write_off")
        @JsonProperty("is_confirm_write_off")
        private Boolean isConfirmWriteOff;

    }


    @Data
    public static class Result {
        private ObjectDataDocument objectData;
        private String tips;
        private Boolean isShowTips;

        public static TPMActivityUnifiedCaseObjCloseTPMActivityAction.Result of(IObjectData objectData, Boolean isShowTips, String tips) {
            TPMActivityUnifiedCaseObjCloseTPMActivityAction.Result result = new TPMActivityUnifiedCaseObjCloseTPMActivityAction.Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            result.setIsShowTips(isShowTips);
            result.setTips(tips);
            return result;
        }
    }

}
