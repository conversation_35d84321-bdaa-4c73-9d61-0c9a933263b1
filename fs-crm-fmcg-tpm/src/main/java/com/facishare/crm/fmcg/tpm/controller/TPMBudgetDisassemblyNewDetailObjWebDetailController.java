package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.TPMBudgetDisassemblyNewDetailsFields;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetDimensionEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *
 */
@SuppressWarnings("all")
public class TPMBudgetDisassemblyNewDetailObjWebDetailController extends StandardWebDetailController {

    public static final BudgetTypeDAO budgetTypeDAO = SpringUtil.getContext().getBean(BudgetTypeDAO.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Result rst = super.after(arg, result);
        removeUselessTimeDimensionFields(rst);
        return rst;
    }

    private void removeUselessTimeDimensionFields(Result rst) {
        if (Objects.nonNull(rst.getData())) {
            String typeId = (String) rst.getData().get(TPMBudgetDisassemblyNewDetailsFields.BUDGET_TYPE_ID);
            String nodeId = (String) rst.getData().get(TPMBudgetDisassemblyNewDetailsFields.BUDGET_NODE_ID);
            if (!Strings.isNullOrEmpty(nodeId) && !Strings.isNullOrEmpty(typeId)) {
                BudgetTypePO type = budgetTypeDAO.get(controllerContext.getTenantId(), typeId);
                if (Objects.nonNull(type)) {
                    BudgetTypeNodeEntity node = type.getNodes().stream().filter(f -> f.getNodeId().equals(nodeId)).findFirst().orElse(null);

                    List<String> dimensionApiNames = node.getDimensions().stream().map(BudgetDimensionEntity::getApiName).collect(Collectors.toList());
                    Set<String> uselessFieldApiNames = TPMBudgetDisassemblyObjDescribeLayoutController.TIME_DIMENSION_FIELD_API_NAME.stream()
                            .filter(dimension -> !dimension.endsWith(node.getTimeDimension()))
                            .collect(Collectors.toSet());
                    uselessFieldApiNames.addAll(TPMBudgetDisassemblyObjDescribeLayoutController.DIMENSION_FIELD_API_NAME.stream()
                            .filter(dimension -> !dimensionApiNames.contains(dimension))
                            .collect(Collectors.toSet()));


                    LayoutExt layoutExt = LayoutExt.of(rst.getLayout());
                    Optional<FormComponentExt> component = layoutExt.getFormComponent();

                    if (component.isPresent()) {
                        FormComponentExt formComponent = component.get();
                        for (IFieldSection fieldSection : formComponent.getFieldSections()) {
                            fieldSection.setFields(fieldSection.getFields().stream()
                                    .filter(field -> !uselessFieldApiNames.contains(field.getFieldName()))
                                    .collect(Collectors.toList()));
                        }
                    }
                }
            }
        }
    }
}
