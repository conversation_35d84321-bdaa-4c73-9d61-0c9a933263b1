package com.facishare.crm.fmcg.tpm.action;

import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetCarryForwardDetailFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetCarryForwardFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.business.abstraction.ICarryForwardActionService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IFiscalTimeService;
import com.facishare.crm.fmcg.tpm.business.enums.CarryForwardType;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.utils.TraceUtil;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IBudgetTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/7/25 18:32
 */
@Slf4j
@SuppressWarnings("Duplicates,unused")
public class TPMBudgetCarryForwardObjEditAction extends StandardEditAction {

    private final IBudgetTypeManager budgetTypeManager = SpringUtil.getContext().getBean(IBudgetTypeManager.class);
    private final IFiscalTimeService fiscalTimeService = SpringUtil.getContext().getBean(IFiscalTimeService.class);
    private final ICarryForwardActionService carryForwardActionService = SpringUtil.getContext().getBean(ICarryForwardActionService.class);

    private BudgetTypeNodeEntity node;
    private IObjectData oldData;

    private CarryForwardType of;

    @Override
    protected void init() {
        IObjectData master = arg.getObjectData().toObjectData();
        String carryForwardType = master.get("__carry_forward_type", String.class);
        if (TPMGrayUtils.carryForwardDefaultCarryForwardType(actionContext.getTenantId()) && (StringUtils.isEmpty(carryForwardType))) {
            carryForwardType = CarryForwardType.CARRY_FORWARD_WITH_NEXT_PERIOD_BUDGET.code();
        }
        of = CarryForwardType.of(carryForwardType);
        if (Objects.isNull(of)) {
            throw new ValidateException("carryForwardType is null");
        }

        correctDetails(of);

        super.init();
    }

    @Override
    protected void before(Arg arg) {
        this.validateLifeStatus();
        stopWatch.lap("lifeStatusCheck");

        this.validateAllowEditFields();
        stopWatch.lap("lifeStatusCheck");

        super.before(arg);
        stopWatch.lap("framework.before");

        this.prepareBudgetTrace();
        this.stopWatch.lap("prepareBudgetTrace");

        this.loadRelatedData();
        this.stopWatch.lap("loadRelatedData");

        this.validateMasterData();
        this.stopWatch.lap("validateMasterData");

        this.validateDetailData();
        this.stopWatch.lap("validateDetailData");
    }

    private void validateAllowEditFields() {
        if (!this.oldData.get(TPMBudgetCarryForwardFields.BUDGET_TYPE_ID, String.class).equals(arg.getObjectData().get(TPMBudgetCarryForwardFields.BUDGET_TYPE_ID))) {
            throw new ValidateException("can not edit budget type.");
        }
        if (!this.oldData.get(TPMBudgetCarryForwardFields.BUDGET_NODE_ID, String.class).equals(arg.getObjectData().get(TPMBudgetCarryForwardFields.BUDGET_NODE_ID))) {
            throw new ValidateException("can not edit budget node.");
        }
    }

    private void validateLifeStatus() {
        this.oldData = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getObjectData().getId(), ApiNames.TPM_BUDGET_CARRY_FORWARD);
        if (!"ineffective".equals(this.oldData.get("life_status"))) {
            throw new ValidateException("can not edit carry forward data.");
        }
    }

    private void prepareBudgetTrace() {
        String traceId = arg.getObjectData().getId().toUpperCase(Locale.ROOT);
        this.customCallbackData.put(TraceUtil.FMCG_TPM_BUDGET_APPROVAL_CALLBACK_TRACE_ID_KEY, traceId);
        this.customCallbackData.put(TraceUtil.FMCG_TPM_BUDGET_BUSINESS_CALLBACK_TRACE_ID_KEY, traceId);
    }

    /**
     * load related data
     */
    private void loadRelatedData() {
        String typeId = (String) arg.getObjectData().get(TPMBudgetCarryForwardFields.BUDGET_TYPE_ID);
        if (Strings.isNullOrEmpty(typeId)) {
            throw new ValidateException("[budget_type_id] can not be null or empty.");
        }

        BudgetTypePO type = budgetTypeManager.get(actionContext.getTenantId(), typeId);
        if (Objects.isNull(type)) {
            throw new ValidateException("budget type not found.");
        }

        String nodeId = (String) arg.getObjectData().get(TPMBudgetCarryForwardFields.BUDGET_NODE_ID);
        if (Strings.isNullOrEmpty(nodeId)) {
            throw new ValidateException("[budget_node_id] can not be null or empty.");
        }
        this.node = type.getNodes().stream().filter(f -> f.getNodeId().equals(nodeId)).findFirst().orElse(null);

        if (Objects.isNull(this.node)) {
            throw new ValidateException("budget node not found.");
        }

        if (!this.node.isEnableCarryForward()) {
            throw new ValidateException("current budget node do not support carry forward operations");
        }
    }

    /**
     * validate master data of TPMBudgetCarryForwardObj
     */
    private void validateMasterData() {
        String sourcePeriodFieldApiName = String.format("source_%s", this.node.getTimeDimension());
        String targetPeriodFieldApiName = String.format("target_%s", this.node.getTimeDimension());

        // carry forward source period
        Long sourcePeriod = (Long) arg.getObjectData().get(sourcePeriodFieldApiName);
        if (Objects.isNull(sourcePeriod)) {
            throw new ValidateException(String.format("[%s] can not be null.", sourcePeriodFieldApiName));
        }

        // carry forward target period
        Long targetPeriod = (Long) arg.getObjectData().get(targetPeriodFieldApiName);
        if (Objects.isNull(targetPeriod)) {
            throw new ValidateException(String.format("[%s] can not be null.", targetPeriodFieldApiName));
        }

        if (fiscalTimeService.notNextPeriod(actionContext.getTenantId(), this.node.getTimeDimension(), sourcePeriod, targetPeriod)) {
            throw new ValidateException(String.format("[%s] must be the next time dimension of [%s].", targetPeriodFieldApiName, sourcePeriodFieldApiName));
        }
    }

    private void validateDetailData() {
        if (MapUtils.isEmpty(this.arg.getDetails())) {
            return;
        }

        List<ObjectDataDocument> details = this.arg.getDetails().get(ApiNames.TPM_BUDGET_CARRY_FORWARD_DETAIL);
        if (CollectionUtils.isEmpty(details)) {
            return;
        }


        for (ObjectDataDocument detail : details) {
            String status = (String) detail.get(TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS);
            if (!Strings.isNullOrEmpty(status) && !status.equals(TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS__SCHEDULE)) {
                throw new ValidateException("detail carry forward status error.");
            }
            switch (of) {
                case CARRY_FORWARD_WITH_NEXT_PERIOD_BUDGET:
                    String targetBudgetId = (String) detail.get(TPMBudgetCarryForwardDetailFields.TARGET_BUDGET_ACCOUNT_ID);
                    if (StringUtils.isEmpty(targetBudgetId)) {
                        throw new ValidateException(I18N.text(I18NKeys.TARGET_BUDGET_IS_NULL));
                    }
                    break;
                case CARRY_FORWARD_WITHOUT_NEXT_PERIOD_BUDGET_OR_CREATE:
                    break;
                default:
                    throw new ValidateException("carry forward type is not supported");
            }
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result inner = super.after(arg, result);
        this.stopWatch.lap("super.after");

        boolean approvalFlowStartSuccess = isApprovalFlowStartSuccess(inner.getObjectData().toObjectData().getId());
        ParallelUtils.createParallelTask().submit(() -> {
            if (approvalFlowStartSuccess) {
                carryForwardActionService.freeze(actionContext.getUser(), result.getObjectData().getId());
            } else {
                carryForwardActionService.carryForward(actionContext.getUser(), result.getObjectData().getId());
            }
        }).run();

        return inner;
    }

    private void correctDetails(CarryForwardType of) {
        List<ObjectDataDocument> details = this.arg.getDetails().get(ApiNames.TPM_BUDGET_CARRY_FORWARD_DETAIL);
        if (CollectionUtils.isEmpty(details)) {
            return;
        }

        switch (of) {
            case CARRY_FORWARD_WITH_NEXT_PERIOD_BUDGET:
                List<ObjectDataDocument> newDetails = details.stream().filter(v -> StringUtils.isNotEmpty((String) v.get(TPMBudgetCarryForwardDetailFields.TARGET_BUDGET_ACCOUNT_ID))).collect(Collectors.toList());
                this.arg.getDetails().put(ApiNames.TPM_BUDGET_CARRY_FORWARD_DETAIL, newDetails);
                break;
            case CARRY_FORWARD_WITHOUT_NEXT_PERIOD_BUDGET_OR_CREATE:
                break;
            default:
                throw new ValidateException("carry forward type is not supported");
        }
    }
}