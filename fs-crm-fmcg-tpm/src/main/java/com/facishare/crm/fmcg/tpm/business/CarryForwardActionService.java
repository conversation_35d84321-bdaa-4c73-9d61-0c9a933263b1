package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetCompareService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetOperator;
import com.facishare.crm.fmcg.tpm.business.abstraction.ICarryForwardActionService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IFiscalTimeService;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetDimensionEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IBudgetTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.auth.model.AuthContext;
import com.facishare.paas.auth.model.FunctionPojo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.UdefButton;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.release.GrayRelease;
import com.fxiaoke.paas.auth.factory.FuncClient;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import groovy.lang.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * description : async carry forward service
 * <p>
 * create by @yangqf
 * create time 2022/7/26 11:20
 */
//IgnoreI18nFile
@Slf4j
@Service
@SuppressWarnings("Duplicates")
public class CarryForwardActionService implements ICarryForwardActionService {

    private static final String SINGLE_ACCOUNT_LOCK_KEY_TEMPLATE = "FMCG:TPM:CARRY_FORWARD:LOCK:%s:%s";
    private static final int LOCK_WAIT = 30;
    private static final int LOCK_LEASE = 420;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource(name = "carryForwardMasterDataService")
    private CarryForwardMasterDataService masterDAO;
    @Resource(name = "carryForwardDetailDataService")
    private CarryForwardDetailDataService detailDAO;
    @Resource
    private RedissonClient redissonCmd;
    @Resource
    private IBudgetTypeManager budgetTypeManager;
    @Resource
    private IFiscalTimeService fiscalTimeService;
    @Resource
    private IBudgetCompareService budgetCompareService;
    @Resource
    private FuncClient funcClient;

    public static final String SCHEDULE = "schedule";
    public static final String FROZEN = "frozen";
    public static final String FROZEN_FAILED = "frozen_failed";
    public static final String UNFROZEN = "unfrozen";
    public static final String UNFROZEN_FAILED = "unfrozen_failed";
    public static final String FAILED = "failed";
    public static final String SUCCESS = "success";

    private static final String BUDGET_CARRY_FORWARD_RETRY_ACTION_CODE = "BudgetCarryForwardRetry";
    private static final String BUDGET_CARRY_FORWARD_RETRY_BUTTON_API_NAME = "BudgetCarryForwardRetry_button_default";

    private static final Map<Action, Map<String, ActionResponse>> ACTION_RESPONSE_MAP = Maps.newHashMap();
    private static final Map<Action, Tuple2<String, String>> ACTION_FAILED_STATUS_MAP = Maps.newHashMap();
    private static final Map<Action, Tuple2<String, String>> ACTION_SUCCESS_STATUS_MAP = Maps.newHashMap();

    private enum Action {
        FREEZE,
        UNFREEZE,
        CARRY_FORWARD
    }

    private enum ActionResponse {
        ALLOW,
        COMPLETED,
        ERROR
    }

    static {
        setActionResponseMap();
        setActionFailedStatusMap();
        setActionSuccessStatusMap();
    }

    @Override
    public void carryForward(User user, String dataId) {
        invoke(Action.CARRY_FORWARD, user, dataId);
    }

    @Override
    public void freeze(User user, String dataId) {
        invoke(Action.FREEZE, user, dataId);
    }

    @Override
    public void unfreeze(User user, String dataId) {
        invoke(Action.UNFREEZE, user, dataId);
    }

    @Override
    public void initRetryButton(String tenantId) {
        IUdefButton old = serviceFacade.findButtonByApiName(User.systemUser(tenantId), BUDGET_CARRY_FORWARD_RETRY_BUTTON_API_NAME, ApiNames.TPM_BUDGET_CARRY_FORWARD);
        if (Objects.isNull(old)) {
            IUdefButton button = new UdefButton();
            button.setTenantId(tenantId);
            button.setDescribeApiName(ApiNames.TPM_BUDGET_CARRY_FORWARD);
            button.setApiName(BUDGET_CARRY_FORWARD_RETRY_BUTTON_API_NAME);
            button.setLabel("重试");
            button.setDefineType("system");
            button.setButtonType("common");
            button.setParamForm(Lists.newArrayList());
            button.setJumpUrl("");
            button.setLockDataShowButton(true);

            Wheres wheres = new Wheres();

            IFilter failedStatusFilter = new Filter();
            failedStatusFilter.setFieldName(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS);
            failedStatusFilter.setOperator(Operator.IN);
            failedStatusFilter.setFieldValues(Lists.newArrayList(
                    TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__FAILED,
                    TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__FROZEN_FAILED,
                    TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__UNFROZEN_FAILED
            ));

            wheres.setFilters(Lists.newArrayList(failedStatusFilter));
            button.setWheres(Lists.newArrayList(wheres));

            button.setIsActive(true);
            button.setDeleted(false);
            button.setUsePages(Lists.newArrayList("detail"));
            serviceFacade.createCustomButton(User.systemUser(tenantId), button);

            AuthContext authContext = AuthContext.builder().tenantId(tenantId).appId("CRM").userId("-10000").build();
            String functionCode = String.format("%s||%s", ApiNames.TPM_BUDGET_CARRY_FORWARD, BUDGET_CARRY_FORWARD_RETRY_ACTION_CODE);
            FunctionPojo function = new FunctionPojo();
            function.setAppId("CRM");
            function.setParentCode("00000000000000000000000000000000");
            function.setTenantId(tenantId);
            function.setFuncName("重试");
            function.setFuncCode(functionCode);
            function.setFuncType(0);
            function.setIsEnabled(true);
            funcClient.addFunc(authContext, Lists.newArrayList(function));

            Set<String> addFunctionCodes = Sets.newHashSet();
            addFunctionCodes.add(functionCode);
            funcClient.updateRoleModifiedFuncPermission(authContext, "00000000000000000000000000000006", addFunctionCodes, Sets.newHashSet());
        }
    }

    //region # 结转主逻辑

    public void invoke(Action action, User user, String dataId) {
        if (!tryLock(user.getTenantId(), dataId)) {
            return;
        }
        IObjectData master = loadMaster(user.getTenantId(), dataId);
        try {
            if (completed(action, master)) {
                return;
            }

            List<IObjectData> details = loadDetails(user.getTenantId(), dataId);
            boolean success = details.stream().allMatch(detail -> detailInvoke(action, user, master, detail));

            carryForwardFinished(action, user, dataId, master, success);
        } catch (AppBusinessException ex) {
            masterDAO.failed(user, master, ACTION_FAILED_STATUS_MAP.get(action).getFirst(), ex.getMessage());
        } catch (Exception ex) {
            log.error(String.format("[%s] carry forward cause unknown exception : ", master.getId()), ex);
            masterDAO.failed(user, master, ACTION_FAILED_STATUS_MAP.get(action).getFirst(), "未知异常。");
        } finally {
            unlock(user.getTenantId(), dataId);
        }
    }

    private void carryForwardFinished(Action action, User user, String dataId, IObjectData master, boolean success) {
        Tuple2<String, String> result = success ? ACTION_SUCCESS_STATUS_MAP.get(action) : ACTION_FAILED_STATUS_MAP.get(action);
        if (action.equals(Action.CARRY_FORWARD)) {
            BigDecimal total = calculateTotalAmountAfterCarryForward(user.getTenantId(), dataId);
            masterDAO.finished(user, master, result.getFirst(), result.getSecond(), total);
        } else {
            masterDAO.finished(user, master, result.getFirst(), result.getSecond());
        }
    }

    private boolean detailInvoke(Action action, User user, IObjectData master, IObjectData detail) {
        IBudgetOperator source = null;
        IBudgetOperator target = null;
        try {
            if (completed(action, detail)) {
                return true;
            }

            if (TPMGrayUtils.carryForwardShowcase(user.getTenantId()) && !"true".equals(MDC.get("is_retry"))) {
                throw new MetaDataBusinessException("[CARRY FORWARD ERROR FOR SHOWCASE]");
            }

            String traceId = master.getId().toUpperCase(Locale.ROOT);
            String sourceId = detail.get(TPMBudgetCarryForwardDetailFields.SOURCE_BUDGET_ACCOUNT_ID, String.class);
            String targetId = detail.get(TPMBudgetCarryForwardDetailFields.TARGET_BUDGET_ACCOUNT_ID, String.class);

            source = BudgetOperatorFactory.initOperator(BizType.CARRY_OVER_OUT, user, sourceId, traceId, traceId, master);
            if (!source.tryLock()) {
                throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_CARRY_FORWARD_ACTION_SERVICE_0), source.getAccount().getName()));
            }
            if (StringUtils.isNotEmpty(targetId)) {
                target = BudgetOperatorFactory.initOperator(BizType.CARRY_OVER_IN, user, targetId, traceId, traceId, master);
                if (!target.tryLock()) {
                    throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_CARRY_FORWARD_ACTION_SERVICE_1), target.getAccount().getName()));
                }
            }
            validateDetailData(user.getTenantId(), master, source, target);
            switch (action) {
                case CARRY_FORWARD:
                    detailDAO.doCarryForwardWithUnfreezeOrNot(user.getTenantId(), detail, source, target);
                    break;
                case FREEZE:
                    detailDAO.freeze(user.getTenantId(), detail, source, target);
                    break;
                case UNFREEZE:
                    detailDAO.unfreeze(user.getTenantId(), detail, source);
                    break;
                default:
                    throw new MetaDataBusinessException("detail action not support.");
            }

            sleep(user.getTenantId());

            return true;
        } catch (AppBusinessException ex) {
            detailDAO.failed(user.getTenantId(), detail, ACTION_FAILED_STATUS_MAP.get(action).getFirst(), ex.getMessage());
            return false;
        } catch (Exception ex) {
            log.error(String.format("[%s] detail carry forward cause unknown exception : ", detail.getId()), ex);
            detailDAO.failed(user.getTenantId(), detail, ACTION_FAILED_STATUS_MAP.get(action).getFirst(), "未知异常。");
            return false;
        } finally {
            if (Objects.nonNull(source)) {
                source.unlock();
            }
            if (Objects.nonNull(target)) {
                target.unlock();
            }
        }
    }

    private void sleep(String tenantId) {
        if (GrayRelease.isAllow("fmcg", "TPM_BUDGET_CARRY_FORWARD_SLEEP", tenantId)) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException ex) {
                Thread.currentThread().interrupt();
            }
        }
    }

    private boolean completed(Action action, IObjectData data) {
        String status = data.get("carry_forward_status", String.class);
        if (Strings.isNullOrEmpty(status)) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_CARRY_FORWARD_ACTION_SERVICE_2));
        }
        ActionResponse response = ACTION_RESPONSE_MAP.get(action).get(status);
        switch (response) {
            case ALLOW:
                return false;
            case COMPLETED:
                return true;
            default:
                throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_CARRY_FORWARD_ACTION_SERVICE_3), action, status));
        }
    }

    //endregion

    //region # 分布式锁

    private boolean tryLock(String tenantId, String dataId) {
        String key = lockKey(tenantId, dataId);
        RLock lock = redissonCmd.getLock(key);
        log.info("[carry_forward_lock] try lock carry forward : {}", key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            return true;
        }
        try {
            return lock.tryLock(LOCK_WAIT, LOCK_LEASE, TimeUnit.SECONDS);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_CARRY_FORWARD_ACTION_SERVICE_4), key));
        }
    }

    private void unlock(String tenantId, String dataId) {
        String key = lockKey(tenantId, dataId);
        RLock lock = redissonCmd.getLock(key);
        log.info("[carry_forward_lock] unlock carry forward : {}", key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    private String lockKey(String tenantId, String dataId) {
        return String.format(SINGLE_ACCOUNT_LOCK_KEY_TEMPLATE, tenantId, dataId);
    }

    //endregion

    //region # 数据读取

    private List<IObjectData> loadDetails(String tenantId, String dataId, List<String> fields) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(-1);
        stq.setOffset(0);
        stq.setNeedReturnCountNum(false);
        stq.setNeedReturnQuote(false);
        stq.setSearchSource("db");

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(TPMBudgetCarryForwardDetailFields.BUDGET_CARRY_FORWARD_ID);
        masterFilter.setOperator(Operator.EQ);
        masterFilter.setFieldValues(Lists.newArrayList(dataId));

        stq.setFilters(Lists.newArrayList(masterFilter));

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.TPM_BUDGET_CARRY_FORWARD_DETAIL,
                stq,
                fields
        );
    }

    private List<IObjectData> loadDetails(String tenantId, String dataId) {
        return loadDetails(tenantId, dataId, Lists.newArrayList(
                CommonFields.ID,
                CommonFields.OBJECT_DESCRIBE_API_NAME,
                CommonFields.TENANT_ID,
                CommonFields.NAME,
                TPMBudgetCarryForwardDetailFields.AMOUNT,
                TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS,
                TPMBudgetCarryForwardDetailFields.SOURCE_BUDGET_ACCOUNT_ID,
                TPMBudgetCarryForwardDetailFields.TARGET_BUDGET_ACCOUNT_ID
        ));
    }

    private List<IObjectData> loadDetailsAfterCarryForward(String tenantId, String dataId) {
        return loadDetails(tenantId, dataId, Lists.newArrayList(
                CommonFields.ID,
                TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS,
                TPMBudgetCarryForwardDetailFields.AMOUNT
        ));
    }

    private IObjectData loadMaster(String tenantId, String dataId) {
        return serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), dataId, ApiNames.TPM_BUDGET_CARRY_FORWARD);
    }

    private BigDecimal calculateTotalAmountAfterCarryForward(String tenantId, String dataId) {
        List<IObjectData> details = loadDetailsAfterCarryForward(tenantId, dataId);
        BigDecimal amount = new BigDecimal("0");
        for (IObjectData detail : details) {
            if (TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS__SUCCESS.equals(detail.get(TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS, String.class))) {
                BigDecimal cur = detail.get(TPMBudgetCarryForwardDetailFields.AMOUNT, BigDecimal.class);
                amount = amount.add(cur);
            }
        }
        return amount;
    }

    //endregion

    //region # 校验
    private void validateDetailData(String tenantId, IObjectData master, IBudgetOperator source, IBudgetOperator target) {
        String nodeId = (String) master.get(TPMBudgetCarryForwardFields.BUDGET_NODE_ID);
        String typeId = (String) master.get(TPMBudgetCarryForwardFields.BUDGET_TYPE_ID);

        BudgetTypeNodeEntity node = loadNode(tenantId, nodeId, typeId);
        if (Objects.isNull(node)) {
            throw new ValidateException("budget node not found.");
        }
        String sourcePeriodFieldApiName = String.format("source_%s", node.getTimeDimension());
        String targetPeriodFieldApiName = String.format("target_%s", node.getTimeDimension());

        IObjectData sourceAccount = source.getAccount();
        validateAccount(tenantId, node, master, sourceAccount, sourcePeriodFieldApiName);
        if (target == null) {
            return;
        }
        IObjectData targetAccount = target.getAccount();
        validateAccount(tenantId, node, master, targetAccount, targetPeriodFieldApiName);
        validateDimension(node, sourceAccount, targetAccount);
    }

    private void validateAccount(String tenantId, BudgetTypeNodeEntity node, IObjectData master, IObjectData account, String masterPeriodFieldApiName) {
        String nodeId = (String) master.get(TPMBudgetCarryForwardFields.BUDGET_NODE_ID);
        String typeId = (String) master.get(TPMBudgetCarryForwardFields.BUDGET_TYPE_ID);
        String accountPeriodFieldApiName = String.format("budget_period_%s", node.getTimeDimension());
        Long period = (Long) master.get(masterPeriodFieldApiName);
        if (!nodeId.equals(account.get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class))) {
            throw new ValidateException(String.format("[%s] detail validate error, wrong budget type id", account.get(CommonFields.NAME)));
        }
        if (!typeId.equals(account.get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class))) {
            throw new ValidateException(String.format("[%s] detail validate error, wrong budget node id", account.get(CommonFields.NAME)));
        }
        if (fiscalTimeService.notSamePeriod(tenantId, node.getTimeDimension(), period, account.get(accountPeriodFieldApiName, Long.class))) {
            throw new ValidateException(String.format("[%s] detail validate error, wrong budget period", account.get(CommonFields.NAME)));
        }
    }

    private void validateDimension(BudgetTypeNodeEntity node, IObjectData sourceAccount, IObjectData targetAccount) {
        Set<String> fields = node.getDimensions().stream().map(BudgetDimensionEntity::getApiName).collect(Collectors.toSet());
        if (!CommonFields.LIFE_STATUS__NORMAL.equals(targetAccount.get(CommonFields.LIFE_STATUS, String.class))) {
            throw new ValidateException(String.format("[%s] target data life status error.", targetAccount.get(CommonFields.NAME)));
        }
        if (budgetCompareService.notSameDimension(fields, sourceAccount, targetAccount)) {
            throw new ValidateException(String.format("[%s - %s] source data dimensions not equal to target data dimensions.", sourceAccount.get(CommonFields.NAME), targetAccount.get(CommonFields.NAME)));
        }
    }

    private BudgetTypeNodeEntity loadNode(String tenantId, String nodeId, String typeId) {
        BudgetTypePO type = budgetTypeManager.get(tenantId, typeId);
        if (Objects.isNull(type)) {
            throw new ValidateException("budget type not found.");
        }
        return type.getNodes().stream().filter(f -> f.getNodeId().equals(nodeId)).findFirst().orElse(null);
    }

    //endregion

    //region # 常量

    private static void setActionResponseMap() {
        Map<String, ActionResponse> carryForward = Maps.newHashMap();
        // 无审批流下允许结转的状态
        carryForward.put(SCHEDULE, ActionResponse.ALLOW);
        carryForward.put(FAILED, ActionResponse.ALLOW);
        carryForward.put(FROZEN, ActionResponse.ALLOW);
        // 无审批流下已经结转成功的状态
        carryForward.put(SUCCESS, ActionResponse.COMPLETED);
        // 无审批流下不允许结转的状态
        carryForward.put(FROZEN_FAILED, ActionResponse.ERROR);
        carryForward.put(UNFROZEN_FAILED, ActionResponse.ERROR);
        carryForward.put(UNFROZEN, ActionResponse.ERROR);
        ACTION_RESPONSE_MAP.put(Action.CARRY_FORWARD, carryForward);

        Map<String, ActionResponse> freeze = Maps.newHashMap();
        // 允许进行冻结的状态
        freeze.put(SCHEDULE, ActionResponse.ALLOW);
        freeze.put(FROZEN_FAILED, ActionResponse.ALLOW);
        freeze.put(UNFROZEN, ActionResponse.ALLOW);
        // 已冻结的状态
        freeze.put(FROZEN, ActionResponse.COMPLETED);
        // 不允进行许冻结的状态
        freeze.put(UNFROZEN_FAILED, ActionResponse.ERROR);
        freeze.put(FAILED, ActionResponse.ERROR);
        freeze.put(SUCCESS, ActionResponse.ERROR);
        ACTION_RESPONSE_MAP.put(Action.FREEZE, freeze);

        Map<String, ActionResponse> unfreeze = Maps.newHashMap();
        // 允许进行解冻的状态
        unfreeze.put(UNFROZEN_FAILED, ActionResponse.ALLOW);
        unfreeze.put(FROZEN, ActionResponse.ALLOW);
        // 已经解冻的状态
        unfreeze.put(UNFROZEN, ActionResponse.COMPLETED);
        // 不允许解冻的状态
        unfreeze.put(SCHEDULE, ActionResponse.ERROR);
        unfreeze.put(FROZEN_FAILED, ActionResponse.ERROR);
        unfreeze.put(FAILED, ActionResponse.ERROR);
        unfreeze.put(SUCCESS, ActionResponse.ERROR);
        ACTION_RESPONSE_MAP.put(Action.UNFREEZE, unfreeze);
    }

    private static void setActionFailedStatusMap() {
        ACTION_FAILED_STATUS_MAP.put(Action.FREEZE, new Tuple2<>(FROZEN_FAILED, "冻结操作失败，请排查明细错误信息后重试冻结操作。"));
        ACTION_FAILED_STATUS_MAP.put(Action.UNFREEZE, new Tuple2<>(UNFROZEN_FAILED, "解冻操作失败，请排查明细错误信息后重试解冻操作。"));
        ACTION_FAILED_STATUS_MAP.put(Action.CARRY_FORWARD, new Tuple2<>(FAILED, "结转操作失败，请排查明细错误信息后重试结转操作。"));
    }

    private static void setActionSuccessStatusMap() {
        ACTION_SUCCESS_STATUS_MAP.put(Action.FREEZE, new Tuple2<>(FROZEN, "冻结操作成功。"));
        ACTION_SUCCESS_STATUS_MAP.put(Action.UNFREEZE, new Tuple2<>(UNFROZEN, "解冻操作成功。"));
        ACTION_SUCCESS_STATUS_MAP.put(Action.CARRY_FORWARD, new Tuple2<>(SUCCESS, "结转操作成功。"));
    }

    //endregion
}
