package com.facishare.crm.fmcg.yqsl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.yqsl.api.AplFunctionNearbyAccounts;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.fmcg.framework.http.AccountProxy;
import com.fmcg.framework.http.contract.sfa.FindNearbyAccounts;
import com.fxiaoke.limit.GuavaLimiter;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class AplFunctionExtendService implements InitializingBean {

    @Resource
    private AccountProxy accountProxy;
    // 附近客户查询最多客户数，配置中心 key - fmcg.yqsl.nearbyAccountsCount
    private int nearbyAccountsCount = 10;
    // 附近客户查询最大公里数，配置中心 key - fmcg.yqsl.nearbyAccountsDistanceLimit
    private int nearbyAccountsDistanceLimit = 2;

    @Override
    public void afterPropertiesSet() {
        ConfigFactory.getConfig("fs-fmcg-framework-config", config -> {
            nearbyAccountsCount = config.getInt("fmcg.yqsl.nearbyAccountsCount", 10);
            nearbyAccountsDistanceLimit = config.getInt("fmcg.yqsl.nearbyAccountsDistanceLimit", 2);
        });
    }

    public AplFunctionNearbyAccounts.Result nearbyAccounts(AplFunctionNearbyAccounts.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        if (!GuavaLimiter.tryAcquireAndWait("limit-yqsl-nearby-stores", context.getTenantId(), 1000)) {
            throw new MetaDataBusinessException("request over limit.");
        }
        if (Strings.isNullOrEmpty(arg.getLatitude())) {
            throw new MetaDataBusinessException("latitude can not be null or empty.");
        }
        if (Strings.isNullOrEmpty(arg.getLongitude())) {
            throw new MetaDataBusinessException("longitude can not be null or empty.");
        }
        if (Objects.isNull(arg.getDistance()) || arg.getDistance() <= 0) {
            throw new MetaDataBusinessException("distance value error.");
        }
        if (CollectionUtils.isEmpty(arg.getFields())) {
            arg.setFields(Lists.newArrayList("_id", "name"));
        }
        List<JSONObject> data = queryNearbyAccounts(context, arg.getLatitude(), arg.getLongitude(), arg.getDistance(), arg.getFields());
        return AplFunctionNearbyAccounts.Result.builder().data(data).build();
    }

    private List<JSONObject> queryNearbyAccounts(ApiContext context, String latitude, String longitude, Double distance, List<String> fields) {
        log.info("begin query nearby accounts - count : {}, distance : {}",
            nearbyAccountsCount, nearbyAccountsDistanceLimit
        );

        FindNearbyAccounts.Arg arg = new FindNearbyAccounts.Arg();
        arg.setGeoSearchType("0");
        arg.setLongitude(longitude);
        arg.setLatitude(latitude);
        arg.setKilometers(Math.min(distance, nearbyAccountsDistanceLimit));
        arg.setIncludeDescribe(false);
        arg.setIncludeAllData(true);

        Map<String, List<String>> fieldMap = new HashMap<>();
        fieldMap.put("AccountAddrObj", Lists.newArrayList("_id", "name"));
        fieldMap.put("AccountObj", fields);
        arg.setNeedFieldMap(fieldMap);

        JSONObject query = new JSONObject();
        query.put("limit", nearbyAccountsCount);
        query.put("offset", 0);

        JSONObject storeFilter = new JSONObject();
        storeFilter.put("field_name", "record_type");
        storeFilter.put("operator", "EQ");
        storeFilter.put("field_values", Lists.newArrayList("default__c"));

        JSONObject relationFilter = new JSONObject();
        relationFilter.put("field_name", "sign_state__c");
        relationFilter.put("operator", "EQ");
        relationFilter.put("field_values", Lists.newArrayList("1"));

        query.put("filters", Lists.newArrayList(storeFilter, relationFilter));
        query.put("orders", Lists.newArrayList());
        query.put("specialSearchAfterFlag", false);
        query.put("whatFieldApiNames", Lists.newArrayList());
        query.put("permissionType", 0);
        query.put("needReturnCountNum", false);

        arg.setSearchQueryInfo(query.toJSONString());

        FindNearbyAccounts.Result result = accountProxy.findNearby(Integer.parseInt(context.getTenantId()), -10000, arg);

        if (Objects.isNull(result)) {
            throw new MetaDataBusinessException("find nearby failed.");
        }

        if (result.getCode() != 0) {
            throw new MetaDataBusinessException(result.getMessage());
        }

        return result.getData().getDataList();
    }
}