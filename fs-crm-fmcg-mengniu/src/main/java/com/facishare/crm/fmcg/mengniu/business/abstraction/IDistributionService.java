package com.facishare.crm.fmcg.mengniu.business.abstraction;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.mengniu.api.FormQrcode;
import com.facishare.crm.fmcg.mengniu.api.QueryQrcodeStatus;
import com.facishare.crm.fmcg.mengniu.api.UseRebate;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;

/**
 * Author: linmj
 * Date: 2024/1/24 16:49
 */
public interface IDistributionService {

    void dealSingleTenantSendCoupons(String tenantId, String downstreamTenantId, String objectId, IObjectData dealer, long month, List<String> feeTypes, JSONObject extraData);

    String sendCouponsByObject(String tenantId, String objectId, JSONObject extraData);

    String setSendCouponsTask(String tenantId, String objectId, JSONObject extraData);

    FormQrcode.Result formQrcode(FormQrcode.Arg arg);

    QueryQrcodeStatus.Result queryQrcodeStatus(QueryQrcodeStatus.Arg arg);

    UseRebate.Result useRebate(UseRebate.Arg arg);

    void deleteDuplicateCoupons(String tenantId, List<String> fixedTenantAccounts,String type, JSONObject extraData);

    void recoverData(String tenantId, List<String> fixedTenantAccounts);

    void dealSingleTenantSendCouponsByAgreement(String tenantId, String downstreamTenantId, String objectId, IObjectData dealer, long month, List<String> feeTypes, JSONObject extraData);


}
