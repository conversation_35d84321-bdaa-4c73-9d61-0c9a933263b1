package com.facishare.crm.fmcg.mengniu.api;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface FillActivityItem {

    @Data
    @ToString
    class Arg implements Serializable {

        private String upTenantId;

        private List<String> tenantIds;

        private Map<String, String> activityIdMap;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private String data;
    }
}