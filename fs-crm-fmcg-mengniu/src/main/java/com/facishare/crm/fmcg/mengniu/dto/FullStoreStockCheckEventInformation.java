package com.facishare.crm.fmcg.mengniu.dto;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@Builder
public class FullStoreStockCheckEventInformation implements Serializable {

    private IObjectData storeStockCheck;

    private List<FullStoreStockCheckDetailInformation> storeStockCheckDetails;

    private IObjectData store;

    private IObjectData activity;

    private RewardLimitConfig rewardLimitConfig;

    private String tenantName;
}