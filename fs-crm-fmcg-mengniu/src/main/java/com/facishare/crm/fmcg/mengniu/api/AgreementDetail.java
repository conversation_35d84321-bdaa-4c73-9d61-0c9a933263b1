package com.facishare.crm.fmcg.mengniu.api;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface AgreementDetail {

    @Data
    @ToString
    class Arg implements Serializable {

        private String id;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private ObjectDataDocument data;

        private Map<String, List<ObjectDataDocument>> details;

        private Map<String, ObjectDescribeDocument> describes;

        @JSONField(name = "simple_layout")
        @JsonProperty(value = "simple_layout")
        @SerializedName("simple_layout")
        private JSONObject simpleLayout;
    }
}