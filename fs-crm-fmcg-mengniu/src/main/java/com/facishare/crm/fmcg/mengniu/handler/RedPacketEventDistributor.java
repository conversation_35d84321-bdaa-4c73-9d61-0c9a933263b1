package com.facishare.crm.fmcg.mengniu.handler;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.RedPacketRecordFields;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class RedPacketEventDistributor {

    @Resource
    private PresetRedPacketEventHandler presetRedPacketEventHandler;


    @Resource
    private SelfRedPacketEventHandler selfRedPacketEventHandler;

    @Resource
    private ServiceFacade serviceFacade;

    public void process(IObjectData objectData) {
        String describeApiName = objectData.getDescribeApiName();
        switch (describeApiName) {
            case ApiNames.RED_PACKET_RECORD_OBJ:
                presetRedPacketEventHandler.userAuthRemind(objectData);
                break;
            case RedPacketRecordFields.API_NAME:
                selfRedPacketEventHandler.userAuthRemind(objectData);
                break;
            default:
                log.warn("unknown red packet event apiName : {}", describeApiName);
                break;
        }
    }


    public void redPacketAuthUserRemind(String tenantId, String redPacketId, String apiName) {
        log.info("redPacketAuthUserRemind tenantId is {},redPacketId is {}", tenantId, redPacketId);
        try {
            IObjectData objectData = serviceFacade.findObjectDataIncludeDeleted(User.systemUser(tenantId), redPacketId, apiName);
            process(objectData);
        } catch (Exception ex) {
            log.warn("redPacketEvent process error ", ex);
        }
    }
}

