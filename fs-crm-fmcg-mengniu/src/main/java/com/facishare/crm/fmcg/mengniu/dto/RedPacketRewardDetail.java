package com.facishare.crm.fmcg.mengniu.dto;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ToString
public class RedPacketRewardDetail implements Serializable {

    private String salesOrderDetailId;

    private String salesOrderDetailName;

    private String productId;

    private String productCode;

    private String serialNumberId;

    private String serialNumberName;

    private String batchCode;

    private Long manufactureDate;

    private BigDecimal amount;

    private String amountConfigId;
}