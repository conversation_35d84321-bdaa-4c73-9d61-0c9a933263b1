package com.facishare.crm.fmcg.mengniu.api;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@Builder
public
class MengNiuHeaderVO implements Serializable {

    private String key;

    private String label;

    @JSONField(name = "type")
    @JsonProperty(value = "type")
    @SerializedName("type")
    private String type;

    private boolean hide;

    @JSONField(name = "options")
    @JsonProperty(value = "options")
    @SerializedName("options")
    private List<MengNiuOptionVO> options;
}
