package com.facishare.crm.fmcg.mengniu.service;

import com.facishare.crm.fmcg.tpm.web.contract.OperateAudit;
import com.facishare.crm.fmcg.tpm.web.contract.StoreAuditDataList;
import com.facishare.crm.fmcg.tpm.web.contract.StoreAuditSummaryList;
import com.facishare.crm.fmcg.tpm.web.contract.StoreOperateAudit;

public interface IStoreAuditService {

    StoreAuditSummaryList.Result auditSummaryList(StoreAuditSummaryList.Arg arg);

    StoreAuditDataList.Result dataList(StoreAuditDataList.Arg arg);

    StoreOperateAudit.Result operateAudit(StoreOperateAudit.Arg arg);
}
