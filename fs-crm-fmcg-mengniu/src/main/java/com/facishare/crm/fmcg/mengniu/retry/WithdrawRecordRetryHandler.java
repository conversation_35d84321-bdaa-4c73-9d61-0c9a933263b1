package com.facishare.crm.fmcg.mengniu.retry;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RetryTaskPO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RetryTaskStatusEnum;
import com.facishare.crm.fmcg.tpm.retry.annotation.RetryHandlerType;
import com.facishare.crm.fmcg.tpm.retry.handler.BaseHandler;
import com.facishare.crm.fmcg.tpm.retry.handler.RetryHandler;
import com.facishare.crm.fmcg.tpm.retry.handler.RetryHandlerEnum;
import com.facishare.crm.fmcg.tpm.retry.setter.RedPacketWithdrawSetter;
import com.facishare.crm.fmcg.tpm.service.abstraction.IWithdrawRecordService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * Author: ljs
 * Date: 2023/11/17 11:29
 */
@Component
@Slf4j
@RetryHandlerType(name = RetryHandlerEnum.RED_PACKET_WITHDRAW_UPDATE_HANDLER)
public class WithdrawRecordRetryHandler extends BaseHandler implements RetryHandler {

    @Resource
    private IWithdrawRecordService withdrawRecordService;
    @Resource
    private RedPacketWithdrawSetter redPacketWithdrawSetter;

    @Override
    public void mainDo(RetryTaskPO retryTaskPO) {
        if (Strings.isNullOrEmpty(retryTaskPO.getParams())) {
            redPacketWithdrawSetter.setNewStatus(retryTaskPO.getId().toString(), RetryTaskStatusEnum.SUCCESS.code());
            return;
        }
        JSONObject params = JSON.parseObject(retryTaskPO.getParams());
        String withdrawId = params.getString("withdrawId");
        String tenantId = params.getString("tenantId");
        String apiName = params.getString("apiName");
        IObjectData withdrawRecord = serviceFacade.findObjectData(User.systemUser(tenantId), withdrawId, apiName);
        if (Objects.isNull(withdrawRecord)){
            return;
        }
        if (withdrawRecordService.refreshWithdrawStatus(withdrawRecord)) {
            redPacketWithdrawSetter.setNewStatus(retryTaskPO.getId().toString(), RetryTaskStatusEnum.FAIL.code());
        } else {
            redPacketWithdrawSetter.setNewStatus(retryTaskPO.getId().toString(), RetryTaskStatusEnum.SUCCESS.code());
        }
    }

    @Override
    public void handleException(RetryTaskPO retryTaskPO, Exception e) {
        super.handleException(retryTaskPO, e);
        if (retryTaskPO == null) {
            log.info("task is null");
            return;
        }
        redPacketWithdrawSetter.setNewStatus(retryTaskPO.getId().toString(), RetryTaskStatusEnum.FAIL.code());
    }
}