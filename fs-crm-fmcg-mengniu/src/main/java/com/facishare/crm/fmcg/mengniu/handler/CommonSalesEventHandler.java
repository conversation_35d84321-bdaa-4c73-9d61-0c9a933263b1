package com.facishare.crm.fmcg.mengniu.handler;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.mengniu.dto.*;
import com.facishare.crm.fmcg.tpm.api.MengNiuTenantInformation;
import com.facishare.organization.api.model.department.arg.GetDepartmentDtoArg;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
import com.facishare.uc.api.model.fscore.result.GetSimpleEnterpriseResult;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Slf4j
public abstract class CommonSalesEventHandler<T extends Serializable> extends SalesEventHandler<T> {

    protected TenantCloudPaymentAccount loadTenantCloudAccount(String tenantId) {
        return loadTenantCloudAccount(tenantId, null);
    }

    protected TenantCloudPaymentAccount loadTenantCloudAccount(String tenantId, CloudAccountInformation cloudAccountInformation) {
        GetSimpleEnterpriseArg arg = new GetSimpleEnterpriseArg();
        arg.setEnterpriseId(Integer.parseInt(tenantId));
        GetSimpleEnterpriseResult result = enterpriseEditionService.getSimpleEnterprise(arg);
        return TenantCloudPaymentAccount.builder().tenantAccount(result.getSimpleEnterprise().getEnterpriseAccount()).tenantName(result.getSimpleEnterprise().getEnterpriseName()).cloudAccountDealerId(cloudAccountInformation == null ? null : cloudAccountInformation.getDealerId()).cloudAccountBrokerId(cloudAccountInformation == null ? null : cloudAccountInformation.getBrokerId()).build();
    }

    protected TenantWeChatPaymentAccount loadTenantWeChatAccount(String tenantId) {
        GetSimpleEnterpriseArg arg = new GetSimpleEnterpriseArg();
        arg.setEnterpriseId(Integer.parseInt(tenantId));
        GetSimpleEnterpriseResult result = enterpriseEditionService.getSimpleEnterprise(arg);
        return TenantWeChatPaymentAccount.builder().tenantAccount(result.getSimpleEnterprise().getEnterpriseAccount()).tenantName(result.getSimpleEnterprise().getEnterpriseName()).build();
    }

    protected WeChatPaymentAccount loadWeChatPaymentAccountFromEmployee(String tenantId, String employeeId) {
        IObjectData employee = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), employeeId, "PersonnelObj");
        String appId = employee.get(PersonnelFields.MENGNIU_WECHAT_APP_ID, String.class);

        if (Strings.isNullOrEmpty(appId)) {
            MengNiuTenantInformation tenant = tenantHierarchyService.load(tenantId);
            appId = tenantHierarchyService.findManufacturerByTenantId(tenant.getManufacturer().getTenantId()).getRedPacketAccountWeChatAppId();
        }

        return WeChatPaymentAccount.builder().openId(employee.get(PersonnelFields.MENGNIU_WECHAT_OPEN_ID, String.class)).idCardNumber(employee.get(PersonnelFields.MENGNIU_IDCARD, String.class)).realName(employee.get(PersonnelFields.FULL_NAME, String.class)).phoneNumber(employee.get(PersonnelFields.PHONE, String.class)).unionId(employee.get(PersonnelFields.MENGNIU_WX_UNION_ID, String.class)).appId(appId).build();
    }

    protected BigDecimal calculateIncomingRewardAmount(List<RedPacketReward> data) {
        BigDecimal total = new BigDecimal("0");
        for (RedPacketReward datum : data) {
            if (Objects.nonNull(datum)) {
                BigDecimal amount = datum.getAmount();
                if (Objects.nonNull(amount)) {
                    total = total.add(datum.getAmount());
                }
            }
        }
        return total;
    }

    protected BigDecimal total(List<RedPacketRewardDetail> data) {
        BigDecimal total = new BigDecimal("0");
        for (RedPacketRewardDetail datum : data) {
            if (Objects.nonNull(datum)) {
                BigDecimal amount = datum.getAmount();
                if (Objects.nonNull(amount)) {
                    total = total.add(datum.getAmount());
                }
            }
        }
        return total;
    }

    protected PaymentAccount loadWeChatPaymentAccountFromStore(String tenantId, IObjectData contact) {
        return buildPaymentAccount(tenantId, contact);
    }

    protected PaymentAccount loadWeChatPaymentAccountFromStore(String tenantId, String storeId) {
        IObjectData contact = findContactData(tenantId, storeId);

        if (Objects.isNull(contact)) {
            return PaymentAccount.of(WeChatPaymentAccount.builder().build());
        }

        return buildPaymentAccount(tenantId, contact);
    }

    // todo : 店仓激励
    @SuppressWarnings("unchecked")
    protected PaymentAccount loadWeChatPaymentAccountFromStoreWarehouse(MengNiuTenantInformation tenant, String tenantId, String storeId) {
        IObjectData store = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenant.getManufacturer().getTenantId()), storeId, ApiNames.ACCOUNT_OBJ);
        List<String> sellerIds = store.get("sellers__c", List.class);
        if (CollectionUtils.isNotEmpty(sellerIds)) {
            for (String sellerId : sellerIds) {
                IObjectData seller = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenant.getManufacturer().getTenantId()), sellerId, ApiNames.ACCOUNT_OBJ);
                String recordType = seller.getRecordType();

                if (recordType.equals("shop_warehouse__c")) {
                    IObjectData storeWarehouse = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), sellerId, ApiNames.ACCOUNT_OBJ);
                    List<String> owners = storeWarehouse.get("store_owner__c", List.class);
                    if (CollectionUtils.isNotEmpty(owners)) {
                        String owner = owners.get(0);
                        return PaymentAccount.of(
                          ApiNames.PERSONNEL_OBJ,
                          String.format("%s.%s", tenantId, owner),
                          loadWeChatPaymentAccountFromEmployee(tenantId, owner));
                    }
                }
            }
        }
        return PaymentAccount.of(WeChatPaymentAccount.builder().build());
    }

    private PaymentAccount buildPaymentAccount(String tenantId, IObjectData contact) {
        if (Objects.isNull(contact)) {
            return PaymentAccount.of(WeChatPaymentAccount.builder().build());
        }

        String rewardPersonId = null;
        String publicEmployeeId = contact.get(ContactFields.PUBLIC_EMPLOYEE_ID, String.class);
        if (Strings.isNullOrEmpty(publicEmployeeId)) {
            Filter contactIdFilter = new Filter();
            contactIdFilter.setFieldName(PublicEmployeeFields.CONTRACT_ID);
            contactIdFilter.setOperator(Operator.EQ);
            contactIdFilter.setFieldValues(Lists.newArrayList(contact.getId()));

            SearchTemplateQuery query = QueryDataUtil.minimumQuery(contactIdFilter);
            query.setLimit(1);

            List<IObjectData> employees = QueryDataUtil.find(
                serviceFacade,
                contact.getTenantId(),
                ApiNames.PUBLIC_EMPLOYEE_OBJ,
                query,
                Lists.newArrayList(CommonFields.ID, PublicEmployeeFields.OUTER_TENANT_ID)
            );

            if (!CollectionUtils.isEmpty(employees)) {
                IObjectData employee = employees.get(0);
                rewardPersonId = String.format("%s.%s", employee.get(PublicEmployeeFields.OUTER_TENANT_ID, String.class), employee.getId());
            }
        } else {
            try {
                IObjectData publicEmployeeObj = serviceFacade.findObjectDataIgnoreAll(User.systemUser(contact.getTenantId()), publicEmployeeId, ApiNames.PUBLIC_EMPLOYEE_OBJ);
                if (Objects.nonNull(publicEmployeeObj)){
                    rewardPersonId = String.format("%s.%s", publicEmployeeObj.get(PublicEmployeeFields.OUTER_TENANT_ID, String.class), publicEmployeeObj.getId());
                }
            }catch (Exception ex){
                log.warn("find publicEmployeeObj error, ", ex);
            }
        }

        return PaymentAccount.of(ApiNames.CONTACT_OBJ, rewardPersonId, convertContactToWeChatPaymentAccount(tenantId, contact));
    }

    protected String getTenantName(String tenantId) {
        GetSimpleEnterpriseArg arg = new GetSimpleEnterpriseArg();
        arg.setEnterpriseId(Integer.parseInt(tenantId));
        return enterpriseEditionService.getSimpleEnterprise(arg).getSimpleEnterprise().getEnterpriseName();
    }

    protected RoleRewardAmount calculateRewardAmount(IObjectData activity, String role, String storeId, String skuId) {
        String defaultAmountKey = String.format("%s_red_packet_amount__c", role.toLowerCase());
        BigDecimal amount = activity.get(defaultAmountKey, BigDecimal.class, BigDecimal.ZERO);
        RewardAmountConfig amountConfig = rewardAmountConfigService.get(activity.getTenantId(), activity.getId(), role, storeId, skuId);
        if (!Objects.isNull(amountConfig)) {
            amount = amountConfig.getAmount();
        }
        return RoleRewardAmount.builder().amount(amount).configId(Objects.isNull(amountConfig) ? null : amountConfig.getId()).build();
    }

    protected String findMBossEmployeeId(String tenantId) {
        GetDepartmentDtoArg arg = new GetDepartmentDtoArg();
        arg.setDepartmentId(999999);
        arg.setEnterpriseId(Integer.parseInt(tenantId));
        Integer principalId = departmentProviderService.getDepartmentDto(arg).getDepartment().getPrincipalId();
        return principalId == null ? null : principalId.toString();
    }

    protected PaymentAccount loadTenantCloudAccountByRole(MengNiuTenantInformation tenant, IObjectData activity, String role) {
        String fromAccountTypeKey = String.format("%s_red_packet_account_type__c", role.toLowerCase());
        String fromAccountType = activity.get(fromAccountTypeKey, String.class);

        PaymentAccount account;
        if ("0".equals(fromAccountType)) {
            account = PaymentAccount.of(loadTenantCloudAccount(tenant.getManufacturer().getTenantId()));
        } else if ("2".equals(fromAccountType)) {
            if (tenant.getRole().equals(MengNiuTenantInformation.ROLE_MANUFACTURER)) {
                return null;
            }
            account = PaymentAccount.of(loadTenantCloudAccount(tenant.getN().getTenantId()));
        } else if (RewardConstants.CLOUD_ACCOUNT_DEALER_ID_MAP.containsKey(fromAccountType)) {
            account = PaymentAccount.of(loadTenantCloudAccount(tenant.getManufacturer().getTenantId(), RewardConstants.CLOUD_ACCOUNT_DEALER_ID_MAP.get(fromAccountType)));
        } else {
            account = PaymentAccount.of(loadTenantCloudAccount(tenant.getManufacturer().getTenantId()));
        }
        return account;
    }

    protected BigDecimal calculateRewardedAmount(IObjectData activity) {
        if (TPMGrayUtils.allowMengNiuRedPacketPublishV2(activity.getTenantId(), activity.getRecordType())) {
            return newRewardService.calculateRewardedAmount(activity);
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(RedPacketRecordFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activity.getId()));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("normal"));

        query.setFilters(Lists.newArrayList(lifeStatusFilter, activityIdFilter));

        List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(User.systemUser(activity.getTenantId()), query, "red_packet_record__c", Lists.newArrayList("activity_id__c"), "sum", "amount__c");

        if (!CollectionUtils.isEmpty(data)) {
            return data.get(0).get("sum_amount__c", BigDecimal.class);
        } else {
            return new BigDecimal("0");
        }
    }

    protected IObjectData findContactData(String tenantId, String storeId) {
        IFilter identityFilter = new Filter();
        identityFilter.setFieldName("account_id");
        identityFilter.setOperator(Operator.EQ);
        identityFilter.setFieldValues(Lists.newArrayList(storeId));

        IFilter storeOwnerFlagFilter = new Filter();
        storeOwnerFlagFilter.setFieldName("field_2m89p__c");
        storeOwnerFlagFilter.setOperator(Operator.EQ);
        storeOwnerFlagFilter.setFieldValues(Lists.newArrayList("1"));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(identityFilter, storeOwnerFlagFilter);
        stq.setLimit(1);

        List<IObjectData> data = QueryDataUtil.find(serviceFacade, tenantId, "ContactObj", stq, Lists.newArrayList(
            "_id",
            CommonFields.TENANT_ID,
            ContactFields.NAME,
            ContactFields.MENGNIU_ID_CARD_NUMBER,
            ContactFields.MENGNIU_WECHAT_OPEN_ID,
            ContactFields.MENGNIU_WECHAT_UNION_ID,
            ContactFields.MENGNIU_WECHAT_APP_ID,
            ContactFields.MOBILE1,
            ContactFields.PUBLIC_EMPLOYEE_ID
        ));

        log.info("contact data : {}", data);

        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        return data.get(0);
    }

    private WeChatPaymentAccount convertContactToWeChatPaymentAccount(String tenantId, IObjectData contact) {
        String appId = contact.get(ContactFields.MENGNIU_WECHAT_APP_ID, String.class);
        if (Strings.isNullOrEmpty(appId)) {
            MengNiuTenantInformation tenant = tenantHierarchyService.load(tenantId);
            appId = tenantHierarchyService.findManufacturerByTenantId(tenant.getManufacturer().getTenantId()).getRedPacketAccountWeChatAppId();
        }
        return WeChatPaymentAccount.builder().openId(contact.get(ContactFields.MENGNIU_WECHAT_OPEN_ID, String.class)).idCardNumber(contact.get(ContactFields.MENGNIU_ID_CARD_NUMBER, String.class)).realName(contact.get(ContactFields.NAME, String.class)).phoneNumber(contact.get(ContactFields.MOBILE1, String.class)).unionId(contact.get(ContactFields.MENGNIU_WECHAT_UNION_ID, String.class)).appId(appId).build();
    }
}
