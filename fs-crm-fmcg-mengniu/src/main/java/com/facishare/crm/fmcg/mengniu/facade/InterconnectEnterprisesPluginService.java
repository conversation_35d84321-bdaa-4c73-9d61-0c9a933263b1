package com.facishare.crm.fmcg.mengniu.facade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.mengniu.business.abstraction.InterconnectEnterprisesService;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/10/11 18:04
 */
@Slf4j
@Service
@ServiceModule("interconnect_enterprises")
public class InterconnectEnterprisesPluginService {

    @Resource
    private InterconnectEnterprisesService interconnectionEnterprisesService;

    @ServiceMethod("create_interconnect")
    public void createInterconnect(JSONObject arg, ServiceContext serviceContext) {
        log.info("create interconnect enterprises arg : {}", JSON.toJSONString(arg));

        String objectId = arg.getString("objectId");
        String apiName = arg.getString("apiName");
        String mapperAccountId = arg.getString("mapper_account_id");
        String enterpriseAccount = arg.getString("enterprise_account");

        if (!Strings.isNullOrEmpty(objectId)) {
            log.info("create interconnect enterprises tenantId : {}", serviceContext.getTenantId());
            interconnectionEnterprisesService.triggerInterconnection(serviceContext.getTenantId(), objectId, apiName, mapperAccountId, enterpriseAccount);
        }
    }
}