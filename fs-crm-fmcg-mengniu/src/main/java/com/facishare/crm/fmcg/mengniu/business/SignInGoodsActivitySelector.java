package com.facishare.crm.fmcg.mengniu.business;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.mengniu.business.abstraction.ActivitySelector;
import com.facishare.crm.fmcg.mengniu.business.abstraction.ILocateActivityArg;
import com.facishare.crm.fmcg.tpm.api.MengNiuTenantInformation;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
@SuppressWarnings("Duplicates")
public class SignInGoodsActivitySelector extends ActivitySelector<SignInGoodsActivitySelector.Arg> {

    @Override
    protected List<IObjectData> queryActivityByAccount(Arg arg, String accountId) {
        if (Strings.isNullOrEmpty(accountId)) {
            return Lists.newArrayList();
        }

        IFilter dealerIdFilter = new Filter();
        dealerIdFilter.setFieldName("reward_activity_tenant__c");
        dealerIdFilter.setOperator(Operator.EQ);
        dealerIdFilter.setFieldValues(Lists.newArrayList(accountId));

        IFilter activityStatusFilter = new Filter();
        activityStatusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        activityStatusFilter.setOperator(Operator.EQ);
        activityStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS));

        IFilter activityTypeFilter = new Filter();
        activityTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
        activityTypeFilter.setOperator(Operator.EQ);
        activityTypeFilter.setFieldValues(Lists.newArrayList("sign_in_goods_rewards__c"));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
                Lists.newArrayList(dealerIdFilter, activityStatusFilter, activityTypeFilter),
                Lists.newArrayList(order)
        );

        return QueryDataUtil.find(
                serviceFacade,
                arg.getTenant().getManufacturer().getTenantId(),
                ApiNames.TPM_ACTIVITY_OBJ,
                stq,
                Lists.newArrayList(CommonFields.ID, TPMActivityFields.STORE_RANGE, TPMActivityFields.PRODUCT_RANGE)
        );
    }

    @Override
    protected List<IObjectData> queryActivityByDepartment(Arg arg, String departmentId) {
        if (Strings.isNullOrEmpty(departmentId)) {
            return Lists.newArrayList();
        }

        List<String> upperDepartmentIds = organizationService.queryUpperDepartmentIds(
                Integer.parseInt(arg.getTenant().getManufacturer().getTenantId()),
                Integer.parseInt(departmentId))
            .stream()
            .map(Object::toString)
            .collect(Collectors.toList());

        IFilter dealerIdFilter = new Filter();
        dealerIdFilter.setFieldName("reward_activity_tenant__c");
        dealerIdFilter.setOperator(Operator.IS);
        dealerIdFilter.setFieldValues(Lists.newArrayList());

        IFilter departmentFilter = new Filter();
        departmentFilter.setFieldName(TPMActivityFields.MULTI_DEPARTMENT_RANGE);
        departmentFilter.setOperator(Operator.HASANYOF);
        departmentFilter.setFieldValues(upperDepartmentIds);

        IFilter activityStatusFilter = new Filter();
        activityStatusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        activityStatusFilter.setOperator(Operator.EQ);
        activityStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS));

        IFilter activityTypeFilter = new Filter();
        activityTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
        activityTypeFilter.setOperator(Operator.EQ);
        activityTypeFilter.setFieldValues(Lists.newArrayList("sign_in_goods_rewards__c"));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
            Lists.newArrayList(dealerIdFilter, departmentFilter, activityStatusFilter, activityTypeFilter),
            Lists.newArrayList(order)
        );

        return QueryDataUtil.find(
            serviceFacade,
            arg.getTenant().getManufacturer().getTenantId(),
            ApiNames.TPM_ACTIVITY_OBJ,
            stq,
            Lists.newArrayList(CommonFields.ID, TPMActivityFields.STORE_RANGE, TPMActivityFields.PRODUCT_RANGE)
        );
    }

    @Getter
    @Builder
    public static class Arg implements ILocateActivityArg {

        private MengNiuTenantInformation tenant;

        private String storeId;
    }
}