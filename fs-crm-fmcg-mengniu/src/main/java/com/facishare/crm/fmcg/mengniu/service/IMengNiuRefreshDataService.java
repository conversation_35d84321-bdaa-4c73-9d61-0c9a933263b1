package com.facishare.crm.fmcg.mengniu.service;

import com.facishare.crm.fmcg.mengniu.api.FillActivityItem;
import com.facishare.crm.fmcg.mengniu.api.FillProductRangeData;
import com.facishare.crm.fmcg.mengniu.api.FillStoreInformation;
import com.facishare.crm.fmcg.mengniu.api.FillTenantInformation;

public interface IMengNiuRefreshDataService {

    FillStoreInformation.Result fillStoreInformation(FillStoreInformation.Arg arg);

    FillStoreInformation.Result fillStoreInformationV1(FillStoreInformation.Arg arg);

    FillTenantInformation.Result fillTenantInformation(FillTenantInformation.Arg arg);

    FillActivityItem.Result fillActivityItem(FillActivityItem.Arg arg);

    FillProductRangeData.Result fillProductRangeData(FillProductRangeData.Arg arg);
}