package com.facishare.crm.fmcg.mengniu.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.mengniu.api.RewardBizDescribe;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.DescribeResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static com.facishare.crm.fmcg.mengniu.consumer.ObjectDataMqConsumer.SUPPORT_OBJECT_API_NAMES;
import static com.facishare.crm.fmcg.mengniu.consumer.ObjectDataMqConsumer.SUPPORT_OP;

@Service
@Slf4j
public class ObjectActionService implements IObjectActionService {
    protected static final Map<String, String> TEMPLATE_TENANT_ID = Maps.newHashMap();
    protected static final Map<String, Map<String, String>> TEMPLATE_TENANT_ID_GROUP_BY_TENANT_ID = Maps.newHashMap();
    public static final List<String> AI_SUPPORT_OBJECT_API_NAMES = Lists.newArrayList();
    protected static final List<String> FMCG_TPM_OBJECT_ACTION_REWARDS_CONDITION_REFERENCE_SUPPORT_FIELDS = Lists.newArrayList();

    @Resource
    private ServiceFacade serviceFacade;

    static {

        ConfigFactory.getConfig("gray-rel-fmcg", conf -> {
            String json = conf.get("mengniu_template_tenant_config");
            if (!Strings.isNullOrEmpty(json)) {
                try {
                    JSONObject templateTenant = JSON.parseObject(json);
                    String n = templateTenant.getString("n");
                    String m = templateTenant.getString("m");
                    if (!StringUtils.isEmpty(n)) {
                        TEMPLATE_TENANT_ID.put("1", n);
                    }
                    if (!StringUtils.isEmpty(m)) {
                        TEMPLATE_TENANT_ID.put("2", m);
                    }
                } catch (Exception ex) {
                    log.error("mengniu template tenant id config parse error : ", ex);
                }
            }

            String mengniuTemplateTenantConfigGroupByTenant = conf.get("mengniu_template_tenant_config_group_by_tenant");
            if (!Strings.isNullOrEmpty(mengniuTemplateTenantConfigGroupByTenant)) {
                try {
                    Map<String, Map<String, String>> templateTenantMap = JSON.parseObject(mengniuTemplateTenantConfigGroupByTenant, new TypeReference<Map<String, Map<String, String>>>() {
                    });
                    for (Map.Entry<String, Map<String, String>> templateTenantEntry : templateTenantMap.entrySet()) {
                        Map<String, String> value = templateTenantEntry.getValue();
                        if (CollectionUtils.isEmpty(value)) {
                            continue;
                        }
                        Map<String, String> templateTenant = Maps.newHashMap();
                        templateTenant.put("1", value.get("n"));
                        templateTenant.put("2", value.get("m"));
                        TEMPLATE_TENANT_ID_GROUP_BY_TENANT_ID.put(templateTenantEntry.getKey(), templateTenant);
                    }
                } catch (Exception ex) {
                    log.error("mengniu_template_tenant_config_group_by_tenant parse error : ", ex);
                }
            }

            String apiSupportJson = conf.get("fmcg_tpm_object_action_rewards_support_ai_object_api_names");
            if (!Strings.isNullOrEmpty(apiSupportJson)) {
                try {
                    AI_SUPPORT_OBJECT_API_NAMES.addAll(JSON.parseArray(apiSupportJson, String.class));
                } catch (Exception ex) {
                    log.error("mengniu fmcg_tpm_object_action_rewards_support_ai_object_api_names parse error : ", ex);
                }
            }

            String objectActionRewardsConditionReferenceSupportFields = conf.get("fmcg_tpm_object_action_rewards_condition_reference_support_fields");
            if (!Strings.isNullOrEmpty(objectActionRewardsConditionReferenceSupportFields)) {
                try {
                    FMCG_TPM_OBJECT_ACTION_REWARDS_CONDITION_REFERENCE_SUPPORT_FIELDS.addAll(JSON.parseArray(objectActionRewardsConditionReferenceSupportFields, String.class));
                } catch (Exception ex) {
                    log.error("mengniu fmcg_tpm_object_action_rewards_condition_reference_support_fields parse error : ", ex);
                }
            }
        });
    }

    @Override
    public RewardBizDescribe.Result bizDescribe(RewardBizDescribe.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        String tenantId;
        if (!TPMGrayUtils.isAllowUseTemplateGroupByTenantId(context.getTenantId())) {
            Map<String, String> tempTenantMap = TEMPLATE_TENANT_ID_GROUP_BY_TENANT_ID.get(context.getTenantId());
            if (CollectionUtils.isEmpty(tempTenantMap)) {
                return RewardBizDescribe.Result.builder().build();
            }
            tenantId = tempTenantMap.get(arg.getTenantTemplate());
        } else {
            tenantId = TEMPLATE_TENANT_ID.get(arg.getTenantTemplate());
        }

        if (StringUtils.isEmpty(tenantId)) {
            return RewardBizDescribe.Result.builder().build();
        }
        if (!SUPPORT_OP.contains(arg.getAction())) {
            return RewardBizDescribe.Result.builder().build();
        }
        if (!SUPPORT_OBJECT_API_NAMES.contains(arg.getBizKey())) {
            return RewardBizDescribe.Result.builder().build();
        }
        DescribeResult describe = serviceFacade.findDescribeAndLayout(User.systemUser(tenantId), arg.getBizKey(), false, null);
        IObjectDescribe objectDescribe = describe.getObjectDescribe();

        objectDescribe.set("__condition_reference_support_fields", FMCG_TPM_OBJECT_ACTION_REWARDS_CONDITION_REFERENCE_SUPPORT_FIELDS);
        objectDescribe.set("__show", AI_SUPPORT_OBJECT_API_NAMES.contains(arg.getBizKey()) || ApiNames.SALES_ORDER_OBJ.equals(arg.getAction()));

        return RewardBizDescribe.Result.builder().bizDescribe(ObjectDescribeDocument.of(objectDescribe)).build();
    }
}
