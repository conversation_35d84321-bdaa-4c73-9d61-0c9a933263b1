package com.facishare.crm.fmcg.mengniu.dao;

import com.facishare.crm.fmcg.mengniu.dao.po.BasePO;
import com.github.mongo.support.DatastoreExt;
import com.google.common.base.Strings;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;

import javax.annotation.Resource;
import java.lang.reflect.ParameterizedType;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Author: linmj
 * Date: 2023/8/9 10:11
 */
public abstract class BaseDAO<T extends BasePO> {

    @Resource
    protected DatastoreExt mongoContext;

    private Class<T> clazz;

    public BaseDAO() {

        this.clazz = (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }


    public String save(T t) {
        if (Strings.isNullOrEmpty(t.getCreatedBy())) {
            t.setCreatedBy("-10000");
        }
        if (Objects.isNull(t.getCreatedAt())) {
            t.setCreatedAt(System.currentTimeMillis());
        }
        return mongoContext.save(t).getId().toString();
    }

    public T get(String id) {
        Query<T> query = mongoContext.createQuery(clazz);
        query.field("_id").equal(new ObjectId(id));
        return query.get();
    }

    public void batchUpdate(List<String> ids, String field, String value) {
        Query<T> query = mongoContext.createQuery(clazz);
        query.field("_id").in(ids.stream().map(ObjectId::new).collect(Collectors.toSet()));

        UpdateOperations<T> updateOperations = mongoContext.createUpdateOperations(clazz)
                .set(field, value)
                .set(BasePO.F_UPDATED_AT, System.currentTimeMillis())
                .set(BasePO.F_UPDATED_BY, "-10000");

        mongoContext.update(query, updateOperations);
    }
}
