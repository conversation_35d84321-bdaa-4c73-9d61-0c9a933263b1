package com.facishare.crm.fmcg.mengniu.service;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.mengniu.api.FillActivityItem;
import com.facishare.crm.fmcg.mengniu.api.FillProductRangeData;
import com.facishare.crm.fmcg.mengniu.api.FillStoreInformation;
import com.facishare.crm.fmcg.mengniu.api.FillTenantInformation;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.UpdateMasterAndDetailData;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
import com.facishare.uc.api.model.fscore.result.GetSimpleEnterpriseResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
@SuppressWarnings("Duplicates")
@Slf4j
public class MengNiuRefreshDataService implements IMengNiuRefreshDataService {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private EnterpriseEditionService enterpriseEditionService;

    @Resource
    private EIEAConverter eieaConverter;

    static final int PAGE_SIZE = 50;

    @Override
    public FillStoreInformation.Result fillStoreInformation(FillStoreInformation.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        log.info("context : {}", JSON.toJSONString(context));

        int page = 1;
        while (page < 500) {
            List<IObjectData> records = queryRecords(context, page);
            log.info("records : {}", JSON.toJSONString(records));

            if (CollectionUtils.isEmpty(records)) {
                break;
            }

            List<IObjectData> data = Lists.newArrayList();

            for (IObjectData datum : records) {
                String oldRelatedStoreId = datum.get(RedPacketRecordFields.RELATED_STORE_ID, String.class);

                if (Strings.isNullOrEmpty(oldRelatedStoreId)) {
                    String storeId;
                    try {
                        storeId = findStoreId(context, datum);
                    } catch (Exception ex) {
                        storeId = "";
                        log.info("find related store id error : ", ex);
                    }
                    if (!Strings.isNullOrEmpty(storeId)) {
                        datum.set(RedPacketRecordFields.RELATED_STORE_ID, storeId);
                        data.add(datum);
                    }
                }
            }

            log.info("data : {}", JSON.toJSONString(data));
            if (CollectionUtils.isNotEmpty(data)) {
                serviceFacade.batchUpdateByFields(User.systemUser(context.getTenantId()), data, Lists.newArrayList(RedPacketRecordFields.RELATED_STORE_ID));
            }

            page = page + 1;
        }

        return FillStoreInformation.Result.builder().build();
    }

    @Override
    public FillStoreInformation.Result fillStoreInformationV1(FillStoreInformation.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        log.info("context : {}", JSON.toJSONString(context));

        int page = 1;
        while (page < 500) {
            List<IObjectData> records = queryRecords(context, page);
            log.info("records : {}", JSON.toJSONString(records));

            if (CollectionUtils.isEmpty(records)) {
                break;
            }

            for (IObjectData datum : records) {
                String oldRelatedStoreId = datum.get(RedPacketRecordFields.RELATED_STORE_ID, String.class);
                String oldRelatedStore = datum.get(RedPacketRecordFields.RELATED_STORE, String.class);

                if (!Strings.isNullOrEmpty(oldRelatedStoreId) && Strings.isNullOrEmpty(oldRelatedStore)) {
                    datum.set(RedPacketRecordFields.RELATED_STORE, oldRelatedStoreId);
                    try {
                        serviceFacade.batchUpdateByFields(User.systemUser(context.getTenantId()), Lists.newArrayList(datum), Lists.newArrayList(RedPacketRecordFields.RELATED_STORE));
                    } catch (Exception ex) {
                        log.error("update error : ", ex);
                    }
                }
            }

            page = page + 1;
        }

        return FillStoreInformation.Result.builder().build();
    }

    @Override
    public FillTenantInformation.Result fillTenantInformation(FillTenantInformation.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        log.info("context : {}", JSON.toJSONString(context));

        IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), ApiNames.FMCG_SERIAL_NUMBER_OBJ);

        int page = 1;
        while (page < 500) {
            List<IObjectData> records = queryConsumerRecords(context, page);
            log.info("records : {}", JSON.toJSONString(records));

            if (CollectionUtils.isEmpty(records)) {
                break;
            }

            for (IObjectData datum : records) {
                try {
                    String oldTenantId = datum.get(RedPacketRecordFields.EVENT_OBJECT_TENANT_ID, String.class);
                    String oldTenantName = datum.get(RedPacketRecordFields.EVENT_OBJECT_TENANT_NAME, String.class);

                    if (Objects.equals(oldTenantId, context.getTenantId())) {
                        String tenantId = findTenantId(context, datum, describe);
                        if (Strings.isNullOrEmpty(tenantId)) {
                            log.error("find tenant id error : {}", datum.getName());
                            continue;
                        }

                        String tenantName = getTenantName(tenantId);
                        if (Strings.isNullOrEmpty(tenantName)) {
                            log.error("tenant name empty : {}", tenantId);
                        }

                        datum.set(RedPacketRecordFields.EVENT_OBJECT_TENANT_ID, tenantId);
                        datum.set(RedPacketRecordFields.EVENT_OBJECT_TENANT_NAME, tenantName);

                        serviceFacade.batchUpdateByFields(
                            User.systemUser(context.getTenantId()),
                            Lists.newArrayList(datum),
                            Lists.newArrayList(
                                RedPacketRecordFields.EVENT_OBJECT_TENANT_ID,
                                RedPacketRecordFields.EVENT_OBJECT_TENANT_NAME
                            )
                        );
                    } else if (!Objects.equals(oldTenantId, context.getTenantId()) && Strings.isNullOrEmpty(oldTenantName)) {
                        String tenantName = getTenantName(oldTenantId);
                        if (Strings.isNullOrEmpty(tenantName)) {
                            log.error("tenant name empty : {}", oldTenantId);
                            continue;
                        }

                        datum.set(RedPacketRecordFields.EVENT_OBJECT_TENANT_NAME, tenantName);

                        serviceFacade.batchUpdateByFields(
                            User.systemUser(context.getTenantId()),
                            Lists.newArrayList(datum),
                            Lists.newArrayList(
                                RedPacketRecordFields.EVENT_OBJECT_TENANT_NAME
                            )
                        );
                    }
                } catch (Exception ex) {
                    log.error("update error : ", ex);
                }
            }

            page = page + 1;
        }

        return FillTenantInformation.Result.builder().build();
    }

    @Override
    public FillActivityItem.Result fillActivityItem(FillActivityItem.Arg arg) {
        // 查询企业
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            // 查询所有下游企业集合
            arg.setTenantIds(getAllTenantIdByUpTenantId(arg.getUpTenantId()));
        }
        SearchTemplateQuery searchTemplateQuery = QueryDataUtil.minimumQuery();
        buildQueryFilter(searchTemplateQuery);

        List<String> fields = Lists.newArrayList(CommonFields.ID, CommonFields.NAME, CommonFields.OBJECT_DESCRIBE_API_NAME);
        // 查询陈列项目
        List<IObjectData> costItemDataList = QueryDataUtil.find(serviceFacade, arg.getUpTenantId(), ApiNames.TPM_ACTIVITY_ITEM_OBJ, searchTemplateQuery, fields);
        if (CollectionUtils.isEmpty(costItemDataList)) {
            FillActivityItem.Result.builder().data("data is empty").build();
        }
        log.info("query cost item list size is {}", costItemDataList.size());
        Map<String, IObjectData> costItemMap = costItemDataList.stream().collect(Collectors.toMap(DBRecord::getId, v -> v));
        log.info("arg tenantIds size is {}", arg.getTenantIds().size());
        int index = 1;
        List<String> errorTenantIds = new ArrayList<>();
        for (String tenantId : arg.getTenantIds()) {

            if (arg.getActivityIdMap() != null) {
                if (arg.getActivityIdMap().containsKey(tenantId)) {
                    String id = arg.getActivityIdMap().get(tenantId);
                    handlerActivityItem(tenantId, id, costItemMap);
                    continue;
                }
            }
            QueryDataUtil.findAndConsume(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_OBJ, searchTemplateQuery, fields, dataList -> {
                log.info("query activity data size is {}", dataList.size());
                try{
                    for (IObjectData activity : dataList) {
                        handlerActivityItem(tenantId, activity.getId(), costItemMap);
                    }
                }catch (Exception exception){
                    log.info("handler error is", exception);
                    errorTenantIds.add(tenantId);
                }
            });

            log.info("current handler end tenantId is {}, index is {}", tenantId, index++);
        }

        log.info("handler error tenantId is {}", errorTenantIds);
        return FillActivityItem.Result.builder().data("done").build();

    }

    @Override
    public FillProductRangeData.Result fillProductRangeData(FillProductRangeData.Arg arg) {
        // 读取企业下的旧字段，刷到新字段的上。
        String tenantId = arg.getTenantId();
        String apiName = arg.getApiName();
        Map<String, String> fieldMap = arg.getFields();
        log.info("fillProductRangeData tenantId is {}, apiName is {}", tenantId, apiName);

        SearchTemplateQuery searchTemplateQuery = QueryDataUtil.minimumQuery();

        List<String> fieldList = new ArrayList<>(fieldMap.keySet());
        fieldList.addAll(Lists.newArrayList(CommonFields.TENANT_ID, CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.NAME));
        QueryDataUtil.findAndConsume(serviceFacade, User.systemUser(tenantId), apiName, searchTemplateQuery, fieldList, dataList -> {
            for (IObjectData objectData : dataList) {
                for (String field : fieldList) {
                    String newField = fieldMap.get(field);
                    objectData.set(newField, objectData.get(field));
                }
            }
            List<List<IObjectData>> partition = Lists.partition(dataList, 200);
            for (List<IObjectData> objectData : partition) {
                // 200一批
                try{
                    serviceFacade.batchUpdateByFields(User.systemUser(tenantId), objectData, new ArrayList<>(fieldMap.values()));
                }catch (Exception exception){
                    log.error("fillProductRangeData batchUpdateByFields fail ", exception);
                }
            }
        });

        return FillProductRangeData.Result.builder().data("SUCCESS").build();
    }

    private void buildQueryFilter(SearchTemplateQuery searchTemplateQuery) {
        IFilter startFilter = new Filter();
        startFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        startFilter.setOperator(Operator.IN);
        startFilter.setFieldValues(Lists.newArrayList(
            TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS,
            TPMActivityFields.ACTIVITY_STATUS__SCHEDULE,
            TPMActivityFields.ACTIVITY_STATUS__APPROVAL));

        IFilter liftFilter = new Filter();
        liftFilter.setFieldName(CommonFields.LIFE_STATUS);
        liftFilter.setOperator(Operator.EQ);
        liftFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        IFilter monthFilter = new Filter();
        monthFilter.setFieldName("month__c");
        monthFilter.setOperator(Operator.IN);
        monthFilter.setFieldValues(Lists.newArrayList("7","8","9","10","11","12"));

        searchTemplateQuery.setFilters(Lists.newArrayList(startFilter, liftFilter, monthFilter));

    }

    private List<String> getAllTenantIdByUpTenantId(String upTenantId) {

        List<String> ids = new ArrayList<>();
        List<IObjectData> objectData = QueryDataUtil.find(serviceFacade, upTenantId, ApiNames.ENTERPRISE_RELATION_OBJ, QueryDataUtil.minimumQuery(), Lists.newArrayList(CommonFields.ID, CommonFields.NAME, "enterprise_account"));
        if (CollectionUtils.isNotEmpty(objectData)) {
            for (IObjectData objectDatum : objectData) {
                String ea = objectDatum.get("enterprise_account", String.class);
                if (!Strings.isNullOrEmpty(ea)) {
                    ids.add(String.valueOf(eieaConverter.enterpriseAccountToId(ea)));
                }
            }
        }
        log.info("getAllTenantIdByUpTenantId ids size is {}", ids);
        return ids;
    }

    private void handlerActivityItem(String tenantId, String id, Map<String, IObjectData> costItemMap) {
        if (Strings.isNullOrEmpty(id)) {
            return;
        }
        // 查询参与活动项目
        List<IObjectData> itemDetail = getItemDetail(tenantId, id);
        // 补全缺失，
        if (CollectionUtils.isNotEmpty(itemDetail)) {
            fillNeedActivityItem(tenantId, itemDetail, costItemMap);
        }
    }

    private void fillNeedActivityItem(String tenantId, List<IObjectData> itemDetail, Map<String, IObjectData> costItemMap) {
        List<String> itemIds = itemDetail.stream().map(v -> v.get(TPMActivityDetailFields.ACTIVITY_ITEM_ID, String.class)).collect(Collectors.toList());
        List<String> needItemIds = new ArrayList<>(costItemMap.keySet());
        needItemIds.removeAll(itemIds);
        IObjectData objectData = itemDetail.get(0);
        List<IObjectData> needData = new ArrayList<>();
        for (String needItemId : needItemIds) {
            IObjectData costItemObj = costItemMap.get(needItemId);
            // 活动项目
            IObjectData data = new ObjectData();
            data.set(CommonFields.NAME, costItemObj.getName());
            data.set(TPMActivityDetailFields.ACTIVITY_ITEM_ID, needItemId);
            data.set(TPMActivityDetailFields.ACTIVITY_ID, objectData.get(TPMActivityDetailFields.ACTIVITY_ID, String.class));
            data.set(TPMActivityDetailFields.CALCULATE_PATTERN, objectData.get(TPMActivityDetailFields.CALCULATE_PATTERN, String.class));
            data.set(TPMActivityDetailFields.AMOUNT_STANDARD_CHECK, objectData.get(TPMActivityDetailFields.AMOUNT_STANDARD_CHECK, Boolean.class));
            data.set(TPMActivityDetailFields.IS_REPORT_ITEM_QUANTITY, objectData.get(TPMActivityDetailFields.IS_REPORT_ITEM_QUANTITY, Boolean.class));
            data.set(TPMActivityDetailFields.PAYMENT_MODE, objectData.get(TPMActivityDetailFields.PAYMENT_MODE, String.class));
            data.set(TPMActivityDetailFields.ACTIVITY_COST_STANDARD, objectData.get(TPMActivityDetailFields.ACTIVITY_COST_STANDARD, BigDecimal.class));
            data.set(TPMActivityDetailFields.ACTIVITY_AMOUNT_STANDARD, objectData.get(TPMActivityDetailFields.ACTIVITY_AMOUNT_STANDARD, BigDecimal.class));
            data.setOwner(objectData.getOwner());
            data.setDescribeApiName(objectData.getDescribeApiName());
            data.setDataOwnDepartment(objectData.getDataOwnDepartment());
            data.setTenantId(objectData.getTenantId());
            data.setRecordType(objectData.getRecordType());
            needData.add(data);
        }
        if (CollectionUtils.isNotEmpty(needData)) {
            serviceFacade.bulkSaveObjectData(needData, User.systemUser(tenantId));
        }
    }

    private List<IObjectData> getItemDetail(String tenantId, String activityId) {
        //TPMActivityDetailObj
        IFilter filter = new Filter();
        filter.setFieldName(TPMActivityDetailFields.ACTIVITY_ID);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(activityId));
        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_DETAIL_OBJ, QueryDataUtil.minimumQuery(filter));
    }

    protected String getTenantName(String tenantId) {
        GetSimpleEnterpriseArg arg = new GetSimpleEnterpriseArg();
        arg.setEnterpriseId(Integer.parseInt(tenantId));

        log.info("load enterprise arg : {}", JSON.toJSONString(arg));

        GetSimpleEnterpriseResult result = enterpriseEditionService.getSimpleEnterprise(arg);

        log.info("load enterprise result : {}", JSON.toJSONString(arg));

        return result.getSimpleEnterprise().getEnterpriseName();
    }

    private String findTenantId(ApiContext context, IObjectData datum, IObjectDescribe describe) {
        String apiName = datum.get(RedPacketRecordFields.EVENT_OBJECT_API_NAME, String.class);
        String dataId = datum.get(RedPacketRecordFields.EVENT_OBJECT_DATA_ID, String.class);
        if (Strings.isNullOrEmpty(apiName) || Strings.isNullOrEmpty(dataId) || !ApiNames.FMCG_SERIAL_NUMBER_OBJ.equals(apiName)) {
            return "";
        }
        IObjectData snObj = serviceFacade.findObjectDataIncludeDeleted(User.systemUser(context.getTenantId()), dataId, apiName);
        return findTenantIdBySn(context, snObj, describe);
    }

    private String findStoreId(ApiContext context, IObjectData datum) {
        String eventTenantId = datum.get(RedPacketRecordFields.EVENT_OBJECT_TENANT_ID, String.class);
        String apiName = datum.get(RedPacketRecordFields.EVENT_OBJECT_API_NAME, String.class);
        String dataId = datum.get(RedPacketRecordFields.EVENT_OBJECT_DATA_ID, String.class);

        IObjectData data = serviceFacade.findObjectDataIncludeDeleted(User.systemUser(eventTenantId), dataId, apiName);
        switch (data.getDescribeApiName()) {
            case "SalesOrderObj":
                return data.get(SalesOrderObjFields.ACCOUNT_ID, String.class);
            case "ShelfReportObj":
                return data.get(ShelfReportObjFields.CUSTOMER_ID, String.class);
            case "DeliveryNoteObj":
                return data.get(DeliveryNoteFields.ACCOUNT_ID, String.class);
            case "FMCGSerialNumberObj":
                return findStoreIdBySn(context, data);
            default:
                return "";
        }
    }

    private String findTenantIdBySn(ApiContext context, IObjectData sn, IObjectDescribe describe) {
        IFilter stateFilter = new Filter();
        stateFilter.setFieldName(FMCGSerialNumberStatusFields.CURRENT_STATE);
        stateFilter.setOperator(Operator.EQ);
        stateFilter.setFieldValues(Lists.newArrayList("6"));

        IFilter snFilter = new Filter();
        snFilter.setFieldName(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID);
        snFilter.setOperator(Operator.EQ);
        snFilter.setFieldValues(Lists.newArrayList(sn.getId()));

        SearchTemplateQuery query = QueryDataUtil.minimumQuery(stateFilter, snFilter);
        query.setLimit(1);

        QueryResult<IObjectData> result = serviceFacade.findBySearchQueryWithDeleted(User.systemUser(context.getTenantId()), describe, query, true, false);

        if (CollectionUtils.isNotEmpty(result.getData())) {
            IObjectData serialNumberStatus = result.getData().get(0);
            String deliveryNoteTenantId = String.valueOf(serialNumberStatus.get(FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, BigDecimal.class).intValue());
            if (!Strings.isNullOrEmpty(deliveryNoteTenantId)) {
                return deliveryNoteTenantId;
            } else {
                return "";
            }
        } else {
            return "";
        }
    }

    private String findStoreIdBySn(ApiContext context, IObjectData sn) {
        IFilter stateFilter = new Filter();
        stateFilter.setFieldName(FMCGSerialNumberStatusFields.CURRENT_STATE);
        stateFilter.setOperator(Operator.EQ);
        stateFilter.setFieldValues(Lists.newArrayList("6"));

        IFilter snFilter = new Filter();
        snFilter.setFieldName(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID);
        snFilter.setOperator(Operator.EQ);
        snFilter.setFieldValues(Lists.newArrayList(sn.getId()));

        SearchTemplateQuery query = QueryDataUtil.minimumQuery(stateFilter, snFilter);
        query.setLimit(1);

        List<IObjectData> data = QueryDataUtil.find(serviceFacade, context.getTenantId(), ApiNames.FMCG_SERIAL_NUMBER_STATUS_OBJ, query, Lists.newArrayList(CommonFields.ID, FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, FMCGSerialNumberStatusFields.BUSINESS_OBJECT_ID));

        if (CollectionUtils.isNotEmpty(data)) {
            IObjectData serialNumberStatus = data.get(0);

            String deliveryNoteTenantId = String.valueOf(serialNumberStatus.get(FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, BigDecimal.class).intValue());
            String deliveryNoteId = serialNumberStatus.get(FMCGSerialNumberStatusFields.BUSINESS_OBJECT_ID, String.class);

            if (!Strings.isNullOrEmpty(deliveryNoteId)) {
                IObjectData deliveryNote = serviceFacade.findObjectDataIncludeDeleted(User.systemUser(deliveryNoteTenantId), deliveryNoteId, ApiNames.DELIVERY_NOTE_OBJ);
                return deliveryNote.get(DeliveryNoteFields.ACCOUNT_ID, String.class);
            } else {
                return "";
            }
        } else {
            return "";
        }
    }

    private List<IObjectData> queryConsumerRecords(ApiContext context, int page) {
        int offset = (page - 1) * PAGE_SIZE;

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.ID);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(order);

        stq.setOffset(offset);
        stq.setLimit(PAGE_SIZE);

        return QueryDataUtil.find(
            serviceFacade,
            context.getTenantId(),
            "red_packet_record__c",
            stq,
            Lists.newArrayList(
                CommonFields.ID,
                CommonFields.NAME,
                CommonFields.OBJECT_DESCRIBE_API_NAME,
                CommonFields.TENANT_ID,
                RedPacketRecordFields.RELATED_STORE,
                RedPacketRecordFields.RELATED_STORE_ID,
                RedPacketRecordFields.EVENT_OBJECT_TENANT_ID,
                RedPacketRecordFields.EVENT_OBJECT_TENANT_NAME,
                RedPacketRecordFields.EVENT_OBJECT_API_NAME,
                RedPacketRecordFields.EVENT_OBJECT_DATA_ID
            )
        );
    }

    private List<IObjectData> queryRecords(ApiContext context, int page) {
        int offset = (page - 1) * PAGE_SIZE;

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.ID);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(order);

        stq.setOffset(offset);
        stq.setLimit(PAGE_SIZE);

        return QueryDataUtil.find(
            serviceFacade,
            context.getTenantId(),
            "red_packet_record__c",
            stq,
            Lists.newArrayList(
                CommonFields.ID,
                CommonFields.OBJECT_DESCRIBE_API_NAME,
                CommonFields.TENANT_ID,
                RedPacketRecordFields.RELATED_STORE,
                RedPacketRecordFields.RELATED_STORE_ID,
                RedPacketRecordFields.EVENT_OBJECT_TENANT_ID,
                RedPacketRecordFields.EVENT_OBJECT_TENANT_NAME,
                RedPacketRecordFields.EVENT_OBJECT_API_NAME,
                RedPacketRecordFields.EVENT_OBJECT_DATA_ID
            )
        );
    }
}