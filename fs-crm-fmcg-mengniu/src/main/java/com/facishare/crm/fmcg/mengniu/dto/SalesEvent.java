package com.facishare.crm.fmcg.mengniu.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
public class SalesEvent<T extends Serializable> implements Serializable {

    @JSONField(name = "event_id")
    @JsonProperty(value = "event_id")
    @SerializedName("event_id")
    private String eventId;

    @JSONField(name = "event_time")
    @JsonProperty(value = "event_time")
    @SerializedName("event_time")
    private long eventTime;

    @JSONField(name = "event_type")
    @JsonProperty(value = "event_type")
    @SerializedName("event_type")
    private String eventType;

    @JSONField(name = "tenant_id")
    @JsonProperty(value = "tenant_id")
    @SerializedName("tenant_id")
    private String tenantId;

    @JSONField(name = "user_id")
    @JsonProperty(value = "user_id")
    @SerializedName("user_id")
    private String userId;

    @JSONField(name = "business_id")
    @JsonProperty(value = "business_id")
    @SerializedName("business_id")
    private String businessId;

    @JSONField(name = "data")
    @JsonProperty(value = "data")
    @SerializedName("data")
    private T data;
}