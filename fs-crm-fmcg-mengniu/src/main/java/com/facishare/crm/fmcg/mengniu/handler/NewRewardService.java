package com.facishare.crm.fmcg.mengniu.handler;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.common.adapter.dto.exception.EventAbandonException;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.constant.ScanCodeActionConstants;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.IdentityIdGenerator;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.mengniu.dto.*;
import com.facishare.crm.fmcg.tpm.retry.setter.RedPacketStatusSetter;
import com.facishare.crm.fmcg.tpm.retry.setter.RewardRuleRedPacketSetter;
import com.facishare.crm.fmcg.tpm.reward.dto.CustomerPublish;
import com.facishare.crm.fmcg.tpm.service.abstraction.IRoleService;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.Objects;

//IgnoreI18nFile
@Slf4j
@Component
@SuppressWarnings("Duplicates")
public class NewRewardService<T extends Serializable> {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    protected RewardRuleRedPacketSetter rewardRuleRedPacketSetter;
    @Resource
    protected RedPacketStatusSetter redPacketStatusSetter;
    @Resource(name = "tpmRoleService")
    protected IRoleService roleService;

    @Resource
    private EIEAConverter eieaConverter;


    public RedPacketRecord publish(SalesEvent<T> event, RedPacketReward reward) {
        StopWatch watch = StopWatch.create("PUBLISH." + event.getEventId());

        RedPacketRecord redPacketRecord = findRedPacketRecord(reward.getTenantId(), reward);
        watch.lap("findRedPacketRecord");
        if (Objects.nonNull(redPacketRecord)) {
            return redPacketRecord;
        }
        //人员行为激励单独的校验逻辑,兼容多活动
        if (TPMGrayUtils.objectActionMultiActivityGray(reward.getTenantId())) {
            if (RewardConstants.USER_OBJECT_ACTION_REWARDS.equals(event.getEventType())) {
                reward.setIdentity(reward.getIdentityWithMultiActivity());
                RedPacketRecord redPacketRecordWithMultiActivity = findRedPacketRecord(reward.getTenantId(), reward);
                watch.lap("redPacketRecordWithMultiActivity");
                if (Objects.nonNull(redPacketRecordWithMultiActivity)) {
                    return redPacketRecordWithMultiActivity;
                }
            }
        }
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(ApiNames.RED_PACKET_RECORD_DETAIL_OBJ, serviceFacade.findObject(reward.getTenantId(), ApiNames.RED_PACKET_RECORD_DETAIL_OBJ));
        describeMap.put(ApiNames.RED_PACKET_RECORD_OBJ, serviceFacade.findObject(reward.getTenantId(), ApiNames.RED_PACKET_RECORD_OBJ));
        watch.lap("findObject");

        Map<String, List<IObjectData>> detailMap = Maps.newHashMap();
        List<IObjectData> details = convertToRedPacketRecordDetailObj(reward, reward.getDetails());
        detailMap.put(ApiNames.RED_PACKET_RECORD_DETAIL_OBJ, details);

        IObjectData master = convertToRedPacketRecordObj(event, reward);
        SaveMasterAndDetailData.Arg arg = SaveMasterAndDetailData.Arg.builder()
                .objectDescribes(describeMap)
                .masterObjectData(master)
                .detailObjectData(detailMap)
                .build();

        SaveMasterAndDetailData.Result result = serviceFacade.saveMasterAndDetailData(User.systemUser(master.getTenantId()), arg);
        watch.lap("saveMasterAndDetailData");

        String distributeWay = master.get(RedPacketRecordObjFields.DISTRIBUTE_WAY, String.class);
        IObjectData masterObjectData = result.getMasterObjectData();
        if (RedPacketRecordObjFields.DistributeWay.AUTO.equals(distributeWay)) {
            rewardRuleRedPacketSetter.setUpdateStatusTask(reward.getTenantId(), masterObjectData.getId());
        } else if (RedPacketRecordObjFields.DistributeWay.ARTIFICIAL.equals(distributeWay)) {
            redPacketStatusSetter.setUpdateStatusTask(reward.getTenantId(), masterObjectData.getId(), masterObjectData.get(RedPacketRecordObjFields.EXPIRATION_TIME, Long.class, 0L));
        }
        watch.lap("setUpdateStatusTask");

        watch.logSlow(300);
        log.info("end publishV2");
        return RedPacketRecord.builder().record(master).build();
    }

    private RedPacketRecord findRedPacketRecord(String tenantId, RedPacketReward reward) {
        IFilter identityFilter = new Filter();
        identityFilter.setFieldName(RedPacketRecordObjFields.RECORD_IDENTITY);
        identityFilter.setOperator(Operator.EQ);
        identityFilter.setFieldValues(Lists.newArrayList(reward.getIdentity()));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(identityFilter);
        stq.setLimit(1);

        List<IObjectData> data = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.RED_PACKET_RECORD_OBJ, stq,
                Lists.newArrayList("_id")
        );
        if (CollectionUtils.isEmpty(data)) {
            return null;
        } else {
            IObjectData redPacketRecord = data.get(0);
            return RedPacketRecord.builder().record(redPacketRecord).build();
        }
    }

    private IObjectData convertToRedPacketRecordObj(SalesEvent<T> event, RedPacketReward reward) {
        IObjectData data = new ObjectData();
        data.setTenantId(reward.getTenantId());
        data.setDescribeApiName(ApiNames.RED_PACKET_RECORD_OBJ);
        data.setRecordType(CommonFields.RECORD_TYPE__DEFAULT);
        data.setOwner(Lists.newArrayList(RewardConstants.SYSTEM_USER));

        data.set(RedPacketRecordObjFields.EVENT_TYPE, RewardConstants.ACTIVITY_REWARD);
        data.set(RedPacketRecordObjFields.TRIGGER_EVENT, RewardConstants.EVENT_TYPE_VALUE_MAP_V2.get(event.getEventType()));
        data.set(RedPacketRecordObjFields.ACTIVITY_ID, reward.getActivityId());
        data.set(RedPacketRecordObjFields.ROLE, RewardConstants.ROLE_VALUE_MAP.get(reward.getRole()));
        data.set(RedPacketRecordObjFields.REWARD_AMOUNT, reward.getAmount());

        data.set(RedPacketRecordObjFields.RELATED_OBJECT_API_NAME, reward.getEventObjectApiName());
        data.set(RedPacketRecordObjFields.RELATED_OBJECT_NAME, reward.getEventObjectName());
        data.set(RedPacketRecordObjFields.RELATED_OBJECT_TENANT_ID, reward.getEventObjectTenantId());

        data.set(RedPacketRecordObjFields.RELATED_OBJECT_TENANT_NAME, reward.getEventObjectTenantName());
        data.set(RedPacketRecordObjFields.RELATED_OBJECT_DATA_ID, reward.getEventObjectDataId());
        data.set(RedPacketRecordObjFields.ACCOUNT_NAME, reward.getRelatedStoreName());
        data.set(RedPacketRecordObjFields.ACCOUNT_ID, reward.getRelatedStoreId());

        if ("89150".equals(reward.getTenantId())) {
            data.set(RedPacketRecordObjFields.ACCOUNT_ID, "64cc9b4c5d39b900017975ce");
            data.set(RedPacketRecordObjFields.TRANSFEROR_TENANT_ID, "89150");

        }
        data.set(RedPacketRecordObjFields.TRANSFEROR_ACCOUNT_TYPE, RewardConstants.ACCOUNT_TYPE_VALUE_MAP_V2.get(reward.getFrom().getAccountType()));
        switch (reward.getFrom().getAccountType()) {
            case "WeChat":
                data.set(RedPacketRecordObjFields.TRANSFEROR_NAME, reward.getFrom().getWeChatPaymentAccount().getRealName());
                data.set(RedPacketRecordObjFields.TRANSFEROR_ID, reward.getFrom().getWeChatPaymentAccount().getIdCardNumber());
                data.set(RedPacketRecordObjFields.TRANSFEROR_WECHAT_OPEN_ID, reward.getFrom().getWeChatPaymentAccount().getOpenId());
                data.set(RedPacketRecordObjFields.TRANSFEROR_PHONE, reward.getFrom().getWeChatPaymentAccount().getPhoneNumber());
                break;
            case "TenantCloud":
                String cloudAccountDealerId = reward.getFrom().getTenantCloudPaymentAccount().getCloudAccountDealerId();
                String cloudAccountBrokerId = reward.getFrom().getTenantCloudPaymentAccount().getCloudAccountBrokerId();
                data.set(RedPacketRecordObjFields.FROM_CLOUD_ACCOUNT_DEALER_ID, cloudAccountDealerId);
                data.set(RedPacketRecordObjFields.FROM_CLOUD_ACCOUNT_BROKER_ID, cloudAccountBrokerId);
                if (Strings.isNullOrEmpty(cloudAccountDealerId) && Strings.isNullOrEmpty(cloudAccountBrokerId)) {
                    data.set(RedPacketRecordObjFields.TRANSFEROR_ACCOUNT, "default");
                } else {
                    JSONObject cloudAccountMap = new JSONObject();
                    cloudAccountMap.put("dealerId", cloudAccountDealerId);
                    cloudAccountMap.put("brokerId", cloudAccountBrokerId);
                    data.set(RedPacketRecordObjFields.TRANSFEROR_ACCOUNT, cloudAccountMap.toString());
                }
                String tenantAccount = reward.getFrom().getTenantCloudPaymentAccount().getTenantAccount();
                String tenantName = reward.getFrom().getTenantCloudPaymentAccount().getTenantName();
                data.set(RedPacketRecordObjFields.TRANSFEROR_TENANT_NAME, tenantName);
                data.set(RedPacketRecordObjFields.TRANSFEROR_TENANT_ID, String.valueOf(eieaConverter.enterpriseAccountToId(tenantAccount)));
                break;
            case "TenantWeChat":
                data.set(RedPacketRecordObjFields.TRANSFEROR_ACCOUNT, "default");
                String wxTenantAccount = reward.getFrom().getTenantWeChatPaymentAccount().getTenantAccount();
                String wxTenantName = reward.getFrom().getTenantWeChatPaymentAccount().getTenantName();
                data.set(RedPacketRecordObjFields.TRANSFEROR_TENANT_NAME, wxTenantName);
                data.set(RedPacketRecordObjFields.TRANSFEROR_TENANT_ID, String.valueOf(eieaConverter.enterpriseAccountToId(wxTenantAccount)));
                break;
            default:
                throw new EventAbandonException("from account type not supported.");
        }

        data.set(RedPacketRecordObjFields.TRANSFEREE_ACCOUNT_TYPE, RewardConstants.ACCOUNT_TYPE_VALUE_MAP_V2.get(reward.getTo().getAccountType()));
        data.set(RedPacketRecordObjFields.TRANSFEREE_TENANT_ID, reward.getEventObjectTenantId());
        data.set(RedPacketRecordObjFields.TRANSFEREE_TENANT_NAME, reward.getEventObjectTenantName());
        if (reward.getTo().getAccountType().equals("WeChat")) {
            if (!Objects.isNull(reward.getTo().getWeChatPaymentAccount())) {
                data.set(RedPacketRecordObjFields.TRANSFEREE_NAME, reward.getTo().getWeChatPaymentAccount().getRealName());
                data.set(RedPacketRecordObjFields.TRANSFEREE_ID, reward.getTo().getWeChatPaymentAccount().getIdCardNumber());
                data.set(RedPacketRecordObjFields.TRANSFEREE_WECHAT_OPEN_ID, reward.getTo().getWeChatPaymentAccount().getOpenId());
                data.set(RedPacketRecordObjFields.TRANSFEREE_PHONE, reward.getTo().getWeChatPaymentAccount().getPhoneNumber());
                data.set(RedPacketRecordObjFields.TRANSFEREE_WECHAT_APP_ID, reward.getTo().getWeChatPaymentAccount().getAppId());
                // 奖励人名称
                data.set(RedPacketRecordObjFields.REWARDED_PERSON, reward.getTo().getWeChatPaymentAccount().getRealName());
                data.set(RedPacketRecordObjFields.TRANSFEREE_WX_UNION_ID, reward.getTo().getWeChatPaymentAccount().getUnionId());

            }
        } else {
            throw new EventAbandonException("to account type not supported.");
        }

        data.set(RedPacketRecordObjFields.RECORD_IDENTITY, reward.getIdentity());
        data.set(RedPacketRecordObjFields.RED_PACKET_LEVEL, reward.getCustomerRewardLevel());
        data.set(RedPacketRecordObjFields.BUSINESS_ID, event.getBusinessId());
        data.set(RedPacketRecordObjFields.TRADING_ID, reward.getPaymentIdentity());
        data.set(RedPacketRecordObjFields.PAYMENT_STATUS, RedPacketRecordObjFields.PaymentStatus.INIT);
        data.set(RedPacketRecordObjFields.REMARKS, reward.getRemarks());

        // 预知对象新字段 需要有
        data.set(RedPacketRecordObjFields.RED_PACKET_STATUS, RedPacketRecordObjFields.RedPacketStatus.EFFECTUATE);
        data.set(RedPacketRecordObjFields.REWARD_TIME, event.getEventTime());
        data.set(RedPacketRecordObjFields.EXPIRATION_TIME, reward.getExpirationTime());
        data.set(RedPacketRecordObjFields.REWARDED_PERSON_ID, reward.getRewardPersonId());

        data.set(RedPacketRecordObjFields.IS_OVER_LIMIT, reward.isOverLimit());
        String publishMode = reward.getPublishMode();
        if (!Strings.isNullOrEmpty(publishMode) && RedPacketReward.PUBLISH_MODE_AUTO.equals(publishMode)) {
            data.set(RedPacketRecordObjFields.DISTRIBUTE_WAY, RedPacketRecordObjFields.DistributeWay.AUTO);
            data.set(RedPacketRecordObjFields.WITHDRAWAL_STATUS, RedPacketRecordObjFields.WithdrawalStatus.NO_NEED);
            if (reward.isOverLimit()) {
                data.set(RedPacketRecordObjFields.PAYMENT_STATUS, RedPacketRecordObjFields.PaymentStatus.ERROR);
                data.set(RedPacketRecordObjFields.PAYMENT_ERROR_MESSAGE, "活动余额不足。");
            }
        } else {
            data.set(RedPacketRecordObjFields.WITHDRAWAL_STATUS, RedPacketRecordObjFields.WithdrawalStatus.AWAIT);
            data.set(RedPacketRecordObjFields.DISTRIBUTE_WAY, RedPacketRecordObjFields.DistributeWay.ARTIFICIAL);
            if (reward.isOverLimit()) {
                // 暂不提现 change
                data.set(RedPacketRecordObjFields.PAYMENT_STATUS, RedPacketRecordObjFields.PaymentStatus.ERROR);
            }
        }

        data.set(RedPacketRecordObjFields.REWARD_PART, RewardConstants.ROLE_VALUE_MAP_V2.get(reward.getRole()));
        // 判断是否消费者，CONSUMER = 1，触发事件为消费者领红包，有监听会生成码状态
        if ("CONSUMER".equals(reward.getRole())) {
            data.set(RedPacketRecordObjFields.TRIGGER_EVENT, ScanCodeActionConstants.CONSUMER_GET_RED_PACKAGE);
        }

        // 角色信息
        fillRewardRoleInfo(reward.getEventObjectTenantId(), reward.getRewardPersonId(), reward.getRewardPersonType(), data);
        // 判断角色是店老板，STORE_OWNER, 给奖励人角色ID = 1
        if ("STORE_OWNER".equals(reward.getRole())) {
            String rewardPartCode = data.get(RedPacketRecordObjFields.REWARD_PART_CODE, String.class);
            if ("0".equals(rewardPartCode)) {
                data.set(RedPacketRecordObjFields.REWARD_PART_CODE, "1");
            }
        }

        return data;
    }

    private void fillRewardRoleInfo(String tenantId, String rewardPersonId, String rewardPersonType, IObjectData data) {
        if (rewardPersonId == null) {
            return;
        }
        log.info("salesEvent fillRewardRoleInfo rewardPersonId is {}", rewardPersonId);
        try {
            String[] split = rewardPersonId.split("\\.");
            //填充主角色
            if (ApiNames.CONTACT_OBJ.equals(rewardPersonType)) {
                String outerTenantId = split[0];
                String outerUserId = split[1];
                IObjectData employeeObj = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), outerUserId, ApiNames.PUBLIC_EMPLOYEE_OBJ);
                if (employeeObj != null) {
                    // 查看联系人的 人员类型字段
                    Boolean relationOwner = employeeObj.get(PublicEmployeeFields.RELATION_OWNER, Boolean.class);
                    data.set(RedPacketRecordObjFields.REWARD_PART_CODE, Boolean.TRUE.equals(relationOwner) ? "1" : "0");
                } else {
                    log.info("can not find main role.employee:{}", outerUserId);
                }
            }
        } catch (Exception ex) {
            log.error("salesEvent fillRewardRoleInfo error ", ex);
        }
    }

    private List<IObjectData> convertToRedPacketRecordDetailObj(RedPacketReward reward, List<RedPacketRewardDetail> details) {
        List<IObjectData> data = Lists.newArrayList();
        for (RedPacketRewardDetail detail : details) {
            IObjectData datum = new ObjectData();
            datum.setTenantId(reward.getTenantId());
            datum.setDescribeApiName(ApiNames.RED_PACKET_RECORD_DETAIL_OBJ);
            datum.setOwner(Lists.newArrayList(RewardConstants.SYSTEM_USER));
            datum.setRecordType(CommonFields.RECORD_TYPE__DEFAULT);

            datum.set(RedPacketRecordDetailObjFields.SALES_ORDER_DETAIL_ID, detail.getSalesOrderDetailId());
            datum.set(RedPacketRecordDetailObjFields.SALES_ORDER_DETAIL_NAME, detail.getSalesOrderDetailName());

            datum.set(RedPacketRecordDetailObjFields.PRODUCT_ID, detail.getProductId());

            datum.set(RedPacketRecordDetailObjFields.SERIAL_NUMBER_ID, detail.getSerialNumberId());
            datum.set(RedPacketRecordDetailObjFields.MANUFACTURE_DATE, detail.getManufactureDate());
            datum.set(RedPacketRecordDetailObjFields.BATCH_CODE, detail.getBatchCode());

            datum.set(RedPacketRecordDetailObjFields.RED_PACKET_AMOUNT, detail.getAmount());
            datum.set(RedPacketRecordDetailObjFields.AMOUNT_CONFIG_ID, detail.getAmountConfigId());

            data.add(datum);
        }
        return data;
    }

    public BigDecimal calculateRewardedAmount(IObjectData activity) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(RedPacketRecordObjFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activity.getId()));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(TPMActivityFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("normal"));

        query.setFilters(Lists.newArrayList(lifeStatusFilter, activityIdFilter));

        List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                User.systemUser(activity.getTenantId()),
                query,
                ApiNames.RED_PACKET_RECORD_OBJ,
                Lists.newArrayList(RedPacketRecordObjFields.ACTIVITY_ID),
                "sum",
                RedPacketRecordObjFields.REWARD_AMOUNT);

        if (!CollectionUtils.isEmpty(data)) {
            return data.get(0).get("sum_" + RedPacketRecordObjFields.REWARD_AMOUNT, BigDecimal.class);
        } else {
            return new BigDecimal("0");
        }
    }

    /**
     * 客户发布红包奖励
     *
     * @param arg 客户发布红包参数
     * @return 发布结果
     */
    public CustomerPublish.Result customerPublishRedPacket(CustomerPublish.Arg arg) {
        // 参数校验
        if (Objects.isNull(arg) || Strings.isNullOrEmpty(arg.getTenantId())
                || Strings.isNullOrEmpty(arg.getApiName())
                || Strings.isNullOrEmpty(arg.getDataId())) {
            return buildErrorResult("参数不完整");
        }

        // 查询自定义对象数据
        IObjectData customData = serviceFacade.findObjectDataIgnoreAll(
                User.systemUser(arg.getTenantId()),
                arg.getDataId(),
                arg.getApiName()

        );

        if (Objects.isNull(customData)) {
            log.error("未找到对应的自定义对象数据, tenantId:{}, apiName:{}, dataId:{}",
                    arg.getTenantId(), arg.getApiName(), arg.getDataId());
            return buildErrorResult("未找到对应的自定义对象数据");
        }

        // 判断是否已存在红包关联
        String redPacketId = customData.get("redPacket_id__c", String.class);
        if (!Strings.isNullOrEmpty(redPacketId)) {
            log.info("该数据已存在红包关联, redPacketId:{}", redPacketId);
            return buildErrorResult("该数据已存在红包关联");
        }

        try {
            // 构建销售事件和红包奖励对象
            SalesEvent<T> salesEvent = buildSalesEvent();
            RedPacketReward reward = buildRedPacketReward(arg, customData);

            // 发布红包奖励
            RedPacketRecord result = publish(salesEvent, reward);

            return CustomerPublish.Result.builder()
                    .success(true)
                    .redPackId(result.getRecord().getId())
                    .build();

        } catch (Exception e) {
            log.error("发布红包奖励失败", e);
            return buildErrorResult("发布红包奖励失败: " + e.getMessage());
        }
    }

    /**
     * 构建销售事件
     */
    private SalesEvent<T> buildSalesEvent() {
        SalesEvent<T> event = new SalesEvent<>();
        event.setEventId(String.valueOf(System.currentTimeMillis()));
        // 用户出发的激励
        event.setEventType("CUSTOM_OBJECT_ACTION_TRIGGER");
        event.setEventTime(System.currentTimeMillis());
        event.setBusinessId(IdentityIdGenerator.formPaymentIdentityId());
        return event;
    }

    /**
     * 构建红包奖励对象
     */
    private RedPacketReward buildRedPacketReward(CustomerPublish.Arg arg, IObjectData customData) {
        PaymentAccount fromAccount = buildFromPaymentAccount(customData);
        PaymentAccount toAccount = buildToPaymentAccount(customData);
        String role = getRole(customData.get("role__c", String.class));

        return RedPacketReward.builder()
                .tenantId(arg.getTenantId())
                .identity(customData.get("record_identity__c", String.class))
                .amount(customData.get("red_packet_amount__c", BigDecimal.class))
                .role(role)
                .rewardPersonType("STORE_OWNER".equals(role) ? ApiNames.CONTACT_OBJ : "")
                .eventObjectApiName(arg.getApiName())
                .eventObjectDataId(customData.getId())
                .eventObjectName(customData.getDescribeApiName())
                .eventObjectTenantId(customData.get("transferee_tenant_id__c", String.class))
                .relatedStoreId(customData.get("account_id__c", String.class))
                .expirationTime(getCalendar().getTimeInMillis())
                .publishMode(RedPacketReward.PUBLISH_MODE_ARTIFICIAL)
                .from(fromAccount)
                .to(toAccount)
                .rewardPersonId(customData.get("rewardPersonId__c", String.class))
                .details(Lists.newArrayList())
                .build();
    }

    private String getRole(String role) {
        if (Strings.isNullOrEmpty(role)) {
            return "default";
        }
        return RewardConstants.ROLE_VALUE_MAP_REVERSE.getOrDefault(role, "default");
    }


    /**
     * 获取账户类型
     *
     * @param accountType 原始账户类型
     * @return 转换后的账户类型
     */
    private String getAccountType(String accountType) {
        switch (accountType) {
            case "wechat":
                return "WeChat";
            case "cloud":
                return "TenantCloud";
            case "wechatMerchant":
                return "TenantWeChat";
            default:
                return "default";
        }
    }

    @NotNull
    private static Calendar getCalendar() {
        // 计算过期时间 - 当天往后30天的23:59:59
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 30);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar;
    }

    /**
     * 构建付款方账户信息
     */
    private PaymentAccount buildFromPaymentAccount(IObjectData customData) {
        String accountType = customData.get("transferor_account_type__c", String.class);
        if (Strings.isNullOrEmpty(accountType)) {
            throw new EventAbandonException("accountType is empty.");
        }

        PaymentAccount paymentAccount = new PaymentAccount();
        paymentAccount.setAccountType(getAccountType(accountType));

        switch (accountType) {
            case "wechat":
                WeChatPaymentAccount weChatAccount = WeChatPaymentAccount.builder()
                        .realName(customData.get("transferor_name__c", String.class))
                        .idCardNumber(customData.get("transferor_id__c", String.class))
                        .openId(customData.get("transferor_wechat_open_id__c", String.class))
                        .phoneNumber(customData.get("transferor_phone__c", String.class))
                        .build();
                paymentAccount.setWeChatPaymentAccount(weChatAccount);
                break;

            case "cloud":
                String cloudAccountDealerId = customData.get("from_cloud_account_dealer_id__c", String.class);
                String cloudAccountBrokerId = customData.get("from_cloud_account_broker_id__c", String.class);

                TenantCloudPaymentAccount cloudAccount = TenantCloudPaymentAccount.builder()
                        .cloudAccountDealerId(cloudAccountDealerId)
                        .cloudAccountBrokerId(cloudAccountBrokerId)
                        .tenantAccount(customData.get("transferor_tenant_id__c", String.class))
                        .tenantName(customData.get("transferor_tenant_name__c", String.class))
                        .build();
                paymentAccount.setTenantCloudPaymentAccount(cloudAccount);
                break;

            case "wechatMerchant":
                TenantWeChatPaymentAccount tenantWeChatAccount = TenantWeChatPaymentAccount.builder()
                        .tenantAccount(customData.get("transferor_tenant_id__c", String.class))
                        .tenantName(customData.get("transferor_tenant_name__c", String.class))
                        .account("default")
                        .build();
                paymentAccount.setTenantWeChatPaymentAccount(tenantWeChatAccount);
                break;

            default:
                throw new EventAbandonException("Unsupported payer account type: " + accountType);
        }

        return paymentAccount;
    }

    /**
     * 构建收款方账户信息
     */
    private PaymentAccount buildToPaymentAccount(IObjectData customData) {
        String accountType = customData.get("transferee_account_type__c", String.class);
        if (Strings.isNullOrEmpty(accountType)) {
            throw new EventAbandonException("accountType is empty.");
        }

        PaymentAccount paymentAccount = new PaymentAccount();
        paymentAccount.setAccountType(getAccountType(accountType));

        if ("wechat".equals(accountType)) {
            WeChatPaymentAccount weChatAccount = WeChatPaymentAccount.builder()
                    .realName(customData.get("transferee_name__c", String.class))
                    .idCardNumber(customData.get("transferee_id__c", String.class))
                    .openId(customData.get("transferee_wechat_open_id__c", String.class))
                    .phoneNumber(customData.get("transferee_phone__c", String.class))
                    .appId(customData.get("transferee_wechat_app_id__c", String.class))
                    .unionId(customData.get("transferee_wx_union_id__c", String.class))
                    .build();
            paymentAccount.setWeChatPaymentAccount(weChatAccount);
        } else {
            throw new EventAbandonException("The payee only supports WeChat account type");
        }

        return paymentAccount;
    }

    /**
     * 构建错误结果
     */
    private CustomerPublish.Result buildErrorResult(String message) {
        return CustomerPublish.Result.builder()
                .success(false)
                .message(message)
                .build();
    }
}
