package com.facishare.crm.fmcg.mengniu.api;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

public interface TriggerAction {

    @Builder
    @Data
    @ToString
    class Arg implements Serializable {

        private String tenantId;

        private IObjectData objectData;

        private String apiName;

        private String actionName;

        private boolean triggerWorkflow;

        private boolean triggerFlow;

        private boolean skipBaseValidate;

        private boolean notValidate;

        private boolean skipFunctionAction;

        private boolean skipValidationFunctionCheck;

        private boolean skipValidationRuleCheck;
    }
}
