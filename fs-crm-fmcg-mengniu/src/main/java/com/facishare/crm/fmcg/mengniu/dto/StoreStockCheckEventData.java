package com.facishare.crm.fmcg.mengniu.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
public class StoreStockCheckEventData implements Serializable {

    @JSONField(name = "store_stock_check_id")
    @JsonProperty(value = "store_stock_check_id")
    @SerializedName("store_stock_check_id")
    private String storeStockCheckId;
}