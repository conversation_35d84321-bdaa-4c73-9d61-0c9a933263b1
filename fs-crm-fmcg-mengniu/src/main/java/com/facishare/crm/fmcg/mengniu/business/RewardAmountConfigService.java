package com.facishare.crm.fmcg.mengniu.business;

import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.mengniu.dto.RewardAmountConfig;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@SuppressWarnings("Duplicates")
public class RewardAmountConfigService implements InitializingBean {

    @Override
    public void afterPropertiesSet() {
        cache = CacheBuilder.newBuilder()
                .maximumSize(100)
                .expireAfterWrite(15, TimeUnit.MINUTES)
                .build();
    }

    private Cache<String, Map<String, RewardAmountConfig>> cache;

    @Resource
    private ServiceFacade serviceFacade;

    protected static final Map<String, String> ROLE_VALUE_MAP_REVERSE = Maps.newHashMap();

    static {
        ROLE_VALUE_MAP_REVERSE.put("1", "CONSUMER");
        ROLE_VALUE_MAP_REVERSE.put("2", "STORE_OWNER");
        ROLE_VALUE_MAP_REVERSE.put("3", "SALESMEN");
        ROLE_VALUE_MAP_REVERSE.put("4", "DEALER");
        ROLE_VALUE_MAP_REVERSE.put("5", "M_BOSS");
    }

    public RewardAmountConfig get(String tenantId, String activityId, String role, String storeId, String productId) {
        Map<String, RewardAmountConfig> configs = load(tenantId, activityId);

        RewardAmountConfig amount = configs.get(buildupStoreProductAmountKey(role, storeId, productId));
        if (!Objects.isNull(amount)) {
            return amount;
        }

        amount = configs.get(buildupStoreAmountKey(role, storeId));
        if (!Objects.isNull(amount)) {
            return amount;
        }

        amount = configs.get(buildupProductAmountKey(role, productId));
        if (!Objects.isNull(amount)) {
            return amount;
        }

        amount = configs.get(buildupRoleAmountKey(role));
        if (!Objects.isNull(amount)) {
            return amount;
        }

        return null;
    }

    private Map<String, RewardAmountConfig> load(String tenantId, String activityId) {
        String key = buildupCacheKey(tenantId, activityId);
        Map<String, RewardAmountConfig> configs = cache.getIfPresent(key);
        if (Objects.isNull(configs)) {
            configs = loadConfigsFromPG(tenantId, activityId);
            cache.put(key, configs);
        }
        return configs;
    }

    private Map<String, RewardAmountConfig> loadConfigsFromPG(String tenantId, String activityId) {
        List<RewardAmountConfig> data = queryRewardAmountConfig(tenantId, activityId);
        Map<String, RewardAmountConfig> config = Maps.newHashMap();
        for (RewardAmountConfig datum : data) {
            String role = datum.getRole();
            if (!Strings.isNullOrEmpty(role)) {
                String realRole = ROLE_VALUE_MAP_REVERSE.get(role);
                if (!Strings.isNullOrEmpty(realRole)) {
                    String key = buildupKey(datum.getStoreId(), datum.getProductId(), realRole);
                    if (!Strings.isNullOrEmpty(key)) {
                        config.put(key, datum);
                    }
                }
            }
        }
        return config;
    }

    private String buildupKey(String store, String product, String realRole) {
        String key = null;
        if (!Strings.isNullOrEmpty(store) && !Strings.isNullOrEmpty(product)) {
            key = buildupStoreProductAmountKey(realRole, store, product);
        }
        if (!Strings.isNullOrEmpty(store) && Strings.isNullOrEmpty(product)) {
            key = buildupStoreAmountKey(realRole, store);
        }
        if (Strings.isNullOrEmpty(store) && !Strings.isNullOrEmpty(product)) {
            key = buildupProductAmountKey(realRole, product);
        }
        if (Strings.isNullOrEmpty(store) && Strings.isNullOrEmpty(product)) {
            key = buildupRoleAmountKey(realRole);
        }
        return key;
    }

    private String buildupCacheKey(String tenantId, String activityId) {
        return String.format("CK.T-%s.A-%s", tenantId, activityId);
    }

    private String buildupStoreProductAmountKey(String role, String store, String product) {
        return String.format("AK.R-%s.S-%s.P-%s", role, store, product);
    }

    private String buildupStoreAmountKey(String role, String store) {
        return String.format("AK.R-%s.S-%s", role, store);
    }

    private String buildupProductAmountKey(String role, String product) {
        return String.format("AK.R-%s.P-%s", role, product);
    }

    private String buildupRoleAmountKey(String role) {
        return String.format("AK.R-%s", role);
    }

    private List<RewardAmountConfig> queryRewardAmountConfig(String tenantId, String activityId) {
        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName("activity_id__c");
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        SearchTemplateQuery configQuery = QueryDataUtil.minimumQuery(activityIdFilter);

        List<IObjectData> configs = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                "red_packet_amount_config__c",
                configQuery,
                Lists.newArrayList("_id",
                        "activity_id__c",
                        "store_id__c",
                        "product_id__c",
                        "role__c",
                        "amount__c",
                        "l1_red_packet_amount__c",
                        "l2_red_packet_amount__c",
                        "l3_red_packet_amount__c",
                        "l4_red_packet_amount__c",
                        "l5_red_packet_amount__c"
                ));

        return CollectionUtils.isEmpty(configs) ? Lists.newArrayList() : configs.stream().map(RewardAmountConfig::new).collect(Collectors.toList());
    }
}