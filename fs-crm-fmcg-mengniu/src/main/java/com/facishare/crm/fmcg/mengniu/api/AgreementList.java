package com.facishare.crm.fmcg.mengniu.api;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface AgreementList {

    @Data
    @ToString
    class Arg implements Serializable {

        private String type;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private List<ObjectDataDocument> data;

        private ObjectDescribeDocument describe;

        @JSONField(name = "simple_layout")
        @JsonProperty(value = "simple_layout")
        @SerializedName("simple_layout")
        private JSONObject simpleLayout;
    }
}