package com.facishare.crm.fmcg.mengniu.api;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@Builder
public
class MengNiuOptionVO implements Serializable {

    private String value;

    private String label;

    @JSONField(name = "font_color")
    @JsonProperty(value = "font_color")
    @SerializedName("font_color")
    private String fontColor;
}
