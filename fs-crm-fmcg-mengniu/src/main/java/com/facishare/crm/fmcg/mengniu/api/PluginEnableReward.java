package com.facishare.crm.fmcg.mengniu.api;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

public interface PluginEnableReward {

    @Data
    @ToString
    class Arg implements Serializable {

        private String code;

        private String environment;

        @JSONField(name = "tenant_code")
        @JsonProperty(value = "tenant_code")
        @SerializedName("tenant_code")
        private String tenantCode;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private String status;

        private String code;
    }
}