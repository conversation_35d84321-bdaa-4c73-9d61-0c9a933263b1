package com.facishare.crm.fmcg.mengniu.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.file.FileCenterAdapter;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofAuditSourceConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.service.TransactionProxy;
import com.facishare.crm.fmcg.tpm.service.abstraction.IRoleService;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.contract.AgreementAuditDataList;
import com.facishare.crm.fmcg.tpm.web.contract.AgreementAuditSummaryList;
import com.facishare.crm.fmcg.tpm.web.contract.AgreementItemsAuditList;
import com.facishare.crm.fmcg.tpm.web.contract.OperateAudit;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityTypeVO;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.service.TenantHierarchyService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.Image;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.RecordTypeFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.common.Pair;
import com.github.autoconf.ConfigFactory;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/23 14:11
 */
//IgnoreI18nFile
@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class AgreementAuditService implements IAgreementAuditService {
    public static final String AUDIT_DATA_KEY = "audit_data";
    public static final String APPLICATION_DATA_KEY = "application_data";
    public static final String VISIT_DATA_KEY = "visit_data";

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private EnterpriseEditionService enterpriseEditionService;

    @Resource
    private ActivityTypeManager activityTypeManager;

    @Resource
    private TransactionProxy transactionProxy;

    @Resource(name = "tpmRoleService")
    private IRoleService roleService;

    @Resource
    private RedissonClient redissonCmd;

    @Resource
    private FileCenterAdapter fileCenterAdapter;

    @Resource
    protected InfraServiceFacade infraServiceFacade;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private TenantHierarchyService tenantHierarchyService;

    private static String areaUserRole;
    private static String channelUserRole;
    private static String rbuUserRole;

    private static String itemCode;

    static {
        ConfigFactory.getConfig("fs-fmcg-tpm-config", config -> {
            areaUserRole = config.get("AREA_USER_ROLE");
            channelUserRole = config.get("CHANNEL_USER_ROLE");
            rbuUserRole = config.get("RBU_USER_ROLE");
            itemCode = config.get("ITEM_CODE");
        });
    }


    public AgreementAuditSummaryList.Result auditSummaryList(AgreementAuditSummaryList.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        // 一端协议的id
        String agreementId = arg.getAgreementId();
        log.info("MN auditSummaryList arg is {}", arg);
        if (Strings.isNullOrEmpty(agreementId)) {
            log.info("auditSummaryList agreementId is empty.");
            return buildAuditSummaryListResult();
        }
        String accountRecordType = findAccountRecordType(context.getTenantId(), arg.getDealerId());

        String downstreamTenantId;
        if (Strings.isNullOrEmpty(arg.getOutTenantId())) {
            downstreamTenantId = findDownstreamTenantIdByAccountId(context.getTenantId(), arg.getDealerId());
        } else {
            downstreamTenantId = findDownstreamTenantIdByOutId(context.getTenantId(), arg.getOutTenantId());
        }
        if (Strings.isNullOrEmpty(downstreamTenantId)) {
            log.info("auditSummaryList downstreamTenantId is empty.");
            return buildAuditSummaryListResult();
        }
        // 查询N端的协议数据
        String agreementStatus = getAgreementStatus(downstreamTenantId, agreementId);
        if (Strings.isNullOrEmpty(agreementStatus)) {
            return buildAuditSummaryListResult();
        }

        // "9", "11", "12", "13", "14"  mollercular_company__c 分子公司 "9", "13", "14" -- 0607 fix忽略掉
        if (!Arrays.asList("9", "11", "12", "13", "14").contains(agreementStatus)) {
            return buildAuditSummaryListResult();
        }

        SearchTemplateQuery queryActivity = getSearchTemplateQuery(agreementId);

        List<String> proofIdList = new ArrayList<>();
        User user = User.systemUser(downstreamTenantId);
        List<String> fieldList = Lists.newArrayList(CommonFields.ID, CommonFields.NAME);
        QueryDataUtil.findAndConsume(serviceFacade, user, ApiNames.TPM_ACTIVITY_PROOF_OBJ, queryActivity, fieldList, dataList -> {
            proofIdList.addAll(dataList.stream().map(DBRecord::getId).collect(Collectors.toList()));
        });

        boolean useAuditRole = false;
        List<String> roles = roleService.queryRoleByEmployeeId(Integer.parseInt(context.getTenantId()), context.getEmployeeId());
        if (CollectionUtils.isNotEmpty(roles)) {
            List<String> useRoles = Lists.newArrayList(areaUserRole, channelUserRole, rbuUserRole);
            List<String> role = roles.stream().filter(useRoles::contains).collect(Collectors.toList());
            useAuditRole = CollectionUtils.isNotEmpty(role);
        }

        AgreementAuditSummaryList.AgreementAuditSummaryDatumVO datumVO = new AgreementAuditSummaryList.AgreementAuditSummaryDatumVO();
        datumVO.setDataIdList(proofIdList);
        return buildAuditSummaryListResult(datumVO, useAuditRole);
    }

    private String findAccountRecordType(String tenantId, String dealerId) {
        if (Strings.isNullOrEmpty(dealerId)) {
            return null;
        }
        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(tenantId), dealerId, ApiNames.ACCOUNT_OBJ);
        return objectData != null ? objectData.getRecordType() : null;
    }

    private String getAgreementStatus(String downstreamTenantId, String agreementId) {
        // 自定义字段  field_Gvb1m__c  协议状态
        IObjectData agreementObj = serviceFacade.findObjectData(User.systemUser(downstreamTenantId), agreementId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        return agreementObj != null ? agreementObj.get("field_Gvb1m__c", String.class) : null;
    }

    AgreementAuditSummaryList.Result buildAuditSummaryListResult() {
        return buildAuditSummaryListResult(null, false);
    }

    AgreementAuditSummaryList.Result buildAuditSummaryListResult(AgreementAuditSummaryList.AgreementAuditSummaryDatumVO datumVO,
                                                                 boolean useAuditRole) {
        if (Objects.isNull(datumVO)) {
            return AgreementAuditSummaryList.Result.builder()
                    .data(Lists.newArrayList())
                    .useAuditRole(useAuditRole)
                    .total(1)
                    .build();
        }
        return AgreementAuditSummaryList.Result.builder()
                .data(Lists.newArrayList(datumVO))
                .useAuditRole(useAuditRole)
                .total(1)
                .build();
    }


    private String findDownstreamTenantIdByOutId(String tenantId, String outTenantId) {
        if (Strings.isNullOrEmpty(outTenantId)) {
            log.info("findDownstreamTenantIdByAccountId outTenantId is empty. tenantId : {}", tenantId);
            return null;
        }
        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(tenantId), outTenantId, ApiNames.ENTERPRISE_RELATION_OBJ);
        if (Objects.isNull(objectData)) {
            return null;
        }
        String tenantAccount = objectData.get(EnterpriseRelationFields.ENTERPRISE_ACCOUNT, String.class);
        return String.valueOf(enterpriseEditionService.getEnterpriseId(tenantAccount));
    }

    public AgreementAuditDataList.Result dataList(AgreementAuditDataList.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        // 一端协议的id
        String agreementId = arg.getAgreementId();
        String storeId = arg.getId();
        log.info("MN dataList arg is {}", arg);
        if (Strings.isNullOrEmpty(agreementId) || CollectionUtils.isEmpty(arg.getDataIdList())) {
            return AgreementAuditDataList.Result.builder().build();
        }

        String downstreamTenantId;
        if (Strings.isNullOrEmpty(arg.getOutTenantId())) {
            downstreamTenantId = findDownstreamTenantIdByAccountId(context.getTenantId(), arg.getDealerId());
        } else {
            downstreamTenantId = findDownstreamTenantIdByOutId(context.getTenantId(), arg.getOutTenantId());
        }
        if (Strings.isNullOrEmpty(downstreamTenantId)) {
            return AgreementAuditDataList.Result.builder().build();
        }

        String activityId = arg.getActivityId();
        if (Strings.isNullOrEmpty(activityId)) {
            activityId = findActivityIdByAgreementId(downstreamTenantId, agreementId);
        }

        ActivityTypeExt activityType = activityTypeManager.findByActivityId(downstreamTenantId, activityId);

        Map<String, ObjectDescribeDocument> describeMap = Maps.newHashMap();
        Map<String, Map<String, LayoutDocument>> layoutMap = Maps.newHashMap();

        fillDescribeAndLayout(downstreamTenantId, -10000, activityType.auditSourceConfig(), describeMap, layoutMap);

        List<AgreementAuditDataList.AuditDatumVO> data = loadAuditDatum(
                downstreamTenantId,
                arg.getDataIdList(),
                activityType,
                arg.getAuditObjectRecordType()
        );

        // 图片数据 共享访问
        // handleDataImageShare(downstreamTenantId, data);


        return AgreementAuditDataList.Result.builder()
                .objectDescribeMap(describeMap)
                .layoutMap(layoutMap)
                .fieldShowNameMap(new HashMap<>())
                .mainObjectData(getCoreData(downstreamTenantId, storeId))
                .activityType(ActivityTypeVO.fromPO(activityType.get()))
                .data(data)
                .build();
    }

    private String findActivityIdByAgreementId(String downstreamTenantId, String agreementId) {
        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(downstreamTenantId), agreementId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        return objectData.get(TPMActivityAgreementFields.ACTIVITY_ID, String.class);
    }

    @Nullable
    private ObjectDataDocument getCoreData(String downstreamTenantId, String storeId) {
        ObjectDataDocument coreData = null;
        if (!Strings.isNullOrEmpty(storeId)) {
            IObjectDescribe describe = serviceFacade.findObject(downstreamTenantId, ApiNames.ACCOUNT_OBJ);
            IObjectData coreObjectData = serviceFacade.findObjectData(User.systemUser(downstreamTenantId), storeId, ApiNames.ACCOUNT_OBJ);
            coreData = ObjectDataDocument.of(fillWebDetailAuditData(downstreamTenantId, describe, coreObjectData));
        }
        return coreData;
    }

    private void fillDescribeAndLayout(String tenantId,
                                       Integer userId,
                                       ActivityProofAuditSourceConfigEntity auditSourceConfig,
                                       Map<String, ObjectDescribeDocument> describeMap,
                                       Map<String, Map<String, LayoutDocument>> layoutMap) {
        List<String> relatedObjectApiNames = Lists.newArrayList(auditSourceConfig.getMasterApiName(), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);
        if (!Strings.isNullOrEmpty(auditSourceConfig.getDetailApiName())) {
            relatedObjectApiNames.add(auditSourceConfig.getDetailApiName());
            relatedObjectApiNames.add(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ);
        }

        for (String relatedObjectApiName : relatedObjectApiNames) {
            fillDescribe(tenantId, relatedObjectApiName, describeMap);
            fillLayout(tenantId, userId, relatedObjectApiName, describeMap, layoutMap);
        }
    }


    private void fillLayout(String tenantId, Integer userId, String apiName, Map<String, ObjectDescribeDocument> describeMap, Map<String, Map<String, LayoutDocument>> layoutMap) {
        layoutMap.put(apiName, loadLayoutEntry(tenantId, userId, describeMap.get(apiName)));
    }

    private void fillDescribe(String tenantId, String apiName, Map<String, ObjectDescribeDocument> describeMap) {
        describeMap.put(apiName, ObjectDescribeDocument.of(serviceFacade.findObject(tenantId, apiName)));
    }

    private Map<String, LayoutDocument> loadLayoutEntry(String tenantId, Integer userId, ObjectDescribeDocument describe) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        RecordTypeFieldDescribe recordTypeField = describeExt.getRecordTypeField().orElse(null);
        User user = userId == -10000 ? User.systemUser(tenantId) : User.builder().userId(userId.toString()).tenantId(tenantId).build();
        Map<String, LayoutDocument> data = Maps.newHashMap();
        if (recordTypeField != null) {
            for (IRecordTypeOption recordTypeOption : recordTypeField.getRecordTypeOptions()) {
                LayoutDocument detailLayout = LayoutDocument.of(serviceFacade.getLayoutLogicService().findObjectLayoutByType(user, recordTypeOption.getApiName(), describeExt.getApiName(), "edit"));
                data.put(recordTypeOption.getApiName(), detailLayout);
            }
        }
        return data;
    }

    @Override
    public OperateAudit.Result operateAudit(OperateAudit.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        String tenantId = context.getTenantId();
        // 一端协议的id
        String agreementId = arg.getAgreementId();
        log.info("MN operateAudit arg is {}", arg);
        if (Strings.isNullOrEmpty(agreementId)) {
            return OperateAudit.Result.builder().build();
        }

        String accountRecordType = findAccountRecordType(tenantId, arg.getDealerId());

        String downstreamTenantId;
        // 下游企业id
        if (Strings.isNullOrEmpty(arg.getOutTenantId())) {
            downstreamTenantId = findDownstreamTenantIdByAccountId(tenantId, arg.getDealerId());
        } else {
            downstreamTenantId = findDownstreamTenantIdByOutId(tenantId, arg.getOutTenantId());
        }

        if (Strings.isNullOrEmpty(downstreamTenantId)) {
            return OperateAudit.Result.builder().build();
        }

        // 获取操作人的角色  省区经理：624d2f9ebe74513ca1def918  渠道经理： 00000000000000000000000000000025
        List<String> roles = roleService.queryRoleByEmployeeId(Integer.parseInt(tenantId), context.getEmployeeId());
        if (CollectionUtils.isEmpty(roles)) {
            return OperateAudit.Result.builder().build();
        }
        String auditAdvice = arg.getAuditAdvice();
        String auditOpinion = arg.getAuditOpinion();
        String status = "";
        String upApiName = arg.getApiName() == null ? "object_Jk6CW__c" : arg.getApiName();
        String agreementDetailApiName;
        if (upApiName.equals("object_Jk6CW__c")) {
            agreementDetailApiName = "object_bndbp__c";
            status = getAuditStatusOption(tenantId, arg.getAuditStatus(), roles, accountRecordType);
        } else {
            agreementDetailApiName = ApiNames.TPM_ACTIVITY_AGREEMENT_DETAIL_OBJ;
            status = getNtoMAuditStatusOption(tenantId, arg.getAuditStatus(), roles, accountRecordType);
        }

        if (Strings.isNullOrEmpty(status)) {
            return OperateAudit.Result.builder().build();
        }
        String key = String.format("FMCG_UP_AUDIT_LOCK_%s_%s", tenantId, agreementId);
        tryLock(key);
        try {
            /**
             * 1端城市经理、省区经理，渠道经理、检核过程中发现不合格载体可以修改单载体的结案金额。
             * 检核时带出活动协议项目【TPMActivityAgreementDetailObj】的（核销金额【field_rnE1h__c】字段，
             * 当协议项目【name】=“批发堆头”时，带出批发推头金额【field_M1urI__c】），检核时可修改该字段的金额，
             * 检核完成后，将修改后的值写入到该字段（1端和N端同时写入）；
             */
            List<OperateAudit.AgreementItemData> updateAgreementItems = arg.getUpdateAgreementItems();
            if (CollectionUtils.isNotEmpty(updateAgreementItems)) {
                log.info("updateAgreementItems size is {}", updateAgreementItems.size());
                Map<String, List<OperateAudit.AgreementItemData>> updateMap = updateAgreementItems.stream()
                        .collect(Collectors.groupingBy(OperateAudit.AgreementItemData::getFieldName));
                updateMap.forEach((fieldName, itemDataList) -> {
                    List<String> agreementItemIds = updateUpAgreementItemsData(tenantId, context.getEmployeeId(), itemDataList, fieldName, agreementDetailApiName);
                    if (CollectionUtils.isNotEmpty(agreementItemIds)) {
                        log.info("update result agreementItemIds size is {}", agreementItemIds.size());
                        updateDownAgreementItemsData(downstreamTenantId, itemDataList, fieldName, agreementItemIds);
                    }
                });
            }

            // 更新一端和N端的数据。  status 更新的状态选项值
            String auditStatus = updateUpAgreementData(tenantId, agreementId, context.getEmployeeId(), status, auditOpinion, upApiName, auditAdvice);
            if (!Strings.isNullOrEmpty(auditStatus)) {
                updateDownAgreementData(downstreamTenantId, agreementId, auditStatus, auditOpinion, auditAdvice);
            }

        } catch (Exception ex) {
            log.error("上级检核操作异常", ex);
            String upStatus = getAgreementStatusForTenant(tenantId, agreementId, "object_Jk6CW__c");
            String downStatus = getAgreementStatusForTenant(downstreamTenantId, agreementId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
            throw new ValidateException(String.format(I18N.text(I18NKeys.AGREEMENT_AUDIT_SERVICE_0), upStatus, downStatus));
        } finally {
            unlock(key);
        }

        return OperateAudit.Result.builder().build();
    }

    @Override
    public AgreementItemsAuditList.Result auditAgreementItemList(AgreementItemsAuditList.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        List<String> dataIdList = arg.getDataIdList();
        if (CollectionUtils.isEmpty(dataIdList)) {
            return AgreementItemsAuditList.Result.builder().build();
        }

        String downstreamTenantId;
        if (Strings.isNullOrEmpty(arg.getOutTenantId())) {
            downstreamTenantId = findDownstreamTenantIdByAccountId(context.getTenantId(), arg.getDealerId());
        } else {
            downstreamTenantId = findDownstreamTenantIdByOutId(context.getTenantId(), arg.getOutTenantId());
        }

        IFilter dataIdsFilter = new Filter();
        dataIdsFilter.setFieldName(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID);
        dataIdsFilter.setOperator(Operator.IN);
        dataIdsFilter.setFieldValues(dataIdList);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(dataIdsFilter);
        List<IObjectData> details = QueryDataUtil.find(
                serviceFacade,
                downstreamTenantId,
                ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ,
                stq,
                Lists.newArrayList(CommonFields.ID, TPMActivityProofDetailFields.ACTIVITY_AGREEMENT_DETAIL_ID)
        );
        if (CollectionUtils.isEmpty(details)) {
            return AgreementItemsAuditList.Result.builder().build();
        }
        List<String> agreementDetailIds = details.stream().map(v -> v.get(TPMActivityProofDetailFields.ACTIVITY_AGREEMENT_DETAIL_ID, String.class)).distinct().collect(Collectors.toList());
        List<IObjectData> objectDataByIds = serviceFacade.findObjectDataByIds(downstreamTenantId, agreementDetailIds, ApiNames.TPM_ACTIVITY_AGREEMENT_DETAIL_OBJ);
        objectDataByIds = objectDataByIds.stream().sorted(Comparator.comparing(v -> v.get("code", String.class), Comparator.reverseOrder())).collect(Collectors.toList());

        return AgreementItemsAuditList.Result.builder()
                .agreementItemsData(ObjectDataDocument.ofList(objectDataByIds))
                .agreementItemDescribe(ObjectDescribeDocument.of(serviceFacade.findObject(downstreamTenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_DETAIL_OBJ)))
                .itemCode(itemCode)
                .build();
    }

    private void updateDownAgreementItemsData(String downstreamTenantId,
                                              List<OperateAudit.AgreementItemData> updateAgreementItems,
                                              String fieldName,
                                              List<String> agreementItemIds) {


        List<IObjectData> agreementItems = new ArrayList<>();
        for (OperateAudit.AgreementItemData updateAgreementItem : updateAgreementItems) {
            BigDecimal modifyAmount = updateAgreementItem.getModifyAmount();
            BigDecimal amount = updateAgreementItem.getAmount() == null ? BigDecimal.ZERO : updateAgreementItem.getAmount();
            if (validateAmount(modifyAmount, amount)) {
                continue;
            }

            if (!agreementItemIds.contains(updateAgreementItem.getId())) {
                continue;
            }

            IObjectData data = new ObjectData();
            data.setId(updateAgreementItem.getId());
            data.setTenantId(downstreamTenantId);
            data.setDescribeApiName(ApiNames.TPM_ACTIVITY_AGREEMENT_DETAIL_OBJ);
            data.setOwner(Lists.newArrayList("-10000"));
            data.set(updateAgreementItem.getFieldName(), updateAgreementItem.getModifyAmount());
            agreementItems.add(data);
        }

        serviceFacade.batchUpdateByFields(User.systemUser(downstreamTenantId), agreementItems, Lists.newArrayList(fieldName));
    }

    private List<String> updateUpAgreementItemsData(String tenantId,
                                                    Integer employeeId,
                                                    List<OperateAudit.AgreementItemData> updateAgreementItems,
                                                    String fieldName,
                                                    String agreementDetailApiName) {

        // 批发堆头-核销金额 field_M1urI__c
        String amountField = "field_M1urI__c".equals(fieldName) ? "field_ixS4y__c" : "field_RezG3__c";

        List<String> ids = updateAgreementItems.stream().map(OperateAudit.AgreementItemData::getId).collect(Collectors.toList());
        List<IObjectData> dataList = serviceFacade.findObjectDataByIds(tenantId, ids, agreementDetailApiName);
        Map<String, IObjectData> dataMap = dataList.stream().collect(Collectors.toMap(DBRecord::getId, v -> v));
        List<IObjectData> agreementItems = new ArrayList<>();
        for (OperateAudit.AgreementItemData updateAgreementItem : updateAgreementItems) {
            BigDecimal modifyAmount = updateAgreementItem.getModifyAmount();
            BigDecimal amount = updateAgreementItem.getAmount();
            if (validateAmount(modifyAmount, amount)) {
                continue;
            }

            IObjectData objectData = dataMap.get(updateAgreementItem.getId());
            BigDecimal subTotal = objectData.get(amountField, BigDecimal.class);
            if (modifyAmount.compareTo(subTotal) > 0) {
                continue;
            }

            objectData.set(updateAgreementItem.getFieldName(), updateAgreementItem.getModifyAmount());
            agreementItems.add(objectData);
        }

        List<IObjectData> iObjectData = serviceFacade.batchUpdateByFields(User.systemUser(tenantId), agreementItems, Lists.newArrayList(fieldName));
        if (TPMGrayUtils.allowAgreementAuditAddLogCustomMessage(tenantId)) {
            //异步添加修改记录
            ParallelUtils.createParallelTask().submit(MonitorTaskWrapper.wrap(() -> {
                User user = User.builder().tenantId(tenantId).userId(String.valueOf(employeeId)).build();
                addLogCustomMessage(tenantId, user, agreementDetailApiName, iObjectData, fieldName);
            })).run();
        }
        return iObjectData.stream().map(DBRecord::getId).collect(Collectors.toList());
    }

    private void addLogCustomMessage(String tenantId,
                                     User user,
                                     String agreementDetailApiName,
                                     List<IObjectData> dataList,
                                     String fieldName) {
        IObjectDescribe describe = serviceFacade.findObject(tenantId, agreementDetailApiName);
        log.info("addLogCustomMessage dataList size is {} ", dataList.size());
        for (IObjectData objectData : dataList) {
            serviceFacade.logWithCustomMessage(
                    user,
                    EventType.MODIFY,
                    ActionType.MODIFY,
                    describe,
                    objectData,
                    String.format("检核修改后的金额：%s", objectData.get(fieldName, BigDecimal.class)));
        }
    }

    private boolean validateAmount(BigDecimal modifyAmount, BigDecimal amount) {
        if (modifyAmount == null) {
            return true;
        }
        return amount != null && modifyAmount.compareTo(amount) == 0;
    }

    private String updateUpAgreementData(String tenantId,
                                         String id,
                                         Integer employeeId,
                                         String status,
                                         String auditOpinion,
                                         String upApiName,
                                         String auditAdvice) {

        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(tenantId), id, upApiName);
        String agreementStatus = objectData.get("field_Gvb1m__c", String.class);
        // 如果协议上的状态已经是  不通过了，则不再操作。
        if ("12".equals(agreementStatus) || "14".equals(agreementStatus)) {
            return "";
        }

        Map<String, Object> updateFieldMap = new HashMap<>(8);
        updateFieldMap.put("field_Gvb1m__c", status);
        // 检核人员。
        updateFieldMap.put("final_inspector__c", Lists.newArrayList(String.valueOf(employeeId)));
        if (!Strings.isNullOrEmpty(auditOpinion)) {
            updateFieldMap.put("inspection_opinion__c", auditOpinion);
        }
        // 从对象检核意见，协议上显示。
        if (auditAdvice != null) {
            updateFieldMap.put("inspection_advice__c", auditAdvice);
        }

        User user = User.builder().tenantId(tenantId).userId(String.valueOf(employeeId)).build();
        IObjectData iObjectData = serviceFacade.updateWithMap(user, objectData, updateFieldMap);
        IObjectDescribe describe = serviceFacade.findObject(tenantId, upApiName);
        serviceFacade.logWithCustomMessage(
                user,
                EventType.MODIFY,
                ActionType.MODIFY,
                describe,
                objectData,
                getMessage(status, auditOpinion)
        );
        return iObjectData.get("field_Gvb1m__c", String.class);
    }

    private String getAgreementStatusForTenant(String tenantId, String id, String apiName) {
        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(tenantId), id, apiName);
        return objectData.get("field_Gvb1m__c", String.class);
    }

    private String getMessage(String status, String auditOpinion) {
        switch (status) {
            case "11":
                return "省区经理检核通过";
            case "12":
                return "省区经理检核驳回,驳回意见：" + auditOpinion;
            case "13":
                return "渠道经理检核通过";
            case "14":
                return "渠道经理检核驳回,驳回意见：" + auditOpinion;
            default:
                return "检核完成";
        }
    }

    private String getAuditStatusOption(String tenantId, String auditStatus, List<String> roles, String accountRecordType) {
        //省区经理：624d2f9ebe74513ca1def918  渠道经理： 00000000000000000000000000000025
        // mollercular_company__c  分子公司的，没有省区经理的角色审批。 -- 0607 fix忽略掉
        if (roles.contains(areaUserRole)) {
            return "reject".equals(auditStatus) ? "12" : "11";
        }

        if (roles.contains(channelUserRole)) {
            return "reject".equals(auditStatus) ? "14" : "13";
        }

        // 112测试。
        if ("84931".equals(tenantId)) {
            if (roles.contains("00000000000000000000000000000006")) {
                return "reject".equals(auditStatus) ? "14" : "13";
            }
        }

        return "";
    }

    private String getNtoMAuditStatusOption(String tenantId, String auditStatus, List<String> roles, String accountRecordType) {
        //RBU经理 65fa41bf62bf66000156fd0a
        if (roles.contains(rbuUserRole)) {
            return "reject".equals(auditStatus) ? "12" : "11";
        }

        // 112测试。
        if ("84931".equals(tenantId)) {
            if (roles.contains("00000000000000000000000000000006")) {
                return "reject".equals(auditStatus) ? "12" : "11";
            }
        }
        return null;
    }

    private void updateDownAgreementData(String downstreamTenantId,
                                         String id,
                                         String status,
                                         String auditOpinion,
                                         String auditAdvice) {

        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(downstreamTenantId), id, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        Map<String, Object> updateFieldMap = new HashMap<>(4);
        updateFieldMap.put("field_Gvb1m__c", status);
        if (!Strings.isNullOrEmpty(auditOpinion)) {
            updateFieldMap.put("inspection_opinion__c", auditOpinion);
        }
        // 从对象检核意见，协议上显示。
        if (auditAdvice != null) {
            updateFieldMap.put("inspection_advice__c", auditAdvice);
        }
        serviceFacade.updateWithMap(User.systemUser(String.valueOf(downstreamTenantId)), objectData, updateFieldMap);

        IObjectDescribe describe = serviceFacade.findObject(downstreamTenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        serviceFacade.logWithCustomMessage(
                User.systemUser(String.valueOf(downstreamTenantId)),
                EventType.MODIFY,
                ActionType.MODIFY,
                describe,
                objectData,
                getMessage(status, auditOpinion));

    }

    private List<AgreementAuditDataList.AuditDatumVO> loadAuditDatum(String tenantId,
                                                                     List<String> dataIds,
                                                                     ActivityTypeExt activityType,
                                                                     String auditObjectRecordType) {

        IObjectDescribe describe = serviceFacade.findObject(tenantId, activityType.auditSourceConfig().getMasterApiName());
        IObjectDescribe auditDescribe = serviceFacade.findObject(tenantId, ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);

        IObjectDescribe detailDescribe = null;
        IObjectDescribe auditDetailDescribe = null;
        if (!Strings.isNullOrEmpty(activityType.auditSourceConfig().getDetailApiName())) {
            detailDescribe = serviceFacade.findObject(tenantId, activityType.auditSourceConfig().getDetailApiName());
            auditDetailDescribe = serviceFacade.findObject(tenantId, ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ);
        }

        Map<String, Map<String, IObjectData>> auditDataMap = queryAuditDataMap(tenantId, auditDescribe, dataIds, activityType.auditSourceConfig().getReferenceAuditSourceFieldApiName());
        List<IObjectData> data = findObjectDataByIds(tenantId, describe, dataIds, true);

        List<AgreementAuditDataList.AuditDatumVO> result = Lists.newArrayList();
        for (IObjectData datum : data) {
            Map<String, List<ObjectDataDocument>> details = new HashMap<>();
            if (!Strings.isNullOrEmpty(activityType.auditSourceConfig().getDetailApiName())) {
                details.put(activityType.auditSourceConfig().getDetailApiName(), findDetailsByMasterObject(tenantId, detailDescribe, datum).stream().map(ObjectDataDocument::of).collect(Collectors.toList()));
            }

            AgreementAuditDataList.AuditDatumVO auditDatum = new AgreementAuditDataList.AuditDatumVO();
            auditDatum.setApplicationData(AgreementAuditDataList.ObjectDataVO.builder()
                    .data(ObjectDataDocument.of(datum))
                    .details(details)
                    .auditStatus("schedule")
                    .build()
            );

            auditDatum.setAllImages(Maps.newHashMap());
            auditDatum.getAllImages().put(VISIT_DATA_KEY, Lists.newArrayList());
            auditDatum.getAllImages().put(APPLICATION_DATA_KEY, loadImages(describe, datum, activityType.auditSourceConfig().getDisplayFieldApiNamesOfMaster()));

            Map<String, IObjectData> innerAuditDataMap = auditDataMap.get(datum.getId());

            if (!Objects.isNull(innerAuditDataMap)) {
                auditDatum.setMultipleLevelAuditData(Maps.newHashMap());

                for (Map.Entry<String, IObjectData> entry : innerAuditDataMap.entrySet()) {
                    String auditStatus = entry.getValue().get(TPMActivityProofAuditFields.AUDIT_STATUS, String.class);
                    auditDatum.getApplicationData().setAuditStatus(auditStatus);

                    auditDatum.getAllImages().put(AUDIT_DATA_KEY, loadImages(auditDescribe, entry.getValue(), activityType.auditSourceConfig().getDisplayFieldApiNamesOfAuditMaster()));
                    Map<String, List<ObjectDataDocument>> auditDetails = new HashMap<>();
                    if (!Strings.isNullOrEmpty(activityType.auditSourceConfig().getDetailApiName())) {
                        auditDetails.put(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ, findDetailsByMasterObject(tenantId, auditDetailDescribe, entry.getValue()).stream().map(ObjectDataDocument::of).collect(Collectors.toList()));
                    }

                    auditDatum.getMultipleLevelAuditData().put(
                            entry.getKey(),
                            AgreementAuditDataList.ObjectDataVO.builder()
                                    .isRandomAudit(true)
                                    .isEnableEdit(true)
                                    .data(ObjectDataDocument.of(entry.getValue()))
                                    .details(auditDetails)
                                    .build()
                    );
                }

                if (Strings.isNullOrEmpty(auditObjectRecordType)) {
                    auditObjectRecordType = activityType.auditNode().getObjectRecordType();
                }
                auditDatum.setAuditData(auditDatum.getMultipleLevelAuditData().get(auditObjectRecordType));
            }
            result.add(auditDatum);
        }
        return result;
    }

    @NotNull
    private SearchTemplateQuery getSearchTemplateQuery(String agreementId) {
        SearchTemplateQuery queryActivity = new SearchTemplateQuery();
        queryActivity.setOffset(0);
        queryActivity.setLimit(-1);

        IFilter agreementFilter = new Filter();
        agreementFilter.setFieldName(TPMActivityProofAuditFields.ACTIVITY_AGREEMENT_ID);
        agreementFilter.setOperator(Operator.EQ);
        agreementFilter.setFieldValues(Lists.newArrayList(agreementId));

        IFilter statusFilter = new Filter();
        statusFilter.setFieldName("audit_status");
        statusFilter.setOperator(Operator.EQ);
        statusFilter.setFieldValues(Lists.newArrayList("pass"));

        queryActivity.setFilters(Lists.newArrayList(agreementFilter, statusFilter));
        return queryActivity;
    }


    private String findDownstreamTenantIdByAccountId(String tenantId, String accountId) {
        if (Strings.isNullOrEmpty(accountId)) {
            log.info("findDownstreamTenantIdByAccountId accountId is empty. tenantId : {}", tenantId);
            return null;
        }

        IFilter mapperAccountIdFilter = new Filter();
        mapperAccountIdFilter.setFieldName(EnterpriseRelationFields.MAPPER_ACCOUNT_ID);
        mapperAccountIdFilter.setOperator(Operator.EQ);
        mapperAccountIdFilter.setFieldValues(Lists.newArrayList(accountId));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(mapperAccountIdFilter);
        stq.setLimit(1);
        stq.setOffset(0);

        List<IObjectData> relation = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.ENTERPRISE_RELATION_OBJ, stq, Lists.newArrayList(
                CommonFields.ID, EnterpriseRelationFields.ENTERPRISE_ACCOUNT
        ));

        if (CollectionUtils.isEmpty(relation)) {
            throw new ValidateException("downstream enterprise not found.");
        }

        String tenantAccount = relation.get(0).get(EnterpriseRelationFields.ENTERPRISE_ACCOUNT, String.class);
        if (Strings.isNullOrEmpty(tenantAccount)) {
            throw new ValidateException("downstream enterprise not found.");
        }

        return String.valueOf(enterpriseEditionService.getEnterpriseId(tenantAccount));
    }

    private List<AgreementAuditDataList.AuditImageVO> loadImages(IObjectDescribe describe, IObjectData data, List<String> displayFields) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        List<AgreementAuditDataList.AuditImageVO> images = Lists.newArrayList();

        List<Image> imageFieldList = describeExt.getImageFieldList();
        if (CollectionUtils.isNotEmpty(imageFieldList) && CollectionUtils.isNotEmpty(displayFields)) {
            imageFieldList = imageFieldList.stream().filter(image -> displayFields.contains(image.getApiName())).collect(Collectors.toList());
            for (Image field : imageFieldList) {
                JSONArray imageValue = JSON.parseArray(JSON.toJSONString(data.get(field.getApiName())));
                if (!Objects.isNull(imageValue)) {
                    for (int i = 0; i < imageValue.size(); i++) {
                        images.add(AgreementAuditDataList.AuditImageVO.fromJSON(field.getLabel(), imageValue.getJSONObject(i)));
                    }
                }
            }
        }
        return images;
    }

    private Map<String, Map<String, IObjectData>> queryAuditDataMap(String tenantId, IObjectDescribe describe, List<String> dataIds, String referenceProofFieldApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(true);

        query.setLimit(200);
        query.setOffset(0);

        IFilter applicationDataIdFilter = new Filter();
        applicationDataIdFilter.setFieldName(referenceProofFieldApiName);
        applicationDataIdFilter.setOperator(Operator.IN);
        applicationDataIdFilter.setFieldValues(dataIds);
        query.setFilters(Lists.newArrayList(applicationDataIdFilter));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);
        query.setOrders(Lists.newArrayList(order));

        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, query).getData();

        fillWebDetailAuditData(tenantId, describe, data);

        Map<String, Map<String, IObjectData>> auditData = Maps.newHashMap();

        for (IObjectData datum : data) {
            String auditSourceDataId = datum.get(referenceProofFieldApiName, String.class);
            if (!auditData.containsKey(auditSourceDataId)) {
                auditData.put(auditSourceDataId, Maps.newHashMap());
            }
            auditData.get(auditSourceDataId).put(datum.getRecordType(), datum);
        }
        return auditData;
    }


    private void tryLock(String key) {
        RLock lock = redissonCmd.getLock(key);
        try {
            if (!lock.tryLock(5, 10, TimeUnit.SECONDS)) {
                throw new ValidateException(I18N.text(I18NKeys.AGREEMENT_AUDIT_SERVICE_1));
            }
        } catch (InterruptedException e) {
            throw new ValidateException(I18N.text(I18NKeys.AGREEMENT_AUDIT_SERVICE_2));
        }
    }

    private void unlock(String key) {
        RLock lock = redissonCmd.getLock(key);
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    protected IObjectData fillWebDetailAuditData(String tenantId, IObjectDescribe describe, IObjectData data) {
        return fillWebDetailAuditData(tenantId, describe, Lists.newArrayList(data)).get(0);
    }

    protected List<IObjectData> fillWebDetailAuditData(String tenantId, IObjectDescribe describe, List<IObjectData> data) {
        infraServiceFacade.fillQuoteFieldValue(User.systemUser(tenantId), data, describe, null, false);
        serviceFacade.fillObjectDataWithRefObject(describe, data, User.systemUser(tenantId), null, false);
        serviceFacade.fillUserInfo(describe, data, User.systemUser(tenantId));
        serviceFacade.fillDepartmentInfo(describe, data, User.systemUser(tenantId));

        handleDataImageShare(tenantId, describe, data);
        return data;
    }

    private void handleDataImageShare(String tenantId, IObjectDescribe describe, List<IObjectData> data) {
        String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));

        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        List<Image> imageFieldList = describeExt.getImageFieldList();
        if (CollectionUtils.isNotEmpty(imageFieldList)) {
            for (IObjectData objectData : data) {
                for (Image field : imageFieldList) {
                    List<JSONObject> imageValue = JSON.parseArray(JSON.toJSONString(objectData.get(field.getApiName())), JSONObject.class);
                    if (Objects.nonNull(imageValue)) {
                        for (JSONObject image : imageValue) {
                            if (extendImage(tenantId, image)) {
                                continue;
                            }
                            Pair<String, String> shareFile = fileCenterAdapter.createShareFile(ea, -10000, image.getString("path"));
                            image.put("path", shareFile.getValue());
                        }
                        objectData.set(field.getApiName(), imageValue);
                    }
                }
            }
        }
    }

    private boolean extendImage(String tenantId, JSONObject image) {
        try {
            String signature = image.getString("signature");
            if (!Strings.isNullOrEmpty(signature)) {
                String[] split = signature.split("\\$");
                if (split.length > 0) {
                    String tenantEi = split[0];
                    if (tenantEi != null && !tenantEi.equals(tenantId)) {
                        String tenantEa = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantEi));
                        Pair<String, String> shareFile = fileCenterAdapter.createShareFile(tenantEa, -10000, image.getString("path"));
                        image.put("path", shareFile.getValue());
                        return true;
                    }
                }
            }
        }catch (Exception ex){
            log.error("extendImage fail ex : ", ex);
        }
        return false;
    }

    protected List<IObjectData> findObjectDataByIds(String tenantId, IObjectDescribe describe, List<String> ids, boolean needFillWebDetailData) {
        if (needFillWebDetailData) {
            return fillWebDetailAuditData(tenantId, describe, serviceFacade.findObjectDataByIds(tenantId, ids, describe.getApiName()));
        } else {
            return serviceFacade.findObjectDataByIds(tenantId, ids, describe.getApiName());
        }
    }

    protected List<IObjectData> findDetailsByMasterObject(String tenantId, IObjectDescribe describe, IObjectData masterData) {
        List<IObjectData> applicationDataDetailList = serviceFacade.findDetailObjectDataList(describe, masterData, User.systemUser(tenantId));
        return fillWebDetailAuditData(tenantId, describe, applicationDataDetailList);
    }
}
