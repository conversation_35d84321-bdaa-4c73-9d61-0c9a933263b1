package com.facishare.crm.fmcg.mengniu.service;

import com.facishare.crm.fmcg.tpm.web.contract.AgreementAuditDataList;
import com.facishare.crm.fmcg.tpm.web.contract.AgreementAuditSummaryList;
import com.facishare.crm.fmcg.tpm.web.contract.AgreementItemsAuditList;
import com.facishare.crm.fmcg.tpm.web.contract.OperateAudit;

/**
 * <AUTHOR>
 * @date 2024/1/23 14:13
 */
public interface IAgreementAuditService {

    AgreementAuditSummaryList.Result auditSummaryList(AgreementAuditSummaryList.Arg arg);

    AgreementAuditDataList.Result dataList(AgreementAuditDataList.Arg arg);

    OperateAudit.Result operateAudit(OperateAudit.Arg arg);


    AgreementItemsAuditList.Result auditAgreementItemList(AgreementItemsAuditList.Arg arg);
}
