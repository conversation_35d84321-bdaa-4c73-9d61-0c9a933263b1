package com.facishare.crm.fmcg.mengniu.business.abstraction;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.api.MengNiuTenantInformation;
import com.facishare.crm.fmcg.tpm.common.constant.MockConstant;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("Duplicates")
@Slf4j
public abstract class ActivityListSelector<T extends ILocateActivityArg> {

    public static final String FIXED = "FIXED";
    public static final String CONDITION = "CONDITION";
    public static final String ALL = "ALL";
    @Resource
    protected ServiceFacade serviceFacade;
    @Resource
    protected EnterpriseEditionService enterpriseEditionService;
    @Resource(name = "tpmOrganizationService")
    protected OrganizationService organizationService;

    protected abstract List<IObjectData> queryActivityByAccount(T arg, String accountId);

    protected abstract List<IObjectData> queryActivityByDepartment(T arg, String departmentId);

    protected List<IObjectData> filterActivityList(T arg, List<IObjectData> data) {
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }

        List<ActivityExt> activities = fillExtInformation(arg.getTenant().getManufacturer().getTenantId(), data);

        Map<String, List<ActivityExt>> grouped = activities.stream().collect(Collectors.groupingBy(ActivityExt::getStoreRangeType));

        List<ActivityExt> fixedData = grouped.get(FIXED);
        if (!CollectionUtils.isEmpty(fixedData)) {
            List<IObjectData> fixedActivity = new ArrayList<>();
            for (ActivityExt fixedDatum : fixedData) {
                if (fixedDatum.getStoreIds().contains(arg.getStoreId())) {
                    fixedActivity.add(fixedDatum.getActivity());
                }
            }
            return fixedActivity;
        }

        List<ActivityExt> conditionData = grouped.get(CONDITION);
        if (!CollectionUtils.isEmpty(conditionData)) {
            List<IObjectData> conditionActivity = new ArrayList<>();
            for (ActivityExt conditionDatum : conditionData) {
                if (storeInConditionRange(arg.getTenant().getManufacturer().getTenantId(), conditionDatum, arg.getStoreId())) {
                    conditionActivity.add(conditionDatum.getActivity());
                }
                return conditionActivity;
            }
        }

        List<ActivityExt> allData = grouped.get(ALL);
        if (!CollectionUtils.isEmpty(allData)) {
            return allData.stream().map(ActivityExt::getActivity).collect(Collectors.toList());
        }

        return null;
    }

    protected List<ActivityExt> fillExtInformation(String tenantId, List<IObjectData> data) {
        if (CollectionUtils.isEmpty(data)) {
            return Lists.newArrayList();
        }

        List<ActivityExt> activityExtList = Lists.newArrayList();

        List<String> storeFixedIds = Lists.newArrayList();

        for (IObjectData datum : data) {
            ActivityExt ext = new ActivityExt();
            ext.setId(datum.getId());
            ext.setActivity(datum);

            String storeRangeJson = datum.get(TPMActivityFields.STORE_RANGE, String.class);
            if (!Strings.isNullOrEmpty(storeRangeJson)) {
                JSONObject storeJson = JSON.parseObject(storeRangeJson);
                String storeRangeType = storeJson.getString("type");

                ext.setStoreRangeType(storeRangeType);
                if (ext.getStoreRangeType().equals(CONDITION)) {
                    List<Wheres> wheres = JSON.parseArray(storeJson.getString("value"), Wheres.class);
                    ext.setStoreWheres(wheres);
                }
                if (ext.getStoreRangeType().equals(FIXED)) {
                    storeFixedIds.add(datum.getId());
                }
            }

            activityExtList.add(ext);
        }

        Map<String, List<IObjectData>> storeRangeMap;
        if (!CollectionUtils.isEmpty(storeFixedIds)) {
            IFilter storeActivityIdFilter = new Filter();
            storeActivityIdFilter.setFieldName(TPMActivityStoreFields.ACTIVITY_ID);
            storeActivityIdFilter.setOperator(Operator.IN);
            storeActivityIdFilter.setFieldValues(Lists.newArrayList(storeFixedIds));

            SearchTemplateQuery storeRangeQuery = QueryDataUtil.minimumQuery(storeActivityIdFilter);

            List<IObjectData> storeRange = QueryDataUtil.find(
                    serviceFacade,
                    tenantId,
                    ApiNames.TPM_ACTIVITY_STORE_OBJ,
                    storeRangeQuery,
                    Lists.newArrayList("_id", TPMActivityStoreFields.ACTIVITY_ID, TPMActivityStoreFields.STORE_ID)
            );

            storeRangeMap = storeRange.stream().collect(Collectors.groupingBy(g -> g.get(TPMActivityStoreFields.ACTIVITY_ID, String.class)));
        } else {
            storeRangeMap = Maps.newHashMap();
        }

        for (ActivityExt activityExt : activityExtList) {
            if (storeRangeMap.containsKey(activityExt.getId())) {
                List<String> storeIds = storeRangeMap.get(activityExt.getId()).stream().map(m -> m.get(TPMActivityStoreFields.STORE_ID, String.class)).collect(Collectors.toList());
                activityExt.setStoreIds(storeIds);
            }
        }

        return activityExtList;
    }

    protected boolean storeInConditionRange(String tenantId, ActivityExt activity, String storeId) {
        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(storeId));

        SearchTemplateQuery stq = QueryDataUtil.minimumFindOneQuery(idFilter);
        stq.setWheres(activity.getStoreWheres());

        List<IObjectData> store = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ACCOUNT_OBJ,
                stq,
                Lists.newArrayList("_id")
        );

        return CollectionUtils.isNotEmpty(store);
    }

    public List<IObjectData> invoke(T arg) {
        switch (arg.getTenant().getRole()) {
            case MengNiuTenantInformation.ROLE_N:
                return selectNActivity(arg);
            case MengNiuTenantInformation.ROLE_M:
                return selectMActivity(arg);
            default:
                return null;
        }
    }

    private List<IObjectData> selectMActivity(T arg) {
        String manufacturerTenantId = arg.getTenant().getManufacturer().getTenantId();

        String mAccountId = findTenantRelationAccountId(manufacturerTenantId, arg.getTenant().getM().getTenantId());
        String nAccountId = findTenantRelationAccountId(manufacturerTenantId, arg.getTenant().getN().getTenantId());

        if (nAccountId == null) {
            return null;
        }

        IObjectData nAccount = serviceFacade.findObjectDataIgnoreAll(User.systemUser(manufacturerTenantId), nAccountId, ApiNames.ACCOUNT_OBJ);
        String accountDepartment = null;
        if (CollectionUtils.isNotEmpty(nAccount.getDataOwnDepartment())) {
            accountDepartment = nAccount.getDataOwnDepartment().get(0);
        }

        List<IObjectData> nActivitiesByAccount = queryActivityByAccount(arg, nAccountId);
        Map<String, List<IObjectData>> grouped = groupNActivity(manufacturerTenantId, nActivitiesByAccount, mAccountId);

        List<IObjectData> fixedData = grouped.get(FIXED);
        List<IObjectData> finalActivity = filterActivityList(arg, fixedData);

        if (CollectionUtils.isEmpty(finalActivity)) {
            List<IObjectData> allData = grouped.get(ALL);
            finalActivity = filterActivityList(arg, allData);
        }

        if (CollectionUtils.isEmpty(finalActivity)) {
            List<IObjectData> nActivitiesByDepartment = queryActivityByDepartment(arg, accountDepartment);
            finalActivity = filterActivityList(arg, nActivitiesByDepartment);
        }

        if (CollectionUtils.isEmpty(finalActivity)) {
            return null;
        }

        return serviceFacade.findObjectDataByIds(manufacturerTenantId, finalActivity.stream().map(DBRecord::getId).collect(Collectors.toList()), ApiNames.TPM_ACTIVITY_OBJ);
    }

    private List<IObjectData> selectNActivity(T arg) {
        String manufacturerTenantId = arg.getTenant().getManufacturer().getTenantId();

        String nAccountId = findTenantRelationAccountId(manufacturerTenantId, arg.getTenant().getN().getTenantId());
        if (Objects.isNull(nAccountId)) {
            log.warn("account of N tenant not found.");
            return null;
        }

        IObjectData nAccount = serviceFacade.findObjectDataIgnoreAll(User.systemUser(manufacturerTenantId), nAccountId, ApiNames.ACCOUNT_OBJ);
        String accountDepartment = null;
        if (CollectionUtils.isNotEmpty(nAccount.getDataOwnDepartment())) {
            accountDepartment = nAccount.getDataOwnDepartment().get(0);
        }

        List<IObjectData> nActivitiesByAccount = queryActivityByAccount(arg, nAccountId);
        List<IObjectData> finalActivity = filterActivityList(arg, nActivitiesByAccount);

        if (CollectionUtils.isEmpty(finalActivity)) {
            List<IObjectData> nActivitiesByDepartment = queryActivityByDepartment(arg, accountDepartment);
            finalActivity = filterActivityList(arg, nActivitiesByDepartment);
        }

        if (CollectionUtils.isEmpty(finalActivity)) {
            return null;
        }

        return serviceFacade.findObjectDataByIds(manufacturerTenantId, finalActivity.stream().map(DBRecord::getId).collect(Collectors.toList()), ApiNames.TPM_ACTIVITY_OBJ);
    }

    private Map<String, List<IObjectData>> groupNActivity(String tenantId, List<IObjectData> nActivity, String mAccountId) {
        if (CollectionUtils.isEmpty(nActivity)) {
            return Maps.newHashMap();
        }

        List<String> ids = nActivity.stream().map(DBRecord::getId).collect(Collectors.toList());

        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName("activity_id__c");
        activityIdFilter.setOperator(Operator.IN);
        activityIdFilter.setFieldValues(ids);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(activityIdFilter);

        List<IObjectData> ranges = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                "activity_tenant_range__c",
                stq,
                Lists.newArrayList("_id", "activity_id__c", "account_id__c")
        );

        Map<String, List<String>> rangeGroup = Maps.newHashMap();
        for (String id : ids) {
            rangeGroup.put(id, Lists.newArrayList());
        }

        for (IObjectData range : ranges) {
            String activityId = range.get("activity_id__c", String.class);
            String accountId = range.get("account_id__c", String.class);
            if (Strings.isNullOrEmpty(accountId) || Strings.isNullOrEmpty(activityId)) {
                continue;
            }
            if (rangeGroup.containsKey(activityId)) {
                rangeGroup.get(activityId).add(accountId);
            }
        }

        Map<String, List<IObjectData>> grouped = new HashMap<>();
        grouped.put(ALL, Lists.newArrayList());
        grouped.put(FIXED, Lists.newArrayList());

        Map<String, IObjectData> activityMap = nActivity.stream().collect(Collectors.toMap(DBRecord::getId, v -> v));
        for (Map.Entry<String, List<String>> entry : rangeGroup.entrySet()) {
            if (CollectionUtils.isEmpty(entry.getValue())) {
                grouped.get(ALL).add(activityMap.get(entry.getKey()));
            }
            if (entry.getValue().contains(mAccountId)) {
                grouped.get(FIXED).add(activityMap.get(entry.getKey()));
            }
        }

        return grouped;
    }

    private String findTenantRelationAccountId(String upstreamTenantId, String downstreamTenantId) {
        if (upstreamTenantId.equals(MockConstant.MOCK_MANUFACTURER_TENANT_ID) && downstreamTenantId.equals(MockConstant.MOCK_N_TENANT_ID)) {
            return MockConstant.MOCK_N_ACCOUNT_ID;
        }

        String downstreamTenantAccount = enterpriseEditionService.getEnterpriseAccount(Integer.parseInt(downstreamTenantId));

        IFilter enterpriseAccountFilter = new Filter();
        enterpriseAccountFilter.setFieldName(EnterpriseRelationFields.ENTERPRISE_ACCOUNT);
        enterpriseAccountFilter.setOperator(Operator.EQ);
        enterpriseAccountFilter.setFieldValues(Lists.newArrayList(downstreamTenantAccount));

        IFilter relationTypeFilter = new Filter();
        relationTypeFilter.setFieldName(EnterpriseRelationFields.RELATION_TYPE);
        relationTypeFilter.setOperator(Operator.EQ);
        relationTypeFilter.setFieldValues(Lists.newArrayList("1"));

        SearchTemplateQuery stq = QueryDataUtil.minimumFindOneQuery(enterpriseAccountFilter, relationTypeFilter);

        List<IObjectData> data = QueryDataUtil.find(
                serviceFacade,
                upstreamTenantId,
                ApiNames.ENTERPRISE_RELATION_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID, EnterpriseRelationFields.MAPPER_ACCOUNT_ID
                )
        );

        if (CollectionUtils.isEmpty(data)) {
            return null;
        }

        return data.get(0).get(EnterpriseRelationFields.MAPPER_ACCOUNT_ID, String.class);
    }

    @Data
    @ToString
    public static class ActivityExt implements Serializable {

        private String id;

        private IObjectData activity;

        private String storeRangeType;

        private List<String> storeIds;

        private List<Wheres> storeWheres;

        private String productRangeType;

        private List<String> productIds;

        private List<Wheres> productWheres;
    }
}
