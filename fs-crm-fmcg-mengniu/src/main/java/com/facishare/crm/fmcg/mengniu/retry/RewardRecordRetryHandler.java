package com.facishare.crm.fmcg.mengniu.retry;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.mengniu.business.RewardRecordService;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RetryTaskPO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RetryTaskStatusEnum;
import com.facishare.crm.fmcg.tpm.retry.annotation.RetryHandlerType;
import com.facishare.crm.fmcg.tpm.retry.handler.BaseHandler;
import com.facishare.crm.fmcg.tpm.retry.handler.RetryHandler;
import com.facishare.crm.fmcg.tpm.retry.handler.RetryHandlerEnum;
import com.facishare.crm.fmcg.tpm.retry.setter.RewardRecordSetter;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Author: linmj
 * Date: 2023/8/10 11:29
 */
@Component
@Slf4j
@RetryHandlerType(name = RetryHandlerEnum.REWARD_RECORD_UPDATE_HANDLER)
public class RewardRecordRetryHandler extends BaseHandler implements RetryHandler {

    @Resource
    private RewardRecordService rewardRecordService;
    @Resource
    private RewardRecordSetter rewardRecordSetter;

    @Override
    public void mainDo(RetryTaskPO retryTaskPO) {
        if (Strings.isNullOrEmpty(retryTaskPO.getParams())) {
            rewardRecordSetter.setNewStatus(retryTaskPO.getId().toString(), RetryTaskStatusEnum.SUCCESS.code());
            return;
        }
        JSONObject params = JSON.parseObject(retryTaskPO.getParams());
        String rewardId = params.getString("rewardRecordId");
        String tenantId = params.getString("tenantId");
        String apiName = params.getString("rewardRecordApiName");
        IObjectData rewardRecord = serviceFacade.findObjectData(User.systemUser(tenantId), rewardId, apiName);
        if (rewardRecordService.refreshRewardStatus(rewardRecord)) {
            rewardRecordSetter.setNewStatus(retryTaskPO.getId().toString(), RetryTaskStatusEnum.FAIL.code());
        } else {
            rewardRecordSetter.setNewStatus(retryTaskPO.getId().toString(), RetryTaskStatusEnum.SUCCESS.code());
        }
    }

    @Override
    public void handleException(RetryTaskPO retryTaskPO, Exception e) {
        super.handleException(retryTaskPO, e);
        if (retryTaskPO == null) {
            log.info("task is null");
            return;
        }
        rewardRecordSetter.setNewStatus(retryTaskPO.getId().toString(), RetryTaskStatusEnum.FAIL.code());
    }
}