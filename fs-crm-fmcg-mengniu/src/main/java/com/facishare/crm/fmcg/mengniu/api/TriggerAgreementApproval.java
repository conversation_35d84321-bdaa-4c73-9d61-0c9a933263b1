package com.facishare.crm.fmcg.mengniu.api;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface TriggerAgreementApproval {

    @Data
    @ToString
    class Arg implements Serializable {

        private List<String> tenantIds;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private long total;
    }
}