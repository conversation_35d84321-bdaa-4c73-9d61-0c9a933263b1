package com.facishare.crm.fmcg.mengniu.business;


import com.facishare.crm.fmcg.mengniu.api.TriggerAction;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.handler.HandlerAttributes;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@SuppressWarnings("Duplicates")
public class MengNiuObjectActionService {

    @Resource
    private ServiceFacade serviceFacade;

    public BaseObjectSaveAction.Result triggerAction(TriggerAction.Arg arg) {
        RequestContext requestContext = RequestContext.builder().tenantId(arg.getTenantId()).user(User.systemUser(arg.getTenantId())).build();

        requestContext.setAttribute(RequestContext.Attributes.TRIGGER_WORK_FLOW, arg.isTriggerWorkflow());
        requestContext.setAttribute(RequestContext.Attributes.TRIGGER_FLOW, arg.isTriggerFlow());
        requestContext.setAttribute(RequestContext.Attributes.SKIP_BASE_VALIDATE, arg.isSkipBaseValidate());
        requestContext.setAttribute(RequestContext.Attributes.SKIP_FUNCTION_ACTION, arg.isSkipFunctionAction());
        requestContext.setAttribute(HandlerAttributes.SKIP_VALIDATION_FUNCTION_CHECK, arg.isSkipValidationFunctionCheck());
        requestContext.setAttribute(HandlerAttributes.SKIP_VALIDATION_RULE_CHECK, arg.isSkipValidationRuleCheck());
        requestContext.setAttribute("not_validate", arg.isNotValidate());

        RequestContextManager.setContext(requestContext);

        ActionContext addActionContext = getActionContext(arg, requestContext);

        BaseObjectSaveAction.Arg saveArg = new BaseObjectSaveAction.Arg();
        saveArg.setObjectData(ObjectDataDocument.of(arg.getObjectData()));

        BaseObjectSaveAction.OptionInfo option = new BaseObjectSaveAction.OptionInfo();
        option.setUseValidationRule(arg.isSkipValidationRuleCheck());
        option.setSkipFuncValidate(arg.isSkipFunctionAction());
        saveArg.setOptionInfo(option);

        return serviceFacade.triggerRemoteAction(addActionContext, saveArg, BaseObjectSaveAction.Result.class);
    }

    @NotNull
    private static ActionContext getActionContext(TriggerAction.Arg arg, RequestContext requestContext) {
        ActionContext addActionContext = new ActionContext(
                requestContext,
                arg.getApiName(),
                arg.getActionName()
        );

        addActionContext.setAttribute(RequestContext.Attributes.TRIGGER_WORK_FLOW, arg.isTriggerWorkflow());
        addActionContext.setAttribute(RequestContext.Attributes.TRIGGER_FLOW, arg.isTriggerFlow());
        addActionContext.setAttribute(RequestContext.Attributes.SKIP_BASE_VALIDATE, arg.isSkipBaseValidate());
        addActionContext.setAttribute(RequestContext.Attributes.SKIP_FUNCTION_ACTION, arg.isSkipFunctionAction());
        addActionContext.setAttribute(HandlerAttributes.SKIP_VALIDATION_FUNCTION_CHECK, arg.isSkipValidationFunctionCheck());
        addActionContext.setAttribute(HandlerAttributes.SKIP_VALIDATION_RULE_CHECK, arg.isSkipValidationRuleCheck());
        addActionContext.setAttribute("not_validate", arg.isNotValidate());

        return addActionContext;
    }
}
