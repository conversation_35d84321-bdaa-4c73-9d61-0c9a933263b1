package com.facishare.crm.fmcg.mengniu.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.adapter.abstraction.IPayService;
import com.facishare.crm.fmcg.common.adapter.dto.UserInfo;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.CloudAccount;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.CloudTransfer;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.WXCloudPayReceiverAccount;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.BatchWXTenantTransfer;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.WXPersonalAccount;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.WXTenantAccount;
import com.facishare.crm.fmcg.common.adapter.exception.RewardFmcgException;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.IdentityIdGenerator;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.mengniu.dto.RewardRetryEvent;
import com.facishare.crm.fmcg.tpm.api.MengNiuTenantInformation;
import com.facishare.crm.fmcg.tpm.retry.setter.RewardRecordSetter;
import com.facishare.crm.fmcg.tpm.web.service.TenantHierarchyService;
import com.facishare.organization.api.model.department.arg.GetDepartmentDtoArg;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class RewardRetryHandler {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    protected RewardRecordSetter rewardRecordSetter;
    @Resource
    protected IPayService payService;
    @Resource
    protected TenantHierarchyService tenantHierarchyService;
    @Resource
    protected DepartmentProviderService departmentProviderService;

    protected static final List<String> ALLOW_RETRY_CODE = Lists.newArrayList();

    static {
        ALLOW_RETRY_CODE.add("4");
    }

    public void invoke(RewardRetryEvent event) {
        if (Strings.isNullOrEmpty(event.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_REWARD_RETRY_HANDLER_0), 500_001);
        }

        if (Strings.isNullOrEmpty(event.getId())) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_REWARD_RETRY_HANDLER_1), 500_002);
        }

        IObjectData data;
        try {
            data = serviceFacade.findObjectData(User.systemUser(event.getTenantId()), event.getId(), RedPacketRecordFields.API_NAME);
        } catch (Exception ex) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_REWARD_RETRY_HANDLER_2), 500_003);
        }

        String paymentStatus = data.get(RedPacketRecordFields.PAYMENT_STATUS, String.class);
        if (!ALLOW_RETRY_CODE.contains(paymentStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_REWARD_RETRY_HANDLER_3), 500_004);
        }

        Boolean isOverLimit = data.get(RedPacketRecordFields.IS_OVER_LIMIT, Boolean.class);
        if (Boolean.TRUE.equals(isOverLimit)) {
            String activityId = data.get(RedPacketRecordFields.ACTIVITY_ID, String.class);
            IObjectData activity = serviceFacade.findObjectData(User.systemUser(event.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);

            BigDecimal notOverLimitRewardAmount = calculateNotOverLimitRewardedAmount(event.getTenantId(), activityId);
            BigDecimal activityAmount = activity.get(TPMActivityFields.ACTIVITY_AMOUNT, BigDecimal.class);
            BigDecimal currentRewardAmount = data.get(RedPacketRecordFields.AMOUNT, BigDecimal.class);

            if (activityAmount.compareTo(notOverLimitRewardAmount.add(currentRewardAmount)) >= 0) {
                data.set(RedPacketRecordFields.IS_OVER_LIMIT, false);
                data = serviceFacade.updateObjectData(User.systemUser(event.getTenantId()), data);
            } else {
                throw new ValidateException(I18N.text(I18NKeys.VALIDATE_REWARD_RETRY_HANDLER_4), 500_005);
            }
        }

        transfer(event.getTenantId(), reloadInformation(event.getTenantId(), data));
    }

    protected BigDecimal calculateNotOverLimitRewardedAmount(String tenantId, String activityId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(RedPacketRecordFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter notOverLimitFilter = new Filter();
        notOverLimitFilter.setFieldName(RedPacketRecordFields.IS_OVER_LIMIT);
        notOverLimitFilter.setOperator(Operator.NEQ);
        notOverLimitFilter.setFieldValues(Lists.newArrayList(Boolean.TRUE.toString()));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("normal"));

        query.setFilters(Lists.newArrayList(lifeStatusFilter, activityIdFilter, notOverLimitFilter));

        List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
            User.systemUser(tenantId),
            query,
            "red_packet_record__c",
            Lists.newArrayList("activity_id__c"),
            "sum",
            "amount__c"
        );

        if (!CollectionUtils.isEmpty(data)) {
            return data.get(0).get("sum_amount__c", BigDecimal.class);
        } else {
            return new BigDecimal("0");
        }
    }

    private void transfer(String tenantId, IObjectData data) {
        String fromAccountType = data.get(RedPacketRecordFields.FROM_ACCOUNT_TYPE, String.class);
        switch (fromAccountType) {
            case "2":
                tenantCloudAccountToWeChatAccountTransfer(tenantId, data);
                break;
            case "5":
                tenantWeChatAccountToWeChatAccountTransfer(tenantId, data);
                break;
            default:
                throw new ValidateException(I18N.text(I18NKeys.VALIDATE_REWARD_RETRY_HANDLER_5), 500_006);
        }
        rewardRecordSetter.setUpdateStatusTask(tenantId, RedPacketRecordFields.API_NAME, data.getId());
    }

    private void tenantCloudAccountToWeChatAccountTransfer(String tenantId, IObjectData data) {
        UserInfo user = UserInfo.builder().build();
        user.setUserId(RewardConstants.SYSTEM_USER);
        user.setTenantId(tenantId);

        CloudTransfer.Arg arg = new CloudTransfer.Arg();
        arg.setBusinessId(data.get(RedPacketRecordFields.PAYMENT_BUSINESS_ID, String.class));

        arg.setPayerCloudAccount(convertToWxCloudPayAccount(data));
        arg.setReceiverPayAccount(convertToWxCloudPayReceiverAccount(data));

        arg.setAmount(data.get(RedPacketRecordFields.AMOUNT, BigDecimal.class));
        arg.setRemarks(fixRemarks(data.get(RedPacketRecordFields.REMARKS, String.class)));

        log.info("cloud transfer start - user : {}, arg : {}", user, arg);

        try {
            CloudTransfer.Result result = payService.cloudTransfer(user, arg);

            log.info("cloud transfer result : {}", result);

            Map<String, Object> updater = Maps.newHashMap();
            updater.put(RedPacketRecordFields.PAYMENT_ORDER_ID, result.getTransferId());
            updater.put(RedPacketRecordFields.PAYMENT_STATUS, "1");

            serviceFacade.updateWithMap(User.systemUser(tenantId), data, updater);
        } catch (RewardFmcgException ex) {
            log.error("cloud transfer error : ", ex);

            Map<String, Object> updater = Maps.newHashMap();
            updater.put(RedPacketRecordFields.PAYMENT_STATUS, "0");
            updater.put(RedPacketRecordFields.PAYMENT_ERROR_MESSAGE, ex.getMessage());

            serviceFacade.updateWithMap(User.systemUser(tenantId), data, updater);
        }
    }

    private void tenantWeChatAccountToWeChatAccountTransfer(String tenantId, IObjectData data) {
        UserInfo user = UserInfo.builder().build();
        user.setUserId(RewardConstants.SYSTEM_USER);
        user.setTenantId(tenantId);

        BatchWXTenantTransfer.Arg arg = new BatchWXTenantTransfer.Arg();
        arg.setBatchTransferId(data.get(RedPacketRecordFields.PAYMENT_BUSINESS_ID, String.class));
        arg.setBatchName(data.get(RedPacketRecordFields.PAYMENT_BUSINESS_ID, String.class));

        WXTenantAccount from = new WXTenantAccount();
        from.setTenantAccount(data.get(RedPacketRecordFields.FROM_CLOUD_ACCOUNT, String.class));
        arg.setPayeeWXAccount(from);

        WXPersonalAccount to = new WXPersonalAccount();
        to.setBusinessId(data.get(RedPacketRecordFields.PAYMENT_BUSINESS_ID, String.class));
        to.setAmount(data.get(RedPacketRecordFields.AMOUNT, BigDecimal.class));
        String remarks = fixRemarks(data.get(RedPacketRecordFields.REMARKS, String.class));
        to.setRemarks(remarks);
        to.setOpenId(data.get(RedPacketRecordFields.TO_WECHAT_OPEN_ID, String.class));
        to.setAppId(data.get(RedPacketRecordFields.TO_WX_APP_ID, String.class));

        arg.setReceiverAccounts(Lists.newArrayList(to));
        arg.setBatchRemarks(remarks);

        log.info("cloud transfer start - user : {}, arg : {}", user, arg);

        try {
            BatchWXTenantTransfer.Result result = payService.batchWXTenantTransfer(user, arg);

            log.info("cloud transfer result : {}", result);

            Map<String, Object> updater = Maps.newHashMap();
            updater.put(RedPacketRecordFields.PAYMENT_ORDER_ID, result.getBatchTransferId());
            updater.put(RedPacketRecordFields.PAYMENT_STATUS, "1");
            serviceFacade.updateWithMap(User.systemUser(tenantId), data, updater);

        } catch (RewardFmcgException ex) {
            log.error("cloud transfer error : ", ex);

            Map<String, Object> updater = Maps.newHashMap();
            updater.put(RedPacketRecordFields.PAYMENT_STATUS, "0");
            updater.put(RedPacketRecordFields.PAYMENT_ERROR_MESSAGE, ex.getMessage());

            serviceFacade.updateWithMap(User.systemUser(tenantId), data, updater);
        }
    }

    private static CloudAccount convertToWxCloudPayAccount(IObjectData data) {
        String fromAccountTenantAccount = data.get(RedPacketRecordFields.FROM_CLOUD_ACCOUNT, String.class);
        String fromAccountDealerId = data.get(RedPacketRecordFields.FROM_CLOUD_ACCOUNT_DEALER_ID, String.class);
        String fromAccountBrokerId = data.get(RedPacketRecordFields.FROM_CLOUD_ACCOUNT_BROKER_ID, String.class);

        CloudAccount from = new CloudAccount();
        from.setTenantAccount(fromAccountTenantAccount);
        from.setCloudAccountDealerId(fromAccountDealerId);
        from.setCloudAccountBrokerId(fromAccountBrokerId);
        return from;
    }

    private static WXCloudPayReceiverAccount convertToWxCloudPayReceiverAccount(IObjectData data) {
        String toAccountRealName = data.get(RedPacketRecordFields.TO_REAL_NAME, String.class);
        String toAccountIdCardNumber = data.get(RedPacketRecordFields.TO_ID_CARD_NUMBER, String.class);
        String toAccountWeChatOpenId = data.get(RedPacketRecordFields.TO_WECHAT_OPEN_ID, String.class);
        String toAccountWeChatAppId = data.get(RedPacketRecordFields.TO_WX_APP_ID, String.class);
        String toPhoneNumber = data.get(RedPacketRecordFields.TO_PHONE_NUMBER, String.class);

        WXCloudPayReceiverAccount to = new WXCloudPayReceiverAccount();
        to.setOpenId(toAccountWeChatOpenId);
        to.setRealName(toAccountRealName);
        to.setIdCardNumber(toAccountIdCardNumber);
        to.setPhoneNumber(toPhoneNumber);
        to.setAppId(toAccountWeChatAppId);
        return to;
    }

    private String fixRemarks(String remarks) {
        if (com.google.common.base.Strings.isNullOrEmpty(remarks)) {
            return "--";
        }
        return remarks.length() <= 16 ? remarks : remarks.substring(0, 16);
    }

    private IObjectData reloadInformation(String tenantId, IObjectData data) {
        data.set(RedPacketRecordFields.PAYMENT_BUSINESS_ID, IdentityIdGenerator.formPaymentIdentityId());
        data.set(RedPacketRecordFields.PAYMENT_STATUS, "5");
        data.set(RedPacketRecordFields.RETRY_TIMES, 0);

        String eventRole = data.get(RedPacketRecordFields.ROLE, String.class);

        switch (eventRole) {
            case "1":
                log.warn("consumer reward retry : {}", data.getName());
                break;
            case "2":
                reloadStoreOwnerToAccountInformation(tenantId, data);
                break;
            case "3":
                reloadSalesmenToAccountInformation(tenantId, data);
                break;
            case "5":
                reloadMBossToAccountInformation(tenantId, data);
                break;
            default:
                throw new ValidateException(I18N.text(I18NKeys.VALIDATE_REWARD_RETRY_HANDLER_6));
        }

        return serviceFacade.updateObjectData(User.systemUser(tenantId), data);
    }

    private void reloadMBossToAccountInformation(String tenantId, IObjectData data) {
        String eventTenantId = data.get(RedPacketRecordFields.EVENT_OBJECT_TENANT_ID, String.class);

        MengNiuTenantInformation eventTenant = tenantHierarchyService.load(eventTenantId);
        if (!eventTenant.getRole().equals(MengNiuTenantInformation.ROLE_M)) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_REWARD_RETRY_HANDLER_7));
        }

        String employeeId = findMBossEmployeeId(eventTenantId);
        if (Strings.isNullOrEmpty(employeeId)) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_REWARD_RETRY_HANDLER_8));
        }

        IObjectData employee = serviceFacade.findObjectDataIgnoreAll(User.systemUser(eventTenantId), employeeId, ApiNames.PERSONNEL_OBJ);

        String appId = employee.get(PersonnelFields.MENGNIU_WECHAT_APP_ID, String.class);
        if (Strings.isNullOrEmpty(appId)) {
            MengNiuTenantInformation tenant = tenantHierarchyService.load(tenantId);
            appId = tenantHierarchyService.findManufacturerByTenantId(tenant.getManufacturer().getTenantId()).getRedPacketAccountWeChatAppId();
        }

        data.set(RedPacketRecordFields.TO_WECHAT_OPEN_ID, employee.get(PersonnelFields.MENGNIU_WECHAT_OPEN_ID, String.class));
        data.set(RedPacketRecordFields.TO_ID_CARD_NUMBER, employee.get(PersonnelFields.MENGNIU_IDCARD, String.class));
        data.set(RedPacketRecordFields.TO_REAL_NAME, employee.get(PersonnelFields.FULL_NAME, String.class));
        data.set(RedPacketRecordFields.TO_PHONE_NUMBER, employee.get(PersonnelFields.PHONE, String.class));
        data.set(RedPacketRecordFields.TO_WX_UNION_ID, employee.get(PersonnelFields.MENGNIU_WX_UNION_ID, String.class));
        data.set(RedPacketRecordFields.TO_WX_APP_ID, appId);

        log.info("reload result : {}", JSON.toJSONString(data));
    }

    protected String findMBossEmployeeId(String tenantId) {
        GetDepartmentDtoArg arg = new GetDepartmentDtoArg();
        arg.setDepartmentId(999999);
        arg.setEnterpriseId(Integer.parseInt(tenantId));
        Integer principalId = departmentProviderService.getDepartmentDto(arg).getDepartment().getPrincipalId();
        return principalId == null ? null : principalId.toString();
    }

    protected IObjectData findContactData(String tenantId, String storeId) {
        IFilter identityFilter = new Filter();
        identityFilter.setFieldName("account_id");
        identityFilter.setOperator(Operator.EQ);
        identityFilter.setFieldValues(Lists.newArrayList(storeId));

        IFilter storeOwnerFlagFilter = new Filter();
        storeOwnerFlagFilter.setFieldName("field_2m89p__c");
        storeOwnerFlagFilter.setOperator(Operator.EQ);
        storeOwnerFlagFilter.setFieldValues(Lists.newArrayList("1"));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(identityFilter, storeOwnerFlagFilter);
        stq.setLimit(1);

        List<IObjectData> data = QueryDataUtil.find(serviceFacade, tenantId, "ContactObj", stq, Lists.newArrayList(
            "_id",
            CommonFields.TENANT_ID,
            ContactFields.NAME,
            ContactFields.MENGNIU_ID_CARD_NUMBER,
            ContactFields.MENGNIU_WECHAT_OPEN_ID,
            ContactFields.MENGNIU_WECHAT_UNION_ID,
            ContactFields.MENGNIU_WECHAT_APP_ID,
            ContactFields.MOBILE1,
            ContactFields.PUBLIC_EMPLOYEE_ID
        ));

        if (CollectionUtils.isEmpty(data)) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_REWARD_RETRY_HANDLER_9));
        }
        return data.get(0);
    }

    private void reloadSalesmenToAccountInformation(String tenantId, IObjectData data) {
        String eventType = data.get(RedPacketRecordFields.EVENT_TYPE, String.class);
        String eventTenantId = data.get(RedPacketRecordFields.EVENT_OBJECT_TENANT_ID, String.class);
        String eventObjectApiName = data.get(RedPacketRecordFields.EVENT_OBJECT_API_NAME, String.class);
        String eventObjectId = data.get(RedPacketRecordFields.EVENT_OBJECT_DATA_ID, String.class);
        try {
            IObjectData eventObject = serviceFacade.findObjectData(User.systemUser(eventTenantId), eventObjectId, eventObjectApiName);

            String employeeId;
            if (eventType.equals("2")) {
                employeeId = findSalesmenBySn(tenantId, eventObject);
            } else {
                employeeId = eventObject.getOwner().get(0);
            }

            IObjectData employee = serviceFacade.findObjectData(User.systemUser(eventTenantId), employeeId, ApiNames.PERSONNEL_OBJ);
            String appId = employee.get(PersonnelFields.MENGNIU_WECHAT_APP_ID, String.class);

            if (Strings.isNullOrEmpty(appId)) {
                MengNiuTenantInformation tenant = tenantHierarchyService.load(tenantId);
                appId = tenantHierarchyService.findManufacturerByTenantId(tenant.getManufacturer().getTenantId()).getRedPacketAccountWeChatAppId();
            }

            data.set(RedPacketRecordFields.TO_WECHAT_OPEN_ID, employee.get(PersonnelFields.MENGNIU_WECHAT_OPEN_ID, String.class));
            data.set(RedPacketRecordFields.TO_ID_CARD_NUMBER, employee.get(PersonnelFields.MENGNIU_IDCARD, String.class));
            data.set(RedPacketRecordFields.TO_REAL_NAME, employee.get(PersonnelFields.FULL_NAME, String.class));
            data.set(RedPacketRecordFields.TO_PHONE_NUMBER, employee.get(PersonnelFields.PHONE, String.class));
            data.set(RedPacketRecordFields.TO_WX_UNION_ID, employee.get(PersonnelFields.MENGNIU_WX_UNION_ID, String.class));
            data.set(RedPacketRecordFields.TO_WX_APP_ID, appId);

            log.info("reload result : {}", JSON.toJSONString(data));

        } catch (Exception ex) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_REWARD_RETRY_HANDLER_10), 500_011);
        }
    }

    private String findSalesmenBySn(String tenantId, IObjectData sn) {
        IFilter stateFilter = new Filter();
        stateFilter.setFieldName(FMCGSerialNumberStatusFields.CURRENT_STATE);
        stateFilter.setOperator(Operator.EQ);
        stateFilter.setFieldValues(Lists.newArrayList("6"));

        IFilter snFilter = new Filter();
        snFilter.setFieldName(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID);
        snFilter.setOperator(Operator.EQ);
        snFilter.setFieldValues(Lists.newArrayList(sn.getId()));

        SearchTemplateQuery query = QueryDataUtil.minimumQuery(stateFilter, snFilter);
        query.setLimit(1);

        List<IObjectData> data = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.FMCG_SERIAL_NUMBER_STATUS_OBJ, query, Lists.newArrayList(CommonFields.ID, FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, FMCGSerialNumberStatusFields.BUSINESS_OBJECT_ID));

        if (CollectionUtils.isNotEmpty(data)) {
            IObjectData serialNumberStatus = data.get(0);

            String deliveryNoteTenantId = String.valueOf(serialNumberStatus.get(FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, BigDecimal.class).intValue());
            String deliveryNoteId = serialNumberStatus.get(FMCGSerialNumberStatusFields.BUSINESS_OBJECT_ID, String.class);

            if (!Strings.isNullOrEmpty(deliveryNoteId)) {
                IObjectData deliveryNote = serviceFacade.findObjectDataIncludeDeleted(User.systemUser(deliveryNoteTenantId), deliveryNoteId, ApiNames.DELIVERY_NOTE_OBJ);
                return deliveryNote.getOwner().get(0);
            } else {
                return "";
            }
        } else {
            return "";
        }
    }

    private void reloadStoreOwnerToAccountInformation(String tenantId, IObjectData data) {
        String eventTenantId = data.get(RedPacketRecordFields.EVENT_OBJECT_TENANT_ID, String.class);
        String eventStoreId = data.get(RedPacketRecordFields.RELATED_STORE_ID, String.class);

        IObjectData contact = findContactData(eventTenantId, eventStoreId);

        String appId = contact.get(ContactFields.MENGNIU_WECHAT_APP_ID, String.class);
        if (Strings.isNullOrEmpty(appId)) {
            MengNiuTenantInformation tenant = tenantHierarchyService.load(tenantId);
            appId = tenantHierarchyService.findManufacturerByTenantId(tenant.getManufacturer().getTenantId()).getRedPacketAccountWeChatAppId();
        }

        data.set(RedPacketRecordFields.TO_WECHAT_OPEN_ID, contact.get(ContactFields.MENGNIU_WECHAT_OPEN_ID, String.class));
        data.set(RedPacketRecordFields.TO_ID_CARD_NUMBER, contact.get(ContactFields.MENGNIU_ID_CARD_NUMBER, String.class));
        data.set(RedPacketRecordFields.TO_REAL_NAME, contact.get(ContactFields.NAME, String.class));
        data.set(RedPacketRecordFields.TO_PHONE_NUMBER, contact.get(ContactFields.MOBILE1, String.class));
        data.set(RedPacketRecordFields.TO_WX_UNION_ID, contact.get(ContactFields.MENGNIU_WECHAT_UNION_ID, String.class));
        data.set(RedPacketRecordFields.TO_WX_APP_ID, appId);
    }
}
