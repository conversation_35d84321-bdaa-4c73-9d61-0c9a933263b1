package com.facishare.crm.fmcg.mengniu.business;

import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.common.adapter.dto.pay.CloudTransferPlatformEnum;
import com.facishare.crm.fmcg.common.apiname.RedPacketRecordDetailObjFields;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.adapter.abstraction.IPayService;
import com.facishare.crm.fmcg.common.adapter.dto.UserInfo;
import com.facishare.crm.fmcg.common.adapter.dto.pay.TransferDetail;
import com.facishare.crm.fmcg.common.adapter.dto.pay.TransferDetailStatusEnum;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.CloudAccount;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.CloudTransfer;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.QueryCloudTransferDetails;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.WXCloudPayReceiverAccount;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.BatchWXTenantTransfer;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.QueryWXTenantTransferDetail;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.WXPersonalAccount;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.WXTenantAccount;
import com.facishare.crm.fmcg.common.adapter.exception.RewardFmcgException;
import com.facishare.crm.fmcg.common.apiname.RedPacketAccountTypeEnum;
import com.facishare.crm.fmcg.common.apiname.RedPacketPaymentStatusEnum;
import com.facishare.crm.fmcg.common.apiname.RedPacketRecordFields;
import com.facishare.crm.fmcg.common.utils.IdentityIdGenerator;
import com.facishare.crm.fmcg.tpm.retry.setter.RewardRecordSetter;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Author: linmj
 * Date: 2023/8/7 19:33
 */
//IgnoreI18nFile
@Slf4j
@Service
public class RewardRecordService {

    @Resource
    private IPayService payService;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private RedissonClient redissonCmd;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private RewardRecordSetter rewardRecordSetter;

    public static final List<String> NEED_REASON_STATUS = Lists.newArrayList("4", "WAIT_PAY");


    public boolean refreshRewardStatus(IObjectData rewardRecord) {
        String status = rewardRecord.get(RedPacketRecordFields.PAYMENT_STATUS, String.class);
        switch (RedPacketPaymentStatusEnum.codeOf(status)) {
            case INIT:
            case PUBLISH_FAIL:
            case FAIL:
                return republish(rewardRecord);
            case PROCESSING:
                return queryRecordStatus(rewardRecord);
            default:

        }
        return false;
    }


    private boolean republish(IObjectData rewardRecord) {
        String lockKey = rewardRecord.get(RedPacketRecordFields.RECORD_IDENTITY, String.class);
        tryLock(lockKey);
        try {
            //重新获取一下数据
            rewardRecord = serviceFacade.findObjectData(User.systemUser(rewardRecord.getTenantId()), rewardRecord.getId(), RedPacketRecordFields.API_NAME);
            String paymentBusinessId = rewardRecord.get(RedPacketRecordFields.PAYMENT_BUSINESS_ID, String.class);
            String fromAccountType = rewardRecord.get(RedPacketRecordFields.FROM_ACCOUNT_TYPE, String.class);
            String fromTenantAccount = rewardRecord.get(RedPacketRecordFields.FROM_CLOUD_ACCOUNT, String.class);
            String fromAccountDealerId = rewardRecord.get(RedPacketRecordFields.FROM_CLOUD_ACCOUNT_DEALER_ID, String.class);
            String fromAccountBrokerId = rewardRecord.get(RedPacketRecordFields.FROM_CLOUD_ACCOUNT_BROKER_ID, String.class);

            CloudAccount account = new CloudAccount();
            account.setTenantAccount(fromTenantAccount);
            account.setCloudAccountDealerId(fromAccountDealerId);
            account.setCloudAccountBrokerId(fromAccountBrokerId);

            String fromTenantId = String.valueOf(eieaConverter.enterpriseAccountToId(fromTenantAccount));

            //查看当前的明细状态和记录状态的是否一致
            TransferDetail transferDetail = getTransferDetail(fromTenantId, fromAccountType, paymentBusinessId);

            if (transferDetail == null || TransferDetailStatusEnum.FAIL.codes().contains(transferDetail.getStatus())) {
                //确实失败了，重新发起
                Integer retryCount = rewardRecord.get(RedPacketRecordFields.RETRY_TIMES, Integer.class, 0);
                if (retryCount >= 3) {
                    log.info("retry count is more than 3. rewardRecord:{}", rewardRecord);
                    updateRewardRecordTransferInfo(fromTenantId, rewardRecord, null, null, RedPacketPaymentStatusEnum.EXCEPT.code(), null, null);
                    return false;
                }
                String newPaymentBusinessId = IdentityIdGenerator.formPaymentIdentityId();
                updateRewardRecordTransferInfo(fromTenantId, rewardRecord, newPaymentBusinessId, null, RedPacketPaymentStatusEnum.PROCESSING.code(), "", null);
                UserInfo userInfo = UserInfo.builder().tenantId(fromTenantId).userId("-10000").build();
                BigDecimal amount = rewardRecord.get(RedPacketRecordFields.AMOUNT, BigDecimal.class);
                String remarks = rewardRecord.get(RedPacketRecordFields.REMARKS, String.class);
                if (RedPacketAccountTypeEnum.CLOUD.code().equals(fromAccountType)) {
                    cloudTransfer(userInfo, account, fromTenantId, newPaymentBusinessId, amount, remarks, rewardRecord);
                } else if (RedPacketAccountTypeEnum.ENTERPRISE_WX.code().equals(fromAccountType)) {
                    wxTransfer(userInfo, fromTenantAccount, null, fromTenantId, newPaymentBusinessId, amount, remarks, rewardRecord);
                } else {
                    log.info("异常转出账户类型：{}", fromAccountType);
                    updateRewardRecordTransferInfo(fromTenantId, rewardRecord, null, null, RedPacketPaymentStatusEnum.EXCEPT.code(), "异常转出账户类型。", null);
                }
            } else if (TransferDetailStatusEnum.SUCCESS.codes().contains(transferDetail.getStatus())) {
                //已经成功了，但是状态不对，更新状态
                updateRewardRecordTransferInfo(fromTenantId, rewardRecord, null, null, RedPacketPaymentStatusEnum.SUCCESS.code(), "", null);
            } else {
                return true;
            }
        } finally {
            unlock(lockKey);
        }
        return false;
    }

    private void wxTransfer(UserInfo userInfo, String fromTenantAccount, String account, String fromTenantId, String newPaymentBusinessId, BigDecimal amount, String remarks, IObjectData rewardRecord) {
        BatchWXTenantTransfer.Arg batchWXTenantTransferArg = new BatchWXTenantTransfer.Arg();
        batchWXTenantTransferArg.setBatchName(newPaymentBusinessId);
        batchWXTenantTransferArg.setBatchRemarks(newPaymentBusinessId);
        batchWXTenantTransferArg.setBatchTransferId(newPaymentBusinessId);
        WXTenantAccount wxTenantAccount = new WXTenantAccount();
        wxTenantAccount.setTenantAccount(fromTenantAccount);
        wxTenantAccount.setAccount(account);
        batchWXTenantTransferArg.setPayeeWXAccount(wxTenantAccount);
        WXPersonalAccount wxPersonalAccount = new WXPersonalAccount();
        wxPersonalAccount.setAmount(amount);
        wxPersonalAccount.setOpenId(rewardRecord.get(RedPacketRecordFields.TO_WECHAT_OPEN_ID, String.class));
        wxPersonalAccount.setRemarks(remarks);
        wxPersonalAccount.setBusinessId(newPaymentBusinessId);
        wxPersonalAccount.setAppId(rewardRecord.get(RedPacketRecordFields.TO_WX_APP_ID, String.class));
        batchWXTenantTransferArg.setReceiverAccounts(Lists.newArrayList(wxPersonalAccount));

        BatchWXTenantTransfer.Result queryResult;
        try {
            queryResult = payService.batchWXTenantTransfer(userInfo, batchWXTenantTransferArg);
        } catch (Exception e) {
            String message = e.getMessage();
            if (!(e instanceof RewardFmcgException)) {
                message = "微信转账发生未知异常";
            }
            updateRewardRecordTransferInfo(fromTenantId, rewardRecord, newPaymentBusinessId, null, RedPacketPaymentStatusEnum.FAIL.code(), message, rewardRecord.get(RedPacketRecordFields.RETRY_TIMES, Integer.class, 0) + 1);
            throw e;
        }
        updateRewardRecordTransferInfo(rewardRecord.getTenantId(), rewardRecord, queryResult.getBatchTransferId(), queryResult.getDetailResults().get(0).getTransferId(), RedPacketPaymentStatusEnum.PROCESSING.code(), "", null);
        rewardRecordSetter.setUpdateStatusTask(rewardRecord.getTenantId(), rewardRecord.getDescribeApiName(), rewardRecord.getId());
    }

    private void cloudTransfer(UserInfo userInfo, CloudAccount fromAccount, String fromTenantId, String newPaymentBusinessId, BigDecimal amount, String remarks, IObjectData rewardRecord) {
        CloudTransfer.Arg transferArg = new CloudTransfer.Arg();
        transferArg.setBusinessId(newPaymentBusinessId);
        transferArg.setAmount(amount);
        transferArg.setRemarks(remarks);
        String realName = rewardRecord.get(RedPacketRecordFields.TO_REAL_NAME, String.class);
        String idCard = rewardRecord.get(RedPacketRecordFields.TO_ID_CARD_NUMBER, String.class);
        String phone = rewardRecord.get(RedPacketRecordFields.TO_PHONE_NUMBER, String.class);
        String openId = rewardRecord.get(RedPacketRecordFields.TO_WECHAT_OPEN_ID, String.class);
        String wxAppId = rewardRecord.get(RedPacketRecordFields.TO_WX_APP_ID, String.class);
        transferArg.setPayerCloudAccount(fromAccount);
        WXCloudPayReceiverAccount wxCloudPayReceiverAccount = new WXCloudPayReceiverAccount(realName, idCard, phone, openId, CloudTransferPlatformEnum.FSHARE.label(), realName, "");
        wxCloudPayReceiverAccount.setAppId(wxAppId);
        transferArg.setReceiverPayAccount(wxCloudPayReceiverAccount);
        CloudTransfer.Result transferResult;
        try {
            transferResult = payService.cloudTransfer(userInfo, transferArg);
        } catch (Exception e) {
            String message = e.getMessage();
            if (!(e instanceof RewardFmcgException)) {
                message = "云账户转账发生未知异常";
            }
            updateRewardRecordTransferInfo(fromTenantId, rewardRecord, newPaymentBusinessId, null, RedPacketPaymentStatusEnum.FAIL.code(), message, rewardRecord.get(RedPacketRecordFields.RETRY_TIMES, Integer.class, 0) + 1);
            throw e;
        }
        updateRewardRecordTransferInfo(rewardRecord.getTenantId(), rewardRecord, transferResult.getBusinessId(), transferResult.getTransferId(), RedPacketPaymentStatusEnum.PROCESSING.code(), "", null);
        //publish a new task to update status
        rewardRecordSetter.setUpdateStatusTask(rewardRecord.getTenantId(), rewardRecord.getDescribeApiName(), rewardRecord.getId());
    }

    private boolean queryRecordStatus(IObjectData rewardRecord) {
        String paymentBusinessId = rewardRecord.get(RedPacketRecordFields.PAYMENT_BUSINESS_ID, String.class);
        String fromAccountType = rewardRecord.get(RedPacketRecordFields.FROM_ACCOUNT_TYPE, String.class);
        String fromTenantAccount = rewardRecord.get(RedPacketRecordFields.FROM_CLOUD_ACCOUNT, String.class);
        String fromTenantId = String.valueOf(eieaConverter.enterpriseAccountToId(fromTenantAccount));
        TransferDetail transferDetail = getTransferDetail(fromTenantId, fromAccountType, paymentBusinessId);
        if (transferDetail == null) {
            log.info("transfer detail is null. paymentBusinessId:{}", paymentBusinessId);
            updateRewardRecordTransferInfo(fromTenantId, rewardRecord, null, null, RedPacketPaymentStatusEnum.PUBLISH_FAIL.code(), "转账发起失败", null);
            return true;
        } else if (TransferDetailStatusEnum.FAIL.codes().contains(transferDetail.getStatus())) {
            log.info("transfer fail. transferDetail:{}", transferDetail);
            updateRewardRecordTransferInfo(fromTenantId, rewardRecord, null, null, RedPacketPaymentStatusEnum.FAIL.code(), Strings.isNullOrEmpty(transferDetail.getFailReason()) ? "未知转账失败原因" : transferDetail.getFailReason(), rewardRecord.get(RedPacketRecordFields.RETRY_TIMES, Integer.class, 0) + 1);
            return true;
        } else if (TransferDetailStatusEnum.SUCCESS.codes().contains(transferDetail.getStatus())) {
            updateRewardRecordTransferInfo(fromTenantId, rewardRecord, null, null, RedPacketPaymentStatusEnum.SUCCESS.code(), "", null);
        } else if (NEED_REASON_STATUS.contains(transferDetail.getStatus())) {
            log.info("order status is always processing, due to have some operate to do . transferDetail:{}", transferDetail);
            updateRewardRecordTransferInfo(fromTenantId, rewardRecord, null, null, RedPacketPaymentStatusEnum.PROCESSING.code(), transferDetail.getFailReason(), 3);
        } else {
            log.info("transfer processing. transferDetail:{}", transferDetail);
            return true;
        }
        return false;
    }

    private TransferDetail getTransferDetail(String fromTenantId, String fromAccountType, String paymentBusinessId) {
        TransferDetail transferDetail = null;
        if (RedPacketAccountTypeEnum.ENTERPRISE_WX.code().equals(fromAccountType)) {
            transferDetail = getWxTransferDetail(fromTenantId, paymentBusinessId);
        } else if (RedPacketAccountTypeEnum.CLOUD.code().equals(fromAccountType)) {
            transferDetail = getCloudTransferDetail(fromTenantId, paymentBusinessId);
        } else {
            log.info("暂不支持的账户类型:{}", fromAccountType);
        }
        return transferDetail;
    }

    private TransferDetail getWxTransferDetail(String tenantId, String businessId) {
        QueryWXTenantTransferDetail.Arg batchQueryWXTenantTransferDetailArg = new QueryWXTenantTransferDetail.Arg();
        batchQueryWXTenantTransferDetailArg.setBatchTransferId(businessId);
        batchQueryWXTenantTransferDetailArg.setBusinessId(businessId);
        UserInfo userInfo = UserInfo.builder().userId("-10000").tenantId(tenantId).build();
        QueryWXTenantTransferDetail.Result queryResult = payService.queryWXTenantTransferDetails(userInfo, batchQueryWXTenantTransferDetailArg);
        return CollectionUtils.isNotEmpty(queryResult.getTransferDetails()) ? queryResult.getTransferDetails().get(0) : null;
    }

    private TransferDetail getCloudTransferDetail(String tenantId, String businessId) {
        QueryCloudTransferDetails.Arg arg = new QueryCloudTransferDetails.Arg();
        arg.setBusinessId(businessId);
        UserInfo userInfo = UserInfo.builder().userId("-10000").tenantId(tenantId).build();
        QueryCloudTransferDetails.Result result = payService.queryCloudTransferDetails(userInfo, arg);
        return CollectionUtils.isNotEmpty(result.getTransferDetails()) ? result.getTransferDetails().get(0) : null;
    }

    public void updateRewardRecordTransferInfo(String tenantId, IObjectData rewardRecord, String businessId, String orderId, String status, String failMessage, Integer retryCount) {
        Map<String, Object> updateMap = new HashMap<>();
        String updateMessage = "";
        if (businessId != null) {
            updateMap.put(RedPacketRecordFields.PAYMENT_BUSINESS_ID, businessId);
            updateMessage += fromUpdateMessage(RedPacketRecordFields.PAYMENT_BUSINESS_ID, businessId);
        }
        if (orderId != null) {
            updateMap.put(RedPacketRecordFields.PAYMENT_ORDER_ID, orderId);
            updateMessage += fromUpdateMessage(RedPacketRecordFields.PAYMENT_ORDER_ID, orderId);
        }
        if (status != null) {
            updateMap.put(RedPacketRecordFields.PAYMENT_STATUS, status);
            updateMessage += fromUpdateMessage(RedPacketRecordFields.PAYMENT_STATUS, status);
        }
        if (failMessage != null) {
            updateMap.put(RedPacketRecordFields.PAYMENT_ERROR_MESSAGE, failMessage);
            if (failMessage.isEmpty()) {
                failMessage = "空";
            }
            updateMessage += fromUpdateMessage(RedPacketRecordFields.PAYMENT_ERROR_MESSAGE, failMessage);
        }
        if (retryCount != null) {
            updateMap.put(RedPacketRecordFields.RETRY_TIMES, retryCount);
            updateMessage += fromUpdateMessage(RedPacketRecordFields.RETRY_TIMES, String.valueOf(retryCount));
        }
        if (updateMap.isEmpty()) {
            return;
        }
        User sysUser = User.systemUser(rewardRecord.getTenantId());
        serviceFacade.updateWithMap(sysUser, rewardRecord, updateMap);
        IObjectDescribe objectDescribe = serviceFacade.findObject(rewardRecord.getTenantId(), rewardRecord.getDescribeApiName());
        serviceFacade.logWithCustomMessage(sysUser, EventType.MODIFY, ActionType.Modify, objectDescribe, rewardRecord, updateMessage.substring(0, updateMessage.length() - 1));
    }

    private String fromUpdateMessage(String apiName, String value) {
        return String.format("字段【%s】 变更为  %s ，", apiName, value);
    }


    private void tryLock(String key) {
        RLock lock = redissonCmd.getLock(key);
        try {
            if (!lock.tryLock(20000, TimeUnit.MILLISECONDS)) {
                throw new RewardFmcgException("20001", I18N.text(I18NKeys.REWARD_REWARD_RECORD_SERVICE_0));
            }
        } catch (InterruptedException e) {
            throw new RewardFmcgException("20001", I18N.text(I18NKeys.REWARD_REWARD_RECORD_SERVICE_1));
        }
    }

    private void unlock(String key) {
        RLock lock = redissonCmd.getLock(key);
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
}
