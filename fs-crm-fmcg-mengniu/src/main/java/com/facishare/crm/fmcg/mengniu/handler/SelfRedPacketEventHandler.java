package com.facishare.crm.fmcg.mengniu.handler;

import com.facishare.appserver.checkins.api.model.SendRedEnvelopeRealNameAuthMessage;
import com.facishare.appserver.checkins.api.service.ShopMMService;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

//IgnoreI18nFile
@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class SelfRedPacketEventHandler {
    @Resource
    private ShopMMService shopMMService;

    @Resource
    private ServiceFacade serviceFacade;

    public void userAuthRemind(IObjectData objectData) {
        if (objectData == null) {
            return;
        }

        String idCard = objectData.get(RedPacketRecordFields.TO_ID_CARD_NUMBER, String.class);
        String realName = objectData.get(RedPacketRecordFields.TO_REAL_NAME, String.class);
        String phone = objectData.get(RedPacketRecordFields.TO_PHONE_NUMBER, String.class);
        String openId = objectData.get(RedPacketRecordFields.TO_WECHAT_OPEN_ID, String.class);
        String appId = objectData.get(RedPacketRecordFields.TO_WX_APP_ID, String.class);

        if (validateUserInfo(idCard, realName, phone, openId, appId)) {
            // 调外勤接口
            try {
                SendRedEnvelopeRealNameAuthMessage.Args args = buildSendMessageArgs(objectData);
                log.info("Self SendRedEnvelopeRealNameAuthMessage args is {}", args);
                if (args != null) {
                    shopMMService.sendRedEnvelopeRealNameAuthMessage(args);
                }
            } catch (Exception ex) {
                log.warn("SendRedEnvelopeRealNameAuthMessage fail..", ex);
            }

        }

    }

    private SendRedEnvelopeRealNameAuthMessage.Args buildSendMessageArgs(IObjectData objectData) {
        // 事件所在企业
        String tenantId = objectData.get(RedPacketRecordFields.EVENT_OBJECT_TENANT_ID, String.class);
        BigDecimal amount = objectData.get(RedPacketRecordFields.AMOUNT, BigDecimal.class);
        String storeId = objectData.get(RedPacketRecordFields.RELATED_STORE_ID, String.class);
        String rewardPersonId = getStoreData(tenantId, storeId);
        if (Strings.isNullOrEmpty(rewardPersonId)) {
            return null;
        }
        String[] split = rewardPersonId.split("\\.");
        if (split.length <= 1) {
            return null;
        }
        String code = objectData.get(CommonFields.NAME, String.class);
        long createTime = objectData.get(CommonFields.CREATE_TIME, Long.class);
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String activityName = findActivityName(objectData, objectData.get(RedPacketRecordFields.ACTIVITY_ID, String.class));
        SendRedEnvelopeRealNameAuthMessage.Args args = new SendRedEnvelopeRealNameAuthMessage.Args();
        args.setTenantId(Long.parseLong(tenantId));
        args.setOutTenantId(split[0]);
        args.setOuterUserId(Long.parseLong(split[1]));
        args.setActivityName(activityName);
        args.setActivityTime(sf.format(new Date(createTime)));
        args.setActivityNo(code);
        args.setRedEnvelopeAmount(String.format("%s元", amount));
        return args;
    }

    private String findActivityName(IObjectData objectData, String activityId) {
        String activityName = "激励活动";
        try {
            activityName = serviceFacade.findObjectData(User.systemUser(objectData.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ).getName();
        } catch (ObjectDataNotFoundException exception) {
            log.warn("buildSendMessageArgs activity not find..", exception);
        }
        return activityName;
    }

    private String getStoreData(String tenantId, String storeId) {

        String rewardPersonId = null;
        IObjectData contactData = findContactData(tenantId, storeId);
        if (contactData != null) {
            String publicEmployeeId = contactData.get(ContactFields.PUBLIC_EMPLOYEE_ID, String.class);
            if (!Strings.isNullOrEmpty(publicEmployeeId)) {
                IObjectData publicEmployeeObj = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), publicEmployeeId, ApiNames.PUBLIC_EMPLOYEE_OBJ);
                if (Objects.nonNull(publicEmployeeObj)) {
                    rewardPersonId = String.format("%s.%s", publicEmployeeObj.get(PublicEmployeeFields.OUTER_TENANT_ID, String.class), publicEmployeeObj.getId());
                }
            }
        }
        return rewardPersonId;
    }

    private boolean validateUserInfo(String... userInfo) {
        for (String info : userInfo) {
            if (Strings.isNullOrEmpty(info)) {
                return false;
            }
        }
        return true;
    }

    private IObjectData findContactData(String tenantId, String storeId) {
        IFilter identityFilter = new Filter();
        identityFilter.setFieldName("account_id");
        identityFilter.setOperator(Operator.EQ);
        identityFilter.setFieldValues(Lists.newArrayList(storeId));

        IFilter storeOwnerFlagFilter = new Filter();
        storeOwnerFlagFilter.setFieldName("field_2m89p__c");
        storeOwnerFlagFilter.setOperator(Operator.EQ);
        storeOwnerFlagFilter.setFieldValues(Lists.newArrayList("1"));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(identityFilter, storeOwnerFlagFilter);
        stq.setLimit(1);

        List<IObjectData> data = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.CONTACT_OBJ, stq, Lists.newArrayList(
                CommonFields.ID,
                CommonFields.TENANT_ID,
                ContactFields.NAME,
                ContactFields.MENGNIU_ID_CARD_NUMBER,
                ContactFields.MENGNIU_WECHAT_OPEN_ID,
                ContactFields.MENGNIU_WECHAT_UNION_ID,
                ContactFields.MENGNIU_WECHAT_APP_ID,
                ContactFields.MOBILE1,
                ContactFields.PUBLIC_EMPLOYEE_ID
        ));

        log.info("contact data : {}", data);

        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        return data.get(0);
    }
}
