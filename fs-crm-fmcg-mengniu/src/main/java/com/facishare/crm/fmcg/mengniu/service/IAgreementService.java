package com.facishare.crm.fmcg.mengniu.service;

import com.facishare.crm.fmcg.mengniu.api.*;

public interface IAgreementService {

    TriggerAgreementApproval.Result triggerAgreementApproval(TriggerAgreementApproval.Arg arg);

    FixAgreement.Result fixAgreement(FixAgreement.Arg arg);

    AgreementList.Result list(AgreementList.Arg arg);

    AgreementDetail.Result detail(AgreementDetail.Arg arg);

    AgreementConfirm.Result confirm(AgreementConfirm.Arg arg);

    DownstreamMonthlyAgreementListHeader.Result downstreamMonthlyAgreementListHeader(DownstreamMonthlyAgreementListHeader.Arg arg);

    DownstreamMonthlyAgreementDetailListHeader.Result downstreamMonthlyAgreementDetailListHeader(DownstreamMonthlyAgreementDetailListHeader.Arg arg);

    DownstreamMonthlyAgreementList.Result downstreamMonthlyAgreementList(DownstreamMonthlyAgreementList.Arg arg);

    DownstreamMonthlyAgreementDetailList.Result downstreamMonthlyAgreementDetailList(DownstreamMonthlyAgreementDetailList.Arg arg);

    DownstreamCancelAgreement.Result downstreamCancelAgreement(DownstreamCancelAgreement.Arg arg);
}
