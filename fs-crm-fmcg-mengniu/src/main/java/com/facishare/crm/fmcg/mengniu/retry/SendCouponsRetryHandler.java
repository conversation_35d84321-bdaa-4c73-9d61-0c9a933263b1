package com.facishare.crm.fmcg.mengniu.retry;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.mengniu.business.abstraction.IDistributionService;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RetryTaskPO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RetryTaskStatusEnum;
import com.facishare.crm.fmcg.tpm.retry.annotation.RetryHandlerType;
import com.facishare.crm.fmcg.tpm.retry.handler.BaseHandler;
import com.facishare.crm.fmcg.tpm.retry.handler.RetryHandler;
import com.facishare.crm.fmcg.tpm.retry.handler.RetryHandlerEnum;
import com.facishare.crm.fmcg.tpm.retry.setter.SendCouponsSetter;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Author: linmj
 * Date: 2023/8/10 11:29
 */
@Component
@Slf4j
@RetryHandlerType(name = RetryHandlerEnum.SEND_COUPONS_HANDLER)
public class SendCouponsRetryHandler extends BaseHandler implements RetryHandler {

    @Resource
    private IDistributionService distributionService;

    @Resource
    private SendCouponsSetter sendCouponsSetter;


    @Override
    public void mainDo(RetryTaskPO retryTaskPO) {
        super.mainDo(retryTaskPO);
        if (Strings.isNullOrEmpty(retryTaskPO.getParams())) {
            sendCouponsSetter.setNewStatus(retryTaskPO.getId().toString(), RetryTaskStatusEnum.SUCCESS.code());
            return;
        }
        JSONObject params = JSON.parseObject(retryTaskPO.getParams());
        String objectId = params.getString("objectId");
        String tenantId = params.getString("tenantId");
        String dealerId = params.getString("dealerId");
        Long month = params.getLong("month");
        List<String> feeTypes = params.getJSONArray("feeTypes").toJavaList(String.class);
        String downstreamTenantId = params.getString("downstreamTenantId");
        IObjectData dealer = serviceFacade.findObjectData(User.systemUser(tenantId), dealerId, ApiNames.ACCOUNT_OBJ);
        JSONObject extraData = params.getJSONObject("extraData");
        String version = extraData.getString("version");
        if (Strings.isNullOrEmpty(version)) {
            distributionService.dealSingleTenantSendCoupons(tenantId, downstreamTenantId, objectId, dealer, month, feeTypes, extraData);
        } else if ("agreement".equals(version)) {
            distributionService.dealSingleTenantSendCouponsByAgreement(tenantId, downstreamTenantId, objectId, dealer, month, feeTypes, extraData);
        }
        updateMasterData(tenantId, objectId);
        sendCouponsSetter.setNewStatus(retryTaskPO.getId().toString(), RetryTaskStatusEnum.SUCCESS.code());
    }

    private void updateMasterData(String tenantId, String objectId) {
        IObjectData master = serviceFacade.findObjectData(User.systemUser(tenantId), objectId, "SendCouponsObj__c");
        if (!"1".equals(master.get("has_done__c"))) {
            SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                    SearchQueryUtil.filter("master__c", Operator.EQ, Lists.newArrayList(objectId)),
                    SearchQueryUtil.filter("status__c", Operator.IN, Lists.newArrayList("0", "1"))
            ));
            query.setSearchSource("db");
            List<IObjectData> data = QueryDataUtil.find(serviceFacade, tenantId, "SendCouponsDetailObj__c", query, Lists.newArrayList(CommonFields.ID));
            if (CollectionUtils.isEmpty(data)) {
                master.set("has_done__c", "1");
                serviceFacade.updateObjectData(User.systemUser(tenantId), master);
            } else {
                log.info("data is not empty:{}", data);
            }
        }
    }

    @Override
    public void handleException(RetryTaskPO retryTaskPO, Exception e) {
        super.handleException(retryTaskPO, e);
        if (retryTaskPO == null) {
            log.info("task is null");
            return;
        }
        sendCouponsSetter.setNewStatus(retryTaskPO.getId().toString(), RetryTaskStatusEnum.FAIL.code());
    }
}