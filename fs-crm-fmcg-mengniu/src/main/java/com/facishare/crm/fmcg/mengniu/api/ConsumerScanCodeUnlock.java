package com.facishare.crm.fmcg.mengniu.api;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

public interface ConsumerScanCodeUnlock {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "app_id")
        @JsonProperty(value = "app_id")
        @SerializedName("app_id")
        private String appId;

        private String token;

        @JSONField(name = "open_id")
        @JsonProperty(value = "open_id")
        @SerializedName("open_id")
        private String openId;

        @JSONField(name = "union_id")
        @JsonProperty(value = "union_id")
        @SerializedName("union_id")
        private String unionId;

        private String environment;

        @JSONField(name = "qr_code_id")
        @JsonProperty(value = "qr_code_id")
        @SerializedName("qr_code_id")
        private String qrCodeId;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "product_name")
        @JsonProperty(value = "product_name")
        @SerializedName("product_name")
        private String productName;

        @JSONField(name = "product_code")
        @JsonProperty(value = "product_code")
        @SerializedName("product_code")
        private String productCode;
    }
}