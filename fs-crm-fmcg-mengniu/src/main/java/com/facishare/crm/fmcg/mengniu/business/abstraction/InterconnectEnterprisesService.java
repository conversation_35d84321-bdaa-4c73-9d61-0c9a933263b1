package com.facishare.crm.fmcg.mengniu.business.abstraction;

import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/11 18:06
 */
public interface InterconnectEnterprisesService {

    void triggerInterconnection(String tenantId, String objectId, String apiName, String mapperAccountId, String enterpriseAccount);

    void batchInitEnterpriseLinkApps(Integer tenantId, List<String> eas, String linkAppId);

    void initChannelWebNavigationWithLinkAppOpen(Integer tenantId, String linkAppId);

    void createDownstreamEmployee(String upTenantId, String tenantId, List<String> contactIdList);

}
