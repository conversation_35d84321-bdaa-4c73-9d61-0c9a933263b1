package com.facishare.crm.fmcg.mengniu.dto;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Map;

@Data
@ToString
public class RewardLimitConfig implements Serializable {

    public static final String SINGLE_ORDER = "1";
    public static final String MULTIPLE_ORDER = "2";

    private String limitMode;

    private int totalLimit;

    private int monthlyTotalLimit;

    private Map<String, Integer> skuLimit;

    private Map<String, Integer> monthlySkuLimit;
}
