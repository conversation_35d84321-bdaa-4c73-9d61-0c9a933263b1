package com.facishare.crm.fmcg.mengniu.handler;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.mengniu.dto.*;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.SecureRandom;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@SuppressWarnings("Duplicates")
public class ConsumerScanEventHandler extends CommonSalesEventHandler<ConsumerScanEventData> {

    @Override
    protected String buildEventIdentityKey(SalesEvent<ConsumerScanEventData> event) {
        return String.format("CONSUMER_SCAN_CODE.%s", event.getData().getSnId());
    }

    @Override
    protected List<RedPacketReward> calculateRewards(SalesEvent<ConsumerScanEventData> event) {
        // 计算消费者红包
        RedPacketReward consumerReward = calculateConsumerReward(event);
        // 计算店老板红包
        RedPacketReward storeOwnerReward = calculateStoreOwnerReward(event);
        // 计算业代红包
        RedPacketReward salesmenReward = calculateSalesmenReward(event);

        // 校验发放金额
        List<RedPacketReward> rewards = Lists.newArrayList(consumerReward, storeOwnerReward, salesmenReward);
        validateActivityAmount(event.getData().getActivity(), rewards);

        return rewards;
    }

    private void validateActivityAmount(IObjectData activity, List<RedPacketReward> rewards) {
        Boolean allowOverLimitReward = activity.get("allow_over_limit_reward__c", Boolean.class);
        BigDecimal budget = activity.get(TPMActivityFields.ACTIVITY_AMOUNT, BigDecimal.class);

        BigDecimal incomingRewardAmount = calculateIncomingRewardAmount(rewards);
        BigDecimal rewardedAmount = calculateRewardedAmount(activity);

        log.info("after amount information : {} - {} - {}", budget, rewardedAmount, incomingRewardAmount);

        if (budget.compareTo(rewardedAmount.add(incomingRewardAmount)) <= 0) {
            if (Boolean.TRUE.equals(allowOverLimitReward)) {
                for (RedPacketReward reward : rewards) {
                    if (Objects.nonNull(reward)){
                        reward.setOverLimit(true);
                    }
                }
            } else {
                throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_CONSUMER_SCAN_EVENT_HANDLER_0));
            }
        }
    }

    private RedPacketReward calculateStoreOwnerReward(SalesEvent<ConsumerScanEventData> event) {
        RoleRewardAmount amount = calculateRewardAmount(event.getData().getActivity(), RewardConstants.ROLE_STORE_OWNER, event.getData().getStore().getId(), event.getData().getSku().getId());

        List<RedPacketRewardDetail> details = buildupRedPacketRewardDetails(event, amount);

        PaymentAccount from = loadTenantCloudAccountByRole(event.getData().getRelatedBusinessObjectTenant(), event.getData().getActivity(), RewardConstants.ROLE_STORE_OWNER);
        PaymentAccount to = loadWeChatPaymentAccountFromStore(event.getData().getRelatedBusinessObject().getTenantId(), event.getData().getStore().getId());

        String publishModeKey = String.format("%s_publish_mode__c", RewardConstants.ROLE_STORE_OWNER.toLowerCase());
        String publishMode = event.getData().getActivity().get(publishModeKey, String.class, RedPacketReward.PUBLISH_MODE_AUTO);

        return RedPacketReward.of(event, from, to, details, amount, RewardConstants.ROLE_STORE_OWNER, publishMode);
    }


    private RedPacketReward calculateSalesmenReward(SalesEvent<ConsumerScanEventData> event) {
        RoleRewardAmount amount = calculateRewardAmount(event.getData().getActivity(), RewardConstants.ROLE_SALESMEN, event.getData().getStore().getId(), event.getData().getSku().getId());

        List<RedPacketRewardDetail> details = buildupRedPacketRewardDetails(event, amount);

        PaymentAccount from = loadTenantCloudAccountByRole(event.getData().getRelatedBusinessObjectTenant(), event.getData().getActivity(), RewardConstants.ROLE_SALESMEN);
        String rewardPersonId = event.getData().getRelatedBusinessObject().getOwner().get(0);
        PaymentAccount to = PaymentAccount.of(
            ApiNames.PERSONNEL_OBJ,
            String.format("%s.%s", event.getData().getRelatedBusinessObject().getTenantId(), rewardPersonId),
            loadWeChatPaymentAccountFromEmployee(event.getData().getRelatedBusinessObject().getTenantId(), rewardPersonId)
        );

        String publishModeKey = String.format("%s_publish_mode__c", RewardConstants.ROLE_SALESMEN.toLowerCase());
        String publishMode = event.getData().getActivity().get(publishModeKey, String.class, RedPacketReward.PUBLISH_MODE_AUTO);

        return RedPacketReward.of(event, from, to, details, amount, RewardConstants.ROLE_SALESMEN, publishMode);
    }

    private RedPacketReward calculateConsumerReward(SalesEvent<ConsumerScanEventData> event) {
        RoleRewardAmount amount = calculateConsumerRewardAmount(event);

        List<RedPacketRewardDetail> details = buildupRedPacketRewardDetails(event, amount);

        WeChatPaymentAccount account = WeChatPaymentAccount.builder()
            .openId(event.getData().getOpenId())
            .appId(event.getData().getAppId())
            .unionId(event.getData().getUnionId())
            .build();
        PaymentAccount to = PaymentAccount.of(account);
        PaymentAccount from = PaymentAccount.of(loadTenantWeChatAccount(event.getTenantId()));

        return RedPacketReward.of(event, from, to, details, amount, RewardConstants.ROLE_CONSUMER, RedPacketReward.PUBLISH_MODE_AUTO);
    }

    private static List<RedPacketRewardDetail> buildupRedPacketRewardDetails(SalesEvent<ConsumerScanEventData> event, RoleRewardAmount amount) {
        RedPacketRewardDetail detail = new RedPacketRewardDetail();
        detail.setSerialNumberId(event.getData().getSn().getId());
        detail.setSerialNumberName(event.getData().getSn().getName());
        detail.setBatchCode(event.getData().getSn().get("batch_code", String.class));
        detail.setManufactureDate(event.getData().getSn().get("manufacture_date", Long.class));
        detail.setProductId(event.getData().getSku().getId());
        detail.setAmount(amount.getAmount());
        detail.setAmountConfigId(amount.getConfigId());
        return Lists.newArrayList(detail);
    }

    private RoleRewardAmount calculateConsumerRewardAmount(SalesEvent<ConsumerScanEventData> event) {
        IObjectData activity = event.getData().getActivity();

        RewardAmountConfig config = rewardAmountConfigService.get(
                activity.getTenantId(),
                activity.getId(),
                RewardConstants.ROLE_CONSUMER,
                event.getData().getStore().getId(),
                event.getData().getSku().getId());

        if (!tryLock(activity)) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_CONSUMER_SCAN_EVENT_HANDLER_1));
        }

        try {
            int l1Count = Math.max(activity.get("l1_red_packet_count__c", Integer.class, 0) - activity.get("l1_red_packet_record_count__c", Integer.class, 0), 0);
            int l2Count = Math.max(activity.get("l2_red_packet_count__c", Integer.class, 0) - activity.get("l2_red_packet_record_count__c", Integer.class, 0), 0);
            int l3Count = Math.max(activity.get("l3_red_packet_count__c", Integer.class, 0) - activity.get("l3_red_packet_record_count__c", Integer.class, 0), 0);
            int l4Count = Math.max(activity.get("l4_red_packet_count__c", Integer.class, 0) - activity.get("l4_red_packet_record_count__c", Integer.class, 0), 0);
            int l5Count = Math.max(activity.get("l5_red_packet_count__c", Integer.class, 0) - activity.get("l5_red_packet_record_count__c", Integer.class, 0), 0);

            int total = l1Count + l2Count + l3Count + l4Count + l5Count;

            // 随机0到剩余红包总个数区间内的一个数
            int random = random(total);

            String level;

            // 判断随机数命中的区间，发放指定的金额
            if (random < l1Count) {
                level = "1";
            } else if (random < l1Count + l2Count) {
                level = "2";
            } else if (random < l1Count + l2Count + l3Count) {
                level = "3";
            } else if (random < l1Count + l2Count + l3Count + l4Count) {
                level = "4";
            } else {
                level = "5";
            }

            BigDecimal amount = Objects.isNull(config) ?
                    activity.get(String.format("l%s_red_packet_amount__c", level), BigDecimal.class, BigDecimal.ZERO) :
                    config.getConsumerRedPacketAmount(level);

            if (amount.equals(BigDecimal.ZERO)) {
                throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_CONSUMER_SCAN_EVENT_HANDLER_2));
            }
            return RoleRewardAmount.builder().amount(amount).configId(Objects.isNull(config) ? null : config.getId()).consumerRewardLevel(level).build();
        } catch (Exception ex) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_CONSUMER_SCAN_EVENT_HANDLER_3));
        } finally {
            unlock(activity);
        }
    }

    private int random(int total) {
        SecureRandom ran;
        try {
            ran = SecureRandom.getInstance("SHA1PRNG", "SUN");
        } catch (NoSuchAlgorithmException | NoSuchProviderException e) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_CONSUMER_SCAN_EVENT_HANDLER_4));
        }
        ran.setSeed(SecureRandom.getSeed(16));
        return ran.nextInt(total);
    }

    private void unlock(IObjectData activity) {
        String key = buildActivityIdentityKey(activity);
        RLock lock = redissonCmd.getLock(key);

        log.info("unlock activity : {}", key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    private boolean tryLock(IObjectData activity) {
        String key = buildActivityIdentityKey(activity);
        RLock lock = redissonCmd.getLock(key);

        log.info("try lock activity : {}", key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            return true;
        }
        try {
            return lock.tryLock(LOCK_WAIT, LOCK_LEASE, TimeUnit.SECONDS);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_CONSUMER_SCAN_EVENT_HANDLER_5));
        }
    }

    protected String buildActivityIdentityKey(IObjectData activity) {
        return String.format("CONSUMER_SCAN_CODE.ACTIVITY.%s.%s", activity.getTenantId(), activity.getId());
    }

    @Override
    protected void validateEventData(ConsumerScanEventData data) {
        if (Strings.isNullOrEmpty(data.getSnId())) {
            throw new MetaDataBusinessException("serial number id empty.");
        }
        if (Strings.isNullOrEmpty(data.getOpenId())) {
            throw new MetaDataBusinessException("open id empty.");
        }
        if (Strings.isNullOrEmpty(data.getAppId())) {
            throw new MetaDataBusinessException("app id empty.");
        }
    }
}
