package com.facishare.crm.fmcg.mengniu.api;


import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

public interface RewardBizDescribe {
    @Data
    @ToString
    class Arg {
        private String bizKey;
        private String tenantTemplate;
        private String action;
    }

    @Data
    @ToString
    @Builder
    class Result {
        private ObjectDescribeDocument bizDescribe;
    }


}
