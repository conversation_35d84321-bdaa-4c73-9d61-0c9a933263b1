package com.facishare.crm.fmcg.mengniu.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.api.MengNiuTenantInformation;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
public class ConsumerScanEventData implements Serializable {

    private String snId;

    @JSONField(name = "open_id")
    @JsonProperty(value = "open_id")
    @SerializedName("open_id")
    private String openId;

    @JSONField(name = "union_id")
    @JsonProperty(value = "union_id")
    @SerializedName("union_id")
    private String unionId;

    @JSONField(name = "app_id")
    @JsonProperty(value = "app_id")
    @SerializedName("app_id")
    private String appId;

    private String environment;

    private MengNiuTenantInformation relatedBusinessObjectTenant;

    private String relatedBusinessObjectTenantName;

    @JSONField(name = "related_business_object")
    @JsonProperty(value = "related_business_object")
    @SerializedName("related_business_object")
    private IObjectData relatedBusinessObject;

    private IObjectData store;

    private IObjectData sn;

    private IObjectData sku;

    private IObjectData activity;
}