package com.facishare.crm.fmcg.mengniu.dao.po;


import lombok.Data;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

/**
 * Author: linmj
 * Date: 2023/8/9 10:13
 */
@Data
@ToString
public abstract class BasePO {

    public static final String F_TENANT_ID = "tenant_id";

    public static final String F_IS_DELETED = "is_deleted";

    public static final String F_CREATED_BY = "created_by";

    public static final String F_UPDATED_BY = "updated_by";

    public static final String F_CREATED_AT = "created_at";

    public static final String F_UPDATED_AT = "updated_at";


    @Id
    private ObjectId id;

    @Property(F_TENANT_ID)
    private String tenantId;

    @Property(F_IS_DELETED)
    private int isDeleted;

    @Property(F_CREATED_BY)
    private String createdBy;

    @Property(F_UPDATED_BY)
    private String updatedBy;

    @Property(F_CREATED_AT)
    private Long createdAt;

    @Property(F_UPDATED_AT)
    private Long updatedAt;
}
