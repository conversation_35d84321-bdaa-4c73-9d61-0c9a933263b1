package com.facishare.crm.fmcg.mengniu.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.api.MengNiuTenantInformation;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class RewardActivityService {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    protected EnterpriseEditionService enterpriseEditionService;

    public static final String FIXED = "FIXED";
    public static final String CONDITION = "CONDITION";
    public static final String ALL = "ALL";

    public IObjectData findActivityByObjectAction(MengNiuTenantInformation tenant, String storeId, String apiName, String action, String recordType) {
        switch (tenant.getRole()) {
            case MengNiuTenantInformation.ROLE_N:
                return findNActivityByObjectAction(tenant, storeId, apiName, action, recordType);
            case MengNiuTenantInformation.ROLE_M:
                return findMActivityByObjectAction(tenant, storeId, apiName, action, recordType);
            default:
                return null;
        }
    }

    private IObjectData findMActivityByObjectAction(MengNiuTenantInformation tenant, String storeId, String apiName, String action, String recordType) {
        String mAccountId = findTenantRelationAccountId(tenant.getManufacturer().getTenantId(), tenant.getM().getTenantId());
        List<IObjectData> mActivities = queryActivity(tenant.getManufacturer().getTenantId(), recordType, mAccountId, apiName, action);

        IObjectData mActivity = filterActivityByStore(tenant.getManufacturer().getTenantId(), mActivities, storeId);

        String id;
        if (Objects.isNull(mActivity)) {
            String nAccountId = findTenantRelationAccountId(tenant.getManufacturer().getTenantId(), tenant.getN().getTenantId());
            List<IObjectData> nActivities = queryActivity(tenant.getManufacturer().getTenantId(), recordType, nAccountId, apiName, action);

            Map<String, List<IObjectData>> grouped = groupNActivity(tenant.getManufacturer().getTenantId(), nActivities, mAccountId);

            List<IObjectData> fixedData = grouped.get(FIXED);
            mActivity = filterActivityByStore(tenant.getManufacturer().getTenantId(), fixedData, storeId);

            if (Objects.isNull(mActivity)) {
                List<IObjectData> allData = grouped.get(ALL);
                mActivity = filterActivityByStore(tenant.getManufacturer().getTenantId(), allData, storeId);

                if (Objects.isNull(mActivity)) {
                    return null;
                } else {
                    id = mActivity.getId();
                }
            } else {
                id = mActivity.getId();
            }
        } else {
            id = mActivity.getId();
        }

        return serviceFacade.findObjectData(User.systemUser(tenant.getManufacturer().getTenantId()), id, ApiNames.TPM_ACTIVITY_OBJ);
    }

    private IObjectData findNActivityByObjectAction(MengNiuTenantInformation tenant, String storeId, String apiName, String action, String recordType) {
        String nAccountId = findTenantRelationAccountId(tenant.getManufacturer().getTenantId(), tenant.getN().getTenantId());
        List<IObjectData> nActivities = queryActivity(tenant.getManufacturer().getTenantId(), recordType, nAccountId, apiName, action);

        IObjectData activity = filterActivityByStore(tenant.getManufacturer().getTenantId(), nActivities, storeId);

        if (Objects.isNull(activity)) {
            return null;
        }

        return serviceFacade.findObjectData(User.systemUser(tenant.getManufacturer().getTenantId()), activity.getId(), ApiNames.TPM_ACTIVITY_OBJ);
    }

    public IObjectData findActivityByStore(MengNiuTenantInformation tenant, String storeId, String recordType) {
        switch (tenant.getRole()) {
            case MengNiuTenantInformation.ROLE_N:
                return findNActivityByStore(tenant, storeId, recordType);
            case MengNiuTenantInformation.ROLE_M:
                return findMActivityByStore(tenant, storeId, recordType);
            default:
                return null;
        }
    }

    public IObjectData findActivityByStoreAndProduct(MengNiuTenantInformation tenant, String storeId, String productId, String recordType) {
        switch (tenant.getRole()) {
            case MengNiuTenantInformation.ROLE_N:
                return findNActivityByStoreAndProduct(tenant, storeId, productId, recordType);
            case MengNiuTenantInformation.ROLE_M:
                return findMActivityByStoreAndProduct(tenant, storeId, productId, recordType);
            default:
                return null;
        }
    }

    private IObjectData findMActivityByStoreAndProduct(MengNiuTenantInformation tenant, String storeId, String productId, String recordType) {
        String mAccountId = findTenantRelationAccountId(tenant.getManufacturer().getTenantId(), tenant.getM().getTenantId());
        List<IObjectData> mActivities = queryActivity(tenant.getManufacturer().getTenantId(), recordType, mAccountId);

        IObjectData mActivity = filterActivityByStore(tenant.getManufacturer().getTenantId(), mActivities, storeId, productId);

        String id;
        if (Objects.isNull(mActivity)) {
            String nAccountId = findTenantRelationAccountId(tenant.getManufacturer().getTenantId(), tenant.getN().getTenantId());
            List<IObjectData> nActivities = queryActivity(tenant.getManufacturer().getTenantId(), recordType, nAccountId);

            Map<String, List<IObjectData>> grouped = groupNActivity(tenant.getManufacturer().getTenantId(), nActivities, mAccountId);

            List<IObjectData> fixedData = grouped.get(FIXED);
            mActivity = filterActivityByStore(tenant.getManufacturer().getTenantId(), fixedData, storeId, productId);

            if (Objects.isNull(mActivity)) {
                List<IObjectData> allData = grouped.get(ALL);
                mActivity = filterActivityByStore(tenant.getManufacturer().getTenantId(), allData, storeId, productId);

                if (Objects.isNull(mActivity)) {
                    return null;
                } else {
                    id = mActivity.getId();
                }
            } else {
                id = mActivity.getId();
            }
        } else {
            id = mActivity.getId();
        }

        return serviceFacade.findObjectData(User.systemUser(tenant.getManufacturer().getTenantId()), id, ApiNames.TPM_ACTIVITY_OBJ);
    }

    private IObjectData findMActivityByStore(MengNiuTenantInformation tenant, String storeId, String recordType) {
        String mAccountId = findTenantRelationAccountId(tenant.getManufacturer().getTenantId(), tenant.getM().getTenantId());
        List<IObjectData> mActivities = queryActivity(tenant.getManufacturer().getTenantId(), recordType, mAccountId);

        IObjectData mActivity = filterActivityByStore(tenant.getManufacturer().getTenantId(), mActivities, storeId);

        String id;
        if (Objects.isNull(mActivity)) {
            String nAccountId = findTenantRelationAccountId(tenant.getManufacturer().getTenantId(), tenant.getN().getTenantId());
            List<IObjectData> nActivities = queryActivity(tenant.getManufacturer().getTenantId(), recordType, nAccountId);

            Map<String, List<IObjectData>> grouped = groupNActivity(tenant.getManufacturer().getTenantId(), nActivities, mAccountId);

            List<IObjectData> fixedData = grouped.get(FIXED);
            mActivity = filterActivityByStore(tenant.getManufacturer().getTenantId(), fixedData, storeId);

            if (Objects.isNull(mActivity)) {
                List<IObjectData> allData = grouped.get(ALL);
                mActivity = filterActivityByStore(tenant.getManufacturer().getTenantId(), allData, storeId);

                if (Objects.isNull(mActivity)) {
                    return null;
                } else {
                    id = mActivity.getId();
                }
            } else {
                id = mActivity.getId();
            }
        } else {
            id = mActivity.getId();
        }

        return serviceFacade.findObjectData(User.systemUser(tenant.getManufacturer().getTenantId()), id, ApiNames.TPM_ACTIVITY_OBJ);
    }

    private Map<String, List<IObjectData>> groupNActivity(String tenantId, List<IObjectData> nActivity, String mAccountId) {
        if (CollectionUtils.isEmpty(nActivity)) {
            return Maps.newHashMap();
        }

        List<String> ids = nActivity.stream().map(DBRecord::getId).collect(Collectors.toList());
        Map<String, IObjectData> activityMap = nActivity.stream().collect(Collectors.toMap(DBRecord::getId, v -> v));

        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName("activity_id__c");
        activityIdFilter.setOperator(Operator.IN);
        activityIdFilter.setFieldValues(ids);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(activityIdFilter);

        List<IObjectData> ranges = QueryDataUtil.find(serviceFacade, tenantId, "activity_tenant_range__c", stq, Lists.newArrayList(
                "_id",
                "activity_id__c",
                "account_id__c"
        ));

        Map<String, List<String>> rangeGroup = Maps.newHashMap();
        for (String id : ids) {
            rangeGroup.put(id, Lists.newArrayList());
        }

        for (IObjectData range : ranges) {
            String activityId = range.get("activity_id__c", String.class);
            String accountId = range.get("account_id__c", String.class);
            if (rangeGroup.containsKey(activityId)) {
                rangeGroup.get(activityId).add(accountId);
            }
        }

        Map<String, List<IObjectData>> grouped = new HashMap<>();
        grouped.put(ALL, Lists.newArrayList());
        grouped.put(FIXED, Lists.newArrayList());

        for (Map.Entry<String, List<String>> entry : rangeGroup.entrySet()) {
            if (CollectionUtils.isEmpty(entry.getValue())) {
                grouped.get(ALL).add(activityMap.get(entry.getKey()));
            }

            if (entry.getValue().contains(mAccountId)) {
                grouped.get(FIXED).add(activityMap.get(entry.getKey()));
            }
        }

        return grouped;
    }

    private IObjectData findNActivityByStore(MengNiuTenantInformation tenant, String storeId, String recordType) {
        String nAccountId = findTenantRelationAccountId(tenant.getManufacturer().getTenantId(), tenant.getN().getTenantId());
        List<IObjectData> nActivities = queryActivity(tenant.getManufacturer().getTenantId(), recordType, nAccountId);

        IObjectData activity = filterActivityByStore(tenant.getManufacturer().getTenantId(), nActivities, storeId);

        if (Objects.isNull(activity)) {
            return null;
        }

        return serviceFacade.findObjectData(User.systemUser(tenant.getManufacturer().getTenantId()), activity.getId(), ApiNames.TPM_ACTIVITY_OBJ);
    }

    private IObjectData findNActivityByStoreAndProduct(MengNiuTenantInformation tenant, String storeId, String productId, String recordType) {
        String nAccountId = findTenantRelationAccountId(tenant.getManufacturer().getTenantId(), tenant.getN().getTenantId());
        List<IObjectData> nActivities = queryActivity(tenant.getManufacturer().getTenantId(), recordType, nAccountId);

        IObjectData activity = filterActivityByStore(tenant.getManufacturer().getTenantId(), nActivities, storeId, productId);

        if (Objects.isNull(activity)) {
            return null;
        }

        return serviceFacade.findObjectData(User.systemUser(tenant.getManufacturer().getTenantId()), activity.getId(), ApiNames.TPM_ACTIVITY_OBJ);
    }

    private List<IObjectData> queryActivity(String tenantId, String recordType, String accountId) {
        if (Strings.isNullOrEmpty(accountId)) {
            return Lists.newArrayList();
        }

        IFilter dealerIdFilter = new Filter();
        dealerIdFilter.setFieldName("reward_activity_tenant__c");
        dealerIdFilter.setOperator(Operator.EQ);
        dealerIdFilter.setFieldValues(Lists.newArrayList(accountId));

        IFilter activityStatusFilter = new Filter();
        activityStatusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        activityStatusFilter.setOperator(Operator.EQ);
        activityStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS));

        IFilter activityTypeFilter = new Filter();
        activityTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
        activityTypeFilter.setOperator(Operator.EQ);
        activityTypeFilter.setFieldValues(Lists.newArrayList(recordType));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
                Lists.newArrayList(dealerIdFilter, activityStatusFilter, activityTypeFilter),
                Lists.newArrayList(order)
        );

        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_OBJ, stq, Lists.newArrayList(
                CommonFields.ID,
                TPMActivityFields.STORE_RANGE,
                TPMActivityFields.PRODUCT_RANGE
        ));
    }

    private List<IObjectData> queryActivity(String tenantId, String recordType, String accountId, String apiName, String action) {
        if (Strings.isNullOrEmpty(accountId)) {
            return Lists.newArrayList();
        }

        IFilter dealerIdFilter = new Filter();
        dealerIdFilter.setFieldName("reward_activity_tenant__c");
        dealerIdFilter.setOperator(Operator.EQ);
        dealerIdFilter.setFieldValues(Lists.newArrayList(accountId));

        IFilter objectApiNameFilter = new Filter();
        objectApiNameFilter.setFieldName("action_rule_object__c");
        objectApiNameFilter.setOperator(Operator.EQ);
        objectApiNameFilter.setFieldValues(Lists.newArrayList(apiName));

        IFilter objectActionFilter = new Filter();
        objectActionFilter.setFieldName("action_rule_object_action__c");
        objectActionFilter.setOperator(Operator.EQ);
        objectActionFilter.setFieldValues(Lists.newArrayList(action));

        IFilter activityStatusFilter = new Filter();
        activityStatusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        activityStatusFilter.setOperator(Operator.EQ);
        activityStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS));

        IFilter activityTypeFilter = new Filter();
        activityTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
        activityTypeFilter.setOperator(Operator.EQ);
        activityTypeFilter.setFieldValues(Lists.newArrayList(recordType));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
                Lists.newArrayList(dealerIdFilter, objectApiNameFilter, objectActionFilter, activityStatusFilter, activityTypeFilter),
                Lists.newArrayList(order)
        );

        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_OBJ, stq, Lists.newArrayList(
                CommonFields.ID,
                TPMActivityFields.STORE_RANGE,
                TPMActivityFields.PRODUCT_RANGE
        ));
    }

    private IObjectData filterActivityByStore(String tenantId, List<IObjectData> data, String storeId, String productId) {
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }

        List<ActivityExt> activities = fillExtInformation(tenantId, data);

        Map<String, List<ActivityExt>> grouped = activities.stream().collect(Collectors.groupingBy(ActivityExt::getStoreRangeType));

        List<ActivityExt> fixedData = grouped.get(FIXED);
        if (!CollectionUtils.isEmpty(fixedData)) {
            List<ActivityExt> matchedFixedData = fixedData.stream().filter(f -> f.getStoreIds().contains(storeId)).collect(Collectors.toList());
            ActivityExt activity = filterActivityByProduct(tenantId, matchedFixedData, productId);
            if (!Objects.isNull(activity)) {
                return activity.getActivity();
            }
        }

        List<ActivityExt> conditionData = grouped.get(CONDITION);
        if (!CollectionUtils.isEmpty(conditionData)) {
            List<ActivityExt> matchedConditionData = Lists.newArrayList();
            for (ActivityExt conditionDatum : conditionData) {
                if (storeInConditionRange(tenantId, conditionDatum, storeId)) {
                    matchedConditionData.add(conditionDatum);
                }
            }

            ActivityExt activity = filterActivityByProduct(tenantId, matchedConditionData, productId);
            if (!Objects.isNull(activity)) {
                return activity.getActivity();
            }
        }

        List<ActivityExt> allData = grouped.get(ALL);
        if (!CollectionUtils.isEmpty(allData)) {
            ActivityExt activity = filterActivityByProduct(tenantId, allData, productId);
            if (!Objects.isNull(activity)) {
                return activity.getActivity();
            }
        }

        return null;
    }

    private IObjectData filterActivityByStore(String tenantId, List<IObjectData> data, String storeId) {
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }

        List<ActivityExt> activities = fillExtInformationExcludeProduct(tenantId, data);

        Map<String, List<ActivityExt>> grouped = activities.stream().collect(Collectors.groupingBy(ActivityExt::getStoreRangeType));

        List<ActivityExt> fixedData = grouped.get(FIXED);
        if (!CollectionUtils.isEmpty(fixedData)) {
            for (ActivityExt fixedDatum : fixedData) {
                if (fixedDatum.getStoreIds().contains(storeId)) {
                    return fixedDatum.getActivity();
                }
            }
        }

        List<ActivityExt> conditionData = grouped.get(CONDITION);
        if (!CollectionUtils.isEmpty(conditionData)) {
            for (ActivityExt conditionDatum : conditionData) {
                if (storeInConditionRange(tenantId, conditionDatum, storeId)) {
                    return conditionDatum.getActivity();
                }
            }
        }

        List<ActivityExt> allData = grouped.get(ALL);
        if (!CollectionUtils.isEmpty(allData)) {
            return allData.get(0).getActivity();
        }

        return null;
    }

    private ActivityExt filterActivityByProduct(String tenantId, List<ActivityExt> activities, String productId) {
        if (CollectionUtils.isEmpty(activities)) {
            return null;
        }

        Map<String, List<ActivityExt>> grouped = activities.stream().collect(Collectors.groupingBy(ActivityExt::getProductRangeType));

        List<ActivityExt> fixedData = grouped.get(FIXED);
        if (!CollectionUtils.isEmpty(fixedData)) {
            for (ActivityExt fixedDatum : fixedData) {
                if (fixedDatum.getProductIds().contains(productId)) {
                    return fixedDatum;
                }
            }
        }

        List<ActivityExt> conditionData = grouped.get(CONDITION);
        if (!CollectionUtils.isEmpty(conditionData)) {
            for (ActivityExt conditionDatum : conditionData) {
                if (productInConditionRange(tenantId, conditionDatum, productId)) {
                    return conditionDatum;
                }
            }
        }

        List<ActivityExt> allData = grouped.get(ALL);
        if (!CollectionUtils.isEmpty(allData)) {
            return allData.get(0);
        }

        return null;
    }

    private List<ActivityExt> fillExtInformation(String tenantId, List<IObjectData> data) {
        if (CollectionUtils.isEmpty(data)) {
            return Lists.newArrayList();
        }

        List<ActivityExt> activityExtList = Lists.newArrayList();

        List<String> storeFixedIds = Lists.newArrayList();
        List<String> productFixedIds = Lists.newArrayList();

        for (IObjectData datum : data) {
            ActivityExt ext = new ActivityExt();
            ext.setId(datum.getId());
            ext.setActivity(datum);

            String storeRangeJson = datum.get(TPMActivityFields.STORE_RANGE, String.class);
            if (!Strings.isNullOrEmpty(storeRangeJson)) {
                JSONObject storeJson = JSON.parseObject(storeRangeJson);
                String storeRangeType = storeJson.getString("type");

                ext.setStoreRangeType(storeRangeType);
                if (ext.getStoreRangeType().equals(CONDITION)) {
                    List<Wheres> wheres = JSON.parseArray(storeJson.getString("value"), Wheres.class);
                    ext.setStoreWheres(wheres);
                }
                if (ext.getStoreRangeType().equals(FIXED)) {
                    storeFixedIds.add(datum.getId());
                }
            }

            String productRangeJson = datum.get(TPMActivityFields.PRODUCT_RANGE, String.class);
            if (!Strings.isNullOrEmpty(productRangeJson)) {
                JSONObject productJson = JSON.parseObject(productRangeJson);
                String productRangeType = productJson.getString("type");

                ext.setProductRangeType(productRangeType);
                if (ext.getProductRangeType().equals(CONDITION)) {
                    List<Wheres> wheres = JSON.parseArray(productJson.getString("value"), Wheres.class);
                    ext.setProductWheres(wheres);
                }
                if (ext.getProductRangeType().equals(FIXED)) {
                    productFixedIds.add(datum.getId());
                }
            }

            activityExtList.add(ext);
        }

        Map<String, List<IObjectData>> storeRangeMap;
        if (!CollectionUtils.isEmpty(storeFixedIds)) {
            IFilter storeActivityIdFilter = new Filter();
            storeActivityIdFilter.setFieldName(TPMActivityStoreFields.ACTIVITY_ID);
            storeActivityIdFilter.setOperator(Operator.IN);
            storeActivityIdFilter.setFieldValues(Lists.newArrayList(storeFixedIds));

            SearchTemplateQuery storeRangeQuery = QueryDataUtil.minimumQuery(storeActivityIdFilter);

            List<IObjectData> storeRange = QueryDataUtil.find(
                    serviceFacade,
                    tenantId,
                    ApiNames.TPM_ACTIVITY_STORE_OBJ,
                    storeRangeQuery,
                    Lists.newArrayList("_id", TPMActivityStoreFields.ACTIVITY_ID, TPMActivityStoreFields.STORE_ID)
            );

            storeRangeMap = storeRange.stream().collect(Collectors.groupingBy(g -> g.get(TPMActivityStoreFields.ACTIVITY_ID, String.class)));
        } else {
            storeRangeMap = Maps.newHashMap();
        }

        Map<String, List<IObjectData>> productRangeMap;
        if (!CollectionUtils.isEmpty(productFixedIds)) {
            IFilter productActivityIdFilter = new Filter();
            productActivityIdFilter.setFieldName(TPMActivityStoreFields.ACTIVITY_ID);
            productActivityIdFilter.setOperator(Operator.IN);
            productActivityIdFilter.setFieldValues(Lists.newArrayList(productFixedIds));

            SearchTemplateQuery productRangeQuery = QueryDataUtil.minimumQuery(productActivityIdFilter);

            List<IObjectData> productRange = QueryDataUtil.find(
                    serviceFacade,
                    tenantId,
                    ApiNames.TPM_ACTIVITY_PRODUCT_RANGE_OBJ,
                    productRangeQuery,
                    Lists.newArrayList("_id", TPMActivityProductRangeFields.ACTIVITY_ID, TPMActivityProductRangeFields.PRODUCT_ID)
            );

            productRangeMap = productRange.stream().collect(Collectors.groupingBy(g -> g.get(TPMActivityProductRangeFields.ACTIVITY_ID, String.class)));
        } else {
            productRangeMap = Maps.newHashMap();
        }

        for (ActivityExt activityExt : activityExtList) {
            if (storeRangeMap.containsKey(activityExt.getId())) {
                List<String> storeIds = storeRangeMap.get(activityExt.getId()).stream().map(m -> m.get(TPMActivityStoreFields.STORE_ID, String.class)).collect(Collectors.toList());
                activityExt.setStoreIds(storeIds);
            }

            if (productRangeMap.containsKey(activityExt.getId())) {
                List<String> productIds = productRangeMap.get(activityExt.getId()).stream().map(m -> m.get(TPMActivityProductRangeFields.PRODUCT_ID, String.class)).collect(Collectors.toList());
                activityExt.setProductIds(productIds);
            }
        }

        return activityExtList;
    }

    private List<ActivityExt> fillExtInformationExcludeProduct(String tenantId, List<IObjectData> data) {
        if (CollectionUtils.isEmpty(data)) {
            return Lists.newArrayList();
        }

        List<ActivityExt> activityExtList = Lists.newArrayList();

        List<String> storeFixedIds = Lists.newArrayList();

        for (IObjectData datum : data) {
            ActivityExt ext = new ActivityExt();
            ext.setId(datum.getId());
            ext.setActivity(datum);

            String storeRangeJson = datum.get(TPMActivityFields.STORE_RANGE, String.class);
            if (!Strings.isNullOrEmpty(storeRangeJson)) {
                JSONObject storeJson = JSON.parseObject(storeRangeJson);
                String storeRangeType = storeJson.getString("type");

                ext.setStoreRangeType(storeRangeType);
                if (ext.getStoreRangeType().equals(CONDITION)) {
                    List<Wheres> wheres = JSON.parseArray(storeJson.getString("value"), Wheres.class);
                    ext.setStoreWheres(wheres);
                }
                if (ext.getStoreRangeType().equals(FIXED)) {
                    storeFixedIds.add(datum.getId());
                }
            }

            activityExtList.add(ext);
        }

        Map<String, List<IObjectData>> storeRangeMap;
        if (!CollectionUtils.isEmpty(storeFixedIds)) {
            IFilter storeActivityIdFilter = new Filter();
            storeActivityIdFilter.setFieldName(TPMActivityStoreFields.ACTIVITY_ID);
            storeActivityIdFilter.setOperator(Operator.IN);
            storeActivityIdFilter.setFieldValues(Lists.newArrayList(storeFixedIds));

            SearchTemplateQuery storeRangeQuery = QueryDataUtil.minimumQuery(storeActivityIdFilter);

            List<IObjectData> storeRange = QueryDataUtil.find(
                    serviceFacade,
                    tenantId,
                    ApiNames.TPM_ACTIVITY_STORE_OBJ,
                    storeRangeQuery,
                    Lists.newArrayList("_id", TPMActivityStoreFields.ACTIVITY_ID, TPMActivityStoreFields.STORE_ID)
            );

            storeRangeMap = storeRange.stream().collect(Collectors.groupingBy(g -> g.get(TPMActivityStoreFields.ACTIVITY_ID, String.class)));
        } else {
            storeRangeMap = Maps.newHashMap();
        }

        for (ActivityExt activityExt : activityExtList) {
            if (storeRangeMap.containsKey(activityExt.getId())) {
                List<String> storeIds = storeRangeMap.get(activityExt.getId()).stream().map(m -> m.get(TPMActivityStoreFields.STORE_ID, String.class)).collect(Collectors.toList());
                activityExt.setStoreIds(storeIds);
            }
        }

        return activityExtList;
    }

    private boolean productInConditionRange(String tenantId, ActivityExt activity, String storeId) {
        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(storeId));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);

        stq.setWheres(activity.getProductWheres());
        stq.setLimit(1);

        List<IObjectData> store = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.PRODUCT_OBJ,
                stq,
                Lists.newArrayList("_id")
        );

        return CollectionUtils.isNotEmpty(store);
    }

    private boolean storeInConditionRange(String tenantId, ActivityExt activity, String storeId) {
        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(storeId));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);

        stq.setWheres(activity.getStoreWheres());
        stq.setLimit(1);

        List<IObjectData> store = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ACCOUNT_OBJ,
                stq,
                Lists.newArrayList("_id")
        );

        return CollectionUtils.isNotEmpty(store);
    }

    private String findTenantRelationAccountId(String upstreamTenantId, String downstreamTenantId) {
        if (upstreamTenantId.equals("89150") && downstreamTenantId.equals("M")) {
            return "64cc9b115d39b900017961e8";
        }
        if (upstreamTenantId.equals("89150") && downstreamTenantId.equals("N")) {
            return "64bb4afcf01eca0001fe01dd";
        }

        String downstreamTenantAccount = enterpriseEditionService.getEnterpriseAccount(Integer.parseInt(downstreamTenantId));

        IFilter enterpriseAccountFilter = new Filter();
        enterpriseAccountFilter.setFieldName("enterprise_account");
        enterpriseAccountFilter.setOperator(Operator.EQ);
        enterpriseAccountFilter.setFieldValues(Lists.newArrayList(downstreamTenantAccount));

        IFilter relationTypeFilter = new Filter();
        relationTypeFilter.setFieldName("relation_type");
        relationTypeFilter.setOperator(Operator.EQ);
        relationTypeFilter.setFieldValues(Lists.newArrayList("1"));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(enterpriseAccountFilter, relationTypeFilter);
        stq.setLimit(1);

        List<IObjectData> data = QueryDataUtil.find(serviceFacade, upstreamTenantId, "EnterpriseRelationObj", stq, Lists.newArrayList(
                "_id", "mapper_account_id"
        ));

        if (CollectionUtils.isEmpty(data)) {
            return null;
        }

        return data.get(0).get("mapper_account_id", String.class);
    }

    @Data
    @ToString
    static class ActivityExt implements Serializable {

        private String id;

        private IObjectData activity;

        private String storeRangeType;

        private List<String> storeIds;

        private List<Wheres> storeWheres;

        private String productRangeType;

        private List<String> productIds;

        private List<Wheres> productWheres;
    }
}
