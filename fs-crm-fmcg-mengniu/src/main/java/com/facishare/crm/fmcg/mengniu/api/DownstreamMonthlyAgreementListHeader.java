package com.facishare.crm.fmcg.mengniu.api;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * author: wuyx
 * description:
 * createTime: 2023/11/6 15:56
 */

public interface DownstreamMonthlyAgreementListHeader {

    @Data
    @ToString
    class Arg implements Serializable {

        /**
         * description:  CostApprove__c _id
         * createTime: 2023/11/7 15:01
         */
        @JSONField(name = "_id")
        @JsonProperty(value = "_id")
        @SerializedName("_id")
        private String id;

    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "header_list")
        @JsonProperty(value = "header_list")
        @SerializedName("header_list")
        private List<MengNiuHeaderVO> headerList;

        @JSONField(name = "enable_cancel_agreement_button")
        @JsonProperty(value = "enable_cancel_agreement_button")
        @SerializedName("enable_cancel_agreement_button")
        private boolean enableCancelAgreementButton;

        @JSONField(name = "cancel_agreement_button_name")
        @JsonProperty(value = "cancel_agreement_button_name")
        @SerializedName("cancel_agreement_button_name")
        private String cancelAgreementButtonName;
    }
}