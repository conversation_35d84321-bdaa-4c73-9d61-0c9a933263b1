package com.facishare.crm.fmcg.mengniu.dto;

import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
public class SnData implements Serializable {

    private String sn;

    private String productId;

    private String productCode;

    @SuppressWarnings("unchecked")
    public static List<SnData> fromDeliveryNoteDetail(IObjectData deliveryNoteDetail) {
        List<String> snCodes = deliveryNoteDetail.get("unique_product_code_combination", List.class);
        if (CollectionUtils.isEmpty(snCodes)) {
            return Lists.newArrayList();
        }
        List<SnData> data = Lists.newArrayList();
        for (String snCode : snCodes) {
            SnData sn = new SnData();
            sn.setSn(snCode);
            sn.setProductId(deliveryNoteDetail.get("product_id", String.class));
            sn.setProductCode(deliveryNoteDetail.get("__product_code", String.class));
            data.add(sn);
        }
        return data;
    }

    public static SnData fromSnObj(IObjectData snObj, IObjectData skuObj) {
        SnData sn = new SnData();
        sn.setSn(snObj.get("_id", String.class));
        sn.setProductId(snObj.get("product_id", String.class));
        sn.setProductCode(skuObj.get("product_code", String.class));
        return sn;
    }
}
