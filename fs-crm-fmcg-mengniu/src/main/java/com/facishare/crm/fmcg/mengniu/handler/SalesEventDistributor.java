package com.facishare.crm.fmcg.mengniu.handler;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.mengniu.dto.ConsumerScanEventData;
import com.facishare.crm.fmcg.mengniu.dto.SalesEvent;
import com.facishare.crm.fmcg.mengniu.dto.SignInGoodsEventData;
import com.facishare.crm.fmcg.mengniu.dto.StoreStockCheckEventData;
import com.facishare.crm.fmcg.tpm.web.service.TenantHierarchyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class SalesEventDistributor {

    @Resource
    private SignInGoodsEventHandler signInGoodsEventHandler;
    @Resource
    private MultipleActivitySignInGoodsEventHandler multipleActivitySignInGoodsEventHandler;
    @Resource
    private StoreStockCheckEventHandler storeStockCheckEventHandler;
    @Resource
    private MultipleActivityStoreStockCheckEventHandler multipleActivityStoreStockCheckEventHandler;
    @Resource
    private ConsumerScanEventHandler consumerScanEventHandler;

    public void process(JSONObject message) {
        String eventType = message.getString("event_type");
        switch (eventType) {
            case "SIGN_IN_GOODS":
                SalesEvent<SignInGoodsEventData> signInGoodsEventData = convertToSignInGoodsEvent(message);
                if (TPMGrayUtils.isAllowMultipleActivitySignInGoods(signInGoodsEventData.getTenantId())) {
                    multipleActivitySignInGoodsEventHandler.invoke(signInGoodsEventData);
                } else {
                    signInGoodsEventHandler.invoke(signInGoodsEventData);
                }
                break;
            case "STORE_STOCK_CHECK":
                SalesEvent<StoreStockCheckEventData> storeStockCheckEventData = convertToStoreStockCheckEvent(message);
                if (TPMGrayUtils.isAllowMultipleActivitySignInGoods(storeStockCheckEventData.getTenantId())) {
                    multipleActivityStoreStockCheckEventHandler.invoke(storeStockCheckEventData);
                } else {
                    storeStockCheckEventHandler.invoke(storeStockCheckEventData);
                }
                break;
            case "CONSUMER_SCAN":
                consumerScanEventHandler.invoke(convertToConsumerScanEvent(message));
                break;
            default:
                log.warn("unknown sales event type : {}", eventType);
                break;
        }
    }

    private SalesEvent<ConsumerScanEventData> convertToConsumerScanEvent(JSONObject message) {
        return message.toJavaObject(new TypeReference<SalesEvent<ConsumerScanEventData>>() {
        });
    }

    private SalesEvent<SignInGoodsEventData> convertToSignInGoodsEvent(JSONObject message) {
        return message.toJavaObject(new TypeReference<SalesEvent<SignInGoodsEventData>>() {
        });
    }

    private SalesEvent<StoreStockCheckEventData> convertToStoreStockCheckEvent(JSONObject message) {
        return message.toJavaObject(new TypeReference<SalesEvent<StoreStockCheckEventData>>() {
        });
    }
}
