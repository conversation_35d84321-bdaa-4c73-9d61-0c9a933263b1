package com.facishare.crm.fmcg.mengniu.business;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.mengniu.business.abstraction.IMengNiuConfig;
import com.facishare.crm.fmcg.mengniu.dto.CheckEnableAIRule;
import com.github.autoconf.ConfigFactory;
import org.springframework.stereotype.Service;

/**
 * Author: linmj
 * Date: 2024/3/5 19:42
 */
//IgnoreI18nFile
@Service
public class MengNiuConfig implements IMengNiuConfig {


    @Override
    public CheckEnableAIRule.Result checkEnableAIRule(CheckEnableAIRule.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        CheckEnableAIRule.Result result = new CheckEnableAIRule.Result();
        result.setShowRulePage("ShelfReportObj".equals(arg.getApiName()) || "PurchaseReportingObj".equals(arg.getApiName()));
        result.setProductRangeApiName("ProductGroupObj__c");
        result.setProductRangeLabel("产品组合");
        result.setRuleCountLimit(ConfigFactory.getConfig("fs-fmcg-tpm-config").getInt("AI_RULE_COUNT_LIMIT", 8));

        result.setOrderProductRule(CheckEnableAIRule.OrderProductRule.builder()
                .isShowRulePage(ApiNames.SALES_ORDER_OBJ.equals(arg.getApiName()))
                .productRangeLabel("产品组合").productRangeApiName("ProductGroupObj__c")
                .ruleAndCountLimit(ConfigFactory.getConfig("fs-fmcg-tpm-config").getInt("PRODUCT_RULE_AND_COUNT_LIMIT", 8))
                .ruleOrCountLimit(ConfigFactory.getConfig("fs-fmcg-tpm-config").getInt("PRODUCT_RULE_OR_COUNT_LIMIT", 5)).build());
        return result;
    }
}
