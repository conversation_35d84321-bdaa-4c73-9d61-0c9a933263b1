package com.facishare.crm.fmcg.mengniu.dto;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Getter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@ToString
public class RewardAmountConfig implements Serializable {

    private final String id;

    private final String activityId;

    private final String storeId;

    private final String productId;

    private final String role;

    private final BigDecimal amount;

    private final BigDecimal l1RedPacketAmount;

    private final BigDecimal l2RedPacketAmount;

    private final BigDecimal l3RedPacketAmount;

    private final BigDecimal l4RedPacketAmount;

    private final BigDecimal l5RedPacketAmount;

    public BigDecimal getConsumerRedPacketAmount(String level) {
        switch (level) {
            case "1":
                return this.l1RedPacketAmount;
            case "2":
                return this.l2RedPacketAmount;
            case "3":
                return this.l3RedPacketAmount;
            case "4":
                return this.l4RedPacketAmount;
            case "5":
                return this.l5RedPacketAmount;
            default:
                return BigDecimal.ZERO;
        }
    }

    public RewardAmountConfig(IObjectData config) {
        this.id = config.get("_id", String.class);
        this.activityId = config.get("activity_id__c", String.class);
        this.storeId = config.get("store_id__c", String.class);
        this.productId = config.get("product_id__c", String.class);
        this.role = config.get("role__c", String.class);
        this.amount = config.get("amount__c", BigDecimal.class);
        this.l1RedPacketAmount = config.get("l1_red_packet_amount__c", BigDecimal.class, BigDecimal.ZERO);
        this.l2RedPacketAmount = config.get("l2_red_packet_amount__c", BigDecimal.class, BigDecimal.ZERO);
        this.l3RedPacketAmount = config.get("l3_red_packet_amount__c", BigDecimal.class, BigDecimal.ZERO);
        this.l4RedPacketAmount = config.get("l4_red_packet_amount__c", BigDecimal.class, BigDecimal.ZERO);
        this.l5RedPacketAmount = config.get("l5_red_packet_amount__c", BigDecimal.class, BigDecimal.ZERO);
    }
}
