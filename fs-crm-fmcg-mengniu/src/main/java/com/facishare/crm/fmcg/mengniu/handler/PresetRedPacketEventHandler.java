package com.facishare.crm.fmcg.mengniu.handler;

import com.facishare.appserver.checkins.api.model.SendRedEnvelopeRealNameAuthMessage;
import com.facishare.appserver.checkins.api.service.ShopMMService;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.RedPacketRecordFields;
import com.facishare.crm.fmcg.common.apiname.RedPacketRecordObjFields;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
//IgnoreI18nFile
@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class PresetRedPacketEventHandler {

    protected static String STORE_MASTER_CODE = "1";

    @Resource
    private ShopMMService shopMMService;

    @Resource
    private ServiceFacade serviceFacade;

    public void userAuthRemind(IObjectData objectData) {
        if (objectData == null) {
            return;
        }
        String idCard = objectData.get(RedPacketRecordObjFields.TRANSFEREE_ID, String.class);
        String realName = objectData.get(RedPacketRecordObjFields.TRANSFEREE_NAME, String.class);
        String phone = objectData.get(RedPacketRecordObjFields.TRANSFEREE_PHONE, String.class);
        String openId = objectData.get(RedPacketRecordObjFields.TRANSFEREE_WECHAT_OPEN_ID, String.class);
        String appId = objectData.get(RedPacketRecordObjFields.TRANSFEREE_WECHAT_APP_ID, String.class);

        if (validateUserInfo(idCard, realName, phone, openId, appId)) {
            // 调外勤接口，
            try {
                SendRedEnvelopeRealNameAuthMessage.Args args = buildSendMessageArgs(objectData);
                if (args != null) {
                    log.info("SendRedEnvelopeRealNameAuthMessage args is {}", args);
                    shopMMService.sendRedEnvelopeRealNameAuthMessage(args);
                }
            } catch (Exception ex) {
                log.warn("SendRedEnvelopeRealNameAuthMessage fail..", ex);
            }
        }

    }

    private SendRedEnvelopeRealNameAuthMessage.Args buildSendMessageArgs(IObjectData objectData) {
        String tenantId = objectData.get(RedPacketRecordObjFields.TRANSFEREE_TENANT_ID, String.class);
        String rewardPersonId = objectData.get(RedPacketRecordObjFields.REWARDED_PERSON_ID, String.class);
        long createTime = objectData.get(CommonFields.CREATE_TIME, Long.class);
        BigDecimal amount = objectData.get(RedPacketRecordObjFields.REWARD_AMOUNT, BigDecimal.class);
        String code = objectData.get(CommonFields.NAME, String.class);
        String rewardPartCode = objectData.get(RedPacketRecordObjFields.REWARD_PART_CODE, String.class);
        if (!STORE_MASTER_CODE.equals(rewardPartCode)) {
            return null;
        }
        if (Strings.isNullOrEmpty(rewardPersonId)) {
            return null;
        }
        String[] split = rewardPersonId.split("\\.");
        if (split.length <= 1) {
            return null;
        }
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String activityName = findActivityName(objectData, objectData.get(RedPacketRecordObjFields.ACTIVITY_ID, String.class));
        SendRedEnvelopeRealNameAuthMessage.Args args = new SendRedEnvelopeRealNameAuthMessage.Args();
        args.setTenantId(Long.parseLong(tenantId));
        args.setOutTenantId(split[0]);
        args.setOuterUserId(Long.parseLong(split[1]));
        args.setActivityName(activityName);
        args.setActivityTime(sf.format(new Date(createTime)));
        args.setActivityNo(code);
        args.setRedEnvelopeAmount(String.format("%s元", amount));
        return args;
    }

    private String findActivityName(IObjectData objectData, String activityId) {
        String activityName = "激励活动";
        try {
            activityName = serviceFacade.findObjectData(User.systemUser(objectData.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ).getName();
        } catch (ObjectDataNotFoundException exception) {
            log.warn("buildSendMessageArgs activity not find..", exception);
        }
        return activityName;
    }

    private boolean validateUserInfo(String... userInfo) {
        for (String info : userInfo) {
            if (Strings.isNullOrEmpty(info)) {
                return true;
            }
        }
        return false;
    }

}

